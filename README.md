# 🏢 Portal Karyawan Mobile App - PSG-BMI

Aplikasi portal karyawan mobile dengan **desain modern bergaya mBanking** yang telah disempurnakan. Dibangun menggunakan React Native dan Expo dengan fokus pada **UI/UX premium** dan **performance optimal**.

## ✨ Fitur Utama

- 🔒 **Header Modern** dengan profile dan notifikasi
- 📱 **UI/UX Premium** bergaya mBanking dengan Material Design 3
- 🎯 **Menu Grid Responsif** dengan 8 aplikasi utama dan animasi micro-interaction
- 🔄 **Auto-scroll Carousel** dengan smooth animations dan scale effects
- 📊 **Dashboard Overview** dengan statistik harian real-time
- � **Video Gallery** dengan thumbnail dan metadata
- 📋 **Recent Activities** dengan timeline interaktif
- 📱 **Bottom Navigation** dengan FAB dan badge notifications
- ♿ **Accessibility Support** dengan screen reader dan keyboard navigation
- 🛡️ **Error Boundary** untuk graceful error handling

## 🛠 Teknologi & Stack

### Core Framework
- **React Native 0.72.10** - Cross-platform mobile development
- **Expo SDK 49** - Development platform dan tooling
- **React Hooks** - Modern state management

### UI/UX Libraries
- **React Native Safe Area Context** - Safe area handling
- **Expo Vector Icons** - Material Icons & Feather Icons
- **Animated API** - Smooth animations dan micro-interactions

### Design System
- **Material Design 3** - Modern design language
- **Custom Color Palette** - Consistent branding
- **Typography System** - Hierarchical text styling
- **8px Grid System** - Consistent spacing

## Instalasi

1. Pastikan Node.js dan npm terinstal di sistem Anda
2. Install Expo CLI secara global:
   ```bash
   npm install -g expo-cli
   ```
3. Clone repositori ini
4. Masuk ke direktori proyek:
   ```bash
   cd psg-bmi
   ```
5. Install dependensi:
   ```bash
   npm install
   ```
6. Jalankan aplikasi:
   ```bash
   npm start
   ```

## 🎯 Struktur Menu & Fitur

### Quick Actions (8 Menu Utama)
1. **Attendance Recording** 🕒
   - Informasi absensi bulanan
   - Status kehadiran real-time
   - Pengajuan izin/cuti

2. **ATR Pribadi** 🧾
   - Pengajuan kerja
   - Dinas pribadi
   - Status approval

3. **CNM (Maintenance)** 🔧
   - Sistem maintenance
   - Work orders
   - Equipment status

4. **New Points** ⭐
   - Reward points system
   - Achievement tracking
   - Redemption catalog

5. **SAP** 🗂
   - Modul SAP SHE
   - Greencard system
   - IUT & OTT

6. **iPeak** 📈
   - Performance tracking
   - Penilaian karyawan
   - Kompetensi development

7. **Production** 📊
   - Data produksi harian
   - Grafik & statistik
   - KPI monitoring

8. **View All** �
   - Semua aplikasi
   - Search functionality
   - Kategori filter

### Dashboard Features
- **Today's Overview**: Check-in time, attendance rate, pending tasks
- **Featured Videos**: Company updates, training materials
- **Recent Activities**: Timeline aktivitas terbaru

## 🎨 Design System & UI/UX

### Color Palette
- **Primary**: #2196F3 (Material Blue)
- **Background**: #F8F9FA (Light Gray)
- **Surface**: #FFFFFF (Pure White)
- **Text**: #212121 (Dark Gray) / #757575 (Medium Gray)
- **Accent Colors**: Success (#4CAF50), Warning (#FF9800), Error (#FF5252)

### Typography
- **Headings**: 24px/20px/18px dengan font weight 600-700
- **Body Text**: 16px/14px dengan line height optimal
- **Captions**: 12px untuk metadata dan labels

### Visual Elements
- **Shadows**: Soft elevation dengan opacity 0.1
- **Border Radius**: 12px untuk cards, 20px untuk buttons
- **Animations**: Spring animations dengan useNativeDriver
- **Icons**: Material Icons dengan consistent sizing (24px/20px)

### Micro-interactions
- **Scale Animation**: 0.95x saat pressed
- **Smooth Transitions**: 300ms duration
- **Visual Feedback**: Immediate response untuk semua touch events

## 🚀 Penyempurnaan yang Telah Dilakukan

### ✅ Completed Features
- **Modern UI/UX**: Complete redesign dengan Material Design 3
- **Component Architecture**: Modular, reusable components
- **Error Handling**: Error Boundary dengan graceful fallback
- **Performance**: Optimized animations dan rendering
- **Accessibility**: Screen reader support dan keyboard navigation
- **Auto-scroll Carousel**: Smart carousel dengan user interaction pause
- **Dashboard Stats**: Real-time overview cards
- **Activity Timeline**: Recent activities dengan visual indicators

### 📱 Platform Support
- **iOS**: Native shadows dan haptic feedback
- **Android**: Material elevation dan ripple effects
- **Web**: Responsive design dengan hover states

## 🔧 Development & Contribution

### Prerequisites
- Node.js 16+ dan npm/yarn
- Expo CLI global installation
- iOS Simulator atau Android Emulator (optional)

### Development Workflow
```bash
# Clone repository
git clone [repository-url]
cd psg-bmi

# Install dependencies
npm install

# Start development server
npm start

# Platform specific
npm run ios     # iOS Simulator
npm run android # Android Emulator
npm run web     # Web Browser
```

### Code Structure
```
src/
├── components/     # Reusable UI components
├── screens/       # Screen components
├── styles/        # Style definitions
├── utils/         # Helper functions
└── constants/     # App constants
```

### Contribution Guidelines
1. Fork repository dan create feature branch
2. Follow existing code style dan conventions
3. Add tests untuk new features
4. Update documentation
5. Submit Pull Request dengan clear description

## 📊 Performance Metrics

### Bundle Size
- **Optimized**: Minimal dependencies
- **Tree Shaking**: Unused code elimination
- **Image Optimization**: WebP format dengan fallbacks

### Loading Performance
- **Fast Startup**: < 2s initial load
- **Smooth Animations**: 60fps dengan native driver
- **Memory Efficient**: Optimized re-renders

## 🔮 Roadmap & Future Enhancements

### Phase 2 Features
- [ ] **Push Notifications** dengan Firebase
- [ ] **Offline Support** dengan AsyncStorage
- [ ] **Dark Mode** theme switching
- [ ] **Multi-language** support (ID/EN)
- [ ] **Biometric Authentication** (Face ID/Fingerprint)

### Phase 3 Features
- [ ] **Real-time Chat** untuk internal communication
- [ ] **Document Scanner** dengan OCR
- [ ] **QR Code Scanner** untuk attendance
- [ ] **Calendar Integration** untuk events
- [ ] **Analytics Dashboard** untuk admin

## 📞 Support & Contact

### Technical Support
- **Documentation**: Lihat file `IMPROVEMENTS.md` untuk detail teknis
- **Issues**: Report bugs via GitHub Issues
- **Discussions**: GitHub Discussions untuk feature requests

### Team
- **UI/UX Design**: Modern mBanking interface
- **Frontend Development**: React Native + Expo
- **Performance Optimization**: Native animations
- **Accessibility**: WCAG 2.1 compliance

---

**🎉 Aplikasi Portal Karyawan PAMA - Modern, Fast, dan User-Friendly!**

*Built with ❤️ using React Native & Expo*