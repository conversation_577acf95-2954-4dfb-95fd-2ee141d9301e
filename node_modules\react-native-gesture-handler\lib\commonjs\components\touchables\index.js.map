{"version": 3, "sources": ["index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;;AACA;;AACA;;AACA", "sourcesContent": ["export type { TouchableHighlightProps } from './TouchableHighlight';\nexport type { TouchableOpacityProps } from './TouchableOpacity';\nexport type { TouchableWithoutFeedbackProps } from './TouchableWithoutFeedback';\nexport { default as TouchableNativeFeedback } from './TouchableNativeFeedback';\nexport { default as TouchableWithoutFeedback } from './TouchableWithoutFeedback';\nexport { default as TouchableOpacity } from './TouchableOpacity';\nexport { default as TouchableHighlight } from './TouchableHighlight';\n"]}