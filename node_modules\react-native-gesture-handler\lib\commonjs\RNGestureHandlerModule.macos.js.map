{"version": 3, "sources": ["RNGestureHandlerModule.macos.ts"], "names": ["Gestures", "NativeViewGestureHandler", "PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "ManualGestureHandler", "HammerGestures", "HammerNativeViewGestureHandler", "HammerPanGestureHandler", "HammerTapGestureHandler", "HammerLongPressGestureHandler", "HammerPinchGestureHandler", "HammerRotationGestureHandler", "HammerFlingGestureHandler", "handleSetJSResponder", "_tag", "_blockNativeResponder", "handleClearJSResponder", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "Error", "GestureClass", "NodeManager", "GestureHandlerWebDelegate", "InteractionManager", "getInstance", "configureInteractions", "<PERSON><PERSON><PERSON><PERSON>", "HammerNodeManager", "updateGestureHandler", "attachGestureHandler", "newView", "_actionType", "propsRef", "init", "<PERSON><PERSON><PERSON><PERSON>", "newConfig", "updateGestureConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "mappings": ";;;;;;;AACA;;AAGA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAGA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA;;;;;;;;AAtBA;AAYA;AAYO,MAAMA,QAAQ,GAAG;AACtBC,EAAAA,wBAAwB,EAAxBA,iCADsB;AAEtBC,EAAAA,iBAAiB,EAAjBA,0BAFsB;AAGtBC,EAAAA,iBAAiB,EAAjBA,0BAHsB;AAItBC,EAAAA,uBAAuB,EAAvBA,gCAJsB;AAKtBC,EAAAA,mBAAmB,EAAnBA,4BALsB;AAMtBC,EAAAA,sBAAsB,EAAtBA,+BANsB;AAOtBC,EAAAA,mBAAmB,EAAnBA,4BAPsB;AAQtBC,EAAAA,oBAAoB,EAApBA;AARsB,CAAjB;;AAWA,MAAMC,cAAc,GAAG;AAC5BR,EAAAA,wBAAwB,EAAES,kCADE;AAE5BR,EAAAA,iBAAiB,EAAES,2BAFS;AAG5BR,EAAAA,iBAAiB,EAAES,2BAHS;AAI5BR,EAAAA,uBAAuB,EAAES,iCAJG;AAK5BR,EAAAA,mBAAmB,EAAES,6BALO;AAM5BR,EAAAA,sBAAsB,EAAES,gCANI;AAO5BR,EAAAA,mBAAmB,EAAES;AAPO,CAAvB;;eAUQ;AACbC,EAAAA,oBAAoB,CAACC,IAAD,EAAeC,qBAAf,EAA+C,CACjE;AACD,GAHY;;AAIbC,EAAAA,sBAAsB,GAAG,CACvB;AACD,GANY;;AAObC,EAAAA,oBAAoB,CAClBC,WADkB,EAElBC,UAFkB,EAGlBC,MAHkB,EAIlB;AACA,QAAI,gEAAJ,EAAqC;AACnC,UAAI,EAAEF,WAAW,IAAItB,QAAjB,CAAJ,EAAgC;AAC9B,cAAM,IAAIyB,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD;;AAED,YAAMI,YAAY,GAAG1B,QAAQ,CAACsB,WAAD,CAA7B;;AACAK,2BAAYN,oBAAZ,CACEE,UADF,EAEE,IAAIG,YAAJ,CAAiB,IAAIE,oDAAJ,EAAjB,CAFF;;AAIAC,kCAAmBC,WAAnB,GAAiCC,qBAAjC,CACEJ,qBAAYK,UAAZ,CAAuBT,UAAvB,CADF,EAEEC,MAFF;AAID,KAhBD,MAgBO;AACL,UAAI,EAAEF,WAAW,IAAIb,cAAjB,CAAJ,EAAsC;AACpC,cAAM,IAAIgB,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD,OALI,CAOL;AACA;;;AACA,YAAMI,YAAY,GAAGjB,cAAc,CAACa,WAAD,CAAnC,CATK,CAUL;;AACAW,MAAAA,iBAAiB,CAACZ,oBAAlB,CAAuCE,UAAvC,EAAmD,IAAIG,YAAJ,EAAnD;AACD;;AAED,SAAKQ,oBAAL,CAA0BX,UAA1B,EAAsCC,MAAtC;AACD,GA3CY;;AA4CbW,EAAAA,oBAAoB,CAClBZ,UADkB,EAElBa,OAFkB,EAGlBC,WAHkB,EAIlBC,QAJkB,EAKlB;AACA,QAAI,gEAAJ,EAAqC;AACnCX,2BAAYK,UAAZ,CAAuBT,UAAvB,EAAmCgB,IAAnC,CAAwCH,OAAxC,EAAiDE,QAAjD;AACD,KAFD,MAEO;AACLL,MAAAA,iBAAiB,CAACD,UAAlB,CAA6BT,UAA7B,EAAyCiB,OAAzC,CAAiDJ,OAAjD,EAA0DE,QAA1D;AACD;AACF,GAvDY;;AAwDbJ,EAAAA,oBAAoB,CAACX,UAAD,EAAqBkB,SAArB,EAAwC;AAC1D,QAAI,gEAAJ,EAAqC;AACnCd,2BAAYK,UAAZ,CAAuBT,UAAvB,EAAmCmB,mBAAnC,CAAuDD,SAAvD;;AAEAZ,kCAAmBC,WAAnB,GAAiCC,qBAAjC,CACEJ,qBAAYK,UAAZ,CAAuBT,UAAvB,CADF,EAEEkB,SAFF;AAID,KAPD,MAOO;AACLR,MAAAA,iBAAiB,CAACD,UAAlB,CAA6BT,UAA7B,EAAyCmB,mBAAzC,CAA6DD,SAA7D;AACD;AACF,GAnEY;;AAoEbE,EAAAA,qBAAqB,CAACpB,UAAD,EAAqB;AACxC,QAAI,gEAAJ,EAAqC;AACnC,aAAOI,qBAAYK,UAAZ,CAAuBT,UAAvB,CAAP;AACD,KAFD,MAEO;AACL,aAAOU,iBAAiB,CAACD,UAAlB,CAA6BT,UAA7B,CAAP;AACD;AACF,GA1EY;;AA2EbqB,EAAAA,kBAAkB,CAACrB,UAAD,EAAqB;AACrC,QAAI,gEAAJ,EAAqC;AACnCI,2BAAYiB,kBAAZ,CAA+BrB,UAA/B;AACD,KAFD,MAEO;AACLU,MAAAA,iBAAiB,CAACW,kBAAlB,CAAqCrB,UAArC;AACD;AACF,GAjFY;;AAkFb;AACAsB,EAAAA,eAAe,GAAG,CAAE;;AAnFP,C", "sourcesContent": ["import { ActionType } from './ActionType';\nimport { isNewWebImplementationEnabled } from './EnableNewWebImplementation';\n\n//GestureHandlers\nimport InteractionManager from './web/tools/InteractionManager';\nimport NodeManager from './web/tools/NodeManager';\nimport PanGestureHandler from './web/handlers/PanGestureHandler';\nimport TapGestureHandler from './web/handlers/TapGestureHandler';\nimport LongPressGestureHandler from './web/handlers/LongPressGestureHandler';\nimport PinchGestureHandler from './web/handlers/PinchGestureHandler';\nimport RotationGestureHandler from './web/handlers/RotationGestureHandler';\nimport FlingGestureHandler from './web/handlers/FlingGestureHandler';\nimport NativeViewGestureHandler from './web/handlers/NativeViewGestureHandler';\nimport ManualGestureHandler from './web/handlers/ManualGestureHandler';\n\n//Hammer Handlers\nimport * as HammerNodeManager from './web_hammer/NodeManager';\nimport HammerNativeViewGestureHandler from './web_hammer/NativeViewGestureHandler';\nimport HammerPanGestureHandler from './web_hammer/PanGestureHandler';\nimport HammerTapGestureHandler from './web_hammer/TapGestureHandler';\nimport HammerLongPressGestureHandler from './web_hammer/LongPressGestureHandler';\nimport HammerPinchGestureHandler from './web_hammer/PinchGestureHandler';\nimport HammerRotationGestureHandler from './web_hammer/RotationGestureHandler';\nimport HammerFlingGestureHandler from './web_hammer/FlingGestureHandler';\nimport { Config } from './web/interfaces';\nimport { GestureHandlerWebDelegate } from './web/tools/GestureHandlerWebDelegate';\n\nexport const Gestures = {\n  NativeViewGestureHandler,\n  PanGestureHandler,\n  TapGestureHandler,\n  LongPressGestureHandler,\n  PinchGestureHandler,\n  RotationGestureHandler,\n  FlingGestureHandler,\n  ManualGestureHandler,\n};\n\nexport const HammerGestures = {\n  NativeViewGestureHandler: HammerNativeViewGestureHandler,\n  PanGestureHandler: HammerPanGestureHandler,\n  TapGestureHandler: HammerTapGestureHandler,\n  LongPressGestureHandler: HammerLongPressGestureHandler,\n  PinchGestureHandler: HammerPinchGestureHandler,\n  RotationGestureHandler: HammerRotationGestureHandler,\n  FlingGestureHandler: HammerFlingGestureHandler,\n};\n\nexport default {\n  handleSetJSResponder(_tag: number, _blockNativeResponder: boolean) {\n    // NO-OP\n  },\n  handleClearJSResponder() {\n    // NO-OP\n  },\n  createGestureHandler<T>(\n    handlerName: keyof typeof Gestures,\n    handlerTag: number,\n    config: T\n  ) {\n    if (isNewWebImplementationEnabled()) {\n      if (!(handlerName in Gestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      const GestureClass = Gestures[handlerName];\n      NodeManager.createGestureHandler(\n        handlerTag,\n        new GestureClass(new GestureHandlerWebDelegate())\n      );\n      InteractionManager.getInstance().configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        config as unknown as Config\n      );\n    } else {\n      if (!(handlerName in HammerGestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      // @ts-ignore If it doesn't exist, the error is thrown\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      const GestureClass = HammerGestures[handlerName];\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n      HammerNodeManager.createGestureHandler(handlerTag, new GestureClass());\n    }\n\n    this.updateGestureHandler(handlerTag, config as unknown as Config);\n  },\n  attachGestureHandler(\n    handlerTag: number,\n    newView: number,\n    _actionType: ActionType,\n    propsRef: React.RefObject<unknown>\n  ) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.getHandler(handlerTag).init(newView, propsRef);\n    } else {\n      HammerNodeManager.getHandler(handlerTag).setView(newView, propsRef);\n    }\n  },\n  updateGestureHandler(handlerTag: number, newConfig: Config) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n\n      InteractionManager.getInstance().configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        newConfig\n      );\n    } else {\n      HammerNodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n    }\n  },\n  getGestureHandlerNode(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      return NodeManager.getHandler(handlerTag);\n    } else {\n      return HammerNodeManager.getHandler(handlerTag);\n    }\n  },\n  dropGestureHandler(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.dropGestureHandler(handlerTag);\n    } else {\n      HammerNodeManager.dropGestureHandler(handlerTag);\n    }\n  },\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  flushOperations() {},\n};\n"]}