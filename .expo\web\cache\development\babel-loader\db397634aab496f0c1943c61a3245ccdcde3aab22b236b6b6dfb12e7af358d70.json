{"ast": null, "code": "export var MenuData = [{\n  id: 1,\n  title: 'Attendance Recording',\n  subtitle: '<PERSON><PERSON><PERSON><PERSON> & Kehadiran',\n  icon: 'access-time',\n  iconType: 'MaterialIcons',\n  color: '#4CAF50',\n  route: 'Attendance',\n  description: 'Informasi absensi bulanan, status kehadiran real-time, pengajuan izin/cuti'\n}, {\n  id: 2,\n  title: 'ATR Pribadi',\n  subtitle: 'Pengajuan Kerja',\n  icon: 'assignment',\n  iconType: 'MaterialIcons',\n  color: '#2196F3',\n  route: 'ATR',\n  description: 'Pengajuan kerja, dinas pribadi, status approval'\n}, {\n  id: 3,\n  title: 'CNM',\n  subtitle: 'Maintenance',\n  icon: 'build',\n  iconType: 'MaterialIcons',\n  color: '#FF9800',\n  route: 'CNM',\n  description: 'Sistem maintenance, work orders, equipment status'\n}, {\n  id: 4,\n  title: 'New Points',\n  subtitle: 'Reward System',\n  icon: 'star',\n  iconType: 'MaterialIcons',\n  color: '#9C27B0',\n  route: 'Points',\n  description: 'Reward points system, achievement tracking, redemption catalog'\n}, {\n  id: 5,\n  title: 'SAP',\n  subtitle: 'SHE & Greencard',\n  icon: 'folder',\n  iconType: 'MaterialIcons',\n  color: '#607D8B',\n  route: 'SAP',\n  description: 'Modul SAP SHE, greencard system, IUT & OTT'\n}, {\n  id: 6,\n  title: 'iPeak',\n  subtitle: 'Performance',\n  icon: 'trending-up',\n  iconType: 'MaterialIcons',\n  color: '#E91E63',\n  route: 'iPeak',\n  description: 'Performance tracking, penilaian karyawan, kompetensi development'\n}, {\n  id: 7,\n  title: 'Production',\n  subtitle: 'Data Produksi',\n  icon: 'bar-chart',\n  iconType: 'MaterialIcons',\n  color: '#795548',\n  route: 'Production',\n  description: 'Data produksi harian, grafik & statistik, KPI monitoring'\n}, {\n  id: 8,\n  title: 'View All',\n  subtitle: 'Semua Aplikasi',\n  icon: 'apps',\n  iconType: 'MaterialIcons',\n  color: '#424242',\n  route: 'ViewAll',\n  description: 'Semua aplikasi, search functionality, kategori filter'\n}];\nexport var CarouselData = [{\n  id: 1,\n  title: 'Selamat Datang di Portal PSG-BMI',\n  subtitle: 'Akses semua layanan karyawan dalam satu aplikasi',\n  image: 'https://via.placeholder.com/300x150/2196F3/FFFFFF?text=Welcome',\n  type: 'welcome'\n}, {\n  id: 2,\n  title: 'Update Sistem Absensi',\n  subtitle: 'Fitur baru untuk pencatatan kehadiran yang lebih akurat',\n  image: 'https://via.placeholder.com/300x150/4CAF50/FFFFFF?text=Attendance',\n  type: 'update'\n}, {\n  id: 3,\n  title: 'Training Safety Bulan Ini',\n  subtitle: 'Ikuti pelatihan keselamatan kerja wajib',\n  image: 'https://via.placeholder.com/300x150/FF9800/FFFFFF?text=Safety',\n  type: 'training'\n}, {\n  id: 4,\n  title: 'Pencapaian Produksi Q1',\n  subtitle: 'Target produksi kuartal pertama tercapai 105%',\n  image: 'https://via.placeholder.com/300x150/9C27B0/FFFFFF?text=Achievement',\n  type: 'achievement'\n}];\nexport var DashboardStats = [{\n  id: 1,\n  title: 'Check-in Hari Ini',\n  value: '07:45',\n  subtitle: 'Tepat waktu',\n  icon: 'schedule',\n  color: '#4CAF50',\n  trend: 'positive'\n}, {\n  id: 2,\n  title: 'Tingkat Kehadiran',\n  value: '96%',\n  subtitle: 'Bulan ini',\n  icon: 'trending-up',\n  color: '#2196F3',\n  trend: 'positive'\n}, {\n  id: 3,\n  title: 'Tugas Pending',\n  value: '3',\n  subtitle: 'Perlu diselesaikan',\n  icon: 'assignment',\n  color: '#FF9800',\n  trend: 'neutral'\n}, {\n  id: 4,\n  title: 'Points Reward',\n  value: '1,250',\n  subtitle: 'Total poin',\n  icon: 'star',\n  color: '#9C27B0',\n  trend: 'positive'\n}];", "map": {"version": 3, "names": ["MenuData", "id", "title", "subtitle", "icon", "iconType", "color", "route", "description", "CarouselData", "image", "type", "DashboardStats", "value", "trend"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/constants/MenuData.js"], "sourcesContent": ["// Menu data for the 8 main applications\nexport const MenuData = [\n  {\n    id: 1,\n    title: 'Attendance Recording',\n    subtitle: '<PERSON><PERSON><PERSON><PERSON> & Kehadiran',\n    icon: 'access-time',\n    iconType: 'MaterialIcons',\n    color: '#4CAF50',\n    route: 'Attendance',\n    description: 'Informasi absensi bulanan, status kehadiran real-time, pengajuan izin/cuti',\n  },\n  {\n    id: 2,\n    title: 'ATR Pribadi',\n    subtitle: 'Pengajuan Kerja',\n    icon: 'assignment',\n    iconType: 'MaterialIcons',\n    color: '#2196F3',\n    route: 'ATR',\n    description: 'Pengajuan kerja, dinas pribadi, status approval',\n  },\n  {\n    id: 3,\n    title: 'CNM',\n    subtitle: 'Maintenance',\n    icon: 'build',\n    iconType: 'MaterialIcons',\n    color: '#FF9800',\n    route: 'CNM',\n    description: 'Sistem maintenance, work orders, equipment status',\n  },\n  {\n    id: 4,\n    title: 'New Points',\n    subtitle: 'Reward System',\n    icon: 'star',\n    iconType: 'MaterialIcons',\n    color: '#9C27B0',\n    route: 'Points',\n    description: 'Reward points system, achievement tracking, redemption catalog',\n  },\n  {\n    id: 5,\n    title: 'SAP',\n    subtitle: 'SHE & Greencard',\n    icon: 'folder',\n    iconType: 'MaterialIcons',\n    color: '#607D8B',\n    route: 'SAP',\n    description: 'Modul SAP SHE, greencard system, IUT & OTT',\n  },\n  {\n    id: 6,\n    title: 'iPeak',\n    subtitle: 'Performance',\n    icon: 'trending-up',\n    iconType: 'MaterialIcons',\n    color: '#E91E63',\n    route: 'iPeak',\n    description: 'Performance tracking, penilaian karyawan, kompetensi development',\n  },\n  {\n    id: 7,\n    title: 'Production',\n    subtitle: 'Data Produksi',\n    icon: 'bar-chart',\n    iconType: 'MaterialIcons',\n    color: '#795548',\n    route: 'Production',\n    description: 'Data produksi harian, grafik & statistik, KPI monitoring',\n  },\n  {\n    id: 8,\n    title: 'View All',\n    subtitle: 'Semua Aplikasi',\n    icon: 'apps',\n    iconType: 'MaterialIcons',\n    color: '#424242',\n    route: 'ViewAll',\n    description: 'Semua aplikasi, search functionality, kategori filter',\n  },\n];\n\n// Carousel data for featured content\nexport const CarouselData = [\n  {\n    id: 1,\n    title: 'Selamat Datang di Portal PSG-BMI',\n    subtitle: 'Akses semua layanan karyawan dalam satu aplikasi',\n    image: 'https://via.placeholder.com/300x150/2196F3/FFFFFF?text=Welcome',\n    type: 'welcome',\n  },\n  {\n    id: 2,\n    title: 'Update Sistem Absensi',\n    subtitle: 'Fitur baru untuk pencatatan kehadiran yang lebih akurat',\n    image: 'https://via.placeholder.com/300x150/4CAF50/FFFFFF?text=Attendance',\n    type: 'update',\n  },\n  {\n    id: 3,\n    title: 'Training Safety Bulan Ini',\n    subtitle: 'Ikuti pelatihan keselamatan kerja wajib',\n    image: 'https://via.placeholder.com/300x150/FF9800/FFFFFF?text=Safety',\n    type: 'training',\n  },\n  {\n    id: 4,\n    title: 'Pencapaian Produksi Q1',\n    subtitle: 'Target produksi kuartal pertama tercapai 105%',\n    image: 'https://via.placeholder.com/300x150/9C27B0/FFFFFF?text=Achievement',\n    type: 'achievement',\n  },\n];\n\n// Dashboard stats data\nexport const DashboardStats = [\n  {\n    id: 1,\n    title: 'Check-in Hari Ini',\n    value: '07:45',\n    subtitle: 'Tepat waktu',\n    icon: 'schedule',\n    color: '#4CAF50',\n    trend: 'positive',\n  },\n  {\n    id: 2,\n    title: 'Tingkat Kehadiran',\n    value: '96%',\n    subtitle: 'Bulan ini',\n    icon: 'trending-up',\n    color: '#2196F3',\n    trend: 'positive',\n  },\n  {\n    id: 3,\n    title: 'Tugas Pending',\n    value: '3',\n    subtitle: 'Perlu diselesaikan',\n    icon: 'assignment',\n    color: '#FF9800',\n    trend: 'neutral',\n  },\n  {\n    id: 4,\n    title: 'Points Reward',\n    value: '1,250',\n    subtitle: 'Total poin',\n    icon: 'star',\n    color: '#9C27B0',\n    trend: 'positive',\n  },\n];\n"], "mappings": "AACA,OAAO,IAAMA,QAAQ,GAAG,CACtB;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,sBAAsB;EAC7BC,QAAQ,EAAE,qBAAqB;EAC/BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,YAAY;EACnBC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,KAAK;EACZC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,KAAK;EACZC,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,KAAK;EACZC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,eAAe;EACzBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,KAAK;EACZC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,KAAK;EACZC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,OAAO;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,eAAe;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,YAAY;EACnBC,WAAW,EAAE;AACf,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,UAAU;EACjBC,QAAQ,EAAE,gBAAgB;EAC1BC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;AACf,CAAC,CACF;AAGD,OAAO,IAAMC,YAAY,GAAG,CAC1B;EACER,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,kCAAkC;EACzCC,QAAQ,EAAE,kDAAkD;EAC5DO,KAAK,EAAE,gEAAgE;EACvEC,IAAI,EAAE;AACR,CAAC,EACD;EACEV,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,uBAAuB;EAC9BC,QAAQ,EAAE,yDAAyD;EACnEO,KAAK,EAAE,mEAAmE;EAC1EC,IAAI,EAAE;AACR,CAAC,EACD;EACEV,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,2BAA2B;EAClCC,QAAQ,EAAE,yCAAyC;EACnDO,KAAK,EAAE,+DAA+D;EACtEC,IAAI,EAAE;AACR,CAAC,EACD;EACEV,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,wBAAwB;EAC/BC,QAAQ,EAAE,+CAA+C;EACzDO,KAAK,EAAE,oEAAoE;EAC3EC,IAAI,EAAE;AACR,CAAC,CACF;AAGD,OAAO,IAAMC,cAAc,GAAG,CAC5B;EACEX,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,mBAAmB;EAC1BW,KAAK,EAAE,OAAO;EACdV,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE,UAAU;EAChBE,KAAK,EAAE,SAAS;EAChBQ,KAAK,EAAE;AACT,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,mBAAmB;EAC1BW,KAAK,EAAE,KAAK;EACZV,QAAQ,EAAE,WAAW;EACrBC,IAAI,EAAE,aAAa;EACnBE,KAAK,EAAE,SAAS;EAChBQ,KAAK,EAAE;AACT,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,eAAe;EACtBW,KAAK,EAAE,GAAG;EACVV,QAAQ,EAAE,oBAAoB;EAC9BC,IAAI,EAAE,YAAY;EAClBE,KAAK,EAAE,SAAS;EAChBQ,KAAK,EAAE;AACT,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,eAAe;EACtBW,KAAK,EAAE,OAAO;EACdV,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,MAAM;EACZE,KAAK,EAAE,SAAS;EAChBQ,KAAK,EAAE;AACT,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}