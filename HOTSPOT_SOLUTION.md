# 📱 Mobile Hotspot Solution - PSG-BMI Portal

## 🚨 **Jika Network Timeout Persisten**

### **📶 Mobile Hotspot Method (Paling Efektif):**

#### **Step 1: Setup Hotspot**
1. **Di Phone Anda:**
   - Buka Settings → Mobile Hotspot/Tethering
   - Aktifkan "Mobile Hotspot" atau "Personal Hotspot"
   - Catat nama WiFi dan password

2. **Di PC/Laptop:**
   - Disconnect dari WiFi rumah
   - Connect ke hotspot phone Anda
   - Tunggu sampai connected

#### **Step 2: Restart Expo Server**
```bash
# Stop current server (Ctrl+C)
# Then restart:
npx expo start --clear --port 3000
```

#### **Step 3: Get New IP Address**
```bash
# Check new IP:
ipconfig
# Look for new IPv4 address (biasanya 192.168.43.x atau 172.20.10.x)
```

#### **Step 4: Connect Mobile**
- **Scan QR code baru** dengan IP address baru
- **Atau manual input**: `exp://[NEW_IP]:3000`

---

## 🔄 **Alternative Solutions:**

### **Solution A: Different Port**
```bash
# Try port 4000:
npx expo start --port 4000

# Manual URL: exp://************:4000
```

### **Solution B: Tunnel Mode (If Available)**
```bash
# If ngrok works:
npx expo start --tunnel

# This bypasses local network issues
```

### **Solution C: USB Debugging**
```bash
# If you have Android device:
# Enable USB Debugging
# Connect via USB
npx expo start --localhost
# Then use adb port forwarding
```

---

## 📋 **Current Status:**

### ✅ **Ready to Test:**
- **Server**: Running on port 3000
- **Cache**: Cleared and rebuilt
- **URL**: `exp://************:3000`
- **QR Code**: Available in terminal

### 🎯 **Recommended Order:**

1. **Try Manual URL Input** (most reliable)
   - Open Expo Go
   - Tap "Enter URL manually"
   - Type: `exp://************:3000`

2. **If still timeout → Mobile Hotspot**
   - Setup hotspot on phone
   - Connect PC to hotspot
   - Restart Expo server
   - Get new IP and try again

3. **If hotspot doesn't work → USB Method**
   - Enable USB debugging
   - Connect phone via USB
   - Use localhost mode with port forwarding

---

## 🔧 **Troubleshooting Network Issues:**

### **Common Causes:**
- Router blocking device-to-device communication
- Firewall blocking Expo ports
- WiFi isolation mode enabled
- VPN interfering with local network
- Expo Go cache issues

### **Quick Fixes:**
```bash
# Clear Expo cache:
npx expo start --clear

# Try different port:
npx expo start --port 4000

# Reset network (Windows):
ipconfig /flushdns
ipconfig /release
ipconfig /renew
```

### **Network Diagnostics:**
```bash
# Check if port is accessible:
netstat -an | findstr :3000

# Test connectivity from phone browser:
# Open browser on phone, go to: http://************:3000
```

---

## 🎯 **Success Indicators:**

When working, you should see:
- ✅ "Downloading JavaScript bundle" progress
- ✅ PSG-BMI Portal splash screen
- ✅ Main dashboard with blue header
- ✅ Auto-scroll carousel
- ✅ Menu grid with 8 applications

---

## 📞 **Next Steps:**

1. **Try manual URL**: `exp://************:3000`
2. **If timeout → Setup mobile hotspot**
3. **Report results** so we can try next solution

**PSG-BMI Portal is ready for testing!** 🚀📱
