{"version": 3, "sources": ["flingGesture.ts"], "names": ["FlingGesture", "BaseGesture", "constructor", "handler<PERSON>ame", "numberOfPointers", "pointers", "config", "direction"], "mappings": ";;;;;;;AAAA;;;;AAMO,MAAMA,YAAN,SAA2BC,oBAA3B,CAAwE;AAG7EC,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAF0C,EAE1C;;AAGZ,SAAKC,WAAL,GAAmB,qBAAnB;AACD;;AAEDC,EAAAA,gBAAgB,CAACC,QAAD,EAAmB;AACjC,SAAKC,MAAL,CAAYF,gBAAZ,GAA+BC,QAA/B;AACA,WAAO,IAAP;AACD;;AAEDE,EAAAA,SAAS,CAACA,SAAD,EAAoB;AAC3B,SAAKD,MAAL,CAAYC,SAAZ,GAAwBA,SAAxB;AACA,WAAO,IAAP;AACD;;AAjB4E", "sourcesContent": ["import { BaseGesture, BaseGestureConfig } from './gesture';\nimport {\n  FlingGestureConfig,\n  FlingGestureHandlerEventPayload,\n} from '../FlingGestureHandler';\n\nexport class FlingGesture extends BaseGesture<FlingGestureHandlerEventPayload> {\n  public config: BaseGestureConfig & FlingGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'FlingGestureHandler';\n  }\n\n  numberOfPointers(pointers: number) {\n    this.config.numberOfPointers = pointers;\n    return this;\n  }\n\n  direction(direction: number) {\n    this.config.direction = direction;\n    return this;\n  }\n}\n\nexport type FlingGestureType = InstanceType<typeof FlingGesture>;\n"]}