{"version": 3, "sources": ["gestureHandlerCommon.ts"], "names": ["commonProps", "componentInteractionProps", "baseGestureHandlerProps", "baseGestureHandlerWithMonitorProps", "isConfigParam", "param", "name", "undefined", "Object", "filterConfig", "props", "validProps", "defaults", "filteredConfig", "key", "value", "transformIntoHandlerTags", "top", "left", "bottom", "right", "handlerIDs", "Platform", "OS", "map", "current", "filter", "handle", "handlerID", "handlerIDToTag", "handlerTag", "findNodeHandle", "node", "flushOperationsScheduled", "scheduleFlushOperations", "RNGestureHandlerModule", "flushOperations"], "mappings": ";;;;;;;;;;AAKA;;AAKA;;AACA;;AACA;;AACA;;;;AAbA;AACA;AACA;AACA;AAYA,MAAMA,WAAW,GAAG,CAClB,IADkB,EAElB,SAFkB,EAGlB,yBAHkB,EAIlB,SAJkB,EAKlB,sBALkB,EAMlB,YANkB,EAOlB,cAPkB,CAApB;AAUA,MAAMC,yBAAyB,GAAG,CAChC,SADgC,EAEhC,sBAFgC,EAGhC,gBAHgC,CAAlC;AAMO,MAAMC,uBAAuB,GAAG,CACrC,GAAGF,WADkC,EAErC,GAAGC,yBAFkC,EAGrC,SAHqC,EAIrC,UAJqC,EAKrC,aALqC,EAMrC,aANqC,EAOrC,SAPqC,EAQrC,gBARqC,EASrC,sBATqC,CAAhC;;AAYA,MAAME,kCAAkC,GAAG,CAChD,GAAGH,WAD6C,EAEhD,kBAFgD,EAGhD,kBAHgD,CAA3C;;;AAyIP,SAASI,aAAT,CAAuBC,KAAvB,EAAuCC,IAAvC,EAAqD;AACnD;AACA;AACA,SACED,KAAK,KAAKE,SAAV,KACCF,KAAK,KAAKG,MAAM,CAACH,KAAD,CAAhB,IACC,EAAE,gBAAiBA,KAAnB,CAFF,KAGAC,IAAI,KAAK,sBAHT,IAIAA,IAAI,KAAK,gBALX;AAOD;;AAEM,SAASG,YAAT,CACLC,KADK,EAELC,UAFK,EAGLC,QAAiC,GAAG,EAH/B,EAIL;AACA,QAAMC,cAAc,GAAG,EAAE,GAAGD;AAAL,GAAvB;;AACA,OAAK,MAAME,GAAX,IAAkBH,UAAlB,EAA8B;AAC5B,QAAII,KAAK,GAAGL,KAAK,CAACI,GAAD,CAAjB;;AACA,QAAIV,aAAa,CAACW,KAAD,EAAQD,GAAR,CAAjB,EAA+B;AAC7B,UAAIA,GAAG,KAAK,sBAAR,IAAkCA,GAAG,KAAK,SAA9C,EAAyD;AACvDC,QAAAA,KAAK,GAAGC,wBAAwB,CAACN,KAAK,CAACI,GAAD,CAAN,CAAhC;AACD,OAFD,MAEO,IAAIA,GAAG,KAAK,SAAR,IAAqB,OAAOC,KAAP,KAAiB,QAA1C,EAAoD;AACzDA,QAAAA,KAAK,GAAG;AAAEE,UAAAA,GAAG,EAAEF,KAAP;AAAcG,UAAAA,IAAI,EAAEH,KAApB;AAA2BI,UAAAA,MAAM,EAAEJ,KAAnC;AAA0CK,UAAAA,KAAK,EAAEL;AAAjD,SAAR;AACD;;AACDF,MAAAA,cAAc,CAACC,GAAD,CAAd,GAAsBC,KAAtB;AACD;AACF;;AACD,SAAOF,cAAP;AACD;;AAED,SAASG,wBAAT,CAAkCK,UAAlC,EAAmD;AACjDA,EAAAA,UAAU,GAAG,oBAAQA,UAAR,CAAb;;AAEA,MAAIC,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB,WAAOF,UAAU,CACdG,GADI,CACA,CAAC;AAAEC,MAAAA;AAAF,KAAD,KAAmCA,OADnC,EAEJC,MAFI,CAEIC,MAAD,IAAiBA,MAFpB,CAAP;AAGD,GAPgD,CAQjD;;;AACA,SAAON,UAAU,CACdG,GADI,CAEFI,SAAD;AAAA;;AAAA,WACEC,iCAAeD,SAAf,4BAA6BA,SAAS,CAACH,OAAvC,uDAA6B,mBAAmBK,UAAhD,KAA8D,CAAC,CADjE;AAAA,GAFG,EAKJJ,MALI,CAKII,UAAD,IAAwBA,UAAU,GAAG,CALxC,CAAP;AAMD;;AAEM,SAASC,cAAT,CACLC,IADK,EAEkE;AACvE,MAAIV,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB,WAAOS,IAAP;AACD;;AACD,SAAO,iCAAiBA,IAAjB,CAAP;AACD;;AAED,IAAIC,wBAAwB,GAAG,KAA/B;;AAEO,SAASC,uBAAT,GAAmC;AACxC,MAAI,CAACD,wBAAL,EAA+B;AAC7BA,IAAAA,wBAAwB,GAAG,IAA3B;AACA,4CAAiB,MAAM;AACrBE,sCAAuBC,eAAvB;;AAEAH,MAAAA,wBAAwB,GAAG,KAA3B;AACD,KAJD;AAKD;AACF", "sourcesContent": ["// Previous types exported gesture handlers as classes which creates an interface and variable, both named the same as class.\n// Without those types, we'd introduce breaking change, forcing users to prefix every handler type specification with typeof\n// e.g. React.createRef<TapGestureHandler> -> React.createRef<typeof TapGestureHandler>.\n// See https://www.typescriptlang.org/docs/handbook/classes.html#constructor-functions for reference.\nimport * as React from 'react';\nimport { Platform, findNodeHandle as findNodeHandleRN } from 'react-native';\n\nimport { State } from '../State';\nimport { TouchEventType } from '../TouchEventType';\nimport { ValueOf } from '../typeUtils';\nimport { handlerIDToTag } from './handlersRegistry';\nimport { toArray } from '../utils';\nimport RNGestureHandlerModule from '../RNGestureHandlerModule';\nimport { ghQueueMicrotask } from '../ghQueueMicrotask';\n\nconst commonProps = [\n  'id',\n  'enabled',\n  'shouldCancelWhenOutside',\n  'hitSlop',\n  'cancelsTouchesInView',\n  'userSelect',\n  'activeCursor',\n] as const;\n\nconst componentInteractionProps = [\n  'waitFor',\n  'simultaneousHandlers',\n  'blocksHandlers',\n] as const;\n\nexport const baseGestureHandlerProps = [\n  ...commonProps,\n  ...componentInteractionProps,\n  'onBegan',\n  'onFailed',\n  'onCancelled',\n  'onActivated',\n  'onEnded',\n  'onGestureEvent',\n  'onHandlerStateChange',\n] as const;\n\nexport const baseGestureHandlerWithMonitorProps = [\n  ...commonProps,\n  'needsPointerData',\n  'manualActivation',\n];\n\nexport interface GestureEventPayload {\n  handlerTag: number;\n  numberOfPointers: number;\n  state: ValueOf<typeof State>;\n}\nexport interface HandlerStateChangeEventPayload extends GestureEventPayload {\n  oldState: ValueOf<typeof State>;\n}\n\nexport type HitSlop =\n  | number\n  | Partial<\n      Record<\n        'left' | 'right' | 'top' | 'bottom' | 'vertical' | 'horizontal',\n        number\n      >\n    >\n  | Record<'width' | 'left', number>\n  | Record<'width' | 'right', number>\n  | Record<'height' | 'top', number>\n  | Record<'height' | 'bottom', number>;\n\nexport type UserSelect = 'none' | 'auto' | 'text';\nexport type ActiveCursor =\n  | 'auto'\n  | 'default'\n  | 'none'\n  | 'context-menu'\n  | 'help'\n  | 'pointer'\n  | 'progress'\n  | 'wait'\n  | 'cell'\n  | 'crosshair'\n  | 'text'\n  | 'vertical-text'\n  | 'alias'\n  | 'copy'\n  | 'move'\n  | 'no-drop'\n  | 'not-allowed'\n  | 'grab'\n  | 'grabbing'\n  | 'e-resize'\n  | 'n-resize'\n  | 'ne-resize'\n  | 'nw-resize'\n  | 's-resize'\n  | 'se-resize'\n  | 'sw-resize'\n  | 'w-resize'\n  | 'ew-resize'\n  | 'ns-resize'\n  | 'nesw-resize'\n  | 'nwse-resize'\n  | 'col-resize'\n  | 'row-resize'\n  | 'all-scroll'\n  | 'zoom-in'\n  | 'zoom-out';\n\n//TODO(TS) events in handlers\n\nexport interface GestureEvent<ExtraEventPayloadT = Record<string, unknown>> {\n  nativeEvent: Readonly<GestureEventPayload & ExtraEventPayloadT>;\n}\nexport interface HandlerStateChangeEvent<\n  ExtraEventPayloadT = Record<string, unknown>\n> {\n  nativeEvent: Readonly<HandlerStateChangeEventPayload & ExtraEventPayloadT>;\n}\n\nexport type TouchData = {\n  id: number;\n  x: number;\n  y: number;\n  absoluteX: number;\n  absoluteY: number;\n};\n\nexport type GestureTouchEvent = {\n  handlerTag: number;\n  numberOfTouches: number;\n  state: ValueOf<typeof State>;\n  eventType: TouchEventType;\n  allTouches: TouchData[];\n  changedTouches: TouchData[];\n};\n\nexport type GestureUpdateEvent<GestureEventPayloadT = Record<string, unknown>> =\n  GestureEventPayload & GestureEventPayloadT;\n\nexport type GestureStateChangeEvent<\n  GestureStateChangeEventPayloadT = Record<string, unknown>\n> = HandlerStateChangeEventPayload & GestureStateChangeEventPayloadT;\n\nexport type CommonGestureConfig = {\n  enabled?: boolean;\n  shouldCancelWhenOutside?: boolean;\n  hitSlop?: HitSlop;\n  userSelect?: UserSelect;\n  activeCursor?: ActiveCursor;\n};\n\n// Events payloads are types instead of interfaces due to TS limitation.\n// See https://github.com/microsoft/TypeScript/issues/15300 for more info.\nexport type BaseGestureHandlerProps<\n  ExtraEventPayloadT extends Record<string, unknown> = Record<string, unknown>\n> = CommonGestureConfig & {\n  id?: string;\n  waitFor?: React.Ref<unknown> | React.Ref<unknown>[];\n  simultaneousHandlers?: React.Ref<unknown> | React.Ref<unknown>[];\n  blocksHandlers?: React.Ref<unknown> | React.Ref<unknown>[];\n  testID?: string;\n  cancelsTouchesInView?: boolean;\n  // TODO(TS) - fix event types\n  onBegan?: (event: HandlerStateChangeEvent) => void;\n  onFailed?: (event: HandlerStateChangeEvent) => void;\n  onCancelled?: (event: HandlerStateChangeEvent) => void;\n  onActivated?: (event: HandlerStateChangeEvent) => void;\n  onEnded?: (event: HandlerStateChangeEvent) => void;\n\n  //TODO(TS) consider using NativeSyntheticEvent\n  onGestureEvent?: (event: GestureEvent<ExtraEventPayloadT>) => void;\n  onHandlerStateChange?: (\n    event: HandlerStateChangeEvent<ExtraEventPayloadT>\n  ) => void;\n  // implicit `children` prop has been removed in @types/react^18.0.0\n  children?: React.ReactNode;\n};\n\nfunction isConfigParam(param: unknown, name: string) {\n  // param !== Object(param) returns false if `param` is a function\n  // or an object and returns true if `param` is null\n  return (\n    param !== undefined &&\n    (param !== Object(param) ||\n      !('__isNative' in (param as Record<string, unknown>))) &&\n    name !== 'onHandlerStateChange' &&\n    name !== 'onGestureEvent'\n  );\n}\n\nexport function filterConfig(\n  props: Record<string, unknown>,\n  validProps: string[],\n  defaults: Record<string, unknown> = {}\n) {\n  const filteredConfig = { ...defaults };\n  for (const key of validProps) {\n    let value = props[key];\n    if (isConfigParam(value, key)) {\n      if (key === 'simultaneousHandlers' || key === 'waitFor') {\n        value = transformIntoHandlerTags(props[key]);\n      } else if (key === 'hitSlop' && typeof value !== 'object') {\n        value = { top: value, left: value, bottom: value, right: value };\n      }\n      filteredConfig[key] = value;\n    }\n  }\n  return filteredConfig;\n}\n\nfunction transformIntoHandlerTags(handlerIDs: any) {\n  handlerIDs = toArray(handlerIDs);\n\n  if (Platform.OS === 'web') {\n    return handlerIDs\n      .map(({ current }: { current: any }) => current)\n      .filter((handle: any) => handle);\n  }\n  // converts handler string IDs into their numeric tags\n  return handlerIDs\n    .map(\n      (handlerID: any) =>\n        handlerIDToTag[handlerID] || handlerID.current?.handlerTag || -1\n    )\n    .filter((handlerTag: number) => handlerTag > 0);\n}\n\nexport function findNodeHandle(\n  node: null | number | React.Component<any, any> | React.ComponentClass<any>\n): null | number | React.Component<any, any> | React.ComponentClass<any> {\n  if (Platform.OS === 'web') {\n    return node;\n  }\n  return findNodeHandleRN(node);\n}\n\nlet flushOperationsScheduled = false;\n\nexport function scheduleFlushOperations() {\n  if (!flushOperationsScheduled) {\n    flushOperationsScheduled = true;\n    ghQueueMicrotask(() => {\n      RNGestureHandlerModule.flushOperations();\n\n      flushOperationsScheduled = false;\n    });\n  }\n}\n"]}