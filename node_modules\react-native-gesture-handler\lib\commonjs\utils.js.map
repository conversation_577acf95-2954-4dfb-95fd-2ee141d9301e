{"version": 3, "sources": ["utils.ts"], "names": ["toArray", "object", "Array", "isArray", "withPrevAndCurrent", "array", "mapFn", "previousArr", "currentArr", "transformedArr", "for<PERSON>ach", "current", "i", "previous", "transformed", "push", "hasProperty", "key", "Object", "prototype", "hasOwnProperty", "call", "isJestEnv", "global", "process", "env", "JEST_WORKER_ID", "tagMessage", "msg", "isF<PERSON><PERSON>", "nativeFabricUIManager", "isRemoteDebuggingEnabled", "nativeCallSyncHook", "__REMOTEDEV__"], "mappings": ";;;;;;;;;;;;;AAAO,SAASA,OAAT,CAAoBC,MAApB,EAA0C;AAC/C,MAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,MAAd,CAAL,EAA4B;AAC1B,WAAO,CAACA,MAAD,CAAP;AACD;;AAED,SAAOA,MAAP;AACD;;AAMM,SAASG,kBAAT,CACLC,KADK,EAELC,KAFK,EAGU;AACf,QAAMC,WAAmC,GAAG,CAAC,IAAD,CAA5C;AACA,QAAMC,UAAU,GAAG,CAAC,GAAGH,KAAJ,CAAnB;AACA,QAAMI,cAA6B,GAAG,EAAtC;AACAD,EAAAA,UAAU,CAACE,OAAX,CAAmB,CAACC,OAAD,EAAUC,CAAV,KAAgB;AACjC,UAAMC,QAAQ,GAAGN,WAAW,CAACK,CAAD,CAA5B;AACA,UAAME,WAAW,GAAGR,KAAK,CAACO,QAAD,EAAWF,OAAX,CAAzB;AACAJ,IAAAA,WAAW,CAACQ,IAAZ,CAAiBD,WAAjB;AACAL,IAAAA,cAAc,CAACM,IAAf,CAAoBD,WAApB;AACD,GALD;AAMA,SAAOL,cAAP;AACD,C,CAED;;;AACO,SAASO,WAAT,CAAqBf,MAArB,EAAqCgB,GAArC,EAAkD;AACvD,SAAOC,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCpB,MAArC,EAA6CgB,GAA7C,CAAP;AACD;;AAEM,SAASK,SAAT,GAA8B;AACnC;AACA,SAAON,WAAW,CAACO,MAAD,EAAS,SAAT,CAAX,IAAkC,CAAC,CAACC,OAAO,CAACC,GAAR,CAAYC,cAAvD;AACD;;AAEM,SAASC,UAAT,CAAoBC,GAApB,EAAiC;AACtC,SAAQ,kCAAiCA,GAAI,EAA7C;AACD,C,CAED;AACA;;;AACO,SAASC,QAAT,GAA6B;AAAA;;AAClC;AACA,SAAO,CAAC,aAACN,MAAD,oCAAC,QAAQO,qBAAT,CAAR;AACD;;AAEM,SAASC,wBAAT,GAA6C;AAClD;AACA;AACA,SAAO,CAAER,MAAD,CAAgBS,kBAAjB,IAAwCT,MAAD,CAAgBU,aAA9D;AACD", "sourcesContent": ["export function toArray<T>(object: T | T[]): T[] {\n  if (!Array.isArray(object)) {\n    return [object];\n  }\n\n  return object;\n}\n\nexport type withPrevAndCurrentMapFn<T, Transformed> = (\n  previous: Transformed | null,\n  current: T\n) => Transformed;\nexport function withPrevAndCurrent<T, Transformed>(\n  array: T[],\n  mapFn: withPrevAndCurrentMapFn<T, Transformed>\n): Transformed[] {\n  const previousArr: (null | Transformed)[] = [null];\n  const currentArr = [...array];\n  const transformedArr: Transformed[] = [];\n  currentArr.forEach((current, i) => {\n    const previous = previousArr[i];\n    const transformed = mapFn(previous, current);\n    previousArr.push(transformed);\n    transformedArr.push(transformed);\n  });\n  return transformedArr;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function hasProperty(object: object, key: string) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nexport function isJestEnv(): boolean {\n  // @ts-ignore Do not use `@types/node` because it will prioritise Node types over RN types which breaks the types (ex. setTimeout) in React Native projects.\n  return hasProperty(global, 'process') && !!process.env.JEST_WORKER_ID;\n}\n\nexport function tagMessage(msg: string) {\n  return `[react-native-gesture-handler] ${msg}`;\n}\n\n// helper method to check whether Fabric is enabled, however global.nativeFabricUIManager\n// may not be initialized before the first render\nexport function isFabric(): boolean {\n  // @ts-expect-error nativeFabricUIManager is not yet included in the RN types\n  return !!global?.nativeFabricUIManager;\n}\n\nexport function isRemoteDebuggingEnabled(): boolean {\n  // react-native-reanimated checks if in remote debugging in the same way\n  // @ts-ignore global is available but node types are not included\n  return !(global as any).nativeCallSyncHook || (global as any).__REMOTEDEV__;\n}\n"]}