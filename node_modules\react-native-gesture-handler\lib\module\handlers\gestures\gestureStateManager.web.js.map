{"version": 3, "sources": ["gestureStateManager.web.ts"], "names": ["NodeManager", "GestureStateManager", "create", "handlerTag", "begin", "<PERSON><PERSON><PERSON><PERSON>", "activate", "fail", "end"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,6BAAxB;AAGA,OAAO,MAAMC,mBAAmB,GAAG;AACjCC,EAAAA,MAAM,CAACC,UAAD,EAA8C;AAClD,WAAO;AACLC,MAAAA,KAAK,EAAE,MAAM;AACXJ,QAAAA,WAAW,CAACK,UAAZ,CAAuBF,UAAvB,EAAmCC,KAAnC;AACD,OAHI;AAKLE,MAAAA,QAAQ,EAAE,MAAM;AACdN,QAAAA,WAAW,CAACK,UAAZ,CAAuBF,UAAvB,EAAmCG,QAAnC;AACD,OAPI;AASLC,MAAAA,IAAI,EAAE,MAAM;AACVP,QAAAA,WAAW,CAACK,UAAZ,CAAuBF,UAAvB,EAAmCI,IAAnC;AACD,OAXI;AAaLC,MAAAA,GAAG,EAAE,MAAM;AACTR,QAAAA,WAAW,CAACK,UAAZ,CAAuBF,UAAvB,EAAmCK,GAAnC;AACD;AAfI,KAAP;AAiBD;;AAnBgC,CAA5B", "sourcesContent": ["import NodeManager from '../../web/tools/NodeManager';\nimport { GestureStateManagerType } from './gestureStateManager';\n\nexport const GestureStateManager = {\n  create(handlerTag: number): GestureStateManagerType {\n    return {\n      begin: () => {\n        NodeManager.getHandler(handlerTag).begin();\n      },\n\n      activate: () => {\n        NodeManager.getHandler(handlerTag).activate();\n      },\n\n      fail: () => {\n        NodeManager.getHandler(handlerTag).fail();\n      },\n\n      end: () => {\n        NodeManager.getHandler(handlerTag).end();\n      },\n    };\n  },\n};\n"]}