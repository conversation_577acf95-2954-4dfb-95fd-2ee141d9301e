{"version": 3, "sources": ["InteractionManager.ts"], "names": ["InteractionManager", "constructor", "Map", "configureInteractions", "handler", "config", "dropRelationsForHandlerWithTag", "getTag", "waitFor", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "push", "handlerTag", "waitForRelations", "set", "simultaneousHandlers", "simultaneousRelations", "blocksHandlers", "blocksHandlersRelations", "shouldWaitForHandlerFailure", "get", "find", "tag", "undefined", "shouldRecognizeSimultaneously", "shouldRequireHandlerToWaitForFailure", "shouldHandlerBeCancelledBy", "_handler", "_<PERSON><PERSON><PERSON><PERSON>", "delete", "reset", "clear", "getInstance", "instance"], "mappings": ";;;;;;;;;AAGe,MAAMA,kBAAN,CAAyB;AAMtC;AACA;AACQC,EAAAA,WAAW,GAAG;AAAA,8CANqC,IAAIC,GAAJ,EAMrC;;AAAA,mDAL0C,IAAIA,GAAJ,EAK1C;;AAAA,qDAJ4C,IAAIA,GAAJ,EAI5C;AAAE;;AAEjBC,EAAAA,qBAAqB,CAACC,OAAD,EAA0BC,MAA1B,EAA0C;AACpE,SAAKC,8BAAL,CAAoCF,OAAO,CAACG,MAAR,EAApC;;AAEA,QAAIF,MAAM,CAACG,OAAX,EAAoB;AAClB,YAAMA,OAAiB,GAAG,EAA1B;AACAH,MAAAA,MAAM,CAACG,OAAP,CAAeC,OAAf,CAAwBC,YAAD,IAAiC;AACtD;AACA,YAAI,OAAOA,YAAP,KAAwB,QAA5B,EAAsC;AACpCF,UAAAA,OAAO,CAACG,IAAR,CAAaD,YAAb;AACD,SAFD,MAEO;AACL;AACAF,UAAAA,OAAO,CAACG,IAAR,CAAaD,YAAY,CAACE,UAA1B;AACD;AACF,OARD;AAUA,WAAKC,gBAAL,CAAsBC,GAAtB,CAA0BV,OAAO,CAACG,MAAR,EAA1B,EAA4CC,OAA5C;AACD;;AAED,QAAIH,MAAM,CAACU,oBAAX,EAAiC;AAC/B,YAAMA,oBAA8B,GAAG,EAAvC;AACAV,MAAAA,MAAM,CAACU,oBAAP,CAA4BN,OAA5B,CAAqCC,YAAD,IAAiC;AACnE,YAAI,OAAOA,YAAP,KAAwB,QAA5B,EAAsC;AACpCK,UAAAA,oBAAoB,CAACJ,IAArB,CAA0BD,YAA1B;AACD,SAFD,MAEO;AACLK,UAAAA,oBAAoB,CAACJ,IAArB,CAA0BD,YAAY,CAACE,UAAvC;AACD;AACF,OAND;AAQA,WAAKI,qBAAL,CAA2BF,GAA3B,CAA+BV,OAAO,CAACG,MAAR,EAA/B,EAAiDQ,oBAAjD;AACD;;AAED,QAAIV,MAAM,CAACY,cAAX,EAA2B;AACzB,YAAMA,cAAwB,GAAG,EAAjC;AACAZ,MAAAA,MAAM,CAACY,cAAP,CAAsBR,OAAtB,CAA+BC,YAAD,IAAiC;AAC7D,YAAI,OAAOA,YAAP,KAAwB,QAA5B,EAAsC;AACpCO,UAAAA,cAAc,CAACN,IAAf,CAAoBD,YAApB;AACD,SAFD,MAEO;AACLO,UAAAA,cAAc,CAACN,IAAf,CAAoBD,YAAY,CAACE,UAAjC;AACD;AACF,OAND;AAQA,WAAKM,uBAAL,CAA6BJ,GAA7B,CAAiCV,OAAO,CAACG,MAAR,EAAjC,EAAmDU,cAAnD;AACD;AACF;;AAEME,EAAAA,2BAA2B,CAChCf,OADgC,EAEhCM,YAFgC,EAGvB;AACT,UAAMF,OAA6B,GAAG,KAAKK,gBAAL,CAAsBO,GAAtB,CACpChB,OAAO,CAACG,MAAR,EADoC,CAAtC;AAIA,WACE,CAAAC,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAEa,IAAT,CAAeC,GAAD,IAAiB;AAC7B,aAAOA,GAAG,KAAKZ,YAAY,CAACH,MAAb,EAAf;AACD,KAFD,OAEOgB,SAHT;AAKD;;AAEMC,EAAAA,6BAA6B,CAClCpB,OADkC,EAElCM,YAFkC,EAGzB;AACT,UAAMK,oBAA0C,GAC9C,KAAKC,qBAAL,CAA2BI,GAA3B,CAA+BhB,OAAO,CAACG,MAAR,EAA/B,CADF;AAGA,WACE,CAAAQ,oBAAoB,SAApB,IAAAA,oBAAoB,WAApB,YAAAA,oBAAoB,CAAEM,IAAtB,CAA4BC,GAAD,IAAiB;AAC1C,aAAOA,GAAG,KAAKZ,YAAY,CAACH,MAAb,EAAf;AACD,KAFD,OAEOgB,SAHT;AAKD;;AAEME,EAAAA,oCAAoC,CACzCrB,OADyC,EAEzCM,YAFyC,EAGhC;AACT,UAAMF,OAA6B,GAAG,KAAKU,uBAAL,CAA6BE,GAA7B,CACpChB,OAAO,CAACG,MAAR,EADoC,CAAtC;AAIA,WACE,CAAAC,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAEa,IAAT,CAAeC,GAAD,IAAiB;AAC7B,aAAOA,GAAG,KAAKZ,YAAY,CAACH,MAAb,EAAf;AACD,KAFD,OAEOgB,SAHT;AAKD;;AAEMG,EAAAA,0BAA0B,CAC/BC,QAD+B,EAE/BC,aAF+B,EAGtB;AACT;AACA,WAAO,KAAP;AACD;;AAEMtB,EAAAA,8BAA8B,CAACM,UAAD,EAA2B;AAC9D,SAAKC,gBAAL,CAAsBgB,MAAtB,CAA6BjB,UAA7B;AACA,SAAKI,qBAAL,CAA2Ba,MAA3B,CAAkCjB,UAAlC;AACA,SAAKM,uBAAL,CAA6BW,MAA7B,CAAoCjB,UAApC;AACD;;AAEMkB,EAAAA,KAAK,GAAG;AACb,SAAKjB,gBAAL,CAAsBkB,KAAtB;AACA,SAAKf,qBAAL,CAA2Be,KAA3B;AACA,SAAKb,uBAAL,CAA6Ba,KAA7B;AACD;;AAEwB,SAAXC,WAAW,GAAuB;AAC9C,QAAI,CAAC,KAAKC,QAAV,EAAoB;AAClB,WAAKA,QAAL,GAAgB,IAAIjC,kBAAJ,EAAhB;AACD;;AAED,WAAO,KAAKiC,QAAZ;AACD;;AA7HqC;;;;gBAAnBjC,kB", "sourcesContent": ["import GestureHandler from '../handlers/GestureHandler';\nimport { Config, Handler } from '../interfaces';\n\nexport default class InteractionManager {\n  private static instance: InteractionManager;\n  private readonly waitForRelations: Map<number, number[]> = new Map();\n  private readonly simultaneousRelations: Map<number, number[]> = new Map();\n  private readonly blocksHandlersRelations: Map<number, number[]> = new Map();\n\n  // Private becaues of singleton\n  // eslint-disable-next-line no-useless-constructor, @typescript-eslint/no-empty-function\n  private constructor() {}\n\n  public configureInteractions(handler: GestureHandler, config: Config) {\n    this.dropRelationsForHandlerWithTag(handler.getTag());\n\n    if (config.waitFor) {\n      const waitFor: number[] = [];\n      config.waitFor.forEach((otherHandler: Handler): void => {\n        // New API reference\n        if (typeof otherHandler === 'number') {\n          waitFor.push(otherHandler);\n        } else {\n          // Old API reference\n          waitFor.push(otherHandler.handlerTag);\n        }\n      });\n\n      this.waitForRelations.set(handler.getTag(), waitFor);\n    }\n\n    if (config.simultaneousHandlers) {\n      const simultaneousHandlers: number[] = [];\n      config.simultaneousHandlers.forEach((otherHandler: Handler): void => {\n        if (typeof otherHandler === 'number') {\n          simultaneousHandlers.push(otherHandler);\n        } else {\n          simultaneousHandlers.push(otherHandler.handlerTag);\n        }\n      });\n\n      this.simultaneousRelations.set(handler.getTag(), simultaneousHandlers);\n    }\n\n    if (config.blocksHandlers) {\n      const blocksHandlers: number[] = [];\n      config.blocksHandlers.forEach((otherHandler: Handler): void => {\n        if (typeof otherHandler === 'number') {\n          blocksHandlers.push(otherHandler);\n        } else {\n          blocksHandlers.push(otherHandler.handlerTag);\n        }\n      });\n\n      this.blocksHandlersRelations.set(handler.getTag(), blocksHandlers);\n    }\n  }\n\n  public shouldWaitForHandlerFailure(\n    handler: GestureHandler,\n    otherHandler: GestureHandler\n  ): boolean {\n    const waitFor: number[] | undefined = this.waitForRelations.get(\n      handler.getTag()\n    );\n\n    return (\n      waitFor?.find((tag: number) => {\n        return tag === otherHandler.getTag();\n      }) !== undefined\n    );\n  }\n\n  public shouldRecognizeSimultaneously(\n    handler: GestureHandler,\n    otherHandler: GestureHandler\n  ): boolean {\n    const simultaneousHandlers: number[] | undefined =\n      this.simultaneousRelations.get(handler.getTag());\n\n    return (\n      simultaneousHandlers?.find((tag: number) => {\n        return tag === otherHandler.getTag();\n      }) !== undefined\n    );\n  }\n\n  public shouldRequireHandlerToWaitForFailure(\n    handler: GestureHandler,\n    otherHandler: GestureHandler\n  ): boolean {\n    const waitFor: number[] | undefined = this.blocksHandlersRelations.get(\n      handler.getTag()\n    );\n\n    return (\n      waitFor?.find((tag: number) => {\n        return tag === otherHandler.getTag();\n      }) !== undefined\n    );\n  }\n\n  public shouldHandlerBeCancelledBy(\n    _handler: GestureHandler,\n    _otherHandler: GestureHandler\n  ): boolean {\n    //TODO: Implement logic\n    return false;\n  }\n\n  public dropRelationsForHandlerWithTag(handlerTag: number): void {\n    this.waitForRelations.delete(handlerTag);\n    this.simultaneousRelations.delete(handlerTag);\n    this.blocksHandlersRelations.delete(handlerTag);\n  }\n\n  public reset() {\n    this.waitForRelations.clear();\n    this.simultaneousRelations.clear();\n    this.blocksHandlersRelations.clear();\n  }\n\n  public static getInstance(): InteractionManager {\n    if (!this.instance) {\n      this.instance = new InteractionManager();\n    }\n\n    return this.instance;\n  }\n}\n"]}