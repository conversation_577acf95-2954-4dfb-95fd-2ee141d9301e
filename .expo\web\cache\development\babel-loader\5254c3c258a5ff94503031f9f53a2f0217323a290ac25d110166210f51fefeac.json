{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nvar _jsxFileName = \"D:\\\\aplikasi\\\\TRAE\\\\psg-bmi\\\\src\\\\components\\\\BottomNavigation.js\",\n  _this = this;\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport { Colors, Typography, Spacing, Animations } from '../constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport var BOTTOM_NAV_HEIGHT = 70;\nexport var FAB_ELEVATION = 20;\nexport var getBottomNavHeight = function getBottomNavHeight() {\n  var safeAreaBottom = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  return BOTTOM_NAV_HEIGHT + safeAreaBottom;\n};\nvar navigationItems = [{\n  id: 1,\n  name: 'Home',\n  icon: 'home',\n  label: 'Beranda',\n  badge: 0\n}, {\n  id: 2,\n  name: 'Activities',\n  icon: 'assignment',\n  label: 'Aktivitas',\n  badge: 3\n}, {\n  id: 3,\n  name: 'FAB',\n  icon: 'add',\n  label: '',\n  badge: 0\n}, {\n  id: 4,\n  name: 'Notifications',\n  icon: 'notifications',\n  label: 'Notifikasi',\n  badge: 5\n}, {\n  id: 5,\n  name: 'Profile',\n  icon: 'person',\n  label: 'Profil',\n  badge: 0\n}];\nvar BottomNavigation = function BottomNavigation() {\n  var _React$useState = React.useState('Home'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeTab = _React$useState2[0],\n    setActiveTab = _React$useState2[1];\n  var fabScale = React.useRef(new Animated.Value(1)).current;\n  var tabAnimations = React.useRef(navigationItems.reduce(function (acc, item) {\n    acc[item.name] = new Animated.Value(item.name === 'Home' ? 1 : 0.8);\n    return acc;\n  }, {})).current;\n  var insets = useSafeAreaInsets();\n  var handleTabPress = function handleTabPress(item) {\n    if (item.name === 'FAB') {\n      handleFABPress();\n      return;\n    }\n    setActiveTab(item.name);\n    Object.keys(tabAnimations).forEach(function (key) {\n      Animated.spring(tabAnimations[key], _objectSpread(_objectSpread({\n        toValue: key === item.name ? 1 : 0.8\n      }, Animations.spring.default), {}, {\n        useNativeDriver: true\n      })).start();\n    });\n    console.log(`Navigate to ${item.name}`);\n  };\n  var handleFABPress = function handleFABPress() {\n    Animated.sequence([Animated.spring(fabScale, _objectSpread(_objectSpread({\n      toValue: 0.9\n    }, Animations.spring.default), {}, {\n      useNativeDriver: true\n    })), Animated.spring(fabScale, _objectSpread(_objectSpread({\n      toValue: 1\n    }, Animations.spring.bouncy), {}, {\n      useNativeDriver: true\n    }))]).start();\n    Alert.alert('Quick Actions', 'Pilih aksi cepat yang ingin dilakukan', [{\n      text: 'Check-in/out',\n      onPress: function onPress() {\n        return console.log('Quick check-in');\n      }\n    }, {\n      text: 'Laporan Cepat',\n      onPress: function onPress() {\n        return console.log('Quick report');\n      }\n    }, {\n      text: 'Emergency',\n      onPress: function onPress() {\n        return console.log('Emergency action');\n      },\n      style: 'destructive'\n    }, {\n      text: 'Batal',\n      style: 'cancel'\n    }]);\n  };\n  var renderTabItem = function renderTabItem(item) {\n    if (item.name === 'FAB') {\n      return _jsxDEV(View, {\n        style: styles.fabContainer,\n        children: _jsxDEV(Animated.View, {\n          style: {\n            transform: [{\n              scale: fabScale\n            }]\n          },\n          children: _jsxDEV(TouchableOpacity, {\n            style: styles.fab,\n            onPress: function onPress() {\n              return handleTabPress(item);\n            },\n            activeOpacity: 0.8,\n            children: _jsxDEV(MaterialIcons, {\n              name: item.icon,\n              size: Spacing.iconSize.lg,\n              color: Colors.onPrimary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, _this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, _this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, _this)\n      }, item.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, _this);\n    }\n    var isActive = activeTab === item.name;\n    return _jsxDEV(Animated.View, {\n      style: [styles.tabItem, {\n        transform: [{\n          scale: tabAnimations[item.name]\n        }]\n      }],\n      children: _jsxDEV(TouchableOpacity, {\n        style: styles.tabButton,\n        onPress: function onPress() {\n          return handleTabPress(item);\n        },\n        activeOpacity: 0.7,\n        children: [_jsxDEV(View, {\n          style: styles.iconContainer,\n          children: [_jsxDEV(MaterialIcons, {\n            name: item.icon,\n            size: Spacing.iconSize.md,\n            color: isActive ? Colors.bottomNavActive : Colors.bottomNavInactive\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, _this), item.badge > 0 && _jsxDEV(View, {\n            style: styles.badge,\n            children: _jsxDEV(Text, {\n              style: styles.badgeText,\n              children: item.badge > 9 ? '9+' : item.badge\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, _this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, _this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, _this), _jsxDEV(Text, {\n          style: [styles.tabLabel, {\n            color: isActive ? Colors.bottomNavActive : Colors.bottomNavInactive\n          }],\n          children: item.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, _this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, _this)\n    }, item.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, _this);\n  };\n  return _jsxDEV(View, {\n    style: [styles.container, {\n      paddingBottom: insets.bottom\n    }],\n    children: _jsxDEV(View, {\n      style: styles.navigationBar,\n      children: navigationItems.map(renderTabItem)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, _this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, _this);\n};\nvar styles = StyleSheet.create({\n  container: _objectSpread({\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    zIndex: 1000,\n    backgroundColor: Colors.bottomNavBackground\n  }, Platform.select({\n    web: {\n      position: 'fixed',\n      WebkitBackfaceVisibility: 'hidden',\n      backfaceVisibility: 'hidden'\n    }\n  })),\n  navigationBar: {\n    flexDirection: 'row',\n    backgroundColor: Colors.bottomNavBackground,\n    paddingTop: Spacing.padding.sm,\n    paddingBottom: Spacing.padding.md,\n    paddingHorizontal: Spacing.padding.sm,\n    borderTopWidth: 1,\n    borderTopColor: Colors.outline,\n    height: BOTTOM_NAV_HEIGHT,\n    shadowColor: Colors.shadow,\n    shadowOffset: {\n      width: 0,\n      height: -2\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 8\n  },\n  tabItem: {\n    flex: 1,\n    alignItems: 'center'\n  },\n  tabButton: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingVertical: Spacing.padding.xs\n  },\n  iconContainer: {\n    position: 'relative',\n    marginBottom: Spacing.xs / 2\n  },\n  tabLabel: _objectSpread(_objectSpread({}, Typography.caption), {}, {\n    fontSize: 10,\n    textAlign: 'center'\n  }),\n  fabContainer: {\n    flex: 1,\n    alignItems: 'center',\n    marginTop: -FAB_ELEVATION\n  },\n  fab: {\n    width: 56,\n    height: 56,\n    borderRadius: 28,\n    backgroundColor: Colors.primary,\n    alignItems: 'center',\n    justifyContent: 'center',\n    shadowColor: Colors.shadow,\n    shadowOffset: {\n      width: 0,\n      height: 4\n    },\n    shadowOpacity: 0.3,\n    shadowRadius: 8,\n    elevation: 8\n  },\n  badge: {\n    position: 'absolute',\n    top: -4,\n    right: -4,\n    backgroundColor: Colors.error,\n    borderRadius: 8,\n    minWidth: 16,\n    height: 16,\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderWidth: 2,\n    borderColor: Colors.bottomNavBackground\n  },\n  badgeText: _objectSpread(_objectSpread({}, Typography.caption), {}, {\n    color: Colors.onPrimary,\n    fontSize: 9,\n    fontWeight: '600'\n  })\n});\nexport default BottomNavigation;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "TouchableOpacity", "Animated", "<PERSON><PERSON>", "Platform", "MaterialIcons", "useSafeAreaInsets", "Colors", "Typography", "Spacing", "Animations", "jsxDEV", "_jsxDEV", "BOTTOM_NAV_HEIGHT", "FAB_ELEVATION", "getBottomNavHeight", "safeAreaBottom", "arguments", "length", "undefined", "navigationItems", "id", "name", "icon", "label", "badge", "BottomNavigation", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "activeTab", "setActiveTab", "fabScale", "useRef", "Value", "current", "tabAnimations", "reduce", "acc", "item", "insets", "handleTabPress", "handleFABPress", "Object", "keys", "for<PERSON>ach", "key", "spring", "_objectSpread", "toValue", "default", "useNativeDriver", "start", "console", "log", "sequence", "bouncy", "alert", "text", "onPress", "style", "renderTabItem", "styles", "fabContainer", "children", "transform", "scale", "fab", "activeOpacity", "size", "iconSize", "lg", "color", "onPrimary", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_this", "isActive", "tabItem", "tabButton", "iconContainer", "md", "bottomNavActive", "bottomNavInactive", "badgeText", "tabLabel", "container", "paddingBottom", "bottom", "navigationBar", "map", "create", "position", "left", "right", "zIndex", "backgroundColor", "bottomNavBackground", "select", "web", "WebkitBackfaceVisibility", "backfaceVisibility", "flexDirection", "paddingTop", "padding", "sm", "paddingHorizontal", "borderTopWidth", "borderTopColor", "outline", "height", "shadowColor", "shadow", "shadowOffset", "width", "shadowOpacity", "shadowRadius", "elevation", "flex", "alignItems", "justifyContent", "paddingVertical", "xs", "marginBottom", "caption", "fontSize", "textAlign", "marginTop", "borderRadius", "primary", "top", "error", "min<PERSON><PERSON><PERSON>", "borderWidth", "borderColor", "fontWeight"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/components/BottomNavigation.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  TouchableOpacity,\n  Animated,\n  Alert,\n  Platform,\n} from 'react-native';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport { Colors, Typography, Spacing, Animations } from '../constants';\n\n// Bottom navigation height constants\nexport const BOTTOM_NAV_HEIGHT = 70; // Base height without safe area\nexport const FAB_ELEVATION = 20; // How much FAB extends above the nav bar\n\n// Utility function to get total bottom navigation height including safe area\nexport const getBottomNavHeight = (safeAreaBottom = 0) => {\n  return BOTTOM_NAV_HEIGHT + safeAreaBottom;\n};\n\nconst navigationItems = [\n  {\n    id: 1,\n    name: 'Home',\n    icon: 'home',\n    label: 'Beranda',\n    badge: 0,\n  },\n  {\n    id: 2,\n    name: 'Activities',\n    icon: 'assignment',\n    label: 'Aktivitas',\n    badge: 3,\n  },\n  {\n    id: 3,\n    name: 'FAB', // This will be the FAB space\n    icon: 'add',\n    label: '',\n    badge: 0,\n  },\n  {\n    id: 4,\n    name: 'Notifications',\n    icon: 'notifications',\n    label: 'Notifikasi',\n    badge: 5,\n  },\n  {\n    id: 5,\n    name: 'Profile',\n    icon: 'person',\n    label: 'Profil',\n    badge: 0,\n  },\n];\n\nconst BottomNavigation = () => {\n  const [activeTab, setActiveTab] = React.useState('Home');\n  const fabScale = React.useRef(new Animated.Value(1)).current;\n  const tabAnimations = React.useRef(\n    navigationItems.reduce((acc, item) => {\n      acc[item.name] = new Animated.Value(item.name === 'Home' ? 1 : 0.8);\n      return acc;\n    }, {})\n  ).current;\n\n  // Get safe area insets for proper bottom padding\n  const insets = useSafeAreaInsets();\n\n  const handleTabPress = (item) => {\n    if (item.name === 'FAB') {\n      handleFABPress();\n      return;\n    }\n\n    setActiveTab(item.name);\n    \n    // Animate tab selection\n    Object.keys(tabAnimations).forEach((key) => {\n      Animated.spring(tabAnimations[key], {\n        toValue: key === item.name ? 1 : 0.8,\n        ...Animations.spring.default,\n        useNativeDriver: true,\n      }).start();\n    });\n\n    console.log(`Navigate to ${item.name}`);\n  };\n\n  const handleFABPress = () => {\n    // Animate FAB press\n    Animated.sequence([\n      Animated.spring(fabScale, {\n        toValue: 0.9,\n        ...Animations.spring.default,\n        useNativeDriver: true,\n      }),\n      Animated.spring(fabScale, {\n        toValue: 1,\n        ...Animations.spring.bouncy,\n        useNativeDriver: true,\n      }),\n    ]).start();\n\n    // Show quick actions menu\n    Alert.alert(\n      'Quick Actions',\n      'Pilih aksi cepat yang ingin dilakukan',\n      [\n        {\n          text: 'Check-in/out',\n          onPress: () => console.log('Quick check-in'),\n        },\n        {\n          text: 'Laporan Cepat',\n          onPress: () => console.log('Quick report'),\n        },\n        {\n          text: 'Emergency',\n          onPress: () => console.log('Emergency action'),\n          style: 'destructive',\n        },\n        {\n          text: 'Batal',\n          style: 'cancel',\n        },\n      ]\n    );\n  };\n\n  const renderTabItem = (item) => {\n    if (item.name === 'FAB') {\n      return (\n        <View key={item.id} style={styles.fabContainer}>\n          <Animated.View style={{ transform: [{ scale: fabScale }] }}>\n            <TouchableOpacity\n              style={styles.fab}\n              onPress={() => handleTabPress(item)}\n              activeOpacity={0.8}\n            >\n              <MaterialIcons\n                name={item.icon}\n                size={Spacing.iconSize.lg}\n                color={Colors.onPrimary}\n              />\n            </TouchableOpacity>\n          </Animated.View>\n        </View>\n      );\n    }\n\n    const isActive = activeTab === item.name;\n    \n    return (\n      <Animated.View\n        key={item.id}\n        style={[\n          styles.tabItem,\n          { transform: [{ scale: tabAnimations[item.name] }] },\n        ]}\n      >\n        <TouchableOpacity\n          style={styles.tabButton}\n          onPress={() => handleTabPress(item)}\n          activeOpacity={0.7}\n        >\n          <View style={styles.iconContainer}>\n            <MaterialIcons\n              name={item.icon}\n              size={Spacing.iconSize.md}\n              color={isActive ? Colors.bottomNavActive : Colors.bottomNavInactive}\n            />\n            {item.badge > 0 && (\n              <View style={styles.badge}>\n                <Text style={styles.badgeText}>\n                  {item.badge > 9 ? '9+' : item.badge}\n                </Text>\n              </View>\n            )}\n          </View>\n          <Text\n            style={[\n              styles.tabLabel,\n              { color: isActive ? Colors.bottomNavActive : Colors.bottomNavInactive },\n            ]}\n          >\n            {item.label}\n          </Text>\n        </TouchableOpacity>\n      </Animated.View>\n    );\n  };\n\n  return (\n    <View style={[styles.container, { paddingBottom: insets.bottom }]}>\n      <View style={styles.navigationBar}>\n        {navigationItems.map(renderTabItem)}\n      </View>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    zIndex: 1000, // Ensure it stays above all content\n    backgroundColor: Colors.bottomNavBackground,\n    ...Platform.select({\n      web: {\n        position: 'fixed',\n        // Ensure it works well with web scrolling\n        WebkitBackfaceVisibility: 'hidden',\n        backfaceVisibility: 'hidden',\n      },\n    }),\n  },\n  navigationBar: {\n    flexDirection: 'row',\n    backgroundColor: Colors.bottomNavBackground,\n    paddingTop: Spacing.padding.sm,\n    paddingBottom: Spacing.padding.md,\n    paddingHorizontal: Spacing.padding.sm,\n    borderTopWidth: 1,\n    borderTopColor: Colors.outline,\n    height: BOTTOM_NAV_HEIGHT,\n    shadowColor: Colors.shadow,\n    shadowOffset: {\n      width: 0,\n      height: -2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 8,\n  },\n  tabItem: {\n    flex: 1,\n    alignItems: 'center',\n  },\n  tabButton: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingVertical: Spacing.padding.xs,\n  },\n  iconContainer: {\n    position: 'relative',\n    marginBottom: Spacing.xs / 2,\n  },\n  tabLabel: {\n    ...Typography.caption,\n    fontSize: 10,\n    textAlign: 'center',\n  },\n  fabContainer: {\n    flex: 1,\n    alignItems: 'center',\n    marginTop: -FAB_ELEVATION, // Elevate FAB above the navigation bar\n  },\n  fab: {\n    width: 56,\n    height: 56,\n    borderRadius: 28,\n    backgroundColor: Colors.primary,\n    alignItems: 'center',\n    justifyContent: 'center',\n    shadowColor: Colors.shadow,\n    shadowOffset: {\n      width: 0,\n      height: 4,\n    },\n    shadowOpacity: 0.3,\n    shadowRadius: 8,\n    elevation: 8,\n  },\n  badge: {\n    position: 'absolute',\n    top: -4,\n    right: -4,\n    backgroundColor: Colors.error,\n    borderRadius: 8,\n    minWidth: 16,\n    height: 16,\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderWidth: 2,\n    borderColor: Colors.bottomNavBackground,\n  },\n  badgeText: {\n    ...Typography.caption,\n    color: Colors.onPrimary,\n    fontSize: 9,\n    fontWeight: '600',\n  },\n});\n\nexport default BottomNavigation;\n"], "mappings": ";;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,QAAA;AAU1B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGvE,OAAO,IAAMC,iBAAiB,GAAG,EAAE;AACnC,OAAO,IAAMC,aAAa,GAAG,EAAE;AAG/B,OAAO,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAA2B;EAAA,IAAvBC,cAAc,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACnD,OAAOJ,iBAAiB,GAAGG,cAAc;AAC3C,CAAC;AAED,IAAMI,eAAe,GAAG,CACtB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,eAAe;EACrBC,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE;AACT,CAAC,CACF;AAED,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;EAC7B,IAAAC,eAAA,GAAkC9B,KAAK,CAAC+B,QAAQ,CAAC,MAAM,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAjDI,SAAS,GAAAF,gBAAA;IAAEG,YAAY,GAAAH,gBAAA;EAC9B,IAAMI,QAAQ,GAAGpC,KAAK,CAACqC,MAAM,CAAC,IAAIhC,QAAQ,CAACiC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAC5D,IAAMC,aAAa,GAAGxC,KAAK,CAACqC,MAAM,CAChCd,eAAe,CAACkB,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAK;IACpCD,GAAG,CAACC,IAAI,CAAClB,IAAI,CAAC,GAAG,IAAIpB,QAAQ,CAACiC,KAAK,CAACK,IAAI,CAAClB,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;IACnE,OAAOiB,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CACP,CAAC,CAACH,OAAO;EAGT,IAAMK,MAAM,GAAGnC,iBAAiB,CAAC,CAAC;EAElC,IAAMoC,cAAc,GAAG,SAAjBA,cAAcA,CAAIF,IAAI,EAAK;IAC/B,IAAIA,IAAI,CAAClB,IAAI,KAAK,KAAK,EAAE;MACvBqB,cAAc,CAAC,CAAC;MAChB;IACF;IAEAX,YAAY,CAACQ,IAAI,CAAClB,IAAI,CAAC;IAGvBsB,MAAM,CAACC,IAAI,CAACR,aAAa,CAAC,CAACS,OAAO,CAAC,UAACC,GAAG,EAAK;MAC1C7C,QAAQ,CAAC8C,MAAM,CAACX,aAAa,CAACU,GAAG,CAAC,EAAAE,aAAA,CAAAA,aAAA;QAChCC,OAAO,EAAEH,GAAG,KAAKP,IAAI,CAAClB,IAAI,GAAG,CAAC,GAAG;MAAG,GACjCZ,UAAU,CAACsC,MAAM,CAACG,OAAO;QAC5BC,eAAe,EAAE;MAAI,EACtB,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC;IAEFC,OAAO,CAACC,GAAG,CAAC,eAAef,IAAI,CAAClB,IAAI,EAAE,CAAC;EACzC,CAAC;EAED,IAAMqB,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAE3BzC,QAAQ,CAACsD,QAAQ,CAAC,CAChBtD,QAAQ,CAAC8C,MAAM,CAACf,QAAQ,EAAAgB,aAAA,CAAAA,aAAA;MACtBC,OAAO,EAAE;IAAG,GACTxC,UAAU,CAACsC,MAAM,CAACG,OAAO;MAC5BC,eAAe,EAAE;IAAI,EACtB,CAAC,EACFlD,QAAQ,CAAC8C,MAAM,CAACf,QAAQ,EAAAgB,aAAA,CAAAA,aAAA;MACtBC,OAAO,EAAE;IAAC,GACPxC,UAAU,CAACsC,MAAM,CAACS,MAAM;MAC3BL,eAAe,EAAE;IAAI,EACtB,CAAC,CACH,CAAC,CAACC,KAAK,CAAC,CAAC;IAGVlD,KAAK,CAACuD,KAAK,CACT,eAAe,EACf,uCAAuC,EACvC,CACE;MACEC,IAAI,EAAE,cAAc;MACpBC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQN,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAAA;IAC9C,CAAC,EACD;MACEI,IAAI,EAAE,eAAe;MACrBC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQN,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAAA;IAC5C,CAAC,EACD;MACEI,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQN,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAAA;MAC9CM,KAAK,EAAE;IACT,CAAC,EACD;MACEF,IAAI,EAAE,OAAO;MACbE,KAAK,EAAE;IACT,CAAC,CAEL,CAAC;EACH,CAAC;EAED,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAItB,IAAI,EAAK;IAC9B,IAAIA,IAAI,CAAClB,IAAI,KAAK,KAAK,EAAE;MACvB,OACEV,OAAA,CAACd,IAAI;QAAe+D,KAAK,EAAEE,MAAM,CAACC,YAAa;QAAAC,QAAA,EAC7CrD,OAAA,CAACV,QAAQ,CAACJ,IAAI;UAAC+D,KAAK,EAAE;YAAEK,SAAS,EAAE,CAAC;cAAEC,KAAK,EAAElC;YAAS,CAAC;UAAE,CAAE;UAAAgC,QAAA,EACzDrD,OAAA,CAACX,gBAAgB;YACf4D,KAAK,EAAEE,MAAM,CAACK,GAAI;YAClBR,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQlB,cAAc,CAACF,IAAI,CAAC;YAAA,CAAC;YACpC6B,aAAa,EAAE,GAAI;YAAAJ,QAAA,EAEnBrD,OAAA,CAACP,aAAa;cACZiB,IAAI,EAAEkB,IAAI,CAACjB,IAAK;cAChB+C,IAAI,EAAE7D,OAAO,CAAC8D,QAAQ,CAACC,EAAG;cAC1BC,KAAK,EAAElE,MAAM,CAACmE;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,GAAAC,KACzB;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,GAAAC,KACc;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KACN;MAAC,GAbPvC,IAAI,CAACnB,EAAE;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KAcZ,CAAC;IAEX;IAEA,IAAMC,QAAQ,GAAGjD,SAAS,KAAKS,IAAI,CAAClB,IAAI;IAExC,OACEV,OAAA,CAACV,QAAQ,CAACJ,IAAI;MAEZ+D,KAAK,EAAE,CACLE,MAAM,CAACkB,OAAO,EACd;QAAEf,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE9B,aAAa,CAACG,IAAI,CAAClB,IAAI;QAAE,CAAC;MAAE,CAAC,CACpD;MAAA2C,QAAA,EAEFrD,OAAA,CAACX,gBAAgB;QACf4D,KAAK,EAAEE,MAAM,CAACmB,SAAU;QACxBtB,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQlB,cAAc,CAACF,IAAI,CAAC;QAAA,CAAC;QACpC6B,aAAa,EAAE,GAAI;QAAAJ,QAAA,GAEnBrD,OAAA,CAACd,IAAI;UAAC+D,KAAK,EAAEE,MAAM,CAACoB,aAAc;UAAAlB,QAAA,GAChCrD,OAAA,CAACP,aAAa;YACZiB,IAAI,EAAEkB,IAAI,CAACjB,IAAK;YAChB+C,IAAI,EAAE7D,OAAO,CAAC8D,QAAQ,CAACa,EAAG;YAC1BX,KAAK,EAAEO,QAAQ,GAAGzE,MAAM,CAAC8E,eAAe,GAAG9E,MAAM,CAAC+E;UAAkB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,GAAAC,KACrE,CAAC,EACDvC,IAAI,CAACf,KAAK,GAAG,CAAC,IACbb,OAAA,CAACd,IAAI;YAAC+D,KAAK,EAAEE,MAAM,CAACtC,KAAM;YAAAwC,QAAA,EACxBrD,OAAA,CAACb,IAAI;cAAC8D,KAAK,EAAEE,MAAM,CAACwB,SAAU;cAAAtB,QAAA,EAC3BzB,IAAI,CAACf,KAAK,GAAG,CAAC,GAAG,IAAI,GAAGe,IAAI,CAACf;YAAK;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,GAAAC,KAC/B;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,GAAAC,KACH,CACP;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KACG,CAAC,EACPnE,OAAA,CAACb,IAAI;UACH8D,KAAK,EAAE,CACLE,MAAM,CAACyB,QAAQ,EACf;YAAEf,KAAK,EAAEO,QAAQ,GAAGzE,MAAM,CAAC8E,eAAe,GAAG9E,MAAM,CAAC+E;UAAkB,CAAC,CACvE;UAAArB,QAAA,EAEDzB,IAAI,CAAChB;QAAK;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KACP,CAAC;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KACS;IAAC,GAjCdvC,IAAI,CAACnB,EAAE;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,GAAAC,KAkCC,CAAC;EAEpB,CAAC;EAED,OACEnE,OAAA,CAACd,IAAI;IAAC+D,KAAK,EAAE,CAACE,MAAM,CAAC0B,SAAS,EAAE;MAAEC,aAAa,EAAEjD,MAAM,CAACkD;IAAO,CAAC,CAAE;IAAA1B,QAAA,EAChErD,OAAA,CAACd,IAAI;MAAC+D,KAAK,EAAEE,MAAM,CAAC6B,aAAc;MAAA3B,QAAA,EAC/B7C,eAAe,CAACyE,GAAG,CAAC/B,aAAa;IAAC;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,GAAAC,KAC/B;EAAC;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,GAAAC,KACH,CAAC;AAEX,CAAC;AAED,IAAMhB,MAAM,GAAG/D,UAAU,CAAC8F,MAAM,CAAC;EAC/BL,SAAS,EAAAxC,aAAA;IACP8C,QAAQ,EAAE,UAAU;IACpBJ,MAAM,EAAE,CAAC;IACTK,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,IAAI;IACZC,eAAe,EAAE5F,MAAM,CAAC6F;EAAmB,GACxChG,QAAQ,CAACiG,MAAM,CAAC;IACjBC,GAAG,EAAE;MACHP,QAAQ,EAAE,OAAO;MAEjBQ,wBAAwB,EAAE,QAAQ;MAClCC,kBAAkB,EAAE;IACtB;EACF,CAAC,CAAC,CACH;EACDZ,aAAa,EAAE;IACba,aAAa,EAAE,KAAK;IACpBN,eAAe,EAAE5F,MAAM,CAAC6F,mBAAmB;IAC3CM,UAAU,EAAEjG,OAAO,CAACkG,OAAO,CAACC,EAAE;IAC9BlB,aAAa,EAAEjF,OAAO,CAACkG,OAAO,CAACvB,EAAE;IACjCyB,iBAAiB,EAAEpG,OAAO,CAACkG,OAAO,CAACC,EAAE;IACrCE,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAExG,MAAM,CAACyG,OAAO;IAC9BC,MAAM,EAAEpG,iBAAiB;IACzBqG,WAAW,EAAE3G,MAAM,CAAC4G,MAAM;IAC1BC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRJ,MAAM,EAAE,CAAC;IACX,CAAC;IACDK,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDvC,OAAO,EAAE;IACPwC,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE;EACd,CAAC;EACDxC,SAAS,EAAE;IACTwC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,eAAe,EAAEnH,OAAO,CAACkG,OAAO,CAACkB;EACnC,CAAC;EACD1C,aAAa,EAAE;IACbY,QAAQ,EAAE,UAAU;IACpB+B,YAAY,EAAErH,OAAO,CAACoH,EAAE,GAAG;EAC7B,CAAC;EACDrC,QAAQ,EAAAvC,aAAA,CAAAA,aAAA,KACHzC,UAAU,CAACuH,OAAO;IACrBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE;EAAQ,EACpB;EACDjE,YAAY,EAAE;IACZyD,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,QAAQ;IACpBQ,SAAS,EAAE,CAACpH;EACd,CAAC;EACDsD,GAAG,EAAE;IACHiD,KAAK,EAAE,EAAE;IACTJ,MAAM,EAAE,EAAE;IACVkB,YAAY,EAAE,EAAE;IAChBhC,eAAe,EAAE5F,MAAM,CAAC6H,OAAO;IAC/BV,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBT,WAAW,EAAE3G,MAAM,CAAC4G,MAAM;IAC1BC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRJ,MAAM,EAAE;IACV,CAAC;IACDK,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACD/F,KAAK,EAAE;IACLsE,QAAQ,EAAE,UAAU;IACpBsC,GAAG,EAAE,CAAC,CAAC;IACPpC,KAAK,EAAE,CAAC,CAAC;IACTE,eAAe,EAAE5F,MAAM,CAAC+H,KAAK;IAC7BH,YAAY,EAAE,CAAC;IACfI,QAAQ,EAAE,EAAE;IACZtB,MAAM,EAAE,EAAE;IACVS,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBa,WAAW,EAAE,CAAC;IACdC,WAAW,EAAElI,MAAM,CAAC6F;EACtB,CAAC;EACDb,SAAS,EAAAtC,aAAA,CAAAA,aAAA,KACJzC,UAAU,CAACuH,OAAO;IACrBtD,KAAK,EAAElE,MAAM,CAACmE,SAAS;IACvBsD,QAAQ,EAAE,CAAC;IACXU,UAAU,EAAE;EAAK;AAErB,CAAC,CAAC;AAEF,eAAehH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}