{"ast": null, "code": "export var defaultProps = {\n  children: true,\n  dataSet: true,\n  dir: true,\n  id: true,\n  ref: true,\n  suppressHydrationWarning: true,\n  tabIndex: true,\n  testID: true,\n  focusable: true,\n  nativeID: true\n};\nexport var accessibilityProps = {\n  'aria-activedescendant': true,\n  'aria-atomic': true,\n  'aria-autocomplete': true,\n  'aria-busy': true,\n  'aria-checked': true,\n  'aria-colcount': true,\n  'aria-colindex': true,\n  'aria-colspan': true,\n  'aria-controls': true,\n  'aria-current': true,\n  'aria-describedby': true,\n  'aria-details': true,\n  'aria-disabled': true,\n  'aria-errormessage': true,\n  'aria-expanded': true,\n  'aria-flowto': true,\n  'aria-haspopup': true,\n  'aria-hidden': true,\n  'aria-invalid': true,\n  'aria-keyshortcuts': true,\n  'aria-label': true,\n  'aria-labelledby': true,\n  'aria-level': true,\n  'aria-live': true,\n  'aria-modal': true,\n  'aria-multiline': true,\n  'aria-multiselectable': true,\n  'aria-orientation': true,\n  'aria-owns': true,\n  'aria-placeholder': true,\n  'aria-posinset': true,\n  'aria-pressed': true,\n  'aria-readonly': true,\n  'aria-required': true,\n  role: true,\n  'aria-roledescription': true,\n  'aria-rowcount': true,\n  'aria-rowindex': true,\n  'aria-rowspan': true,\n  'aria-selected': true,\n  'aria-setsize': true,\n  'aria-sort': true,\n  'aria-valuemax': true,\n  'aria-valuemin': true,\n  'aria-valuenow': true,\n  'aria-valuetext': true,\n  accessibilityActiveDescendant: true,\n  accessibilityAtomic: true,\n  accessibilityAutoComplete: true,\n  accessibilityBusy: true,\n  accessibilityChecked: true,\n  accessibilityColumnCount: true,\n  accessibilityColumnIndex: true,\n  accessibilityColumnSpan: true,\n  accessibilityControls: true,\n  accessibilityCurrent: true,\n  accessibilityDescribedBy: true,\n  accessibilityDetails: true,\n  accessibilityDisabled: true,\n  accessibilityErrorMessage: true,\n  accessibilityExpanded: true,\n  accessibilityFlowTo: true,\n  accessibilityHasPopup: true,\n  accessibilityHidden: true,\n  accessibilityInvalid: true,\n  accessibilityKeyShortcuts: true,\n  accessibilityLabel: true,\n  accessibilityLabelledBy: true,\n  accessibilityLevel: true,\n  accessibilityLiveRegion: true,\n  accessibilityModal: true,\n  accessibilityMultiline: true,\n  accessibilityMultiSelectable: true,\n  accessibilityOrientation: true,\n  accessibilityOwns: true,\n  accessibilityPlaceholder: true,\n  accessibilityPosInSet: true,\n  accessibilityPressed: true,\n  accessibilityReadOnly: true,\n  accessibilityRequired: true,\n  accessibilityRole: true,\n  accessibilityRoleDescription: true,\n  accessibilityRowCount: true,\n  accessibilityRowIndex: true,\n  accessibilityRowSpan: true,\n  accessibilitySelected: true,\n  accessibilitySetSize: true,\n  accessibilitySort: true,\n  accessibilityValueMax: true,\n  accessibilityValueMin: true,\n  accessibilityValueNow: true,\n  accessibilityValueText: true\n};\nexport var clickProps = {\n  onClick: true,\n  onAuxClick: true,\n  onContextMenu: true,\n  onGotPointerCapture: true,\n  onLostPointerCapture: true,\n  onPointerCancel: true,\n  onPointerDown: true,\n  onPointerEnter: true,\n  onPointerMove: true,\n  onPointerLeave: true,\n  onPointerOut: true,\n  onPointerOver: true,\n  onPointerUp: true\n};\nexport var focusProps = {\n  onBlur: true,\n  onFocus: true\n};\nexport var keyboardProps = {\n  onKeyDown: true,\n  onKeyDownCapture: true,\n  onKeyUp: true,\n  onKeyUpCapture: true\n};\nexport var mouseProps = {\n  onMouseDown: true,\n  onMouseEnter: true,\n  onMouseLeave: true,\n  onMouseMove: true,\n  onMouseOver: true,\n  onMouseOut: true,\n  onMouseUp: true\n};\nexport var touchProps = {\n  onTouchCancel: true,\n  onTouchCancelCapture: true,\n  onTouchEnd: true,\n  onTouchEndCapture: true,\n  onTouchMove: true,\n  onTouchMoveCapture: true,\n  onTouchStart: true,\n  onTouchStartCapture: true\n};\nexport var styleProps = {\n  style: true\n};", "map": {"version": 3, "names": ["defaultProps", "children", "dataSet", "dir", "id", "ref", "suppressHydrationWarning", "tabIndex", "testID", "focusable", "nativeID", "accessibilityProps", "role", "accessibilityActiveDescendant", "accessibilityAtomic", "accessibilityAutoComplete", "accessibilityBusy", "accessibilityChecked", "accessibilityColumnCount", "accessibilityColumnIndex", "accessibilityColumnSpan", "accessibilityControls", "accessibilityCurrent", "accessibilityDescribedBy", "accessibilityDetails", "accessibilityDisabled", "accessibilityErrorMessage", "accessibilityExpanded", "accessibilityFlowTo", "accessibilityHasPopup", "accessibilityHidden", "accessibilityInvalid", "accessibilityKeyShortcuts", "accessibilityLabel", "accessibilityLabelledBy", "accessibilityLevel", "accessibilityLiveRegion", "accessibilityModal", "accessibilityMultiline", "accessibilityMultiSelectable", "accessibilityOrientation", "accessibilityOwns", "accessibilityPlaceholder", "accessibilityPosInSet", "accessibilityPressed", "accessibilityReadOnly", "accessibilityRequired", "accessibilityRole", "accessibilityRoleDescription", "accessibilityRowCount", "accessibilityRowIndex", "accessibilityRowSpan", "accessibilitySelected", "accessibilitySetSize", "accessibilitySort", "accessibilityValueMax", "accessibilityValueMin", "accessibilityValueNow", "accessibilityValueText", "clickProps", "onClick", "onAuxClick", "onContextMenu", "onGotPointerCapture", "onLostPointerCapture", "onPointerCancel", "onPointerDown", "onPointerEnter", "onPointerMove", "onPointerLeave", "onPointerOut", "onPointerOver", "onPointerUp", "focusProps", "onBlur", "onFocus", "keyboardProps", "onKeyDown", "onKeyDownCapture", "onKeyUp", "onKeyUpCapture", "mouseProps", "onMouseDown", "onMouseEnter", "onMouseLeave", "onMouseMove", "onMouseOver", "onMouseOut", "onMouseUp", "touchProps", "onTouchCancel", "onTouchCancelCapture", "onTouchEnd", "onTouchEndCapture", "onTouchMove", "onTouchMoveCapture", "onTouchStart", "onTouchStartCapture", "styleProps", "style"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/modules/forwardedProps/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nexport var defaultProps = {\n  children: true,\n  dataSet: true,\n  dir: true,\n  id: true,\n  ref: true,\n  suppressHydrationWarning: true,\n  tabIndex: true,\n  testID: true,\n  // @deprecated\n  focusable: true,\n  nativeID: true\n};\nexport var accessibilityProps = {\n  'aria-activedescendant': true,\n  'aria-atomic': true,\n  'aria-autocomplete': true,\n  'aria-busy': true,\n  'aria-checked': true,\n  'aria-colcount': true,\n  'aria-colindex': true,\n  'aria-colspan': true,\n  'aria-controls': true,\n  'aria-current': true,\n  'aria-describedby': true,\n  'aria-details': true,\n  'aria-disabled': true,\n  'aria-errormessage': true,\n  'aria-expanded': true,\n  'aria-flowto': true,\n  'aria-haspopup': true,\n  'aria-hidden': true,\n  'aria-invalid': true,\n  'aria-keyshortcuts': true,\n  'aria-label': true,\n  'aria-labelledby': true,\n  'aria-level': true,\n  'aria-live': true,\n  'aria-modal': true,\n  'aria-multiline': true,\n  'aria-multiselectable': true,\n  'aria-orientation': true,\n  'aria-owns': true,\n  'aria-placeholder': true,\n  'aria-posinset': true,\n  'aria-pressed': true,\n  'aria-readonly': true,\n  'aria-required': true,\n  role: true,\n  'aria-roledescription': true,\n  'aria-rowcount': true,\n  'aria-rowindex': true,\n  'aria-rowspan': true,\n  'aria-selected': true,\n  'aria-setsize': true,\n  'aria-sort': true,\n  'aria-valuemax': true,\n  'aria-valuemin': true,\n  'aria-valuenow': true,\n  'aria-valuetext': true,\n  // @deprecated\n  accessibilityActiveDescendant: true,\n  accessibilityAtomic: true,\n  accessibilityAutoComplete: true,\n  accessibilityBusy: true,\n  accessibilityChecked: true,\n  accessibilityColumnCount: true,\n  accessibilityColumnIndex: true,\n  accessibilityColumnSpan: true,\n  accessibilityControls: true,\n  accessibilityCurrent: true,\n  accessibilityDescribedBy: true,\n  accessibilityDetails: true,\n  accessibilityDisabled: true,\n  accessibilityErrorMessage: true,\n  accessibilityExpanded: true,\n  accessibilityFlowTo: true,\n  accessibilityHasPopup: true,\n  accessibilityHidden: true,\n  accessibilityInvalid: true,\n  accessibilityKeyShortcuts: true,\n  accessibilityLabel: true,\n  accessibilityLabelledBy: true,\n  accessibilityLevel: true,\n  accessibilityLiveRegion: true,\n  accessibilityModal: true,\n  accessibilityMultiline: true,\n  accessibilityMultiSelectable: true,\n  accessibilityOrientation: true,\n  accessibilityOwns: true,\n  accessibilityPlaceholder: true,\n  accessibilityPosInSet: true,\n  accessibilityPressed: true,\n  accessibilityReadOnly: true,\n  accessibilityRequired: true,\n  accessibilityRole: true,\n  accessibilityRoleDescription: true,\n  accessibilityRowCount: true,\n  accessibilityRowIndex: true,\n  accessibilityRowSpan: true,\n  accessibilitySelected: true,\n  accessibilitySetSize: true,\n  accessibilitySort: true,\n  accessibilityValueMax: true,\n  accessibilityValueMin: true,\n  accessibilityValueNow: true,\n  accessibilityValueText: true\n};\nexport var clickProps = {\n  onClick: true,\n  onAuxClick: true,\n  onContextMenu: true,\n  onGotPointerCapture: true,\n  onLostPointerCapture: true,\n  onPointerCancel: true,\n  onPointerDown: true,\n  onPointerEnter: true,\n  onPointerMove: true,\n  onPointerLeave: true,\n  onPointerOut: true,\n  onPointerOver: true,\n  onPointerUp: true\n};\nexport var focusProps = {\n  onBlur: true,\n  onFocus: true\n};\nexport var keyboardProps = {\n  onKeyDown: true,\n  onKeyDownCapture: true,\n  onKeyUp: true,\n  onKeyUpCapture: true\n};\nexport var mouseProps = {\n  onMouseDown: true,\n  onMouseEnter: true,\n  onMouseLeave: true,\n  onMouseMove: true,\n  onMouseOver: true,\n  onMouseOut: true,\n  onMouseUp: true\n};\nexport var touchProps = {\n  onTouchCancel: true,\n  onTouchCancelCapture: true,\n  onTouchEnd: true,\n  onTouchEndCapture: true,\n  onTouchMove: true,\n  onTouchMoveCapture: true,\n  onTouchStart: true,\n  onTouchStartCapture: true\n};\nexport var styleProps = {\n  style: true\n};"], "mappings": "AASA,OAAO,IAAIA,YAAY,GAAG;EACxBC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,GAAG,EAAE,IAAI;EACTC,EAAE,EAAE,IAAI;EACRC,GAAG,EAAE,IAAI;EACTC,wBAAwB,EAAE,IAAI;EAC9BC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EAEZC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC;AACD,OAAO,IAAIC,kBAAkB,GAAG;EAC9B,uBAAuB,EAAE,IAAI;EAC7B,aAAa,EAAE,IAAI;EACnB,mBAAmB,EAAE,IAAI;EACzB,WAAW,EAAE,IAAI;EACjB,cAAc,EAAE,IAAI;EACpB,eAAe,EAAE,IAAI;EACrB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,kBAAkB,EAAE,IAAI;EACxB,cAAc,EAAE,IAAI;EACpB,eAAe,EAAE,IAAI;EACrB,mBAAmB,EAAE,IAAI;EACzB,eAAe,EAAE,IAAI;EACrB,aAAa,EAAE,IAAI;EACnB,eAAe,EAAE,IAAI;EACrB,aAAa,EAAE,IAAI;EACnB,cAAc,EAAE,IAAI;EACpB,mBAAmB,EAAE,IAAI;EACzB,YAAY,EAAE,IAAI;EAClB,iBAAiB,EAAE,IAAI;EACvB,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,gBAAgB,EAAE,IAAI;EACtB,sBAAsB,EAAE,IAAI;EAC5B,kBAAkB,EAAE,IAAI;EACxB,WAAW,EAAE,IAAI;EACjB,kBAAkB,EAAE,IAAI;EACxB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,eAAe,EAAE,IAAI;EACrB,eAAe,EAAE,IAAI;EACrBC,IAAI,EAAE,IAAI;EACV,sBAAsB,EAAE,IAAI;EAC5B,eAAe,EAAE,IAAI;EACrB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,WAAW,EAAE,IAAI;EACjB,eAAe,EAAE,IAAI;EACrB,eAAe,EAAE,IAAI;EACrB,eAAe,EAAE,IAAI;EACrB,gBAAgB,EAAE,IAAI;EAEtBC,6BAA6B,EAAE,IAAI;EACnCC,mBAAmB,EAAE,IAAI;EACzBC,yBAAyB,EAAE,IAAI;EAC/BC,iBAAiB,EAAE,IAAI;EACvBC,oBAAoB,EAAE,IAAI;EAC1BC,wBAAwB,EAAE,IAAI;EAC9BC,wBAAwB,EAAE,IAAI;EAC9BC,uBAAuB,EAAE,IAAI;EAC7BC,qBAAqB,EAAE,IAAI;EAC3BC,oBAAoB,EAAE,IAAI;EAC1BC,wBAAwB,EAAE,IAAI;EAC9BC,oBAAoB,EAAE,IAAI;EAC1BC,qBAAqB,EAAE,IAAI;EAC3BC,yBAAyB,EAAE,IAAI;EAC/BC,qBAAqB,EAAE,IAAI;EAC3BC,mBAAmB,EAAE,IAAI;EACzBC,qBAAqB,EAAE,IAAI;EAC3BC,mBAAmB,EAAE,IAAI;EACzBC,oBAAoB,EAAE,IAAI;EAC1BC,yBAAyB,EAAE,IAAI;EAC/BC,kBAAkB,EAAE,IAAI;EACxBC,uBAAuB,EAAE,IAAI;EAC7BC,kBAAkB,EAAE,IAAI;EACxBC,uBAAuB,EAAE,IAAI;EAC7BC,kBAAkB,EAAE,IAAI;EACxBC,sBAAsB,EAAE,IAAI;EAC5BC,4BAA4B,EAAE,IAAI;EAClCC,wBAAwB,EAAE,IAAI;EAC9BC,iBAAiB,EAAE,IAAI;EACvBC,wBAAwB,EAAE,IAAI;EAC9BC,qBAAqB,EAAE,IAAI;EAC3BC,oBAAoB,EAAE,IAAI;EAC1BC,qBAAqB,EAAE,IAAI;EAC3BC,qBAAqB,EAAE,IAAI;EAC3BC,iBAAiB,EAAE,IAAI;EACvBC,4BAA4B,EAAE,IAAI;EAClCC,qBAAqB,EAAE,IAAI;EAC3BC,qBAAqB,EAAE,IAAI;EAC3BC,oBAAoB,EAAE,IAAI;EAC1BC,qBAAqB,EAAE,IAAI;EAC3BC,oBAAoB,EAAE,IAAI;EAC1BC,iBAAiB,EAAE,IAAI;EACvBC,qBAAqB,EAAE,IAAI;EAC3BC,qBAAqB,EAAE,IAAI;EAC3BC,qBAAqB,EAAE,IAAI;EAC3BC,sBAAsB,EAAE;AAC1B,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG;EACtBC,OAAO,EAAE,IAAI;EACbC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EACnBC,mBAAmB,EAAE,IAAI;EACzBC,oBAAoB,EAAE,IAAI;EAC1BC,eAAe,EAAE,IAAI;EACrBC,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE,IAAI;EACpBC,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE,IAAI;EACpBC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE;AACf,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG;EACtBC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAIC,aAAa,GAAG;EACzBC,SAAS,EAAE,IAAI;EACfC,gBAAgB,EAAE,IAAI;EACtBC,OAAO,EAAE,IAAI;EACbC,cAAc,EAAE;AAClB,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG;EACtBC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG;EACtBC,aAAa,EAAE,IAAI;EACnBC,oBAAoB,EAAE,IAAI;EAC1BC,UAAU,EAAE,IAAI;EAChBC,iBAAiB,EAAE,IAAI;EACvBC,WAAW,EAAE,IAAI;EACjBC,kBAAkB,EAAE,IAAI;EACxBC,YAAY,EAAE,IAAI;EAClBC,mBAAmB,EAAE;AACvB,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG;EACtBC,KAAK,EAAE;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}