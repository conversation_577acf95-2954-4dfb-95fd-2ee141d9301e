{"ast": null, "code": "export var loaded = {};\nexport var loadPromises = {};", "map": {"version": 3, "names": ["loaded", "loadPromises"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-font\\src\\memory.ts"], "sourcesContent": ["export const loaded: { [name: string]: boolean } = {};\nexport const loadPromises: { [name: string]: Promise<void> } = {};\n"], "mappings": "AAAA,OAAO,IAAMA,MAAM,GAAgC,EAAE;AACrD,OAAO,IAAMC,YAAY,GAAsC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}