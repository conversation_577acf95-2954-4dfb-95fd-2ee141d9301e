{"version": 3, "sources": ["RNGestureHandlerModule.web.ts"], "names": ["React", "isNewWebImplementationEnabled", "InteractionManager", "NodeManager", "PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "NativeViewGestureHandler", "ManualGestureHandler", "HoverGestureHandler", "HammerNodeManager", "HammerNativeViewGestureHandler", "HammerPanGestureHandler", "HammerTapGestureHandler", "HammerLongPressGestureHandler", "HammerPinchGestureHandler", "HammerRotationGestureHandler", "HammerFlingGestureHandler", "GestureHandlerWebDelegate", "Gestures", "HammerGestures", "handleSetJSResponder", "tag", "blockNativeResponder", "console", "warn", "handleClearJSResponder", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "Error", "GestureClass", "getInstance", "configureInteractions", "<PERSON><PERSON><PERSON><PERSON>", "updateGestureHandler", "attachGestureHandler", "newView", "_actionType", "propsRef", "HTMLElement", "Component", "init", "<PERSON><PERSON><PERSON><PERSON>", "newConfig", "updateGestureConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAGA,SAASC,6BAAT,QAA8C,8BAA9C,C,CAEA;;AACA,OAAOC,kBAAP,MAA+B,gCAA/B;AACA,OAAOC,WAAP,MAAwB,yBAAxB;AACA,OAAOC,iBAAP,MAA8B,kCAA9B;AACA,OAAOC,iBAAP,MAA8B,kCAA9B;AACA,OAAOC,uBAAP,MAAoC,wCAApC;AACA,OAAOC,mBAAP,MAAgC,oCAAhC;AACA,OAAOC,sBAAP,MAAmC,uCAAnC;AACA,OAAOC,mBAAP,MAAgC,oCAAhC;AACA,OAAOC,wBAAP,MAAqC,yCAArC;AACA,OAAOC,oBAAP,MAAiC,qCAAjC;AACA,OAAOC,mBAAP,MAAgC,oCAAhC,C,CAEA;;AACA,OAAO,KAAKC,iBAAZ,MAAmC,0BAAnC;AACA,OAAOC,8BAAP,MAA2C,uCAA3C;AACA,OAAOC,uBAAP,MAAoC,gCAApC;AACA,OAAOC,uBAAP,MAAoC,gCAApC;AACA,OAAOC,6BAAP,MAA0C,sCAA1C;AACA,OAAOC,yBAAP,MAAsC,kCAAtC;AACA,OAAOC,4BAAP,MAAyC,qCAAzC;AACA,OAAOC,yBAAP,MAAsC,kCAAtC;AAEA,SAASC,yBAAT,QAA0C,uCAA1C;AAEA,OAAO,MAAMC,QAAQ,GAAG;AACtBZ,EAAAA,wBADsB;AAEtBN,EAAAA,iBAFsB;AAGtBC,EAAAA,iBAHsB;AAItBC,EAAAA,uBAJsB;AAKtBC,EAAAA,mBALsB;AAMtBC,EAAAA,sBANsB;AAOtBC,EAAAA,mBAPsB;AAQtBE,EAAAA,oBARsB;AAStBC,EAAAA;AATsB,CAAjB;AAYP,OAAO,MAAMW,cAAc,GAAG;AAC5Bb,EAAAA,wBAAwB,EAAEI,8BADE;AAE5BV,EAAAA,iBAAiB,EAAEW,uBAFS;AAG5BV,EAAAA,iBAAiB,EAAEW,uBAHS;AAI5BV,EAAAA,uBAAuB,EAAEW,6BAJG;AAK5BV,EAAAA,mBAAmB,EAAEW,yBALO;AAM5BV,EAAAA,sBAAsB,EAAEW,4BANI;AAO5BV,EAAAA,mBAAmB,EAAEW;AAPO,CAAvB;AAUP,eAAe;AACbI,EAAAA,oBAAoB,CAACC,GAAD,EAAcC,oBAAd,EAA6C;AAC/DC,IAAAA,OAAO,CAACC,IAAR,CAAa,wBAAb,EAAuCH,GAAvC,EAA4CC,oBAA5C;AACD,GAHY;;AAIbG,EAAAA,sBAAsB,GAAG;AACvBF,IAAAA,OAAO,CAACC,IAAR,CAAa,0BAAb;AACD,GANY;;AAObE,EAAAA,oBAAoB,CAClBC,WADkB,EAElBC,UAFkB,EAGlBC,MAHkB,EAIlB;AACA,QAAIhC,6BAA6B,EAAjC,EAAqC;AACnC,UAAI,EAAE8B,WAAW,IAAIT,QAAjB,CAAJ,EAAgC;AAC9B,cAAM,IAAIY,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD;;AAED,YAAMI,YAAY,GAAGb,QAAQ,CAACS,WAAD,CAA7B;AACA5B,MAAAA,WAAW,CAAC2B,oBAAZ,CACEE,UADF,EAEE,IAAIG,YAAJ,CAAiB,IAAId,yBAAJ,EAAjB,CAFF;AAIAnB,MAAAA,kBAAkB,CAACkC,WAAnB,GAAiCC,qBAAjC,CACElC,WAAW,CAACmC,UAAZ,CAAuBN,UAAvB,CADF,EAEEC,MAFF;AAID,KAhBD,MAgBO;AACL,UAAI,EAAEF,WAAW,IAAIR,cAAjB,CAAJ,EAAsC;AACpC,cAAM,IAAIW,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD,OALI,CAOL;AACA;;;AACA,YAAMI,YAAY,GAAGZ,cAAc,CAACQ,WAAD,CAAnC,CATK,CAUL;;AACAlB,MAAAA,iBAAiB,CAACiB,oBAAlB,CAAuCE,UAAvC,EAAmD,IAAIG,YAAJ,EAAnD;AACD;;AAED,SAAKI,oBAAL,CAA0BP,UAA1B,EAAsCC,MAAtC;AACD,GA3CY;;AA4CbO,EAAAA,oBAAoB,CAClBR,UADkB,EAElB;AACAS,EAAAA,OAHkB,EAIlBC,WAJkB,EAKlBC,QALkB,EAMlB;AACA,QACE,EAAEF,OAAO,YAAYG,WAAnB,IAAkCH,OAAO,YAAYzC,KAAK,CAAC6C,SAA7D,CADF,EAEE;AACA;AACD;;AAED,QAAI5C,6BAA6B,EAAjC,EAAqC;AACnC;AACAE,MAAAA,WAAW,CAACmC,UAAZ,CAAuBN,UAAvB,EAAmCc,IAAnC,CAAwCL,OAAxC,EAAiDE,QAAjD;AACD,KAHD,MAGO;AACL;AACA9B,MAAAA,iBAAiB,CAACyB,UAAlB,CAA6BN,UAA7B,EAAyCe,OAAzC,CAAiDN,OAAjD,EAA0DE,QAA1D;AACD;AACF,GAhEY;;AAiEbJ,EAAAA,oBAAoB,CAACP,UAAD,EAAqBgB,SAArB,EAAwC;AAC1D,QAAI/C,6BAA6B,EAAjC,EAAqC;AACnCE,MAAAA,WAAW,CAACmC,UAAZ,CAAuBN,UAAvB,EAAmCiB,mBAAnC,CAAuDD,SAAvD;AAEA9C,MAAAA,kBAAkB,CAACkC,WAAnB,GAAiCC,qBAAjC,CACElC,WAAW,CAACmC,UAAZ,CAAuBN,UAAvB,CADF,EAEEgB,SAFF;AAID,KAPD,MAOO;AACLnC,MAAAA,iBAAiB,CAACyB,UAAlB,CAA6BN,UAA7B,EAAyCiB,mBAAzC,CAA6DD,SAA7D;AACD;AACF,GA5EY;;AA6EbE,EAAAA,qBAAqB,CAAClB,UAAD,EAAqB;AACxC,QAAI/B,6BAA6B,EAAjC,EAAqC;AACnC,aAAOE,WAAW,CAACmC,UAAZ,CAAuBN,UAAvB,CAAP;AACD,KAFD,MAEO;AACL,aAAOnB,iBAAiB,CAACyB,UAAlB,CAA6BN,UAA7B,CAAP;AACD;AACF,GAnFY;;AAoFbmB,EAAAA,kBAAkB,CAACnB,UAAD,EAAqB;AACrC,QAAI/B,6BAA6B,EAAjC,EAAqC;AACnCE,MAAAA,WAAW,CAACgD,kBAAZ,CAA+BnB,UAA/B;AACD,KAFD,MAEO;AACLnB,MAAAA,iBAAiB,CAACsC,kBAAlB,CAAqCnB,UAArC;AACD;AACF,GA1FY;;AA2Fb;AACAoB,EAAAA,eAAe,GAAG,CAAE;;AA5FP,CAAf", "sourcesContent": ["import React from 'react';\n\nimport { ActionType } from './ActionType';\nimport { isNewWebImplementationEnabled } from './EnableNewWebImplementation';\n\n//GestureHandlers\nimport InteractionManager from './web/tools/InteractionManager';\nimport NodeManager from './web/tools/NodeManager';\nimport PanGestureHandler from './web/handlers/PanGestureHandler';\nimport TapGestureHandler from './web/handlers/TapGestureHandler';\nimport LongPressGestureHandler from './web/handlers/LongPressGestureHandler';\nimport PinchGestureHandler from './web/handlers/PinchGestureHandler';\nimport RotationGestureHandler from './web/handlers/RotationGestureHandler';\nimport FlingGestureHandler from './web/handlers/FlingGestureHandler';\nimport NativeViewGestureHandler from './web/handlers/NativeViewGestureHandler';\nimport ManualGestureHandler from './web/handlers/ManualGestureHandler';\nimport HoverGestureHandler from './web/handlers/HoverGestureHandler';\n\n//Hammer Handlers\nimport * as HammerNodeManager from './web_hammer/NodeManager';\nimport HammerNativeViewGestureHandler from './web_hammer/NativeViewGestureHandler';\nimport HammerPanGestureHandler from './web_hammer/PanGestureHandler';\nimport HammerTapGestureHandler from './web_hammer/TapGestureHandler';\nimport HammerLongPressGestureHandler from './web_hammer/LongPressGestureHandler';\nimport HammerPinchGestureHandler from './web_hammer/PinchGestureHandler';\nimport HammerRotationGestureHandler from './web_hammer/RotationGestureHandler';\nimport HammerFlingGestureHandler from './web_hammer/FlingGestureHandler';\nimport { Config } from './web/interfaces';\nimport { GestureHandlerWebDelegate } from './web/tools/GestureHandlerWebDelegate';\n\nexport const Gestures = {\n  NativeViewGestureHandler,\n  PanGestureHandler,\n  TapGestureHandler,\n  LongPressGestureHandler,\n  PinchGestureHandler,\n  RotationGestureHandler,\n  FlingGestureHandler,\n  ManualGestureHandler,\n  HoverGestureHandler,\n};\n\nexport const HammerGestures = {\n  NativeViewGestureHandler: HammerNativeViewGestureHandler,\n  PanGestureHandler: HammerPanGestureHandler,\n  TapGestureHandler: HammerTapGestureHandler,\n  LongPressGestureHandler: HammerLongPressGestureHandler,\n  PinchGestureHandler: HammerPinchGestureHandler,\n  RotationGestureHandler: HammerRotationGestureHandler,\n  FlingGestureHandler: HammerFlingGestureHandler,\n};\n\nexport default {\n  handleSetJSResponder(tag: number, blockNativeResponder: boolean) {\n    console.warn('handleSetJSResponder: ', tag, blockNativeResponder);\n  },\n  handleClearJSResponder() {\n    console.warn('handleClearJSResponder: ');\n  },\n  createGestureHandler<T>(\n    handlerName: keyof typeof Gestures,\n    handlerTag: number,\n    config: T\n  ) {\n    if (isNewWebImplementationEnabled()) {\n      if (!(handlerName in Gestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      const GestureClass = Gestures[handlerName];\n      NodeManager.createGestureHandler(\n        handlerTag,\n        new GestureClass(new GestureHandlerWebDelegate())\n      );\n      InteractionManager.getInstance().configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        config as unknown as Config\n      );\n    } else {\n      if (!(handlerName in HammerGestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      // @ts-ignore If it doesn't exist, the error is thrown\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      const GestureClass = HammerGestures[handlerName];\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n      HammerNodeManager.createGestureHandler(handlerTag, new GestureClass());\n    }\n\n    this.updateGestureHandler(handlerTag, config as unknown as Config);\n  },\n  attachGestureHandler(\n    handlerTag: number,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    newView: any,\n    _actionType: ActionType,\n    propsRef: React.RefObject<unknown>\n  ) {\n    if (\n      !(newView instanceof HTMLElement || newView instanceof React.Component)\n    ) {\n      return;\n    }\n\n    if (isNewWebImplementationEnabled()) {\n      //@ts-ignore Types should be HTMLElement or React.Component\n      NodeManager.getHandler(handlerTag).init(newView, propsRef);\n    } else {\n      //@ts-ignore Types should be HTMLElement or React.Component\n      HammerNodeManager.getHandler(handlerTag).setView(newView, propsRef);\n    }\n  },\n  updateGestureHandler(handlerTag: number, newConfig: Config) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n\n      InteractionManager.getInstance().configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        newConfig\n      );\n    } else {\n      HammerNodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n    }\n  },\n  getGestureHandlerNode(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      return NodeManager.getHandler(handlerTag);\n    } else {\n      return HammerNodeManager.getHandler(handlerTag);\n    }\n  },\n  dropGestureHandler(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.dropGestureHandler(handlerTag);\n    } else {\n      HammerNodeManager.dropGestureHandler(handlerTag);\n    }\n  },\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  flushOperations() {},\n};\n"]}