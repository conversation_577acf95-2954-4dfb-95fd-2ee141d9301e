{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nvar _jsxFileName = \"D:\\\\aplikasi\\\\TRAE\\\\psg-bmi\\\\App.js\";\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { SafeAreaProvider } from 'react-native-safe-area-context';\nimport { StatusBar } from 'expo-status-bar';\nimport * as SplashScreen from 'expo-splash-screen';\nimport ErrorBoundary from './src/components/ErrorBoundary';\nimport HomeScreen from './src/screens/HomeScreen';\nimport BottomNavigation from './src/components/BottomNavigation';\nimport { Colors } from './src/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nSplashScreen.preventAutoHideAsync();\nexport default function App() {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    appIsReady = _React$useState2[0],\n    setAppIsReady = _React$useState2[1];\n  React.useEffect(function () {\n    function prepare() {\n      return _prepare.apply(this, arguments);\n    }\n    function _prepare() {\n      _prepare = _asyncToGenerator(function* () {\n        try {\n          yield new Promise(function (resolve) {\n            return setTimeout(resolve, 1000);\n          });\n        } catch (e) {\n          console.warn(e);\n        } finally {\n          setAppIsReady(true);\n        }\n      });\n      return _prepare.apply(this, arguments);\n    }\n    prepare();\n  }, []);\n  var onLayoutRootView = React.useCallback(_asyncToGenerator(function* () {\n    if (appIsReady) {\n      yield SplashScreen.hideAsync();\n    }\n  }), [appIsReady]);\n  if (!appIsReady) {\n    return null;\n  }\n  return _jsxDEV(ErrorBoundary, {\n    children: _jsxDEV(SafeAreaProvider, {\n      children: _jsxDEV(View, {\n        style: styles.container,\n        onLayout: onLayoutRootView,\n        children: [_jsxDEV(StatusBar, {\n          style: \"light\",\n          backgroundColor: Colors.primary\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), _jsxDEV(HomeScreen, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), _jsxDEV(BottomNavigation, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: Colors.background\n  },\n  content: {\n    flex: 1\n  }\n});", "map": {"version": 3, "names": ["React", "View", "StyleSheet", "SafeAreaProvider", "StatusBar", "SplashScreen", "Error<PERSON>ou<PERSON><PERSON>", "HomeScreen", "BottomNavigation", "Colors", "jsxDEV", "_jsxDEV", "preventAutoHideAsync", "App", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "appIsReady", "setAppIsReady", "useEffect", "prepare", "_prepare", "apply", "arguments", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "e", "console", "warn", "onLayoutRootView", "useCallback", "<PERSON><PERSON><PERSON>", "children", "style", "styles", "container", "onLayout", "backgroundColor", "primary", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "create", "flex", "background", "content"], "sources": ["D:/aplikasi/TRAE/psg-bmi/App.js"], "sourcesContent": ["import React from 'react';\nimport { View, StyleSheet } from 'react-native';\nimport { SafeAreaProvider } from 'react-native-safe-area-context';\nimport { StatusBar } from 'expo-status-bar';\nimport * as SplashScreen from 'expo-splash-screen';\n\n// Import components\nimport ErrorBoundary from './src/components/ErrorBoundary';\nimport HomeScreen from './src/screens/HomeScreen';\nimport BottomNavigation from './src/components/BottomNavigation';\nimport { Colors } from './src/constants';\n\n// Keep the splash screen visible while we fetch resources\nSplashScreen.preventAutoHideAsync();\n\nexport default function App() {\n  const [appIsReady, setAppIsReady] = React.useState(false);\n\n  React.useEffect(() => {\n    async function prepare() {\n      try {\n        // Pre-load fonts, make any API calls you need to do here\n        // For now, we'll just simulate a loading delay\n        await new Promise(resolve => setTimeout(resolve, 1000));\n      } catch (e) {\n        console.warn(e);\n      } finally {\n        // Tell the application to render\n        setAppIsReady(true);\n      }\n    }\n\n    prepare();\n  }, []);\n\n  const onLayoutRootView = React.useCallback(async () => {\n    if (appIsReady) {\n      // This tells the splash screen to hide immediately\n      await SplashScreen.hideAsync();\n    }\n  }, [appIsReady]);\n\n  if (!appIsReady) {\n    return null;\n  }\n\n  return (\n    <ErrorBoundary>\n      <SafeAreaProvider>\n        <View style={styles.container} onLayout={onLayoutRootView}>\n          <StatusBar style=\"light\" backgroundColor={Colors.primary} />\n\n          {/* Main Content - Now takes full height */}\n          <HomeScreen />\n\n          {/* Fixed Bottom Navigation */}\n          <BottomNavigation />\n        </View>\n      </SafeAreaProvider>\n    </ErrorBoundary>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: Colors.background,\n  },\n  content: {\n    flex: 1,\n  },\n});\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAE1B,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,KAAKC,YAAY,MAAM,oBAAoB;AAGlD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,SAASC,MAAM,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzCN,YAAY,CAACO,oBAAoB,CAAC,CAAC;AAEnC,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC5B,IAAAC,eAAA,GAAoCd,KAAK,CAACe,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAlDI,UAAU,GAAAF,gBAAA;IAAEG,aAAa,GAAAH,gBAAA;EAEhChB,KAAK,CAACoB,SAAS,CAAC,YAAM;IAAA,SACLC,OAAOA,CAAA;MAAA,OAAAC,QAAA,CAAAC,KAAA,OAAAC,SAAA;IAAA;IAAA,SAAAF,SAAA;MAAAA,QAAA,GAAAG,iBAAA,CAAtB,aAAyB;QACvB,IAAI;UAGF,MAAM,IAAIC,OAAO,CAAC,UAAAC,OAAO;YAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;UAAA,EAAC;QACzD,CAAC,CAAC,OAAOE,CAAC,EAAE;UACVC,OAAO,CAACC,IAAI,CAACF,CAAC,CAAC;QACjB,CAAC,SAAS;UAERV,aAAa,CAAC,IAAI,CAAC;QACrB;MACF,CAAC;MAAA,OAAAG,QAAA,CAAAC,KAAA,OAAAC,SAAA;IAAA;IAEDH,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMW,gBAAgB,GAAGhC,KAAK,CAACiC,WAAW,CAAAR,iBAAA,CAAC,aAAY;IACrD,IAAIP,UAAU,EAAE;MAEd,MAAMb,YAAY,CAAC6B,SAAS,CAAC,CAAC;IAChC;EACF,CAAC,GAAE,CAAChB,UAAU,CAAC,CAAC;EAEhB,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,OACEP,OAAA,CAACL,aAAa;IAAA6B,QAAA,EACZxB,OAAA,CAACR,gBAAgB;MAAAgC,QAAA,EACfxB,OAAA,CAACV,IAAI;QAACmC,KAAK,EAAEC,MAAM,CAACC,SAAU;QAACC,QAAQ,EAAEP,gBAAiB;QAAAG,QAAA,GACxDxB,OAAA,CAACP,SAAS;UAACgC,KAAK,EAAC,OAAO;UAACI,eAAe,EAAE/B,MAAM,CAACgC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAG5DlC,OAAA,CAACJ,UAAU;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAGdlC,OAAA,CAACH,gBAAgB;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEpB;AAEA,IAAMR,MAAM,GAAGnC,UAAU,CAAC4C,MAAM,CAAC;EAC/BR,SAAS,EAAE;IACTS,IAAI,EAAE,CAAC;IACPP,eAAe,EAAE/B,MAAM,CAACuC;EAC1B,CAAC;EACDC,OAAO,EAAE;IACPF,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}