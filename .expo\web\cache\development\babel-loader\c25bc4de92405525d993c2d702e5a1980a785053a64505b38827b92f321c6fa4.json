{"ast": null, "code": "export default {\n  get name() {\n    return 'ExpoSplashScreen';\n  },\n  preventAutoHideAsync: function preventAutoHideAsync() {\n    return false;\n  },\n  hideAsync: function hideAsync() {\n    return false;\n  }\n};", "map": {"version": 3, "names": ["name", "preventAutoHideAsync", "<PERSON><PERSON><PERSON>"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-splash-screen\\src\\ExpoSplashScreen.web.ts"], "sourcesContent": ["export default {\n  get name(): string {\n    return 'ExpoSplashScreen';\n  },\n  preventAutoHideAsync() {\n    return false;\n  },\n  hideAsync() {\n    return false;\n  },\n};\n"], "mappings": "AAAA,eAAe;EACb,IAAIA,IAAIA,CAAA;IACN,OAAO,kBAAkB;EAC3B,CAAC;EACDC,oBAAoB,WAApBA,oBAAoBA,CAAA;IAClB,OAAO,KAAK;EACd,CAAC;EACDC,SAAS,WAATA,SAASA,CAAA;IACP,OAAO,KAAK;EACd;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}