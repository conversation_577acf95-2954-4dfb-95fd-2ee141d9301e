{"version": 3, "sources": ["TouchEventManager.ts"], "names": ["EventTypes", "MouseButtons", "PointerType", "TouchEventType", "EventManager", "isPointerInBounds", "TouchEventManager", "setListeners", "view", "addEventListener", "event", "i", "changedTouches", "length", "adaptedEvent", "mapEvent", "DOWN", "x", "y", "touchType", "markAsInBounds", "pointerId", "activePointersCounter", "eventType", "ADDITIONAL_POINTER_DOWN", "onPointerAdd", "onPointerDown", "MOVE", "inBounds", "pointerIndex", "pointersInBounds", "indexOf", "ENTER", "onPointerEnter", "onPointerMove", "LEAVE", "onPointerLeave", "markAsOutOfBounds", "onPointerOutOfBounds", "UP", "ADDITIONAL_POINTER_UP", "onPointerRemove", "onPointerUp", "CANCEL", "CANCELLED", "onPointerCancel", "index", "touchEventType", "rect", "getBoundingClientRect", "clientX", "clientY", "offsetX", "left", "offsetY", "top", "identifier", "pointerType", "TOUCH", "buttons", "NONE", "time", "timeStamp", "allTouches", "touches"], "mappings": "AAAA,SAEEA,UAFF,EAGEC,YAHF,EAIEC,WAJF,EAKEC,cALF,QAMO,eANP;AAOA,OAAOC,YAAP,MAAyB,gBAAzB;AACA,SAASC,iBAAT,QAAkC,UAAlC;AAEA,eAAe,MAAMC,iBAAN,SAAgCF,YAAhC,CAA0D;AAChEG,EAAAA,YAAY,GAAS;AAC1B,SAAKC,IAAL,CAAUC,gBAAV,CAA2B,YAA3B,EAA0CC,KAAD,IAAuB;AAC9D,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,cAAN,CAAqBC,MAAzC,EAAiD,EAAEF,CAAnD,EAAsD;AACpD,cAAMG,YAA0B,GAAG,KAAKC,QAAL,CACjCL,KADiC,EAEjCV,UAAU,CAACgB,IAFsB,EAGjCL,CAHiC,EAIjCR,cAAc,CAACa,IAJkB,CAAnC,CADoD,CAQpD;AACA;;AACA,YACE,CAACX,iBAAiB,CAAC,KAAKG,IAAN,EAAY;AAC5BS,UAAAA,CAAC,EAAEH,YAAY,CAACG,CADY;AAE5BC,UAAAA,CAAC,EAAEJ,YAAY,CAACI;AAFY,SAAZ,CAAlB,IAIA;AACAR,QAAAA,KAAK,CAACE,cAAN,CAAqBD,CAArB,EAAwBQ,SAAxB,KAAsC,QANxC,EAOE;AACA;AACD;;AAED,aAAKC,cAAL,CAAoBN,YAAY,CAACO,SAAjC;;AAEA,YAAI,EAAE,KAAKC,qBAAP,GAA+B,CAAnC,EAAsC;AACpCR,UAAAA,YAAY,CAACS,SAAb,GAAyBvB,UAAU,CAACwB,uBAApC;AACA,eAAKC,YAAL,CAAkBX,YAAlB;AACD,SAHD,MAGO;AACL,eAAKY,aAAL,CAAmBZ,YAAnB;AACD;AACF;AACF,KA/BD;AAiCA,SAAKN,IAAL,CAAUC,gBAAV,CAA2B,WAA3B,EAAyCC,KAAD,IAAuB;AAC7D,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,cAAN,CAAqBC,MAAzC,EAAiD,EAAEF,CAAnD,EAAsD;AACpD,cAAMG,YAA0B,GAAG,KAAKC,QAAL,CACjCL,KADiC,EAEjCV,UAAU,CAAC2B,IAFsB,EAGjChB,CAHiC,EAIjCR,cAAc,CAACwB,IAJkB,CAAnC,CADoD,CAOpD;;AACA,YAAIjB,KAAK,CAACE,cAAN,CAAqBD,CAArB,EAAwBQ,SAAxB,KAAsC,QAA1C,EAAoD;AAClD;AACD;;AAED,cAAMS,QAAiB,GAAGvB,iBAAiB,CAAC,KAAKG,IAAN,EAAY;AACrDS,UAAAA,CAAC,EAAEH,YAAY,CAACG,CADqC;AAErDC,UAAAA,CAAC,EAAEJ,YAAY,CAACI;AAFqC,SAAZ,CAA3C;AAKA,cAAMW,YAAoB,GAAG,KAAKC,gBAAL,CAAsBC,OAAtB,CAC3BjB,YAAY,CAACO,SADc,CAA7B;;AAIA,YAAIO,QAAJ,EAAc;AACZ,cAAIC,YAAY,GAAG,CAAnB,EAAsB;AACpBf,YAAAA,YAAY,CAACS,SAAb,GAAyBvB,UAAU,CAACgC,KAApC;AACA,iBAAKC,cAAL,CAAoBnB,YAApB;AACA,iBAAKM,cAAL,CAAoBN,YAAY,CAACO,SAAjC;AACD,WAJD,MAIO;AACL,iBAAKa,aAAL,CAAmBpB,YAAnB;AACD;AACF,SARD,MAQO;AACL,cAAIe,YAAY,IAAI,CAApB,EAAuB;AACrBf,YAAAA,YAAY,CAACS,SAAb,GAAyBvB,UAAU,CAACmC,KAApC;AACA,iBAAKC,cAAL,CAAoBtB,YAApB;AACA,iBAAKuB,iBAAL,CAAuBvB,YAAY,CAACO,SAApC;AACD,WAJD,MAIO;AACL,iBAAKiB,oBAAL,CAA0BxB,YAA1B;AACD;AACF;AACF;AACF,KAxCD;AA0CA,SAAKN,IAAL,CAAUC,gBAAV,CAA2B,UAA3B,EAAwCC,KAAD,IAAuB;AAC5D,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,cAAN,CAAqBC,MAAzC,EAAiD,EAAEF,CAAnD,EAAsD;AACpD;AACA;AACA;AACA;AACA,YAAI,KAAKW,qBAAL,KAA+B,CAAnC,EAAsC;AACpC;AACD,SAPmD,CASpD;;;AACA,YAAIZ,KAAK,CAACE,cAAN,CAAqBD,CAArB,EAAwBQ,SAAxB,KAAsC,QAA1C,EAAoD;AAClD;AACD;;AAED,cAAML,YAA0B,GAAG,KAAKC,QAAL,CACjCL,KADiC,EAEjCV,UAAU,CAACuC,EAFsB,EAGjC5B,CAHiC,EAIjCR,cAAc,CAACoC,EAJkB,CAAnC;AAOA,aAAKF,iBAAL,CAAuBvB,YAAY,CAACO,SAApC;;AAEA,YAAI,EAAE,KAAKC,qBAAP,GAA+B,CAAnC,EAAsC;AACpCR,UAAAA,YAAY,CAACS,SAAb,GAAyBvB,UAAU,CAACwC,qBAApC;AACA,eAAKC,eAAL,CAAqB3B,YAArB;AACD,SAHD,MAGO;AACL,eAAK4B,WAAL,CAAiB5B,YAAjB;AACD;AACF;AACF,KA/BD;AAiCA,SAAKN,IAAL,CAAUC,gBAAV,CAA2B,aAA3B,EAA2CC,KAAD,IAAuB;AAC/D,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,cAAN,CAAqBC,MAAzC,EAAiD,EAAEF,CAAnD,EAAsD;AACpD,cAAMG,YAA0B,GAAG,KAAKC,QAAL,CACjCL,KADiC,EAEjCV,UAAU,CAAC2C,MAFsB,EAGjChC,CAHiC,EAIjCR,cAAc,CAACyC,SAJkB,CAAnC,CADoD,CAQpD;;AACA,YAAIlC,KAAK,CAACE,cAAN,CAAqBD,CAArB,EAAwBQ,SAAxB,KAAsC,QAA1C,EAAoD;AAClD;AACD;;AAED,aAAK0B,eAAL,CAAqB/B,YAArB;AACA,aAAKuB,iBAAL,CAAuBvB,YAAY,CAACO,SAApC;AACA,aAAKC,qBAAL,GAA6B,CAA7B;AACD;AACF,KAlBD;AAmBD;;AAESP,EAAAA,QAAQ,CAChBL,KADgB,EAEhBa,SAFgB,EAGhBuB,KAHgB,EAIhBC,cAJgB,EAKF;AACd,UAAMC,IAAI,GAAG,KAAKxC,IAAL,CAAUyC,qBAAV,EAAb;AACA,UAAMC,OAAO,GAAGxC,KAAK,CAACE,cAAN,CAAqBkC,KAArB,EAA4BI,OAA5C;AACA,UAAMC,OAAO,GAAGzC,KAAK,CAACE,cAAN,CAAqBkC,KAArB,EAA4BK,OAA5C;AAEA,WAAO;AACLlC,MAAAA,CAAC,EAAEiC,OADE;AAELhC,MAAAA,CAAC,EAAEiC,OAFE;AAGLC,MAAAA,OAAO,EAAEF,OAAO,GAAGF,IAAI,CAACK,IAHnB;AAILC,MAAAA,OAAO,EAAEH,OAAO,GAAGH,IAAI,CAACO,GAJnB;AAKLlC,MAAAA,SAAS,EAAEX,KAAK,CAACE,cAAN,CAAqBkC,KAArB,EAA4BU,UALlC;AAMLjC,MAAAA,SAAS,EAAEA,SANN;AAOLkC,MAAAA,WAAW,EAAEvD,WAAW,CAACwD,KAPpB;AAQLC,MAAAA,OAAO,EAAE1D,YAAY,CAAC2D,IARjB;AASLC,MAAAA,IAAI,EAAEnD,KAAK,CAACoD,SATP;AAULC,MAAAA,UAAU,EAAErD,KAAK,CAACsD,OAVb;AAWLpD,MAAAA,cAAc,EAAEF,KAAK,CAACE,cAXjB;AAYLmC,MAAAA,cAAc,EAAEA;AAZX,KAAP;AAcD;;AA3JsE", "sourcesContent": ["import {\n  AdaptedEvent,\n  EventTypes,\n  MouseButtons,\n  PointerType,\n  TouchEventType,\n} from '../interfaces';\nimport EventManager from './EventManager';\nimport { isPointerInBounds } from '../utils';\n\nexport default class TouchEventManager extends EventManager<HTMLElement> {\n  public setListeners(): void {\n    this.view.addEventListener('touchstart', (event: TouchEvent) => {\n      for (let i = 0; i < event.changedTouches.length; ++i) {\n        const adaptedEvent: AdaptedEvent = this.mapEvent(\n          event,\n          EventTypes.DOWN,\n          i,\n          TouchEventType.DOWN\n        );\n\n        // Here we skip stylus, because in case of anything different than touch we want to handle it by using PointerEvents\n        // If we leave stylus to send touch events, handlers will receive every action twice\n        if (\n          !isPointerInBounds(this.view, {\n            x: adaptedEvent.x,\n            y: adaptedEvent.y,\n          }) ||\n          //@ts-ignore touchType field does exist\n          event.changedTouches[i].touchType === 'stylus'\n        ) {\n          continue;\n        }\n\n        this.markAsInBounds(adaptedEvent.pointerId);\n\n        if (++this.activePointersCounter > 1) {\n          adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_DOWN;\n          this.onPointerAdd(adaptedEvent);\n        } else {\n          this.onPointerDown(adaptedEvent);\n        }\n      }\n    });\n\n    this.view.addEventListener('touchmove', (event: TouchEvent) => {\n      for (let i = 0; i < event.changedTouches.length; ++i) {\n        const adaptedEvent: AdaptedEvent = this.mapEvent(\n          event,\n          EventTypes.MOVE,\n          i,\n          TouchEventType.MOVE\n        );\n        //@ts-ignore touchType field does exist\n        if (event.changedTouches[i].touchType === 'stylus') {\n          continue;\n        }\n\n        const inBounds: boolean = isPointerInBounds(this.view, {\n          x: adaptedEvent.x,\n          y: adaptedEvent.y,\n        });\n\n        const pointerIndex: number = this.pointersInBounds.indexOf(\n          adaptedEvent.pointerId\n        );\n\n        if (inBounds) {\n          if (pointerIndex < 0) {\n            adaptedEvent.eventType = EventTypes.ENTER;\n            this.onPointerEnter(adaptedEvent);\n            this.markAsInBounds(adaptedEvent.pointerId);\n          } else {\n            this.onPointerMove(adaptedEvent);\n          }\n        } else {\n          if (pointerIndex >= 0) {\n            adaptedEvent.eventType = EventTypes.LEAVE;\n            this.onPointerLeave(adaptedEvent);\n            this.markAsOutOfBounds(adaptedEvent.pointerId);\n          } else {\n            this.onPointerOutOfBounds(adaptedEvent);\n          }\n        }\n      }\n    });\n\n    this.view.addEventListener('touchend', (event: TouchEvent) => {\n      for (let i = 0; i < event.changedTouches.length; ++i) {\n        // When we call reset on gesture handlers, it also resets their event managers\n        // In some handlers (like RotationGestureHandler) reset is called before all pointers leave view\n        // This means, that activePointersCounter will be set to 0, while there are still remaining pointers on view\n        // Removing them will end in activePointersCounter going below 0, therefore handlers won't behave properly\n        if (this.activePointersCounter === 0) {\n          break;\n        }\n\n        //@ts-ignore touchType field does exist\n        if (event.changedTouches[i].touchType === 'stylus') {\n          continue;\n        }\n\n        const adaptedEvent: AdaptedEvent = this.mapEvent(\n          event,\n          EventTypes.UP,\n          i,\n          TouchEventType.UP\n        );\n\n        this.markAsOutOfBounds(adaptedEvent.pointerId);\n\n        if (--this.activePointersCounter > 0) {\n          adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_UP;\n          this.onPointerRemove(adaptedEvent);\n        } else {\n          this.onPointerUp(adaptedEvent);\n        }\n      }\n    });\n\n    this.view.addEventListener('touchcancel', (event: TouchEvent) => {\n      for (let i = 0; i < event.changedTouches.length; ++i) {\n        const adaptedEvent: AdaptedEvent = this.mapEvent(\n          event,\n          EventTypes.CANCEL,\n          i,\n          TouchEventType.CANCELLED\n        );\n\n        //@ts-ignore touchType field does exist\n        if (event.changedTouches[i].touchType === 'stylus') {\n          continue;\n        }\n\n        this.onPointerCancel(adaptedEvent);\n        this.markAsOutOfBounds(adaptedEvent.pointerId);\n        this.activePointersCounter = 0;\n      }\n    });\n  }\n\n  protected mapEvent(\n    event: TouchEvent,\n    eventType: EventTypes,\n    index: number,\n    touchEventType: TouchEventType\n  ): AdaptedEvent {\n    const rect = this.view.getBoundingClientRect();\n    const clientX = event.changedTouches[index].clientX;\n    const clientY = event.changedTouches[index].clientY;\n\n    return {\n      x: clientX,\n      y: clientY,\n      offsetX: clientX - rect.left,\n      offsetY: clientY - rect.top,\n      pointerId: event.changedTouches[index].identifier,\n      eventType: eventType,\n      pointerType: PointerType.TOUCH,\n      buttons: MouseButtons.NONE,\n      time: event.timeStamp,\n      allTouches: event.touches,\n      changedTouches: event.changedTouches,\n      touchEventType: touchEventType,\n    };\n  }\n}\n"]}