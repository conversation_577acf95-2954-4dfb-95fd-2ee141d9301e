{"ast": null, "code": "import * as React from 'react';\nimport Text from \"../../../../exports/Text\";\nimport createAnimatedComponent from \"../createAnimatedComponent\";\nexport default createAnimatedComponent(Text);", "map": {"version": 3, "names": ["React", "Text", "createAnimatedComponent"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/vendor/react-native/Animated/components/AnimatedText.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport * as React from 'react';\nimport Text from '../../../../exports/Text';\nimport createAnimatedComponent from '../createAnimatedComponent';\nexport default createAnimatedComponent(Text);"], "mappings": "AAUA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI;AACX,OAAOC,uBAAuB;AAC9B,eAAeA,uBAAuB,CAACD,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}