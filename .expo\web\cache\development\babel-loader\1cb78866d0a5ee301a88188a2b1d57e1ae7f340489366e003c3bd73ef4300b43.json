{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\react-native-safe-area-context\\src\\SafeArea.types.ts"], "sourcesContent": ["import type * as React from 'react';\nimport type { NativeSyntheticEvent, ViewProps } from 'react-native';\nimport NativeSafeAreaView from './specs/NativeSafeAreaView';\n\nexport type Edge = 'top' | 'right' | 'bottom' | 'left';\nexport type EdgeMode = 'off' | 'additive' | 'maximum';\n\nexport type EdgeRecord = Partial<Record<Edge, EdgeMode>>;\nexport type Edges = readonly Edge[] | Readonly<EdgeRecord>;\n\nexport interface EdgeInsets {\n  top: number;\n  right: number;\n  bottom: number;\n  left: number;\n}\n\nexport interface Rect {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n}\n\nexport interface Metrics {\n  insets: EdgeInsets;\n  frame: Rect;\n}\n\nexport type InsetChangedEvent = NativeSyntheticEvent<Metrics>;\n\nexport type InsetChangeNativeCallback = (event: InsetChangedEvent) => void;\n\nexport interface NativeSafeAreaProviderProps extends ViewProps {\n  children?: React.ReactNode;\n  onInsetsChange: InsetChangeNativeCallback;\n}\n\nexport interface NativeSafeAreaViewProps extends ViewProps {\n  children?: React.ReactNode;\n  mode?: 'padding' | 'margin';\n  edges?: Edges;\n}\n\nexport type NativeSafeAreaViewInstance = InstanceType<\n  typeof NativeSafeAreaView\n>;\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}