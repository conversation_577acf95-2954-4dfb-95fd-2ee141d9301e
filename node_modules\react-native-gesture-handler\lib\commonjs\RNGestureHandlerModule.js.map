{"version": 3, "sources": ["RNGestureHandlerModule.ts"], "names": ["RNGestureHandlerModule", "NativeModules", "console", "error", "split", "map", "line", "trim", "join", "flushOperations", "undefined"], "mappings": ";;;;;;;AAAA;;AAEA;;AACA,MAAM;AAAEA,EAAAA;AAAF,IAA6BC,0BAAnC;;AAEA,IAAID,sBAAsB,IAAI,IAA9B,EAAoC;AAClCE,EAAAA,OAAO,CAACC,KAAR,CACE,uBACG;AACP;AACA,gIAFM,CAGGC,KAHH,CAGS,IAHT,EAIGC,GAJH,CAIQC,IAAD,IAAUA,IAAI,CAACC,IAAL,EAJjB,EAKGC,IALH,CAKQ,IALR,CADF,CADF;AAUD;;AAED,IACER,sBAAsB,IACtBA,sBAAsB,CAACS,eAAvB,KAA2CC,SAF7C,EAGE;AACAV,EAAAA,sBAAsB,CAACS,eAAvB,GAAyC,MAAM,CAC7C;AACD,GAFD;AAGD;;eAwBcT,sB", "sourcesContent": ["import { NativeModules } from 'react-native';\nimport { ActionType } from './ActionType';\nimport { tagMessage } from './utils';\nconst { RNGestureHandlerModule } = NativeModules;\n\nif (RNGestureHandlerModule == null) {\n  console.error(\n    tagMessage(\n      `react-native-gesture-handler module was not found. Make sure you're running your app on the native platform and your code is linked properly (cd ios && pod install && cd ..).\n\n      For installation instructions, please refer to https://docs.swmansion.com/react-native-gesture-handler/docs/#installation`\n        .split('\\n')\n        .map((line) => line.trim())\n        .join('\\n')\n    )\n  );\n}\n\nif (\n  RNGestureHandlerModule &&\n  RNGestureHandlerModule.flushOperations === undefined\n) {\n  RNGestureHandlerModule.flushOperations = () => {\n    // NO-OP if not defined\n  };\n}\n\nexport type RNGestureHandlerModuleProps = {\n  handleSetJSResponder: (tag: number, blockNativeResponder: boolean) => void;\n  handleClearJSResponder: () => void;\n  createGestureHandler: (\n    handlerName: string,\n    handlerTag: number,\n    config: Readonly<Record<string, unknown>>\n  ) => void;\n  attachGestureHandler: (\n    handlerTag: number,\n    newView: number,\n    actionType: ActionType\n  ) => void;\n  updateGestureHandler: (\n    handlerTag: number,\n    newConfig: Readonly<Record<string, unknown>>\n  ) => void;\n  dropGestureHandler: (handlerTag: number) => void;\n  install: () => void;\n  flushOperations: () => void;\n};\n\nexport default RNGestureHandlerModule as RNGestureHandlerModuleProps;\n"]}