{"ast": null, "code": "export var Typography = {\n  h1: {\n    fontSize: 24,\n    fontWeight: '700',\n    lineHeight: 32,\n    letterSpacing: 0\n  },\n  h2: {\n    fontSize: 20,\n    fontWeight: '600',\n    lineHeight: 28,\n    letterSpacing: 0\n  },\n  h3: {\n    fontSize: 18,\n    fontWeight: '600',\n    lineHeight: 24,\n    letterSpacing: 0\n  },\n  body1: {\n    fontSize: 16,\n    fontWeight: '400',\n    lineHeight: 24,\n    letterSpacing: 0.15\n  },\n  body2: {\n    fontSize: 14,\n    fontWeight: '400',\n    lineHeight: 20,\n    letterSpacing: 0.25\n  },\n  caption: {\n    fontSize: 12,\n    fontWeight: '400',\n    lineHeight: 16,\n    letterSpacing: 0.4\n  },\n  label: {\n    fontSize: 12,\n    fontWeight: '500',\n    lineHeight: 16,\n    letterSpacing: 0.5\n  },\n  button: {\n    fontSize: 14,\n    fontWeight: '500',\n    lineHeight: 20,\n    letterSpacing: 0.1,\n    textTransform: 'uppercase'\n  },\n  overline: {\n    fontSize: 10,\n    fontWeight: '500',\n    lineHeight: 16,\n    letterSpacing: 1.5,\n    textTransform: 'uppercase'\n  },\n  subtitle1: {\n    fontSize: 16,\n    fontWeight: '500',\n    lineHeight: 24,\n    letterSpacing: 0.15\n  },\n  subtitle2: {\n    fontSize: 14,\n    fontWeight: '500',\n    lineHeight: 20,\n    letterSpacing: 0.1\n  }\n};", "map": {"version": 3, "names": ["Typography", "h1", "fontSize", "fontWeight", "lineHeight", "letterSpacing", "h2", "h3", "body1", "body2", "caption", "label", "button", "textTransform", "overline", "subtitle1", "subtitle2"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/constants/Typography.js"], "sourcesContent": ["// Typography System based on Material Design 3\nexport const Typography = {\n  // Heading Styles\n  h1: {\n    fontSize: 24,\n    fontWeight: '700',\n    lineHeight: 32,\n    letterSpacing: 0,\n  },\n  h2: {\n    fontSize: 20,\n    fontWeight: '600',\n    lineHeight: 28,\n    letterSpacing: 0,\n  },\n  h3: {\n    fontSize: 18,\n    fontWeight: '600',\n    lineHeight: 24,\n    letterSpacing: 0,\n  },\n  \n  // Body Text Styles\n  body1: {\n    fontSize: 16,\n    fontWeight: '400',\n    lineHeight: 24,\n    letterSpacing: 0.15,\n  },\n  body2: {\n    fontSize: 14,\n    fontWeight: '400',\n    lineHeight: 20,\n    letterSpacing: 0.25,\n  },\n  \n  // Caption and Label Styles\n  caption: {\n    fontSize: 12,\n    fontWeight: '400',\n    lineHeight: 16,\n    letterSpacing: 0.4,\n  },\n  label: {\n    fontSize: 12,\n    fontWeight: '500',\n    lineHeight: 16,\n    letterSpacing: 0.5,\n  },\n  \n  // Button Text Styles\n  button: {\n    fontSize: 14,\n    fontWeight: '500',\n    lineHeight: 20,\n    letterSpacing: 0.1,\n    textTransform: 'uppercase',\n  },\n  \n  // Overline Style\n  overline: {\n    fontSize: 10,\n    fontWeight: '500',\n    lineHeight: 16,\n    letterSpacing: 1.5,\n    textTransform: 'uppercase',\n  },\n  \n  // Subtitle Styles\n  subtitle1: {\n    fontSize: 16,\n    fontWeight: '500',\n    lineHeight: 24,\n    letterSpacing: 0.15,\n  },\n  subtitle2: {\n    fontSize: 14,\n    fontWeight: '500',\n    lineHeight: 20,\n    letterSpacing: 0.1,\n  },\n};\n"], "mappings": "AACA,OAAO,IAAMA,UAAU,GAAG;EAExBC,EAAE,EAAE;IACFC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EACDC,EAAE,EAAE;IACFJ,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EACDE,EAAE,EAAE;IACFL,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EAGDG,KAAK,EAAE;IACLN,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EACDI,KAAK,EAAE;IACLP,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EAGDK,OAAO,EAAE;IACPR,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EACDM,KAAK,EAAE;IACLT,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EAGDO,MAAM,EAAE;IACNV,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,GAAG;IAClBQ,aAAa,EAAE;EACjB,CAAC;EAGDC,QAAQ,EAAE;IACRZ,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,GAAG;IAClBQ,aAAa,EAAE;EACjB,CAAC;EAGDE,SAAS,EAAE;IACTb,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EACDW,SAAS,EAAE;IACTd,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}