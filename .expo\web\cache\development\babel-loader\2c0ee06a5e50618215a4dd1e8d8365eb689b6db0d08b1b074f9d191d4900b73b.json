{"ast": null, "code": "export var Uuidv5Namespace;\n(function (Uuidv5Namespace) {\n  Uuidv5Namespace[\"dns\"] = \"6ba7b810-9dad-11d1-80b4-00c04fd430c8\";\n  Uuidv5Namespace[\"url\"] = \"6ba7b811-9dad-11d1-80b4-00c04fd430c8\";\n  Uuidv5Namespace[\"oid\"] = \"6ba7b812-9dad-11d1-80b4-00c04fd430c8\";\n  Uuidv5Namespace[\"x500\"] = \"6ba7b814-9dad-11d1-80b4-00c04fd430c8\";\n})(Uuidv5Namespace || (Uuidv5Namespace = {}));", "map": {"version": 3, "names": ["Uuidv5Namespace"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-modules-core\\src\\uuid\\uuid.types.ts"], "sourcesContent": ["/**\n * Collection of utilities used for generating Universally Unique Identifiers.\n */\nexport type UUID = {\n  /**\n   * A UUID generated randomly.\n   */\n  v4: () => string;\n  /**\n   * A UUID generated based on the `value` and `namespace` parameters, which always produces the same result for the same inputs.\n   */\n  v5: (name: string, namespace: string | number[]) => string;\n  namespace: typeof Uuidv5Namespace;\n};\n\n/**\n * Default namespaces for UUID v5 defined in RFC 4122\n */\nexport enum Uuidv5Namespace {\n  // Source of the UUIDs: https://datatracker.ietf.org/doc/html/rfc4122\n  dns = '6ba7b810-9dad-11d1-80b4-00c04fd430c8',\n  url = '6ba7b811-9dad-11d1-80b4-00c04fd430c8',\n  oid = '6ba7b812-9dad-11d1-80b4-00c04fd430c8',\n  x500 = '6ba7b814-9dad-11d1-80b4-00c04fd430c8',\n}\n"], "mappings": "AAkBA,WAAYA,eAMX;AAND,WAAYA,eAAe;EAEzBA,eAAA,gDAA4C;EAC5CA,eAAA,gDAA4C;EAC5CA,eAAA,gDAA4C;EAC5CA,eAAA,iDAA6C;AAC/C,CAAC,EANWA,eAAe,KAAfA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}