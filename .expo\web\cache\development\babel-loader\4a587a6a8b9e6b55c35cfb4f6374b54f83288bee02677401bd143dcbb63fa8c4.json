{"ast": null, "code": "'use strict';\n\nmodule.exports = require('@react-native/assets-registry/registry');", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native/Libraries/Image/AssetRegistry.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\n'use strict';\n\nmodule.exports = require('@react-native/assets-registry/registry');\n"], "mappings": "AAUA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,wCAAwC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}