# vm-browserify Change Log
All notable changes to this project will be documented in this file.
This project adheres to [Semantic Versioning](http://semver.org/).

## 1.1.2 - 2019-11-04
* Update another jQuery reference in the readme. (https://github.com/browserify/vm-browserify/pull/27)
* Get rid of jQuery references altogether from samples and readme. (https://github.com/browserify/vm-browserify/commit/d509e8e5afb7b1ead191cbbd49c37a3fb934b2dc)

## 1.1.1 - 2019-11-04
* Update a reference to jQuery in an example file that was setting off some security software. (https://github.com/browserify/vm-browserify/pull/22)

## 1.1.0 - 2018-06-15
* Add `vm.isContext(sandbox)`. (https://github.com/browserify/vm-browserify/commit/038c3cb33edcad9eec33aa8a8beae31b15c1a006)

## 1.0.1 - 2018-04-13
* Remove the `component-indexof` dependency. (https://github.com/browserify/vm-browserify/commit/0d9bd4c99f80db12c5c45e260a23ebfc51ec850d)

## 1.0.0 - 2018-03-23

(This is not a breaking change.)

* Make the `sandbox` argument to `runInNewContext` optional, like in Node. (https://github.com/browserify/vm-browserify/pull/13)
* Substituting `component-indexof` for deprecated `indexof`. (https://github.com/browserify/vm-browserify/pull/14)
