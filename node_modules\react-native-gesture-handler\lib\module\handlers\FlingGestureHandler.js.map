{"version": 3, "sources": ["FlingGestureHandler.ts"], "names": ["createHandler", "baseGestureHandlerProps", "flingGestureHandlerProps", "flingHandlerName", "FlingGestureHandler", "name", "allowedProps", "config"], "mappings": "AAAA,OAAOA,aAAP,MAA0B,iBAA1B;AACA,SAEEC,uBAFF,QAGO,wBAHP;AAKA,OAAO,MAAMC,wBAAwB,GAAG,CACtC,kBADsC,EAEtC,WAFsC,CAAjC;AAsCP,OAAO,MAAMC,gBAAgB,GAAG,qBAAzB;AAGP;AACA,OAAO,MAAMC,mBAAmB,GAAGJ,aAAa,CAG9C;AACAK,EAAAA,IAAI,EAAEF,gBADN;AAEAG,EAAAA,YAAY,EAAE,CACZ,GAAGL,uBADS,EAEZ,GAAGC,wBAFS,CAFd;AAMAK,EAAAA,MAAM,EAAE;AANR,CAH8C,CAAzC", "sourcesContent": ["import createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\nexport const flingGestureHandlerProps = [\n  'numberOfPointers',\n  'direction',\n] as const;\n\nexport type FlingGestureHandlerEventPayload = {\n  x: number;\n  y: number;\n  absoluteX: number;\n  absoluteY: number;\n};\nexport interface FlingGestureConfig {\n  /**\n   * Expressed allowed direction of movement. It's possible to pass one or many\n   * directions in one parameter:\n   *\n   * ```js\n   * direction={Directions.RIGHT | Directions.LEFT}\n   * ```\n   *\n   * or\n   *\n   * ```js\n   * direction={Directions.DOWN}\n   * ```\n   */\n  direction?: number;\n\n  /**\n   * Determine exact number of points required to handle the fling gesture.\n   */\n  numberOfPointers?: number;\n}\n\nexport interface FlingGestureHandlerProps\n  extends BaseGestureHandlerProps<FlingGestureHandlerEventPayload>,\n    FlingGestureConfig {}\n\nexport const flingHandlerName = 'FlingGestureHandler';\n\nexport type FlingGestureHandler = typeof FlingGestureHandler;\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const FlingGestureHandler = createHandler<\n  FlingGestureHandlerProps,\n  FlingGestureHandlerEventPayload\n>({\n  name: flingHandlerName,\n  allowedProps: [\n    ...baseGestureHandlerProps,\n    ...flingGestureHandlerProps,\n  ] as const,\n  config: {},\n});\n"]}