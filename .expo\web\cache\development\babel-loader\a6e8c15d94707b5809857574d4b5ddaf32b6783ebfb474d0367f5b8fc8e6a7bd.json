{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/Fontisto.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/Fontisto.json\";\nvar iconSet = createIconSet(glyphMap, \"Fontisto\", font);\nexport default iconSet;", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap", "iconSet"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\@expo\\vector-icons\\src\\Fontisto.ts"], "sourcesContent": ["/**\n * Feather icon set component.\n * Usage: <Feather name=\"icon-name\" size={20} color=\"#4F8EF7\" />\n */\n\nimport createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/Fontisto.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/Fontisto.json\";\n\nconst iconSet = createIconSet(glyphMap, \"Fontisto\", font);\n\nexport default iconSet;\n"], "mappings": "AAKA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,IAAMC,OAAO,GAAGH,aAAa,CAACE,QAAQ,EAAE,UAAU,EAAED,IAAI,CAAC;AAEzD,eAAeE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}