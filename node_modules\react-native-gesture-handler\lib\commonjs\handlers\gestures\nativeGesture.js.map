{"version": 3, "sources": ["nativeGesture.ts"], "names": ["NativeGesture", "BaseGesture", "constructor", "handler<PERSON>ame", "shouldActivateOnStart", "value", "config", "disallowInterruption"], "mappings": ";;;;;;;AAAA;;;;AAMO,MAAMA,aAAN,SAA4BC,oBAA5B,CAAyE;AAG9EC,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAF+C,EAE/C;;AAGZ,SAAKC,WAAL,GAAmB,0BAAnB;AACD;;AAEDC,EAAAA,qBAAqB,CAACC,KAAD,EAAiB;AACpC,SAAKC,MAAL,CAAYF,qBAAZ,GAAoCC,KAApC;AACA,WAAO,IAAP;AACD;;AAEDE,EAAAA,oBAAoB,CAACF,KAAD,EAAiB;AACnC,SAAKC,MAAL,CAAYC,oBAAZ,GAAmCF,KAAnC;AACA,WAAO,IAAP;AACD;;AAjB6E", "sourcesContent": ["import { BaseGestureConfig, BaseGesture } from './gesture';\nimport {\n  NativeViewGestureConfig,\n  NativeViewGestureHandlerPayload,\n} from '../NativeViewGestureHandler';\n\nexport class NativeGesture extends BaseGesture<NativeViewGestureHandlerPayload> {\n  public config: BaseGestureConfig & NativeViewGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'NativeViewGestureHandler';\n  }\n\n  shouldActivateOnStart(value: boolean) {\n    this.config.shouldActivateOnStart = value;\n    return this;\n  }\n\n  disallowInterruption(value: boolean) {\n    this.config.disallowInterruption = value;\n    return this;\n  }\n}\n\nexport type NativeGestureType = InstanceType<typeof NativeGesture>;\n"]}