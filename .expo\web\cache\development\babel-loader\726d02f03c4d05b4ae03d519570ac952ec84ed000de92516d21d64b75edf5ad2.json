{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Colors, Typography, Spacing, Animations } from \"../constants\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar MenuCard = function MenuCard(_ref) {\n  var title = _ref.title,\n    subtitle = _ref.subtitle,\n    icon = _ref.icon,\n    _ref$iconType = _ref.iconType,\n    iconType = _ref$iconType === void 0 ? 'MaterialIcons' : _ref$iconType,\n    _ref$color = _ref.color,\n    color = _ref$color === void 0 ? Colors.primary : _ref$color,\n    onPress = _ref.onPress,\n    style = _ref.style,\n    _ref$disabled = _ref.disabled,\n    disabled = _ref$disabled === void 0 ? false : _ref$disabled;\n  var scaleValue = React.useRef(new Animated.Value(1)).current;\n  var opacityValue = React.useRef(new Animated.Value(1)).current;\n  var handlePressIn = function handlePressIn() {\n    Animated.parallel([Animated.spring(scaleValue, _objectSpread(_objectSpread({\n      toValue: Animations.scale.pressed\n    }, Animations.spring.default), {}, {\n      useNativeDriver: true\n    })), Animated.timing(opacityValue, {\n      toValue: 0.8,\n      duration: Animations.duration.fast,\n      useNativeDriver: true\n    })]).start();\n  };\n  var handlePressOut = function handlePressOut() {\n    Animated.parallel([Animated.spring(scaleValue, _objectSpread(_objectSpread({\n      toValue: Animations.scale.normal\n    }, Animations.spring.default), {}, {\n      useNativeDriver: true\n    })), Animated.timing(opacityValue, {\n      toValue: 1,\n      duration: Animations.duration.fast,\n      useNativeDriver: true\n    })]).start();\n  };\n  var IconComponent = MaterialIcons;\n  return _jsx(Animated.View, {\n    style: [{\n      transform: [{\n        scale: scaleValue\n      }],\n      opacity: opacityValue\n    }, style],\n    children: _jsxs(TouchableOpacity, {\n      style: [styles.container, disabled && styles.disabled],\n      onPress: onPress,\n      onPressIn: handlePressIn,\n      onPressOut: handlePressOut,\n      disabled: disabled,\n      activeOpacity: 0.9,\n      children: [_jsx(View, {\n        style: [styles.iconContainer, {\n          backgroundColor: `${color}15`\n        }],\n        children: _jsx(IconComponent, {\n          name: icon,\n          size: Spacing.iconSize.lg,\n          color: color\n        })\n      }), _jsxs(View, {\n        style: styles.textContainer,\n        children: [_jsx(Text, {\n          style: styles.title,\n          numberOfLines: 2,\n          children: title\n        }), _jsx(Text, {\n          style: styles.subtitle,\n          numberOfLines: 1,\n          children: subtitle\n        })]\n      })]\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    backgroundColor: Colors.surface,\n    borderRadius: Spacing.borderRadius.lg,\n    padding: Spacing.padding.md,\n    alignItems: 'center',\n    justifyContent: 'center',\n    minHeight: 120,\n    shadowColor: Colors.cardShadow,\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3\n  },\n  iconContainer: {\n    width: 56,\n    height: 56,\n    borderRadius: Spacing.borderRadius.xl,\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginBottom: Spacing.sm\n  },\n  textContainer: {\n    alignItems: 'center',\n    flex: 1\n  },\n  title: _objectSpread(_objectSpread({}, Typography.subtitle2), {}, {\n    color: Colors.onSurface,\n    textAlign: 'center',\n    marginBottom: Spacing.xs\n  }),\n  subtitle: _objectSpread(_objectSpread({}, Typography.caption), {}, {\n    color: Colors.onSurfaceVariant,\n    textAlign: 'center'\n  }),\n  disabled: {\n    opacity: Animations.opacity.disabled\n  }\n});\nexport default MenuCard;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "TouchableOpacity", "Animated", "MaterialIcons", "Colors", "Typography", "Spacing", "Animations", "jsx", "_jsx", "jsxs", "_jsxs", "MenuCard", "_ref", "title", "subtitle", "icon", "_ref$iconType", "iconType", "_ref$color", "color", "primary", "onPress", "style", "_ref$disabled", "disabled", "scaleValue", "useRef", "Value", "current", "opacityValue", "handlePressIn", "parallel", "spring", "_objectSpread", "toValue", "scale", "pressed", "default", "useNativeDriver", "timing", "duration", "fast", "start", "handlePressOut", "normal", "IconComponent", "transform", "opacity", "children", "styles", "container", "onPressIn", "onPressOut", "activeOpacity", "iconContainer", "backgroundColor", "name", "size", "iconSize", "lg", "textContainer", "numberOfLines", "create", "surface", "borderRadius", "padding", "md", "alignItems", "justifyContent", "minHeight", "shadowColor", "cardShadow", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation", "xl", "marginBottom", "sm", "flex", "subtitle2", "onSurface", "textAlign", "xs", "caption", "onSurfaceVariant"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/components/MenuCard.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  TouchableOpacity,\n  Animated,\n} from 'react-native';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Colors, Typography, Spacing, Animations } from '../constants';\n\nconst MenuCard = ({\n  title,\n  subtitle,\n  icon,\n  iconType = 'MaterialIcons',\n  color = Colors.primary,\n  onPress,\n  style,\n  disabled = false,\n}) => {\n  const scaleValue = React.useRef(new Animated.Value(1)).current;\n  const opacityValue = React.useRef(new Animated.Value(1)).current;\n\n  const handlePressIn = () => {\n    Animated.parallel([\n      Animated.spring(scaleValue, {\n        toValue: Animations.scale.pressed,\n        ...Animations.spring.default,\n        useNativeDriver: true,\n      }),\n      Animated.timing(opacityValue, {\n        toValue: 0.8,\n        duration: Animations.duration.fast,\n        useNativeDriver: true,\n      }),\n    ]).start();\n  };\n\n  const handlePressOut = () => {\n    Animated.parallel([\n      Animated.spring(scaleValue, {\n        toValue: Animations.scale.normal,\n        ...Animations.spring.default,\n        useNativeDriver: true,\n      }),\n      Animated.timing(opacityValue, {\n        toValue: 1,\n        duration: Animations.duration.fast,\n        useNativeDriver: true,\n      }),\n    ]).start();\n  };\n\n  const IconComponent = MaterialIcons;\n\n  return (\n    <Animated.View\n      style={[\n        {\n          transform: [{ scale: scaleValue }],\n          opacity: opacityValue,\n        },\n        style,\n      ]}\n    >\n      <TouchableOpacity\n        style={[styles.container, disabled && styles.disabled]}\n        onPress={onPress}\n        onPressIn={handlePressIn}\n        onPressOut={handlePressOut}\n        disabled={disabled}\n        activeOpacity={0.9}\n      >\n        <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>\n          <IconComponent\n            name={icon}\n            size={Spacing.iconSize.lg}\n            color={color}\n          />\n        </View>\n        \n        <View style={styles.textContainer}>\n          <Text style={styles.title} numberOfLines={2}>\n            {title}\n          </Text>\n          <Text style={styles.subtitle} numberOfLines={1}>\n            {subtitle}\n          </Text>\n        </View>\n      </TouchableOpacity>\n    </Animated.View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    backgroundColor: Colors.surface,\n    borderRadius: Spacing.borderRadius.lg,\n    padding: Spacing.padding.md,\n    alignItems: 'center',\n    justifyContent: 'center',\n    minHeight: 120,\n    shadowColor: Colors.cardShadow,\n    shadowOffset: {\n      width: 0,\n      height: 2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3,\n  },\n  iconContainer: {\n    width: 56,\n    height: 56,\n    borderRadius: Spacing.borderRadius.xl,\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginBottom: Spacing.sm,\n  },\n  textContainer: {\n    alignItems: 'center',\n    flex: 1,\n  },\n  title: {\n    ...Typography.subtitle2,\n    color: Colors.onSurface,\n    textAlign: 'center',\n    marginBottom: Spacing.xs,\n  },\n  subtitle: {\n    ...Typography.caption,\n    color: Colors.onSurfaceVariant,\n    textAlign: 'center',\n  },\n  disabled: {\n    opacity: Animations.opacity.disabled,\n  },\n});\n\nexport default MenuCard;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,QAAA;AAQ1B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU;AAAuB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEvE,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EASR;EAAA,IARJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IACRC,IAAI,GAAAH,IAAA,CAAJG,IAAI;IAAAC,aAAA,GAAAJ,IAAA,CACJK,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,eAAe,GAAAA,aAAA;IAAAE,UAAA,GAAAN,IAAA,CAC1BO,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAGf,MAAM,CAACiB,OAAO,GAAAF,UAAA;IACtBG,OAAO,GAAAT,IAAA,CAAPS,OAAO;IACPC,KAAK,GAAAV,IAAA,CAALU,KAAK;IAAAC,aAAA,GAAAX,IAAA,CACLY,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;EAEhB,IAAME,UAAU,GAAG7B,KAAK,CAAC8B,MAAM,CAAC,IAAIzB,QAAQ,CAAC0B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAC9D,IAAMC,YAAY,GAAGjC,KAAK,CAAC8B,MAAM,CAAC,IAAIzB,QAAQ,CAAC0B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAEhE,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1B7B,QAAQ,CAAC8B,QAAQ,CAAC,CAChB9B,QAAQ,CAAC+B,MAAM,CAACP,UAAU,EAAAQ,aAAA,CAAAA,aAAA;MACxBC,OAAO,EAAE5B,UAAU,CAAC6B,KAAK,CAACC;IAAO,GAC9B9B,UAAU,CAAC0B,MAAM,CAACK,OAAO;MAC5BC,eAAe,EAAE;IAAI,EACtB,CAAC,EACFrC,QAAQ,CAACsC,MAAM,CAACV,YAAY,EAAE;MAC5BK,OAAO,EAAE,GAAG;MACZM,QAAQ,EAAElC,UAAU,CAACkC,QAAQ,CAACC,IAAI;MAClCH,eAAe,EAAE;IACnB,CAAC,CAAC,CACH,CAAC,CAACI,KAAK,CAAC,CAAC;EACZ,CAAC;EAED,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B1C,QAAQ,CAAC8B,QAAQ,CAAC,CAChB9B,QAAQ,CAAC+B,MAAM,CAACP,UAAU,EAAAQ,aAAA,CAAAA,aAAA;MACxBC,OAAO,EAAE5B,UAAU,CAAC6B,KAAK,CAACS;IAAM,GAC7BtC,UAAU,CAAC0B,MAAM,CAACK,OAAO;MAC5BC,eAAe,EAAE;IAAI,EACtB,CAAC,EACFrC,QAAQ,CAACsC,MAAM,CAACV,YAAY,EAAE;MAC5BK,OAAO,EAAE,CAAC;MACVM,QAAQ,EAAElC,UAAU,CAACkC,QAAQ,CAACC,IAAI;MAClCH,eAAe,EAAE;IACnB,CAAC,CAAC,CACH,CAAC,CAACI,KAAK,CAAC,CAAC;EACZ,CAAC;EAED,IAAMG,aAAa,GAAG3C,aAAa;EAEnC,OACEM,IAAA,CAACP,QAAQ,CAACJ,IAAI;IACZyB,KAAK,EAAE,CACL;MACEwB,SAAS,EAAE,CAAC;QAAEX,KAAK,EAAEV;MAAW,CAAC,CAAC;MAClCsB,OAAO,EAAElB;IACX,CAAC,EACDP,KAAK,CACL;IAAA0B,QAAA,EAEFtC,KAAA,CAACV,gBAAgB;MACfsB,KAAK,EAAE,CAAC2B,MAAM,CAACC,SAAS,EAAE1B,QAAQ,IAAIyB,MAAM,CAACzB,QAAQ,CAAE;MACvDH,OAAO,EAAEA,OAAQ;MACjB8B,SAAS,EAAErB,aAAc;MACzBsB,UAAU,EAAET,cAAe;MAC3BnB,QAAQ,EAAEA,QAAS;MACnB6B,aAAa,EAAE,GAAI;MAAAL,QAAA,GAEnBxC,IAAA,CAACX,IAAI;QAACyB,KAAK,EAAE,CAAC2B,MAAM,CAACK,aAAa,EAAE;UAAEC,eAAe,EAAE,GAAGpC,KAAK;QAAK,CAAC,CAAE;QAAA6B,QAAA,EACrExC,IAAA,CAACqC,aAAa;UACZW,IAAI,EAAEzC,IAAK;UACX0C,IAAI,EAAEpD,OAAO,CAACqD,QAAQ,CAACC,EAAG;UAC1BxC,KAAK,EAAEA;QAAM,CACd;MAAC,CACE,CAAC,EAEPT,KAAA,CAACb,IAAI;QAACyB,KAAK,EAAE2B,MAAM,CAACW,aAAc;QAAAZ,QAAA,GAChCxC,IAAA,CAACV,IAAI;UAACwB,KAAK,EAAE2B,MAAM,CAACpC,KAAM;UAACgD,aAAa,EAAE,CAAE;UAAAb,QAAA,EACzCnC;QAAK,CACF,CAAC,EACPL,IAAA,CAACV,IAAI;UAACwB,KAAK,EAAE2B,MAAM,CAACnC,QAAS;UAAC+C,aAAa,EAAE,CAAE;UAAAb,QAAA,EAC5ClC;QAAQ,CACL,CAAC;MAAA,CACH,CAAC;IAAA,CACS;EAAC,CACN,CAAC;AAEpB,CAAC;AAED,IAAMmC,MAAM,GAAGlD,UAAU,CAAC+D,MAAM,CAAC;EAC/BZ,SAAS,EAAE;IACTK,eAAe,EAAEpD,MAAM,CAAC4D,OAAO;IAC/BC,YAAY,EAAE3D,OAAO,CAAC2D,YAAY,CAACL,EAAE;IACrCM,OAAO,EAAE5D,OAAO,CAAC4D,OAAO,CAACC,EAAE;IAC3BC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAEnE,MAAM,CAACoE,UAAU;IAC9BC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDvB,aAAa,EAAE;IACbmB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVV,YAAY,EAAE3D,OAAO,CAAC2D,YAAY,CAACc,EAAE;IACrCX,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBW,YAAY,EAAE1E,OAAO,CAAC2E;EACxB,CAAC;EACDpB,aAAa,EAAE;IACbO,UAAU,EAAE,QAAQ;IACpBc,IAAI,EAAE;EACR,CAAC;EACDpE,KAAK,EAAAoB,aAAA,CAAAA,aAAA,KACA7B,UAAU,CAAC8E,SAAS;IACvB/D,KAAK,EAAEhB,MAAM,CAACgF,SAAS;IACvBC,SAAS,EAAE,QAAQ;IACnBL,YAAY,EAAE1E,OAAO,CAACgF;EAAE,EACzB;EACDvE,QAAQ,EAAAmB,aAAA,CAAAA,aAAA,KACH7B,UAAU,CAACkF,OAAO;IACrBnE,KAAK,EAAEhB,MAAM,CAACoF,gBAAgB;IAC9BH,SAAS,EAAE;EAAQ,EACpB;EACD5D,QAAQ,EAAE;IACRuB,OAAO,EAAEzC,UAAU,CAACyC,OAAO,CAACvB;EAC9B;AACF,CAAC,CAAC;AAEF,eAAeb,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}