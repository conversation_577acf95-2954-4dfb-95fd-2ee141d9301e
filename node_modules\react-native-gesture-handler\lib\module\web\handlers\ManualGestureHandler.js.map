{"version": 3, "sources": ["ManualGestureHandler.ts"], "names": ["Gesture<PERSON>andler", "ManualGestureHandler", "init", "ref", "propsRef", "updateGestureConfig", "enabled", "props", "onPointerDown", "event", "tracker", "addToTracker", "begin", "onPointerAdd", "onPointerMove", "track", "onPointerOutOfBounds", "onPointerUp", "removeFromTracker", "pointerId", "onPointerRemove"], "mappings": "AACA,OAAOA,cAAP,MAA2B,kBAA3B;AAEA,eAAe,MAAMC,oBAAN,SAAmCD,cAAnC,CAAkD;AACxDE,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAkD;AAC3D,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AACD;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;AACD;;AAESC,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AACA,SAAKG,KAAL;AACD;;AAESC,EAAAA,YAAY,CAACJ,KAAD,EAA4B;AAChD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMI,YAAN,CAAmBJ,KAAnB;AACD;;AAESK,EAAAA,aAAa,CAACL,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaK,KAAb,CAAmBN,KAAnB;AACA,UAAMK,aAAN,CAAoBL,KAApB;AACD;;AAESO,EAAAA,oBAAoB,CAACP,KAAD,EAA4B;AACxD,SAAKC,OAAL,CAAaK,KAAb,CAAmBN,KAAnB;AACA,UAAMO,oBAAN,CAA2BP,KAA3B;AACD;;AAESQ,EAAAA,WAAW,CAACR,KAAD,EAA4B;AAC/C,UAAMQ,WAAN,CAAkBR,KAAlB;AACA,SAAKC,OAAL,CAAaQ,iBAAb,CAA+BT,KAAK,CAACU,SAArC;AACD;;AAESC,EAAAA,eAAe,CAACX,KAAD,EAA4B;AACnD,UAAMW,eAAN,CAAsBX,KAAtB;AACA,SAAKC,OAAL,CAAaQ,iBAAb,CAA+BT,KAAK,CAACU,SAArC;AACD;;AAtC8D", "sourcesContent": ["import { AdaptedEvent, Config } from '../interfaces';\nimport G<PERSON>ure<PERSON>and<PERSON> from './GestureHandler';\n\nexport default class ManualGestureHandler extends GestureHandler {\n  public init(ref: number, propsRef: React.RefObject<unknown>) {\n    super.init(ref, propsRef);\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n    this.begin();\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n    super.onPointerMove(event);\n  }\n\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    this.tracker.track(event);\n    super.onPointerOutOfBounds(event);\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.tracker.removeFromTracker(event.pointerId);\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.tracker.removeFromTracker(event.pointerId);\n  }\n}\n"]}