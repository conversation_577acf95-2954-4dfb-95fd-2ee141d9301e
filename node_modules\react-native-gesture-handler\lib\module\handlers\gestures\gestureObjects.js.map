{"version": 3, "sources": ["gestureObjects.ts"], "names": ["FlingGesture", "ForceTouchGesture", "ComposedGesture", "ExclusiveGesture", "SimultaneousGesture", "LongPressGesture", "PanGesture", "PinchGesture", "RotationGesture", "TapGesture", "NativeGesture", "ManualGesture", "HoverGesture", "GestureObjects", "Tap", "Pan", "Pinch", "Rotation", "Fling", "Long<PERSON>ress", "ForceTouch", "Native", "Manual", "Hover", "Race", "gestures", "Simultaneous", "Exclusive"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,SAASC,iBAAT,QAAkC,qBAAlC;AAEA,SACEC,eADF,EAEEC,gBAFF,EAGEC,mBAHF,QAIO,sBAJP;AAKA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,SAASC,UAAT,QAA2B,cAA3B;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,SAASC,UAAT,QAA2B,cAA3B;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,SAASC,YAAT,QAA6B,gBAA7B;AAEA,OAAO,MAAMC,cAAc,GAAG;AAC5BC,EAAAA,GAAG,EAAE,MAAM;AACT,WAAO,IAAIL,UAAJ,EAAP;AACD,GAH2B;AAK5BM,EAAAA,GAAG,EAAE,MAAM;AACT,WAAO,IAAIT,UAAJ,EAAP;AACD,GAP2B;AAS5BU,EAAAA,KAAK,EAAE,MAAM;AACX,WAAO,IAAIT,YAAJ,EAAP;AACD,GAX2B;AAa5BU,EAAAA,QAAQ,EAAE,MAAM;AACd,WAAO,IAAIT,eAAJ,EAAP;AACD,GAf2B;AAiB5BU,EAAAA,KAAK,EAAE,MAAM;AACX,WAAO,IAAIlB,YAAJ,EAAP;AACD,GAnB2B;AAqB5BmB,EAAAA,SAAS,EAAE,MAAM;AACf,WAAO,IAAId,gBAAJ,EAAP;AACD,GAvB2B;AAyB5Be,EAAAA,UAAU,EAAE,MAAM;AAChB,WAAO,IAAInB,iBAAJ,EAAP;AACD,GA3B2B;AA6B5BoB,EAAAA,MAAM,EAAE,MAAM;AACZ,WAAO,IAAIX,aAAJ,EAAP;AACD,GA/B2B;AAiC5BY,EAAAA,MAAM,EAAE,MAAM;AACZ,WAAO,IAAIX,aAAJ,EAAP;AACD,GAnC2B;AAqC5BY,EAAAA,KAAK,EAAE,MAAM;AACX,WAAO,IAAIX,YAAJ,EAAP;AACD,GAvC2B;;AAyC5B;AACF;AACA;AACA;AACEY,EAAAA,IAAI,EAAE,CAAC,GAAGC,QAAJ,KAA4B;AAChC,WAAO,IAAIvB,eAAJ,CAAoB,GAAGuB,QAAvB,CAAP;AACD,GA/C2B;;AAiD5B;AACF;AACA;AACEC,EAAAA,YAAY,CAAC,GAAGD,QAAJ,EAAyB;AACnC,WAAO,IAAIrB,mBAAJ,CAAwB,GAAGqB,QAA3B,CAAP;AACD,GAtD2B;;AAwD5B;AACF;AACA;AACA;AACA;AACA;AACA;AACEE,EAAAA,SAAS,CAAC,GAAGF,QAAJ,EAAyB;AAChC,WAAO,IAAItB,gBAAJ,CAAqB,GAAGsB,QAAxB,CAAP;AACD;;AAjE2B,CAAvB", "sourcesContent": ["import { FlingGesture } from './flingGesture';\nimport { ForceTouchGesture } from './forceTouchGesture';\nimport { Gesture } from './gesture';\nimport {\n  ComposedGesture,\n  ExclusiveGesture,\n  SimultaneousGesture,\n} from './gestureComposition';\nimport { LongPressGesture } from './longPressGesture';\nimport { PanGesture } from './panGesture';\nimport { PinchGesture } from './pinchGesture';\nimport { RotationGesture } from './rotationGesture';\nimport { TapGesture } from './tapGesture';\nimport { NativeGesture } from './nativeGesture';\nimport { ManualGesture } from './manualGesture';\nimport { HoverGesture } from './hoverGesture';\n\nexport const GestureObjects = {\n  Tap: () => {\n    return new TapGesture();\n  },\n\n  Pan: () => {\n    return new PanGesture();\n  },\n\n  Pinch: () => {\n    return new PinchGesture();\n  },\n\n  Rotation: () => {\n    return new RotationGesture();\n  },\n\n  Fling: () => {\n    return new FlingGesture();\n  },\n\n  LongPress: () => {\n    return new LongPressGesture();\n  },\n\n  ForceTouch: () => {\n    return new ForceTouchGesture();\n  },\n\n  Native: () => {\n    return new NativeGesture();\n  },\n\n  Manual: () => {\n    return new ManualGesture();\n  },\n\n  Hover: () => {\n    return new HoverGesture();\n  },\n\n  /**\n   * Builds a composed gesture consisting of gestures provided as parameters.\n   * The first one that becomes active cancels the rest of gestures.\n   */\n  Race: (...gestures: Gesture[]) => {\n    return new ComposedGesture(...gestures);\n  },\n\n  /**\n   * Builds a composed gesture that allows all base gestures to run simultaneously.\n   */\n  Simultaneous(...gestures: Gesture[]) {\n    return new SimultaneousGesture(...gestures);\n  },\n\n  /**\n   * Builds a composed gesture where only one of the provided gestures can become active.\n   * Priority is decided through the order of gestures: the first one has higher priority\n   * than the second one, second one has higher priority than the third one, and so on.\n   * For example, to make a gesture that recognizes both single and double tap you need\n   * to call Exclusive(doubleTap, singleTap).\n   */\n  Exclusive(...gestures: Gesture[]) {\n    return new ExclusiveGesture(...gestures);\n  },\n};\n"]}