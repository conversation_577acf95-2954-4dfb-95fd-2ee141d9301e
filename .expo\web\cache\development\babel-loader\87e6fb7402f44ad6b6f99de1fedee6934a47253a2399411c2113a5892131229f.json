{"ast": null, "code": "'use strict';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport AnimatedProps from \"./nodes/AnimatedProps\";\nimport { AnimatedEvent } from \"./AnimatedEvent\";\nimport useRefEffect from \"../Utilities/useRefEffect\";\nimport NativeAnimatedHelper from \"./NativeAnimatedHelper\";\nimport { useCallback, useEffect, useMemo, useReducer, useRef } from 'react';\nimport useLayoutEffect from \"../../../modules/useLayoutEffect\";\nexport default function useAnimatedProps(props) {\n  var _useReducer = useReducer(function (count) {\n      return count + 1;\n    }, 0),\n    scheduleUpdate = _useReducer[1];\n  var onUpdateRef = useRef(null);\n  var node = useMemo(function () {\n    return new AnimatedProps(props, function () {\n      return onUpdateRef.current == null ? void 0 : onUpdateRef.current();\n    });\n  }, [props]);\n  useAnimatedPropsLifecycle(node);\n  var refEffect = useCallback(function (instance) {\n    node.setNativeView(instance);\n    onUpdateRef.current = function () {\n      scheduleUpdate();\n    };\n    var target = getEventTarget(instance);\n    var events = [];\n    for (var propName in props) {\n      var propValue = props[propName];\n      if (propValue instanceof AnimatedEvent && propValue.__isNative) {\n        propValue.__attach(target, propName);\n        events.push([propName, propValue]);\n      }\n    }\n    return function () {\n      onUpdateRef.current = null;\n      for (var _i = 0, _events = events; _i < _events.length; _i++) {\n        var _events$_i = _events[_i],\n          _propName = _events$_i[0],\n          _propValue = _events$_i[1];\n        _propValue.__detach(target, _propName);\n      }\n    };\n  }, [props, node]);\n  var callbackRef = useRefEffect(refEffect);\n  return [reduceAnimatedProps(node), callbackRef];\n}\nfunction reduceAnimatedProps(node) {\n  return _objectSpread(_objectSpread({}, node.__getValue()), {}, {\n    collapsable: false\n  });\n}\nfunction useAnimatedPropsLifecycle(node) {\n  var prevNodeRef = useRef(null);\n  var isUnmountingRef = useRef(false);\n  useEffect(function () {\n    NativeAnimatedHelper.API.flushQueue();\n  });\n  useLayoutEffect(function () {\n    isUnmountingRef.current = false;\n    return function () {\n      isUnmountingRef.current = true;\n    };\n  }, []);\n  useLayoutEffect(function () {\n    node.__attach();\n    if (prevNodeRef.current != null) {\n      var prevNode = prevNodeRef.current;\n      prevNode.__restoreDefaultValues();\n      prevNode.__detach();\n      prevNodeRef.current = null;\n    }\n    return function () {\n      if (isUnmountingRef.current) {\n        node.__detach();\n      } else {\n        prevNodeRef.current = node;\n      }\n    };\n  }, [node]);\n}\nfunction getEventTarget(instance) {\n  return typeof instance === 'object' && typeof (instance == null ? void 0 : instance.getScrollableNode) === 'function' ? instance.getScrollableNode() : instance;\n}\nfunction isFabricInstance(instance) {\n  var _instance$getScrollRe;\n  return hasFabricHandle(instance) || hasFabricHandle(instance == null ? void 0 : instance.getNativeScrollRef == null ? void 0 : instance.getNativeScrollRef()) || hasFabricHandle(instance == null ? void 0 : instance.getScrollResponder == null ? void 0 : (_instance$getScrollRe = instance.getScrollResponder()) == null ? void 0 : _instance$getScrollRe.getNativeScrollRef == null ? void 0 : _instance$getScrollRe.getNativeScrollRef());\n}\nfunction hasFabricHandle(instance) {\n  var _instance$_internalIn, _instance$_internalIn2;\n  return (instance == null ? void 0 : (_instance$_internalIn = instance['_internalInstanceHandle']) == null ? void 0 : (_instance$_internalIn2 = _instance$_internalIn.stateNode) == null ? void 0 : _instance$_internalIn2.canonical) != null;\n}", "map": {"version": 3, "names": ["_objectSpread", "AnimatedProps", "AnimatedEvent", "useRefEffect", "NativeAnimatedHelper", "useCallback", "useEffect", "useMemo", "useReducer", "useRef", "useLayoutEffect", "useAnimatedProps", "props", "_useReducer", "count", "scheduleUpdate", "onUpdateRef", "node", "current", "useAnimatedPropsLifecycle", "refEffect", "instance", "setNativeView", "target", "getEventTarget", "events", "propName", "propValue", "__isNative", "__attach", "push", "_i", "_events", "length", "_events$_i", "_propName", "_propValue", "__detach", "callback<PERSON><PERSON>", "reduceAnimatedProps", "__getValue", "collapsable", "prevNodeRef", "isUnmountingRef", "API", "flushQueue", "prevNode", "__restore<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getScrollableNode", "isFabricInstance", "_instance$getScrollRe", "has<PERSON>ab<PERSON><PERSON><PERSON><PERSON>", "getNativeScrollRef", "getScrollResponder", "_instance$_internalIn", "_instance$_internalIn2", "stateNode", "canonical"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/vendor/react-native/Animated/useAnimatedProps.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport AnimatedProps from './nodes/AnimatedProps';\nimport { AnimatedEvent } from './AnimatedEvent';\nimport useRefEffect from '../Utilities/useRefEffect';\nimport NativeAnimatedHelper from './NativeAnimatedHelper';\nimport { useCallback, useEffect, useMemo, useReducer, useRef } from 'react';\nimport useLayoutEffect from '../../../modules/useLayoutEffect';\nexport default function useAnimatedProps(props) {\n  var _useReducer = useReducer(count => count + 1, 0),\n    scheduleUpdate = _useReducer[1];\n  var onUpdateRef = useRef(null);\n\n  // TODO: Only invalidate `node` if animated props or `style` change. In the\n  // previous implementation, we permitted `style` to override props with the\n  // same name property name as styles, so we can probably continue doing that.\n  // The ordering of other props *should* not matter.\n  var node = useMemo(() => new AnimatedProps(props, () => onUpdateRef.current == null ? void 0 : onUpdateRef.current()), [props]);\n  useAnimatedPropsLifecycle(node);\n\n  // TODO: This \"effect\" does three things:\n  //\n  //   1) Call `setNativeView`.\n  //   2) Update `onUpdateRef`.\n  //   3) Update listeners for `AnimatedEvent` props.\n  //\n  // Ideally, each of these would be separat \"effects\" so that they are not\n  // unnecessarily re-run when irrelevant dependencies change. For example, we\n  // should be able to hoist all `AnimatedEvent` props and only do #3 if either\n  // the `AnimatedEvent` props change or `instance` changes.\n  //\n  // But there is no way to transparently compose three separate callback refs,\n  // so we just combine them all into one for now.\n  var refEffect = useCallback(instance => {\n    // NOTE: This may be called more often than necessary (e.g. when `props`\n    // changes), but `setNativeView` already optimizes for that.\n    node.setNativeView(instance);\n\n    // NOTE: This callback is only used by the JavaScript animation driver.\n    onUpdateRef.current = () => {\n      // Schedule an update for this component to update `reducedProps`,\n      // but do not compute it immediately. If a parent also updated, we\n      // need to merge those new props in before updating.\n      scheduleUpdate();\n    };\n    var target = getEventTarget(instance);\n    var events = [];\n    for (var propName in props) {\n      var propValue = props[propName];\n      if (propValue instanceof AnimatedEvent && propValue.__isNative) {\n        propValue.__attach(target, propName);\n        events.push([propName, propValue]);\n      }\n    }\n    return () => {\n      onUpdateRef.current = null;\n      for (var _i = 0, _events = events; _i < _events.length; _i++) {\n        var _events$_i = _events[_i],\n          _propName = _events$_i[0],\n          _propValue = _events$_i[1];\n        _propValue.__detach(target, _propName);\n      }\n    };\n  }, [props, node]);\n  var callbackRef = useRefEffect(refEffect);\n  return [reduceAnimatedProps(node), callbackRef];\n}\nfunction reduceAnimatedProps(node) {\n  // Force `collapsable` to be false so that the native view is not flattened.\n  // Flattened views cannot be accurately referenced by the native driver.\n  return _objectSpread(_objectSpread({}, node.__getValue()), {}, {\n    collapsable: false\n  });\n}\n\n/**\n * Manages the lifecycle of the supplied `AnimatedProps` by invoking `__attach`\n * and `__detach`. However, this is more complicated because `AnimatedProps`\n * uses reference counting to determine when to recursively detach its children\n * nodes. So in order to optimize this, we avoid detaching until the next attach\n * unless we are unmounting.\n */\nfunction useAnimatedPropsLifecycle(node) {\n  var prevNodeRef = useRef(null);\n  var isUnmountingRef = useRef(false);\n  useEffect(() => {\n    // It is ok for multiple components to call `flushQueue` because it noops\n    // if the queue is empty. When multiple animated components are mounted at\n    // the same time. Only first component flushes the queue and the others will noop.\n    NativeAnimatedHelper.API.flushQueue();\n  });\n  useLayoutEffect(() => {\n    isUnmountingRef.current = false;\n    return () => {\n      isUnmountingRef.current = true;\n    };\n  }, []);\n  useLayoutEffect(() => {\n    node.__attach();\n    if (prevNodeRef.current != null) {\n      var prevNode = prevNodeRef.current;\n      // TODO: Stop restoring default values (unless `reset` is called).\n      prevNode.__restoreDefaultValues();\n      prevNode.__detach();\n      prevNodeRef.current = null;\n    }\n    return () => {\n      if (isUnmountingRef.current) {\n        // NOTE: Do not restore default values on unmount, see D18197735.\n        node.__detach();\n      } else {\n        prevNodeRef.current = node;\n      }\n    };\n  }, [node]);\n}\nfunction getEventTarget(instance) {\n  return typeof instance === 'object' && typeof (instance == null ? void 0 : instance.getScrollableNode) === 'function' ?\n  // $FlowFixMe[incompatible-use] - Legacy instance assumptions.\n  instance.getScrollableNode() : instance;\n}\n\n// $FlowFixMe[unclear-type] - Legacy instance assumptions.\nfunction isFabricInstance(instance) {\n  var _instance$getScrollRe;\n  return hasFabricHandle(instance) ||\n  // Some components have a setNativeProps function but aren't a host component\n  // such as lists like FlatList and SectionList. These should also use\n  // forceUpdate in Fabric since setNativeProps doesn't exist on the underlying\n  // host component. This crazy hack is essentially special casing those lists and\n  // ScrollView itself to use forceUpdate in Fabric.\n  // If these components end up using forwardRef then these hacks can go away\n  // as instance would actually be the underlying host component and the above check\n  // would be sufficient.\n  hasFabricHandle(instance == null ? void 0 : instance.getNativeScrollRef == null ? void 0 : instance.getNativeScrollRef()) || hasFabricHandle(instance == null ? void 0 : instance.getScrollResponder == null ? void 0 : (_instance$getScrollRe = instance.getScrollResponder()) == null ? void 0 : _instance$getScrollRe.getNativeScrollRef == null ? void 0 : _instance$getScrollRe.getNativeScrollRef());\n}\n\n// $FlowFixMe[unclear-type] - Legacy instance assumptions.\nfunction hasFabricHandle(instance) {\n  var _instance$_internalIn, _instance$_internalIn2;\n  // eslint-disable-next-line dot-notation\n  return (instance == null ? void 0 : (_instance$_internalIn = instance['_internalInstanceHandle']) == null ? void 0 : (_instance$_internalIn2 = _instance$_internalIn.stateNode) == null ? void 0 : _instance$_internalIn2.canonical) != null;\n}"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,sCAAsC;AAChE,OAAOC,aAAa;AACpB,SAASC,aAAa;AACtB,OAAOC,YAAY;AACnB,OAAOC,oBAAoB;AAC3B,SAASC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,QAAQ,OAAO;AAC3E,OAAOC,eAAe;AACtB,eAAe,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC9C,IAAIC,WAAW,GAAGL,UAAU,CAAC,UAAAM,KAAK;MAAA,OAAIA,KAAK,GAAG,CAAC;IAAA,GAAE,CAAC,CAAC;IACjDC,cAAc,GAAGF,WAAW,CAAC,CAAC,CAAC;EACjC,IAAIG,WAAW,GAAGP,MAAM,CAAC,IAAI,CAAC;EAM9B,IAAIQ,IAAI,GAAGV,OAAO,CAAC;IAAA,OAAM,IAAIN,aAAa,CAACW,KAAK,EAAE;MAAA,OAAMI,WAAW,CAACE,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGF,WAAW,CAACE,OAAO,CAAC,CAAC;IAAA,EAAC;EAAA,GAAE,CAACN,KAAK,CAAC,CAAC;EAC/HO,yBAAyB,CAACF,IAAI,CAAC;EAe/B,IAAIG,SAAS,GAAGf,WAAW,CAAC,UAAAgB,QAAQ,EAAI;IAGtCJ,IAAI,CAACK,aAAa,CAACD,QAAQ,CAAC;IAG5BL,WAAW,CAACE,OAAO,GAAG,YAAM;MAI1BH,cAAc,CAAC,CAAC;IAClB,CAAC;IACD,IAAIQ,MAAM,GAAGC,cAAc,CAACH,QAAQ,CAAC;IACrC,IAAII,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,QAAQ,IAAId,KAAK,EAAE;MAC1B,IAAIe,SAAS,GAAGf,KAAK,CAACc,QAAQ,CAAC;MAC/B,IAAIC,SAAS,YAAYzB,aAAa,IAAIyB,SAAS,CAACC,UAAU,EAAE;QAC9DD,SAAS,CAACE,QAAQ,CAACN,MAAM,EAAEG,QAAQ,CAAC;QACpCD,MAAM,CAACK,IAAI,CAAC,CAACJ,QAAQ,EAAEC,SAAS,CAAC,CAAC;MACpC;IACF;IACA,OAAO,YAAM;MACXX,WAAW,CAACE,OAAO,GAAG,IAAI;MAC1B,KAAK,IAAIa,EAAE,GAAG,CAAC,EAAEC,OAAO,GAAGP,MAAM,EAAEM,EAAE,GAAGC,OAAO,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;QAC5D,IAAIG,UAAU,GAAGF,OAAO,CAACD,EAAE,CAAC;UAC1BI,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;UACzBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;QAC5BE,UAAU,CAACC,QAAQ,CAACd,MAAM,EAAEY,SAAS,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACvB,KAAK,EAAEK,IAAI,CAAC,CAAC;EACjB,IAAIqB,WAAW,GAAGnC,YAAY,CAACiB,SAAS,CAAC;EACzC,OAAO,CAACmB,mBAAmB,CAACtB,IAAI,CAAC,EAAEqB,WAAW,CAAC;AACjD;AACA,SAASC,mBAAmBA,CAACtB,IAAI,EAAE;EAGjC,OAAOjB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiB,IAAI,CAACuB,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAC7DC,WAAW,EAAE;EACf,CAAC,CAAC;AACJ;AASA,SAAStB,yBAAyBA,CAACF,IAAI,EAAE;EACvC,IAAIyB,WAAW,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC9B,IAAIkC,eAAe,GAAGlC,MAAM,CAAC,KAAK,CAAC;EACnCH,SAAS,CAAC,YAAM;IAIdF,oBAAoB,CAACwC,GAAG,CAACC,UAAU,CAAC,CAAC;EACvC,CAAC,CAAC;EACFnC,eAAe,CAAC,YAAM;IACpBiC,eAAe,CAACzB,OAAO,GAAG,KAAK;IAC/B,OAAO,YAAM;MACXyB,eAAe,CAACzB,OAAO,GAAG,IAAI;IAChC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACNR,eAAe,CAAC,YAAM;IACpBO,IAAI,CAACY,QAAQ,CAAC,CAAC;IACf,IAAIa,WAAW,CAACxB,OAAO,IAAI,IAAI,EAAE;MAC/B,IAAI4B,QAAQ,GAAGJ,WAAW,CAACxB,OAAO;MAElC4B,QAAQ,CAACC,sBAAsB,CAAC,CAAC;MACjCD,QAAQ,CAACT,QAAQ,CAAC,CAAC;MACnBK,WAAW,CAACxB,OAAO,GAAG,IAAI;IAC5B;IACA,OAAO,YAAM;MACX,IAAIyB,eAAe,CAACzB,OAAO,EAAE;QAE3BD,IAAI,CAACoB,QAAQ,CAAC,CAAC;MACjB,CAAC,MAAM;QACLK,WAAW,CAACxB,OAAO,GAAGD,IAAI;MAC5B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;AACZ;AACA,SAASO,cAAcA,CAACH,QAAQ,EAAE;EAChC,OAAO,OAAOA,QAAQ,KAAK,QAAQ,IAAI,QAAQA,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC2B,iBAAiB,CAAC,KAAK,UAAU,GAErH3B,QAAQ,CAAC2B,iBAAiB,CAAC,CAAC,GAAG3B,QAAQ;AACzC;AAGA,SAAS4B,gBAAgBA,CAAC5B,QAAQ,EAAE;EAClC,IAAI6B,qBAAqB;EACzB,OAAOC,eAAe,CAAC9B,QAAQ,CAAC,IAShC8B,eAAe,CAAC9B,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+B,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG/B,QAAQ,CAAC+B,kBAAkB,CAAC,CAAC,CAAC,IAAID,eAAe,CAAC9B,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACgC,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACH,qBAAqB,GAAG7B,QAAQ,CAACgC,kBAAkB,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,qBAAqB,CAACE,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGF,qBAAqB,CAACE,kBAAkB,CAAC,CAAC,CAAC;AAC5Y;AAGA,SAASD,eAAeA,CAAC9B,QAAQ,EAAE;EACjC,IAAIiC,qBAAqB,EAAEC,sBAAsB;EAEjD,OAAO,CAAClC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACiC,qBAAqB,GAAGjC,QAAQ,CAAC,yBAAyB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACkC,sBAAsB,GAAGD,qBAAqB,CAACE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,sBAAsB,CAACE,SAAS,KAAK,IAAI;AAC9O", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}