{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/FontAwesome.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/FontAwesome.json\";\nexport default createIconSet(glyphMap, 'FontAwesome', font);", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\@expo\\vector-icons\\src\\FontAwesome.ts"], "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/FontAwesome.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/FontAwesome.json';\n\nexport default createIconSet(glyphMap, 'FontAwesome', font);\n"], "mappings": "AAAA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,eAAeF,aAAa,CAACE,QAAQ,EAAE,aAAa,EAAED,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}