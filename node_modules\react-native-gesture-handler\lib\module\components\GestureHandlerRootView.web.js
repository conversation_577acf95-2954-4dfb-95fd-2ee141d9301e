import * as React from 'react';
import { View } from 'react-native';
import GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';
export default function GestureHandlerRootView(props) {
  return /*#__PURE__*/React.createElement(GestureHandlerRootViewContext.Provider, {
    value: true
  }, /*#__PURE__*/React.createElement(View, props));
}
//# sourceMappingURL=GestureHandlerRootView.web.js.map