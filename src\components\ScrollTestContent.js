import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Typography, Spacing } from '../constants';

/**
 * Test component to verify fixed navigation behavior
 * Adds extra content to test scrolling and ensure navigation stays fixed
 */
const ScrollTestContent = () => {
  const testItems = Array.from({ length: 10 }, (_, index) => ({
    id: index + 1,
    title: `Test Section ${index + 1}`,
    description: `This is test content section ${index + 1} to verify that the bottom navigation remains fixed while scrolling through the page content.`,
  }));

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Fixed Navigation Test</Text>
      <Text style={styles.subheader}>
        Scroll down to verify that the bottom navigation stays fixed at the bottom of the screen.
      </Text>
      
      {testItems.map((item) => (
        <View key={item.id} style={styles.testItem}>
          <Text style={styles.itemTitle}>{item.title}</Text>
          <Text style={styles.itemDescription}>{item.description}</Text>
          <View style={styles.itemContent}>
            <Text style={styles.itemText}>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor 
              incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis 
              nostrud exercitation ullamco laboris.
            </Text>
          </View>
        </View>
      ))}
      
      <View style={styles.finalSection}>
        <Text style={styles.finalText}>
          🎉 If you can see this text and the bottom navigation is still visible and fixed 
          at the bottom, then the implementation is working correctly!
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: Spacing.padding.md,
  },
  header: {
    ...Typography.h2,
    color: Colors.primary,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  subheader: {
    ...Typography.body1,
    color: Colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  testItem: {
    backgroundColor: Colors.surface,
    borderRadius: Spacing.borderRadius.lg,
    padding: Spacing.padding.md,
    marginBottom: Spacing.lg,
    shadowColor: Colors.cardShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemTitle: {
    ...Typography.h3,
    color: Colors.onSurface,
    marginBottom: Spacing.sm,
  },
  itemDescription: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
    marginBottom: Spacing.md,
  },
  itemContent: {
    borderLeftWidth: 3,
    borderLeftColor: Colors.primary,
    paddingLeft: Spacing.md,
  },
  itemText: {
    ...Typography.body1,
    color: Colors.onSurface,
    lineHeight: 24,
  },
  finalSection: {
    backgroundColor: Colors.successLight,
    borderRadius: Spacing.borderRadius.lg,
    padding: Spacing.padding.lg,
    marginTop: Spacing.xl,
    borderWidth: 2,
    borderColor: Colors.success,
  },
  finalText: {
    ...Typography.body1,
    color: Colors.onSurface,
    textAlign: 'center',
    fontWeight: '600',
  },
});

export default ScrollTestContent;
