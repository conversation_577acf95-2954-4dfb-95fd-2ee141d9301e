{"version": 3, "sources": ["PointerTracker.ts"], "names": ["MAX_POINTERS", "PointerTracker", "constructor", "VelocityTracker", "Map", "x", "y", "lastMovedPointerId", "NaN", "i", "touchEventsIds", "set", "addToTracker", "event", "trackedPointers", "has", "pointerId", "newElement", "lastX", "lastY", "timeStamp", "time", "velocityX", "velocityY", "mapTouchEventId", "cachedAverages", "getLastAvgX", "getLastAvgY", "removeFromTracker", "delete", "removeMappedTouchId", "track", "element", "get", "velocityTracker", "add", "getVelocity", "avgX", "avgY", "id", "mappedId", "touchId", "isNaN", "getMappedTouchEventId", "touchEventId", "key", "value", "entries", "getVelocityX", "getVelocityY", "getLastX", "undefined", "getLastY", "getSumX", "size", "getSumY", "ignoredPointer", "sumX", "for<PERSON>ach", "sumY", "getTrackedPointersCount", "getTrackedPointersID", "keys", "_value", "push", "getData", "resetTracker", "reset", "clear", "shareCommonPointers", "stPointers", "ndPointers", "some", "includes"], "mappings": ";;;;;;;AACA;;;;;;AAYA,MAAMA,YAAY,GAAG,EAArB;;AAEe,MAAMC,cAAN,CAAqB;AAa3BC,EAAAA,WAAW,GAAG;AAAA,6CAZK,IAAIC,wBAAJ,EAYL;;AAAA,6CAXkC,IAAIC,GAAJ,EAWlC;;AAAA,4CANyB,IAAIA,GAAJ,EAMzB;;AAAA;;AAAA,4CAF8B;AAAEC,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE;AAAX,KAE9B;;AACnB,SAAKC,kBAAL,GAA0BC,GAA1B;;AAEA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,YAApB,EAAkC,EAAES,CAApC,EAAuC;AACrC,WAAKC,cAAL,CAAoBC,GAApB,CAAwBF,CAAxB,EAA2BD,GAA3B;AACD;AACF;;AAEMI,EAAAA,YAAY,CAACC,KAAD,EAA4B;AAC7C,QAAI,KAAKC,eAAL,CAAqBC,GAArB,CAAyBF,KAAK,CAACG,SAA/B,CAAJ,EAA+C;AAC7C;AACD;;AAED,SAAKT,kBAAL,GAA0BM,KAAK,CAACG,SAAhC;AAEA,UAAMC,UAA0B,GAAG;AACjCC,MAAAA,KAAK,EAAEL,KAAK,CAACR,CADoB;AAEjCc,MAAAA,KAAK,EAAEN,KAAK,CAACP,CAFoB;AAGjCc,MAAAA,SAAS,EAAEP,KAAK,CAACQ,IAHgB;AAIjCC,MAAAA,SAAS,EAAE,CAJsB;AAKjCC,MAAAA,SAAS,EAAE;AALsB,KAAnC;AAQA,SAAKT,eAAL,CAAqBH,GAArB,CAAyBE,KAAK,CAACG,SAA/B,EAA0CC,UAA1C;AACA,SAAKO,eAAL,CAAqBX,KAAK,CAACG,SAA3B;AAEA,SAAKS,cAAL,GAAsB;AACpBpB,MAAAA,CAAC,EAAE,KAAKqB,WAAL,EADiB;AAEpBpB,MAAAA,CAAC,EAAE,KAAKqB,WAAL;AAFiB,KAAtB;AAID;;AAEMC,EAAAA,iBAAiB,CAACZ,SAAD,EAA0B;AAChD,SAAKF,eAAL,CAAqBe,MAArB,CAA4Bb,SAA5B;AACA,SAAKc,mBAAL,CAAyBd,SAAzB;AACD;;AAEMe,EAAAA,KAAK,CAAClB,KAAD,EAA4B;AACtC,UAAMmB,OAAuB,GAAG,KAAKlB,eAAL,CAAqBmB,GAArB,CAC9BpB,KAAK,CAACG,SADwB,CAAhC;;AAIA,QAAI,CAACgB,OAAL,EAAc;AACZ;AACD;;AAED,SAAKzB,kBAAL,GAA0BM,KAAK,CAACG,SAAhC;AAEA,SAAKkB,eAAL,CAAqBC,GAArB,CAAyBtB,KAAzB;AACA,UAAM,CAACS,SAAD,EAAYC,SAAZ,IAAyB,KAAKW,eAAL,CAAqBE,WAArB,EAA/B;AAEAJ,IAAAA,OAAO,CAACV,SAAR,GAAoBA,SAApB;AACAU,IAAAA,OAAO,CAACT,SAAR,GAAoBA,SAApB;AAEAS,IAAAA,OAAO,CAACd,KAAR,GAAgBL,KAAK,CAACR,CAAtB;AACA2B,IAAAA,OAAO,CAACb,KAAR,GAAgBN,KAAK,CAACP,CAAtB;AAEA,SAAKQ,eAAL,CAAqBH,GAArB,CAAyBE,KAAK,CAACG,SAA/B,EAA0CgB,OAA1C;AAEA,UAAMK,IAAY,GAAG,KAAKX,WAAL,EAArB;AACA,UAAMY,IAAY,GAAG,KAAKX,WAAL,EAArB;AAEA,SAAKF,cAAL,GAAsB;AACpBpB,MAAAA,CAAC,EAAEgC,IADiB;AAEpB/B,MAAAA,CAAC,EAAEgC;AAFiB,KAAtB;AAID,GA/EiC,CAiFlC;;;AACQd,EAAAA,eAAe,CAACe,EAAD,EAAmB;AACxC,SAAK,MAAM,CAACC,QAAD,EAAWC,OAAX,CAAX,IAAkC,KAAK/B,cAAvC,EAAuD;AACrD,UAAIgC,KAAK,CAACD,OAAD,CAAT,EAAoB;AAClB,aAAK/B,cAAL,CAAoBC,GAApB,CAAwB6B,QAAxB,EAAkCD,EAAlC;AACA;AACD;AACF;AACF;;AAEOT,EAAAA,mBAAmB,CAACS,EAAD,EAAmB;AAC5C,UAAMC,QAAgB,GAAG,KAAKG,qBAAL,CAA2BJ,EAA3B,CAAzB;;AACA,QAAI,CAACG,KAAK,CAACF,QAAD,CAAV,EAAsB;AACpB,WAAK9B,cAAL,CAAoBC,GAApB,CAAwB6B,QAAxB,EAAkChC,GAAlC;AACD;AACF;;AAEMmC,EAAAA,qBAAqB,CAACC,YAAD,EAA+B;AACzD,SAAK,MAAM,CAACC,GAAD,EAAMC,KAAN,CAAX,IAA2B,KAAKpC,cAAL,CAAoBqC,OAApB,EAA3B,EAA0D;AACxD,UAAID,KAAK,KAAKF,YAAd,EAA4B;AAC1B,eAAOC,GAAP;AACD;AACF;;AAED,WAAOrC,GAAP;AACD;;AAEMwC,EAAAA,YAAY,CAAChC,SAAD,EAA4B;AAAA;;AAC7C,oCAAO,KAAKF,eAAL,CAAqBmB,GAArB,CAAyBjB,SAAzB,CAAP,0DAAO,sBAAqCM,SAA5C;AACD;;AACM2B,EAAAA,YAAY,CAACjC,SAAD,EAA4B;AAAA;;AAC7C,qCAAO,KAAKF,eAAL,CAAqBmB,GAArB,CAAyBjB,SAAzB,CAAP,2DAAO,uBAAqCO,SAA5C;AACD;AAED;AACF;AACA;;;AAWS2B,EAAAA,QAAQ,CAAClC,SAAD,EAA6B;AAC1C,QAAIA,SAAS,KAAKmC,SAAlB,EAA6B;AAAA;;AAC3B,uCAAO,KAAKrC,eAAL,CAAqBmB,GAArB,CAAyBjB,SAAzB,CAAP,2DAAO,uBAAqCE,KAA5C;AACD,KAFD,MAEO;AAAA;;AACL,uCAAO,KAAKJ,eAAL,CAAqBmB,GAArB,CAAyB,KAAK1B,kBAA9B,CAAP,2DAAO,uBAAmDW,KAA1D;AACD;AACF;AAED;AACF;AACA;;;AAWSkC,EAAAA,QAAQ,CAACpC,SAAD,EAA6B;AAC1C,QAAIA,SAAS,KAAKmC,SAAlB,EAA6B;AAAA;;AAC3B,uCAAO,KAAKrC,eAAL,CAAqBmB,GAArB,CAAyBjB,SAAzB,CAAP,2DAAO,uBAAqCG,KAA5C;AACD,KAFD,MAEO;AAAA;;AACL,uCAAO,KAAKL,eAAL,CAAqBmB,GAArB,CAAyB,KAAK1B,kBAA9B,CAAP,2DAAO,uBAAmDY,KAA1D;AACD;AACF,GA3JiC,CA6JlC;AACA;AACA;AACA;;;AACOO,EAAAA,WAAW,GAAW;AAC3B,UAAMW,IAAY,GAAG,KAAKgB,OAAL,KAAiB,KAAKvC,eAAL,CAAqBwC,IAA3D;AACA,WAAOZ,KAAK,CAACL,IAAD,CAAL,GAAc,KAAKZ,cAAL,CAAoBpB,CAAlC,GAAsCgC,IAA7C;AACD;;AACMV,EAAAA,WAAW,GAAW;AAC3B,UAAMW,IAAY,GAAG,KAAKiB,OAAL,KAAiB,KAAKzC,eAAL,CAAqBwC,IAA3D;AACA,WAAOZ,KAAK,CAACJ,IAAD,CAAL,GAAc,KAAKb,cAAL,CAAoBnB,CAAlC,GAAsCgC,IAA7C;AACD;;AACMe,EAAAA,OAAO,CAACG,cAAD,EAAkC;AAC9C,QAAIC,IAAI,GAAG,CAAX;AAEA,SAAK3C,eAAL,CAAqB4C,OAArB,CAA6B,CAACZ,KAAD,EAAQD,GAAR,KAAgB;AAC3C,UAAIA,GAAG,KAAKW,cAAZ,EAA4B;AAC1BC,QAAAA,IAAI,IAAIX,KAAK,CAAC5B,KAAd;AACD;AACF,KAJD;AAMA,WAAOuC,IAAP;AACD;;AACMF,EAAAA,OAAO,CAACC,cAAD,EAAkC;AAC9C,QAAIG,IAAI,GAAG,CAAX;AAEA,SAAK7C,eAAL,CAAqB4C,OAArB,CAA6B,CAACZ,KAAD,EAAQD,GAAR,KAAgB;AAC3C,UAAIA,GAAG,KAAKW,cAAZ,EAA4B;AAC1BG,QAAAA,IAAI,IAAIb,KAAK,CAAC3B,KAAd;AACD;AACF,KAJD;AAMA,WAAOwC,IAAP;AACD;;AACMC,EAAAA,uBAAuB,GAAW;AACvC,WAAO,KAAK9C,eAAL,CAAqBwC,IAA5B;AACD;;AACMO,EAAAA,oBAAoB,GAAa;AACtC,UAAMC,IAAc,GAAG,EAAvB;AAEA,SAAKhD,eAAL,CAAqB4C,OAArB,CAA6B,CAACK,MAAD,EAASlB,GAAT,KAAiB;AAC5CiB,MAAAA,IAAI,CAACE,IAAL,CAAUnB,GAAV;AACD,KAFD;AAIA,WAAOiB,IAAP;AACD;;AAEMG,EAAAA,OAAO,GAAgC;AAC5C,WAAO,KAAKnD,eAAZ;AACD;;AAEMoD,EAAAA,YAAY,GAAS;AAC1B,SAAKhC,eAAL,CAAqBiC,KAArB;AACA,SAAKrD,eAAL,CAAqBsD,KAArB;AACA,SAAK7D,kBAAL,GAA0BC,GAA1B;;AAEA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,YAApB,EAAkC,EAAES,CAApC,EAAuC;AACrC,WAAKC,cAAL,CAAoBC,GAApB,CAAwBF,CAAxB,EAA2BD,GAA3B;AACD;AACF;;AAEgC,SAAnB6D,mBAAmB,CAC/BC,UAD+B,EAE/BC,UAF+B,EAGtB;AACT,WAAOD,UAAU,CAACE,IAAX,CAAiBxD,SAAD,IAAeuD,UAAU,CAACE,QAAX,CAAoBzD,SAApB,CAA/B,CAAP;AACD;;AA/NiC", "sourcesContent": ["import { AdaptedEvent } from '../interfaces';\nimport VelocityTracker from './VelocityTracker';\n\nexport interface TrackerElement {\n  lastX: number;\n  lastY: number;\n\n  timeStamp: number;\n\n  velocityX: number;\n  velocityY: number;\n}\n\nconst MAX_POINTERS = 20;\n\nexport default class PointerTracker {\n  private velocityTracker = new VelocityTracker();\n  private trackedPointers: Map<number, TrackerElement> = new Map<\n    number,\n    TrackerElement\n  >();\n\n  private touchEventsIds: Map<number, number> = new Map<number, number>();\n\n  private lastMovedPointerId: number;\n\n  private cachedAverages: { x: number; y: number } = { x: 0, y: 0 };\n\n  public constructor() {\n    this.lastMovedPointerId = NaN;\n\n    for (let i = 0; i < MAX_POINTERS; ++i) {\n      this.touchEventsIds.set(i, NaN);\n    }\n  }\n\n  public addToTracker(event: AdaptedEvent): void {\n    if (this.trackedPointers.has(event.pointerId)) {\n      return;\n    }\n\n    this.lastMovedPointerId = event.pointerId;\n\n    const newElement: TrackerElement = {\n      lastX: event.x,\n      lastY: event.y,\n      timeStamp: event.time,\n      velocityX: 0,\n      velocityY: 0,\n    };\n\n    this.trackedPointers.set(event.pointerId, newElement);\n    this.mapTouchEventId(event.pointerId);\n\n    this.cachedAverages = {\n      x: this.getLastAvgX(),\n      y: this.getLastAvgY(),\n    };\n  }\n\n  public removeFromTracker(pointerId: number): void {\n    this.trackedPointers.delete(pointerId);\n    this.removeMappedTouchId(pointerId);\n  }\n\n  public track(event: AdaptedEvent): void {\n    const element: TrackerElement = this.trackedPointers.get(\n      event.pointerId\n    ) as TrackerElement;\n\n    if (!element) {\n      return;\n    }\n\n    this.lastMovedPointerId = event.pointerId;\n\n    this.velocityTracker.add(event);\n    const [velocityX, velocityY] = this.velocityTracker.getVelocity();\n\n    element.velocityX = velocityX;\n    element.velocityY = velocityY;\n\n    element.lastX = event.x;\n    element.lastY = event.y;\n\n    this.trackedPointers.set(event.pointerId, element);\n\n    const avgX: number = this.getLastAvgX();\n    const avgY: number = this.getLastAvgY();\n\n    this.cachedAverages = {\n      x: avgX,\n      y: avgY,\n    };\n  }\n\n  //Mapping TouchEvents ID\n  private mapTouchEventId(id: number): void {\n    for (const [mappedId, touchId] of this.touchEventsIds) {\n      if (isNaN(touchId)) {\n        this.touchEventsIds.set(mappedId, id);\n        break;\n      }\n    }\n  }\n\n  private removeMappedTouchId(id: number): void {\n    const mappedId: number = this.getMappedTouchEventId(id);\n    if (!isNaN(mappedId)) {\n      this.touchEventsIds.set(mappedId, NaN);\n    }\n  }\n\n  public getMappedTouchEventId(touchEventId: number): number {\n    for (const [key, value] of this.touchEventsIds.entries()) {\n      if (value === touchEventId) {\n        return key;\n      }\n    }\n\n    return NaN;\n  }\n\n  public getVelocityX(pointerId: number): number {\n    return this.trackedPointers.get(pointerId)?.velocityX as number;\n  }\n  public getVelocityY(pointerId: number): number {\n    return this.trackedPointers.get(pointerId)?.velocityY as number;\n  }\n\n  /**\n   * Returns X coordinate of last moved pointer\n   */\n  public getLastX(): number;\n\n  /**\n   *\n   * @param pointerId\n   * Returns X coordinate of given pointer\n   */\n  // eslint-disable-next-line @typescript-eslint/unified-signatures\n  public getLastX(pointerId: number): number;\n\n  public getLastX(pointerId?: number): number {\n    if (pointerId !== undefined) {\n      return this.trackedPointers.get(pointerId)?.lastX as number;\n    } else {\n      return this.trackedPointers.get(this.lastMovedPointerId)?.lastX as number;\n    }\n  }\n\n  /**\n   * Returns Y coordinate of last moved pointer\n   */\n  public getLastY(): number;\n\n  /**\n   *\n   * @param pointerId\n   * Returns Y coordinate of given pointer\n   */\n  // eslint-disable-next-line @typescript-eslint/unified-signatures\n  public getLastY(pointerId: number): number;\n\n  public getLastY(pointerId?: number): number {\n    if (pointerId !== undefined) {\n      return this.trackedPointers.get(pointerId)?.lastY as number;\n    } else {\n      return this.trackedPointers.get(this.lastMovedPointerId)?.lastY as number;\n    }\n  }\n\n  // Some handlers use these methods to send average values in native event.\n  // This may happen when pointers have already been removed from tracker (i.e. pointerup event).\n  // In situation when NaN would be sent as a response, we return cached value.\n  // That prevents handlers from crashing\n  public getLastAvgX(): number {\n    const avgX: number = this.getSumX() / this.trackedPointers.size;\n    return isNaN(avgX) ? this.cachedAverages.x : avgX;\n  }\n  public getLastAvgY(): number {\n    const avgY: number = this.getSumY() / this.trackedPointers.size;\n    return isNaN(avgY) ? this.cachedAverages.y : avgY;\n  }\n  public getSumX(ignoredPointer?: number): number {\n    let sumX = 0;\n\n    this.trackedPointers.forEach((value, key) => {\n      if (key !== ignoredPointer) {\n        sumX += value.lastX;\n      }\n    });\n\n    return sumX;\n  }\n  public getSumY(ignoredPointer?: number): number {\n    let sumY = 0;\n\n    this.trackedPointers.forEach((value, key) => {\n      if (key !== ignoredPointer) {\n        sumY += value.lastY;\n      }\n    });\n\n    return sumY;\n  }\n  public getTrackedPointersCount(): number {\n    return this.trackedPointers.size;\n  }\n  public getTrackedPointersID(): number[] {\n    const keys: number[] = [];\n\n    this.trackedPointers.forEach((_value, key) => {\n      keys.push(key);\n    });\n\n    return keys;\n  }\n\n  public getData(): Map<number, TrackerElement> {\n    return this.trackedPointers;\n  }\n\n  public resetTracker(): void {\n    this.velocityTracker.reset();\n    this.trackedPointers.clear();\n    this.lastMovedPointerId = NaN;\n\n    for (let i = 0; i < MAX_POINTERS; ++i) {\n      this.touchEventsIds.set(i, NaN);\n    }\n  }\n\n  public static shareCommonPointers(\n    stPointers: number[],\n    ndPointers: number[]\n  ): boolean {\n    return stPointers.some((pointerId) => ndPointers.includes(pointerId));\n  }\n}\n"]}