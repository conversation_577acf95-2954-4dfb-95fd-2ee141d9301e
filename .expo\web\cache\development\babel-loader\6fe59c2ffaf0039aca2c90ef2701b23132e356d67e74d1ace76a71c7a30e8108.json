{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport { CodedError, Platform } from 'expo-modules-core';\nimport FontObserver from 'fontfaceobserver';\nimport { FontDisplay } from \"./Font.types\";\nfunction getFontFaceStyleSheet() {\n  if (!Platform.isDOMAvailable) {\n    return null;\n  }\n  var styleSheet = getStyleElement();\n  return styleSheet.sheet ? styleSheet.sheet : null;\n}\nfunction getFontFaceRules() {\n  var sheet = getFontFaceStyleSheet();\n  if (sheet) {\n    var rules = _toConsumableArray(sheet.cssRules);\n    var items = [];\n    for (var i = 0; i < rules.length; i++) {\n      var rule = rules[i];\n      if (rule instanceof CSSFontFaceRule) {\n        items.push({\n          rule: rule,\n          index: i\n        });\n      }\n    }\n    return items;\n  }\n  return [];\n}\nfunction getFontFaceRulesMatchingResource(fontFamilyName, options) {\n  var rules = getFontFaceRules();\n  return rules.filter(function (_ref) {\n    var rule = _ref.rule;\n    return rule.style.fontFamily === fontFamilyName && (options && options.display ? options.display === rule.style.fontDisplay : true);\n  });\n}\nexport default {\n  get name() {\n    return 'ExpoFontLoader';\n  },\n  unloadAllAsync: function () {\n    var _unloadAllAsync = _asyncToGenerator(function* () {\n      if (!Platform.isDOMAvailable) return;\n      var element = document.getElementById(ID);\n      if (element && element instanceof HTMLStyleElement) {\n        document.removeChild(element);\n      }\n    });\n    function unloadAllAsync() {\n      return _unloadAllAsync.apply(this, arguments);\n    }\n    return unloadAllAsync;\n  }(),\n  unloadAsync: function () {\n    var _unloadAsync = _asyncToGenerator(function* (fontFamilyName, options) {\n      var sheet = getFontFaceStyleSheet();\n      if (!sheet) return;\n      var items = getFontFaceRulesMatchingResource(fontFamilyName, options);\n      for (var item of items) {\n        sheet.deleteRule(item.index);\n      }\n    });\n    function unloadAsync(_x, _x2) {\n      return _unloadAsync.apply(this, arguments);\n    }\n    return unloadAsync;\n  }(),\n  loadAsync: function () {\n    var _loadAsync = _asyncToGenerator(function* (fontFamilyName, resource) {\n      if (!Platform.isDOMAvailable) {\n        return;\n      }\n      var canInjectStyle = document.head && typeof document.head.appendChild === 'function';\n      if (!canInjectStyle) {\n        throw new CodedError('ERR_WEB_ENVIRONMENT', `The browser's \\`document.head\\` element doesn't support injecting fonts.`);\n      }\n      var style = _createWebStyle(fontFamilyName, resource);\n      document.head.appendChild(style);\n      if (!isFontLoadingListenerSupported()) {\n        return;\n      }\n      return new FontObserver(fontFamilyName, {\n        display: resource.display\n      }).load();\n    });\n    function loadAsync(_x3, _x4) {\n      return _loadAsync.apply(this, arguments);\n    }\n    return loadAsync;\n  }()\n};\nvar ID = 'expo-generated-fonts';\nfunction getStyleElement() {\n  var element = document.getElementById(ID);\n  if (element && element instanceof HTMLStyleElement) {\n    return element;\n  }\n  var styleElement = document.createElement('style');\n  styleElement.id = ID;\n  styleElement.type = 'text/css';\n  return styleElement;\n}\nfunction _createWebStyle(fontFamily, resource) {\n  var fontStyle = `@font-face {\n    font-family: ${fontFamily};\n    src: url(${resource.uri});\n    font-display: ${resource.display || FontDisplay.AUTO};\n  }`;\n  var styleElement = getStyleElement();\n  if (styleElement.styleSheet) {\n    var styleElementIE = styleElement;\n    styleElementIE.styleSheet.cssText = styleElementIE.styleSheet.cssText ? styleElementIE.styleSheet.cssText + fontStyle : fontStyle;\n  } else {\n    var textNode = document.createTextNode(fontStyle);\n    styleElement.appendChild(textNode);\n  }\n  return styleElement;\n}\nfunction isFontLoadingListenerSupported() {\n  var userAgent = window.navigator.userAgent;\n  var isIOS = !!userAgent.match(/iPad|iPhone/i);\n  var isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n  var isEdge = userAgent.includes('Edge');\n  var isIE = userAgent.includes('Trident');\n  var isFirefox = userAgent.includes('Firefox');\n  return !isSafari && !isIOS && !isEdge && !isIE && !isFirefox;\n}", "map": {"version": 3, "names": ["CodedError", "Platform", "FontObserver", "FontDisplay", "getFontFaceStyleSheet", "isDOMAvailable", "styleSheet", "getStyleElement", "sheet", "getFontFaceRules", "rules", "_toConsumableArray", "cssRules", "items", "i", "length", "rule", "CSSFontFaceRule", "push", "index", "getFontFaceRulesMatchingResource", "fontFamilyName", "options", "filter", "_ref", "style", "fontFamily", "display", "fontDisplay", "name", "unloadAllAsync", "_unloadAllAsync", "_asyncToGenerator", "element", "document", "getElementById", "ID", "HTMLStyleElement", "<PERSON><PERSON><PERSON><PERSON>", "apply", "arguments", "unloadAsync", "_unloadAsync", "item", "deleteRule", "_x", "_x2", "loadAsync", "_loadAsync", "resource", "canInjectStyle", "head", "append<PERSON><PERSON><PERSON>", "_createWebStyle", "isFontLoadingListenerSupported", "load", "_x3", "_x4", "styleElement", "createElement", "id", "type", "fontStyle", "uri", "AUTO", "styleElementIE", "cssText", "textNode", "createTextNode", "userAgent", "window", "navigator", "isIOS", "match", "<PERSON><PERSON><PERSON><PERSON>", "test", "isEdge", "includes", "isIE", "isFirefox"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-font\\src\\ExpoFontLoader.web.ts"], "sourcesContent": ["import { CodedError, Platform } from 'expo-modules-core';\nimport FontObserver from 'fontfaceobserver';\n\nimport { UnloadFontOptions } from './Font';\nimport { FontDisplay, FontResource } from './Font.types';\n\nfunction getFontFaceStyleSheet(): CSSStyleSheet | null {\n  if (!Platform.isDOMAvailable) {\n    return null;\n  }\n  const styleSheet = getStyleElement();\n  return styleSheet.sheet ? (styleSheet.sheet as CSSStyleSheet) : null;\n}\n\ntype RuleItem = { rule: CSSFontFaceRule; index: number };\n\nfunction getFontFaceRules(): RuleItem[] {\n  const sheet = getFontFaceStyleSheet();\n  if (sheet) {\n    // @ts-ignore: rule iterator\n    const rules = [...sheet.cssRules];\n\n    const items: RuleItem[] = [];\n\n    for (let i = 0; i < rules.length; i++) {\n      const rule = rules[i];\n      if (rule instanceof CSSFontFaceRule) {\n        items.push({ rule, index: i });\n      }\n    }\n    return items;\n  }\n  return [];\n}\n\nfunction getFontFaceRulesMatchingResource(\n  fontFamilyName: string,\n  options?: UnloadFontOptions\n): RuleItem[] {\n  const rules = getFontFaceRules();\n  return rules.filter(({ rule }) => {\n    return (\n      rule.style.fontFamily === fontFamilyName &&\n      (options && options.display ? options.display === (rule.style as any).fontDisplay : true)\n    );\n  });\n}\n\nexport default {\n  get name(): string {\n    return 'ExpoFontLoader';\n  },\n\n  async unloadAllAsync(): Promise<void> {\n    if (!Platform.isDOMAvailable) return;\n\n    const element = document.getElementById(ID);\n    if (element && element instanceof HTMLStyleElement) {\n      document.removeChild(element);\n    }\n  },\n\n  async unloadAsync(fontFamilyName: string, options?: UnloadFontOptions): Promise<void> {\n    const sheet = getFontFaceStyleSheet();\n    if (!sheet) return;\n    const items = getFontFaceRulesMatchingResource(fontFamilyName, options);\n    for (const item of items) {\n      sheet.deleteRule(item.index);\n    }\n  },\n\n  async loadAsync(fontFamilyName: string, resource: FontResource): Promise<void> {\n    if (!Platform.isDOMAvailable) {\n      return;\n    }\n\n    const canInjectStyle = document.head && typeof document.head.appendChild === 'function';\n    if (!canInjectStyle) {\n      throw new CodedError(\n        'ERR_WEB_ENVIRONMENT',\n        `The browser's \\`document.head\\` element doesn't support injecting fonts.`\n      );\n    }\n\n    const style = _createWebStyle(fontFamilyName, resource);\n    document.head!.appendChild(style);\n\n    if (!isFontLoadingListenerSupported()) {\n      return;\n    }\n\n    return new FontObserver(fontFamilyName, { display: resource.display }).load();\n  },\n};\n\nconst ID = 'expo-generated-fonts';\n\nfunction getStyleElement(): HTMLStyleElement {\n  const element = document.getElementById(ID);\n  if (element && element instanceof HTMLStyleElement) {\n    return element;\n  }\n  const styleElement = document.createElement('style');\n  styleElement.id = ID;\n  styleElement.type = 'text/css';\n  return styleElement;\n}\n\nfunction _createWebStyle(fontFamily: string, resource: FontResource): HTMLStyleElement {\n  const fontStyle = `@font-face {\n    font-family: ${fontFamily};\n    src: url(${resource.uri});\n    font-display: ${resource.display || FontDisplay.AUTO};\n  }`;\n\n  const styleElement = getStyleElement();\n  // @ts-ignore: TypeScript does not define HTMLStyleElement::styleSheet. This is just for IE and\n  // possibly can be removed if it's unnecessary on IE 11.\n  if (styleElement.styleSheet) {\n    const styleElementIE = styleElement as any;\n    styleElementIE.styleSheet.cssText = styleElementIE.styleSheet.cssText\n      ? styleElementIE.styleSheet.cssText + fontStyle\n      : fontStyle;\n  } else {\n    const textNode = document.createTextNode(fontStyle);\n    styleElement.appendChild(textNode);\n  }\n  return styleElement;\n}\n\nfunction isFontLoadingListenerSupported(): boolean {\n  const { userAgent } = window.navigator;\n  // WebKit is broken https://github.com/bramstein/fontfaceobserver/issues/95\n  const isIOS = !!userAgent.match(/iPad|iPhone/i);\n  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n  // Edge is broken https://github.com/bramstein/fontfaceobserver/issues/109#issuecomment-333356795\n  const isEdge = userAgent.includes('Edge');\n  // Internet Explorer\n  const isIE = userAgent.includes('Trident');\n  // Firefox\n  const isFirefox = userAgent.includes('Firefox');\n  return !isSafari && !isIOS && !isEdge && !isIE && !isFirefox;\n}\n"], "mappings": ";;AAAA,SAASA,UAAU,EAAEC,QAAQ,QAAQ,mBAAmB;AACxD,OAAOC,YAAY,MAAM,kBAAkB;AAG3C,SAASC,WAAW;AAEpB,SAASC,qBAAqBA,CAAA;EAC5B,IAAI,CAACH,QAAQ,CAACI,cAAc,EAAE;IAC5B,OAAO,IAAI;;EAEb,IAAMC,UAAU,GAAGC,eAAe,EAAE;EACpC,OAAOD,UAAU,CAACE,KAAK,GAAIF,UAAU,CAACE,KAAuB,GAAG,IAAI;AACtE;AAIA,SAASC,gBAAgBA,CAAA;EACvB,IAAMD,KAAK,GAAGJ,qBAAqB,EAAE;EACrC,IAAII,KAAK,EAAE;IAET,IAAME,KAAK,GAAAC,kBAAA,CAAOH,KAAK,CAACI,QAAQ,CAAC;IAEjC,IAAMC,KAAK,GAAe,EAAE;IAE5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAME,IAAI,GAAGN,KAAK,CAACI,CAAC,CAAC;MACrB,IAAIE,IAAI,YAAYC,eAAe,EAAE;QACnCJ,KAAK,CAACK,IAAI,CAAC;UAAEF,IAAI,EAAJA,IAAI;UAAEG,KAAK,EAAEL;QAAC,CAAE,CAAC;;;IAGlC,OAAOD,KAAK;;EAEd,OAAO,EAAE;AACX;AAEA,SAASO,gCAAgCA,CACvCC,cAAsB,EACtBC,OAA2B;EAE3B,IAAMZ,KAAK,GAAGD,gBAAgB,EAAE;EAChC,OAAOC,KAAK,CAACa,MAAM,CAAC,UAAAC,IAAA,EAAa;IAAA,IAAVR,IAAI,GAAAQ,IAAA,CAAJR,IAAI;IACzB,OACEA,IAAI,CAACS,KAAK,CAACC,UAAU,KAAKL,cAAc,KACvCC,OAAO,IAAIA,OAAO,CAACK,OAAO,GAAGL,OAAO,CAACK,OAAO,KAAMX,IAAI,CAACS,KAAa,CAACG,WAAW,GAAG,IAAI,CAAC;EAE7F,CAAC,CAAC;AACJ;AAEA,eAAe;EACb,IAAIC,IAAIA,CAAA;IACN,OAAO,gBAAgB;EACzB,CAAC;EAEKC,cAAc;IAAA,IAAAC,eAAA,GAAAC,iBAAA;MAClB,IAAI,CAAC/B,QAAQ,CAACI,cAAc,EAAE;MAE9B,IAAM4B,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACC,EAAE,CAAC;MAC3C,IAAIH,OAAO,IAAIA,OAAO,YAAYI,gBAAgB,EAAE;QAClDH,QAAQ,CAACI,WAAW,CAACL,OAAO,CAAC;;IAEjC,CAAC;IAAA,SAPKH,cAAcA,CAAA;MAAA,OAAAC,eAAA,CAAAQ,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAdV,cAAc;EAAA;EASdW,WAAW;IAAA,IAAAC,YAAA,GAAAV,iBAAA,YAACX,cAAsB,EAAEC,OAA2B;MACnE,IAAMd,KAAK,GAAGJ,qBAAqB,EAAE;MACrC,IAAI,CAACI,KAAK,EAAE;MACZ,IAAMK,KAAK,GAAGO,gCAAgC,CAACC,cAAc,EAAEC,OAAO,CAAC;MACvE,KAAK,IAAMqB,IAAI,IAAI9B,KAAK,EAAE;QACxBL,KAAK,CAACoC,UAAU,CAACD,IAAI,CAACxB,KAAK,CAAC;;IAEhC,CAAC;IAAA,SAPKsB,WAAWA,CAAAI,EAAA,EAAAC,GAAA;MAAA,OAAAJ,YAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAXC,WAAW;EAAA;EASXM,SAAS;IAAA,IAAAC,UAAA,GAAAhB,iBAAA,YAACX,cAAsB,EAAE4B,QAAsB;MAC5D,IAAI,CAAChD,QAAQ,CAACI,cAAc,EAAE;QAC5B;;MAGF,IAAM6C,cAAc,GAAGhB,QAAQ,CAACiB,IAAI,IAAI,OAAOjB,QAAQ,CAACiB,IAAI,CAACC,WAAW,KAAK,UAAU;MACvF,IAAI,CAACF,cAAc,EAAE;QACnB,MAAM,IAAIlD,UAAU,CAClB,qBAAqB,EACrB,0EAA0E,CAC3E;;MAGH,IAAMyB,KAAK,GAAG4B,eAAe,CAAChC,cAAc,EAAE4B,QAAQ,CAAC;MACvDf,QAAQ,CAACiB,IAAK,CAACC,WAAW,CAAC3B,KAAK,CAAC;MAEjC,IAAI,CAAC6B,8BAA8B,EAAE,EAAE;QACrC;;MAGF,OAAO,IAAIpD,YAAY,CAACmB,cAAc,EAAE;QAAEM,OAAO,EAAEsB,QAAQ,CAACtB;MAAO,CAAE,CAAC,CAAC4B,IAAI,EAAE;IAC/E,CAAC;IAAA,SArBKR,SAASA,CAAAS,GAAA,EAAAC,GAAA;MAAA,OAAAT,UAAA,CAAAT,KAAA,OAAAC,SAAA;IAAA;IAAA,OAATO,SAAS;EAAA;CAsBhB;AAED,IAAMX,EAAE,GAAG,sBAAsB;AAEjC,SAAS7B,eAAeA,CAAA;EACtB,IAAM0B,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACC,EAAE,CAAC;EAC3C,IAAIH,OAAO,IAAIA,OAAO,YAAYI,gBAAgB,EAAE;IAClD,OAAOJ,OAAO;;EAEhB,IAAMyB,YAAY,GAAGxB,QAAQ,CAACyB,aAAa,CAAC,OAAO,CAAC;EACpDD,YAAY,CAACE,EAAE,GAAGxB,EAAE;EACpBsB,YAAY,CAACG,IAAI,GAAG,UAAU;EAC9B,OAAOH,YAAY;AACrB;AAEA,SAASL,eAAeA,CAAC3B,UAAkB,EAAEuB,QAAsB;EACjE,IAAMa,SAAS,GAAG;mBACDpC,UAAU;eACduB,QAAQ,CAACc,GAAG;oBACPd,QAAQ,CAACtB,OAAO,IAAIxB,WAAW,CAAC6D,IAAI;IACpD;EAEF,IAAMN,YAAY,GAAGnD,eAAe,EAAE;EAGtC,IAAImD,YAAY,CAACpD,UAAU,EAAE;IAC3B,IAAM2D,cAAc,GAAGP,YAAmB;IAC1CO,cAAc,CAAC3D,UAAU,CAAC4D,OAAO,GAAGD,cAAc,CAAC3D,UAAU,CAAC4D,OAAO,GACjED,cAAc,CAAC3D,UAAU,CAAC4D,OAAO,GAAGJ,SAAS,GAC7CA,SAAS;GACd,MAAM;IACL,IAAMK,QAAQ,GAAGjC,QAAQ,CAACkC,cAAc,CAACN,SAAS,CAAC;IACnDJ,YAAY,CAACN,WAAW,CAACe,QAAQ,CAAC;;EAEpC,OAAOT,YAAY;AACrB;AAEA,SAASJ,8BAA8BA,CAAA;EACrC,IAAQe,SAAS,GAAKC,MAAM,CAACC,SAAS,CAA9BF,SAAS;EAEjB,IAAMG,KAAK,GAAG,CAAC,CAACH,SAAS,CAACI,KAAK,CAAC,cAAc,CAAC;EAC/C,IAAMC,QAAQ,GAAG,gCAAgC,CAACC,IAAI,CAACJ,SAAS,CAACF,SAAS,CAAC;EAE3E,IAAMO,MAAM,GAAGP,SAAS,CAACQ,QAAQ,CAAC,MAAM,CAAC;EAEzC,IAAMC,IAAI,GAAGT,SAAS,CAACQ,QAAQ,CAAC,SAAS,CAAC;EAE1C,IAAME,SAAS,GAAGV,SAAS,CAACQ,QAAQ,CAAC,SAAS,CAAC;EAC/C,OAAO,CAACH,QAAQ,IAAI,CAACF,KAAK,IAAI,CAACI,MAAM,IAAI,CAACE,IAAI,IAAI,CAACC,SAAS;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}