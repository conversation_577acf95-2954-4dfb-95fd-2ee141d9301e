{"version": 3, "sources": ["TouchEventManager.ts"], "names": ["TouchEventManager", "EventManager", "setListeners", "view", "addEventListener", "event", "i", "changedTouches", "length", "adaptedEvent", "mapEvent", "EventTypes", "DOWN", "TouchEventType", "x", "y", "touchType", "markAsInBounds", "pointerId", "activePointersCounter", "eventType", "ADDITIONAL_POINTER_DOWN", "onPointerAdd", "onPointerDown", "MOVE", "inBounds", "pointerIndex", "pointersInBounds", "indexOf", "ENTER", "onPointerEnter", "onPointerMove", "LEAVE", "onPointerLeave", "markAsOutOfBounds", "onPointerOutOfBounds", "UP", "ADDITIONAL_POINTER_UP", "onPointerRemove", "onPointerUp", "CANCEL", "CANCELLED", "onPointerCancel", "index", "touchEventType", "rect", "getBoundingClientRect", "clientX", "clientY", "offsetX", "left", "offsetY", "top", "identifier", "pointerType", "PointerType", "TOUCH", "buttons", "MouseButtons", "NONE", "time", "timeStamp", "allTouches", "touches"], "mappings": ";;;;;;;AAAA;;AAOA;;AACA;;;;AAEe,MAAMA,iBAAN,SAAgCC,qBAAhC,CAA0D;AAChEC,EAAAA,YAAY,GAAS;AAC1B,SAAKC,IAAL,CAAUC,gBAAV,CAA2B,YAA3B,EAA0CC,KAAD,IAAuB;AAC9D,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,cAAN,CAAqBC,MAAzC,EAAiD,EAAEF,CAAnD,EAAsD;AACpD,cAAMG,YAA0B,GAAG,KAAKC,QAAL,CACjCL,KADiC,EAEjCM,uBAAWC,IAFsB,EAGjCN,CAHiC,EAIjCO,2BAAeD,IAJkB,CAAnC,CADoD,CAQpD;AACA;;AACA,YACE,CAAC,8BAAkB,KAAKT,IAAvB,EAA6B;AAC5BW,UAAAA,CAAC,EAAEL,YAAY,CAACK,CADY;AAE5BC,UAAAA,CAAC,EAAEN,YAAY,CAACM;AAFY,SAA7B,CAAD,IAIA;AACAV,QAAAA,KAAK,CAACE,cAAN,CAAqBD,CAArB,EAAwBU,SAAxB,KAAsC,QANxC,EAOE;AACA;AACD;;AAED,aAAKC,cAAL,CAAoBR,YAAY,CAACS,SAAjC;;AAEA,YAAI,EAAE,KAAKC,qBAAP,GAA+B,CAAnC,EAAsC;AACpCV,UAAAA,YAAY,CAACW,SAAb,GAAyBT,uBAAWU,uBAApC;AACA,eAAKC,YAAL,CAAkBb,YAAlB;AACD,SAHD,MAGO;AACL,eAAKc,aAAL,CAAmBd,YAAnB;AACD;AACF;AACF,KA/BD;AAiCA,SAAKN,IAAL,CAAUC,gBAAV,CAA2B,WAA3B,EAAyCC,KAAD,IAAuB;AAC7D,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,cAAN,CAAqBC,MAAzC,EAAiD,EAAEF,CAAnD,EAAsD;AACpD,cAAMG,YAA0B,GAAG,KAAKC,QAAL,CACjCL,KADiC,EAEjCM,uBAAWa,IAFsB,EAGjClB,CAHiC,EAIjCO,2BAAeW,IAJkB,CAAnC,CADoD,CAOpD;;AACA,YAAInB,KAAK,CAACE,cAAN,CAAqBD,CAArB,EAAwBU,SAAxB,KAAsC,QAA1C,EAAoD;AAClD;AACD;;AAED,cAAMS,QAAiB,GAAG,8BAAkB,KAAKtB,IAAvB,EAA6B;AACrDW,UAAAA,CAAC,EAAEL,YAAY,CAACK,CADqC;AAErDC,UAAAA,CAAC,EAAEN,YAAY,CAACM;AAFqC,SAA7B,CAA1B;AAKA,cAAMW,YAAoB,GAAG,KAAKC,gBAAL,CAAsBC,OAAtB,CAC3BnB,YAAY,CAACS,SADc,CAA7B;;AAIA,YAAIO,QAAJ,EAAc;AACZ,cAAIC,YAAY,GAAG,CAAnB,EAAsB;AACpBjB,YAAAA,YAAY,CAACW,SAAb,GAAyBT,uBAAWkB,KAApC;AACA,iBAAKC,cAAL,CAAoBrB,YAApB;AACA,iBAAKQ,cAAL,CAAoBR,YAAY,CAACS,SAAjC;AACD,WAJD,MAIO;AACL,iBAAKa,aAAL,CAAmBtB,YAAnB;AACD;AACF,SARD,MAQO;AACL,cAAIiB,YAAY,IAAI,CAApB,EAAuB;AACrBjB,YAAAA,YAAY,CAACW,SAAb,GAAyBT,uBAAWqB,KAApC;AACA,iBAAKC,cAAL,CAAoBxB,YAApB;AACA,iBAAKyB,iBAAL,CAAuBzB,YAAY,CAACS,SAApC;AACD,WAJD,MAIO;AACL,iBAAKiB,oBAAL,CAA0B1B,YAA1B;AACD;AACF;AACF;AACF,KAxCD;AA0CA,SAAKN,IAAL,CAAUC,gBAAV,CAA2B,UAA3B,EAAwCC,KAAD,IAAuB;AAC5D,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,cAAN,CAAqBC,MAAzC,EAAiD,EAAEF,CAAnD,EAAsD;AACpD;AACA;AACA;AACA;AACA,YAAI,KAAKa,qBAAL,KAA+B,CAAnC,EAAsC;AACpC;AACD,SAPmD,CASpD;;;AACA,YAAId,KAAK,CAACE,cAAN,CAAqBD,CAArB,EAAwBU,SAAxB,KAAsC,QAA1C,EAAoD;AAClD;AACD;;AAED,cAAMP,YAA0B,GAAG,KAAKC,QAAL,CACjCL,KADiC,EAEjCM,uBAAWyB,EAFsB,EAGjC9B,CAHiC,EAIjCO,2BAAeuB,EAJkB,CAAnC;AAOA,aAAKF,iBAAL,CAAuBzB,YAAY,CAACS,SAApC;;AAEA,YAAI,EAAE,KAAKC,qBAAP,GAA+B,CAAnC,EAAsC;AACpCV,UAAAA,YAAY,CAACW,SAAb,GAAyBT,uBAAW0B,qBAApC;AACA,eAAKC,eAAL,CAAqB7B,YAArB;AACD,SAHD,MAGO;AACL,eAAK8B,WAAL,CAAiB9B,YAAjB;AACD;AACF;AACF,KA/BD;AAiCA,SAAKN,IAAL,CAAUC,gBAAV,CAA2B,aAA3B,EAA2CC,KAAD,IAAuB;AAC/D,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,cAAN,CAAqBC,MAAzC,EAAiD,EAAEF,CAAnD,EAAsD;AACpD,cAAMG,YAA0B,GAAG,KAAKC,QAAL,CACjCL,KADiC,EAEjCM,uBAAW6B,MAFsB,EAGjClC,CAHiC,EAIjCO,2BAAe4B,SAJkB,CAAnC,CADoD,CAQpD;;AACA,YAAIpC,KAAK,CAACE,cAAN,CAAqBD,CAArB,EAAwBU,SAAxB,KAAsC,QAA1C,EAAoD;AAClD;AACD;;AAED,aAAK0B,eAAL,CAAqBjC,YAArB;AACA,aAAKyB,iBAAL,CAAuBzB,YAAY,CAACS,SAApC;AACA,aAAKC,qBAAL,GAA6B,CAA7B;AACD;AACF,KAlBD;AAmBD;;AAEST,EAAAA,QAAQ,CAChBL,KADgB,EAEhBe,SAFgB,EAGhBuB,KAHgB,EAIhBC,cAJgB,EAKF;AACd,UAAMC,IAAI,GAAG,KAAK1C,IAAL,CAAU2C,qBAAV,EAAb;AACA,UAAMC,OAAO,GAAG1C,KAAK,CAACE,cAAN,CAAqBoC,KAArB,EAA4BI,OAA5C;AACA,UAAMC,OAAO,GAAG3C,KAAK,CAACE,cAAN,CAAqBoC,KAArB,EAA4BK,OAA5C;AAEA,WAAO;AACLlC,MAAAA,CAAC,EAAEiC,OADE;AAELhC,MAAAA,CAAC,EAAEiC,OAFE;AAGLC,MAAAA,OAAO,EAAEF,OAAO,GAAGF,IAAI,CAACK,IAHnB;AAILC,MAAAA,OAAO,EAAEH,OAAO,GAAGH,IAAI,CAACO,GAJnB;AAKLlC,MAAAA,SAAS,EAAEb,KAAK,CAACE,cAAN,CAAqBoC,KAArB,EAA4BU,UALlC;AAMLjC,MAAAA,SAAS,EAAEA,SANN;AAOLkC,MAAAA,WAAW,EAAEC,wBAAYC,KAPpB;AAQLC,MAAAA,OAAO,EAAEC,yBAAaC,IARjB;AASLC,MAAAA,IAAI,EAAEvD,KAAK,CAACwD,SATP;AAULC,MAAAA,UAAU,EAAEzD,KAAK,CAAC0D,OAVb;AAWLxD,MAAAA,cAAc,EAAEF,KAAK,CAACE,cAXjB;AAYLqC,MAAAA,cAAc,EAAEA;AAZX,KAAP;AAcD;;AA3JsE", "sourcesContent": ["import {\n  AdaptedEvent,\n  EventTypes,\n  MouseButtons,\n  PointerType,\n  TouchEventType,\n} from '../interfaces';\nimport EventManager from './EventManager';\nimport { isPointerInBounds } from '../utils';\n\nexport default class TouchEventManager extends EventManager<HTMLElement> {\n  public setListeners(): void {\n    this.view.addEventListener('touchstart', (event: TouchEvent) => {\n      for (let i = 0; i < event.changedTouches.length; ++i) {\n        const adaptedEvent: AdaptedEvent = this.mapEvent(\n          event,\n          EventTypes.DOWN,\n          i,\n          TouchEventType.DOWN\n        );\n\n        // Here we skip stylus, because in case of anything different than touch we want to handle it by using PointerEvents\n        // If we leave stylus to send touch events, handlers will receive every action twice\n        if (\n          !isPointerInBounds(this.view, {\n            x: adaptedEvent.x,\n            y: adaptedEvent.y,\n          }) ||\n          //@ts-ignore touchType field does exist\n          event.changedTouches[i].touchType === 'stylus'\n        ) {\n          continue;\n        }\n\n        this.markAsInBounds(adaptedEvent.pointerId);\n\n        if (++this.activePointersCounter > 1) {\n          adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_DOWN;\n          this.onPointerAdd(adaptedEvent);\n        } else {\n          this.onPointerDown(adaptedEvent);\n        }\n      }\n    });\n\n    this.view.addEventListener('touchmove', (event: TouchEvent) => {\n      for (let i = 0; i < event.changedTouches.length; ++i) {\n        const adaptedEvent: AdaptedEvent = this.mapEvent(\n          event,\n          EventTypes.MOVE,\n          i,\n          TouchEventType.MOVE\n        );\n        //@ts-ignore touchType field does exist\n        if (event.changedTouches[i].touchType === 'stylus') {\n          continue;\n        }\n\n        const inBounds: boolean = isPointerInBounds(this.view, {\n          x: adaptedEvent.x,\n          y: adaptedEvent.y,\n        });\n\n        const pointerIndex: number = this.pointersInBounds.indexOf(\n          adaptedEvent.pointerId\n        );\n\n        if (inBounds) {\n          if (pointerIndex < 0) {\n            adaptedEvent.eventType = EventTypes.ENTER;\n            this.onPointerEnter(adaptedEvent);\n            this.markAsInBounds(adaptedEvent.pointerId);\n          } else {\n            this.onPointerMove(adaptedEvent);\n          }\n        } else {\n          if (pointerIndex >= 0) {\n            adaptedEvent.eventType = EventTypes.LEAVE;\n            this.onPointerLeave(adaptedEvent);\n            this.markAsOutOfBounds(adaptedEvent.pointerId);\n          } else {\n            this.onPointerOutOfBounds(adaptedEvent);\n          }\n        }\n      }\n    });\n\n    this.view.addEventListener('touchend', (event: TouchEvent) => {\n      for (let i = 0; i < event.changedTouches.length; ++i) {\n        // When we call reset on gesture handlers, it also resets their event managers\n        // In some handlers (like RotationGestureHandler) reset is called before all pointers leave view\n        // This means, that activePointersCounter will be set to 0, while there are still remaining pointers on view\n        // Removing them will end in activePointersCounter going below 0, therefore handlers won't behave properly\n        if (this.activePointersCounter === 0) {\n          break;\n        }\n\n        //@ts-ignore touchType field does exist\n        if (event.changedTouches[i].touchType === 'stylus') {\n          continue;\n        }\n\n        const adaptedEvent: AdaptedEvent = this.mapEvent(\n          event,\n          EventTypes.UP,\n          i,\n          TouchEventType.UP\n        );\n\n        this.markAsOutOfBounds(adaptedEvent.pointerId);\n\n        if (--this.activePointersCounter > 0) {\n          adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_UP;\n          this.onPointerRemove(adaptedEvent);\n        } else {\n          this.onPointerUp(adaptedEvent);\n        }\n      }\n    });\n\n    this.view.addEventListener('touchcancel', (event: TouchEvent) => {\n      for (let i = 0; i < event.changedTouches.length; ++i) {\n        const adaptedEvent: AdaptedEvent = this.mapEvent(\n          event,\n          EventTypes.CANCEL,\n          i,\n          TouchEventType.CANCELLED\n        );\n\n        //@ts-ignore touchType field does exist\n        if (event.changedTouches[i].touchType === 'stylus') {\n          continue;\n        }\n\n        this.onPointerCancel(adaptedEvent);\n        this.markAsOutOfBounds(adaptedEvent.pointerId);\n        this.activePointersCounter = 0;\n      }\n    });\n  }\n\n  protected mapEvent(\n    event: TouchEvent,\n    eventType: EventTypes,\n    index: number,\n    touchEventType: TouchEventType\n  ): AdaptedEvent {\n    const rect = this.view.getBoundingClientRect();\n    const clientX = event.changedTouches[index].clientX;\n    const clientY = event.changedTouches[index].clientY;\n\n    return {\n      x: clientX,\n      y: clientY,\n      offsetX: clientX - rect.left,\n      offsetY: clientY - rect.top,\n      pointerId: event.changedTouches[index].identifier,\n      eventType: eventType,\n      pointerType: PointerType.TOUCH,\n      buttons: MouseButtons.NONE,\n      time: event.timeStamp,\n      allTouches: event.touches,\n      changedTouches: event.changedTouches,\n      touchEventType: touchEventType,\n    };\n  }\n}\n"]}