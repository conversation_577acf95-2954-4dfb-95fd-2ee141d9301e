{"version": 3, "sources": ["GestureHandlerRootView.android.tsx"], "names": ["GestureHandlerRootView", "props"], "mappings": ";;;;;;;AAAA;;AAGA;;AACA;;AACA;;;;;;;;AAKe,SAASA,sBAAT,CACbC,KADa,EAEb;AACA;AACA;AACA;AACA;AAEA,sBACE,oBAAC,sCAAD,CAA+B,QAA/B;AAAwC,IAAA,KAAK;AAA7C,kBACE,oBAAC,gDAAD,EAA2CA,KAA3C,CADF,CADF;AAKD", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport { ViewProps } from 'react-native';\nimport { maybeInitializeFabric } from '../init';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\nimport GestureHandlerRootViewNativeComponent from '../specs/RNGestureHandlerRootViewNativeComponent';\n\nexport interface GestureHandlerRootViewProps\n  extends PropsWithChildren<ViewProps> {}\n\nexport default function GestureHandlerRootView(\n  props: GestureHandlerRootViewProps\n) {\n  // try initialize fabric on the first render, at this point we can\n  // reliably check if fabric is enabled (the function contains a flag\n  // to make sure it's called only once)\n  maybeInitializeFabric();\n\n  return (\n    <GestureHandlerRootViewContext.Provider value>\n      <GestureHandlerRootViewNativeComponent {...props} />\n    </GestureHandlerRootViewContext.Provider>\n  );\n}\n"]}