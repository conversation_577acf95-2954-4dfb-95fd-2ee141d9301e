{"version": 3, "sources": ["NativeViewGestureHandler.ts"], "names": ["Platform", "State", "DEFAULT_TOUCH_SLOP", "Gesture<PERSON>andler", "NativeViewGestureHandler", "init", "ref", "propsRef", "setShouldCancelWhenOutside", "OS", "view", "delegate", "get<PERSON>iew", "style", "hasAttribute", "buttonRole", "updateGestureConfig", "enabled", "props", "config", "shouldActivateOnStart", "undefined", "disallowInterruption", "resetConfig", "onPointerDown", "event", "tracker", "addToTracker", "newPointerAction", "onPointerAdd", "startX", "getLastAvgX", "startY", "getLastAvgY", "currentState", "UNDETERMINED", "begin", "activate", "onPointerMove", "track", "dx", "dy", "distSq", "minDistSq", "ACTIVE", "cancel", "BEGAN", "onPointerLeave", "onPointerUp", "onUp", "onPointerRemove", "removeFromTracker", "pointerId", "getTrackedPointersCount", "end", "fail", "shouldRecognizeSimultaneously", "handler", "getState", "disallowsInterruption", "canBeInterrupted", "getTag", "shouldBeCancelledByOther", "_handler"], "mappings": ";;AAAA,SAASA,QAAT,QAAyB,cAAzB;AACA,SAASC,KAAT,QAAsB,aAAtB;AACA,SAASC,kBAAT,QAAmC,cAAnC;AAGA,OAAOC,cAAP,MAA2B,kBAA3B;AACA,eAAe,MAAMC,wBAAN,SAAuCD,cAAvC,CAAsD;AAAA;AAAA;;AAAA;;AAAA,mDAKnC,KALmC;;AAAA,kDAMpC,KANoC;;AAAA,oCAQlD,CARkD;;AAAA,oCASlD,CATkD;;AAAA,uCAU/CD,kBAAkB,GAAGA,kBAV0B;AAAA;;AAY5DG,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAwD;AACjE,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AAEA,SAAKC,0BAAL,CAAgC,IAAhC;;AAEA,QAAIR,QAAQ,CAACS,EAAT,KAAgB,KAApB,EAA2B;AACzB;AACD;;AAED,UAAMC,IAAI,GAAG,KAAKC,QAAL,CAAcC,OAAd,EAAb;AAEAF,IAAAA,IAAI,CAACG,KAAL,CAAW,aAAX,IAA4B,MAA5B,CAXiE,CAajE;;AACAH,IAAAA,IAAI,CAACG,KAAL,CAAW,oBAAX,IAAmC,MAAnC;;AAEA,QAAIH,IAAI,CAACI,YAAL,CAAkB,MAAlB,CAAJ,EAA+B;AAC7B,WAAKC,UAAL,GAAkB,IAAlB;AACD,KAFD,MAEO;AACL,WAAKA,UAAL,GAAkB,KAAlB;AACD;AACF;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;;AAEA,QAAI,KAAKC,MAAL,CAAYC,qBAAZ,KAAsCC,SAA1C,EAAqD;AACnD,WAAKD,qBAAL,GAA6B,KAAKD,MAAL,CAAYC,qBAAzC;AACD;;AACD,QAAI,KAAKD,MAAL,CAAYG,oBAAZ,KAAqCD,SAAzC,EAAoD;AAClD,WAAKC,oBAAL,GAA4B,KAAKH,MAAL,CAAYG,oBAAxC;AACD;AACF;;AAESC,EAAAA,WAAW,GAAS;AAC5B,UAAMA,WAAN;AACD;;AAESC,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AACA,SAAKG,gBAAL;AACD;;AAESC,EAAAA,YAAY,CAACJ,KAAD,EAA4B;AAChD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMI,YAAN,CAAmBJ,KAAnB;AACA,SAAKG,gBAAL;AACD;;AAEOA,EAAAA,gBAAgB,GAAS;AAC/B,SAAKE,MAAL,GAAc,KAAKJ,OAAL,CAAaK,WAAb,EAAd;AACA,SAAKC,MAAL,GAAc,KAAKN,OAAL,CAAaO,WAAb,EAAd;;AAEA,QAAI,KAAKC,YAAL,KAAsBjC,KAAK,CAACkC,YAAhC,EAA8C;AAC5C;AACD;;AAED,SAAKC,KAAL;;AACA,QAAI,KAAKrB,UAAT,EAAqB;AACnB,WAAKsB,QAAL;AACD;AACF;;AAESC,EAAAA,aAAa,CAACb,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaa,KAAb,CAAmBd,KAAnB;AAEA,UAAMe,EAAE,GAAG,KAAKV,MAAL,GAAc,KAAKJ,OAAL,CAAaK,WAAb,EAAzB;AACA,UAAMU,EAAE,GAAG,KAAKT,MAAL,GAAc,KAAKN,OAAL,CAAaO,WAAb,EAAzB;AACA,UAAMS,MAAM,GAAGF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAA9B;;AAEA,QAAIC,MAAM,IAAI,KAAKC,SAAnB,EAA8B;AAC5B,UAAI,KAAK5B,UAAL,IAAmB,KAAKmB,YAAL,KAAsBjC,KAAK,CAAC2C,MAAnD,EAA2D;AACzD,aAAKC,MAAL;AACD,OAFD,MAEO,IAAI,CAAC,KAAK9B,UAAN,IAAoB,KAAKmB,YAAL,KAAsBjC,KAAK,CAAC6C,KAApD,EAA2D;AAChE,aAAKT,QAAL;AACD;AACF;AACF;;AAESU,EAAAA,cAAc,GAAS;AAC/B,QACE,KAAKb,YAAL,KAAsBjC,KAAK,CAAC6C,KAA5B,IACA,KAAKZ,YAAL,KAAsBjC,KAAK,CAAC2C,MAF9B,EAGE;AACA,WAAKC,MAAL;AACD;AACF;;AAESG,EAAAA,WAAW,CAACvB,KAAD,EAA4B;AAC/C,UAAMuB,WAAN,CAAkBvB,KAAlB;AACA,SAAKwB,IAAL,CAAUxB,KAAV;AACD;;AAESyB,EAAAA,eAAe,CAACzB,KAAD,EAA4B;AACnD,UAAMyB,eAAN,CAAsBzB,KAAtB;AACA,SAAKwB,IAAL,CAAUxB,KAAV;AACD;;AAEOwB,EAAAA,IAAI,CAACxB,KAAD,EAA4B;AACtC,SAAKC,OAAL,CAAayB,iBAAb,CAA+B1B,KAAK,CAAC2B,SAArC;;AAEA,QAAI,KAAK1B,OAAL,CAAa2B,uBAAb,OAA2C,CAA/C,EAAkD;AAChD,UAAI,KAAKnB,YAAL,KAAsBjC,KAAK,CAAC2C,MAAhC,EAAwC;AACtC,aAAKU,GAAL;AACD,OAFD,MAEO;AACL,aAAKC,IAAL;AACD;AACF;AACF;;AAEMC,EAAAA,6BAA6B,CAACC,OAAD,EAAmC;AACrE,QAAI,MAAMD,6BAAN,CAAoCC,OAApC,CAAJ,EAAkD;AAChD,aAAO,IAAP;AACD;;AAED,QACEA,OAAO,YAAYrD,wBAAnB,IACAqD,OAAO,CAACC,QAAR,OAAuBzD,KAAK,CAAC2C,MAD7B,IAEAa,OAAO,CAACE,qBAAR,EAHF,EAIE;AACA,aAAO,KAAP;AACD;;AAED,UAAMC,gBAAgB,GAAG,CAAC,KAAKtC,oBAA/B;;AAEA,QACE,KAAKY,YAAL,KAAsBjC,KAAK,CAAC2C,MAA5B,IACAa,OAAO,CAACC,QAAR,OAAuBzD,KAAK,CAAC2C,MAD7B,IAEAgB,gBAHF,EAIE;AACA,aAAO,KAAP;AACD;;AAED,WACE,KAAK1B,YAAL,KAAsBjC,KAAK,CAAC2C,MAA5B,IACAgB,gBADA,IAEAH,OAAO,CAACI,MAAR,KAAmB,CAHrB;AAKD;;AAEMC,EAAAA,wBAAwB,CAACC,QAAD,EAAoC;AACjE,WAAO,CAAC,KAAKzC,oBAAb;AACD;;AAEMqC,EAAAA,qBAAqB,GAAY;AACtC,WAAO,KAAKrC,oBAAZ;AACD;;AA/JkE", "sourcesContent": ["import { Platform } from 'react-native';\nimport { State } from '../../State';\nimport { DEFAULT_TOUCH_SLOP } from '../constants';\nimport { AdaptedEvent, Config } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\nexport default class NativeViewGestureHandler extends GestureHandler {\n  private buttonRole!: boolean;\n\n  //TODO: Implement logic for activation on start\n  //@ts-ignore Logic yet to be implemented\n  private shouldActivateOnStart = false;\n  private disallowInterruption = false;\n\n  private startX = 0;\n  private startY = 0;\n  private minDistSq = DEFAULT_TOUCH_SLOP * DEFAULT_TOUCH_SLOP;\n\n  public init(ref: number, propsRef: React.RefObject<unknown>): void {\n    super.init(ref, propsRef);\n\n    this.setShouldCancelWhenOutside(true);\n\n    if (Platform.OS !== 'web') {\n      return;\n    }\n\n    const view = this.delegate.getView() as HTMLElement;\n\n    view.style['touchAction'] = 'auto';\n\n    //@ts-ignore Turns on defualt touch behavior on Safari\n    view.style['WebkitTouchCallout'] = 'auto';\n\n    if (view.hasAttribute('role')) {\n      this.buttonRole = true;\n    } else {\n      this.buttonRole = false;\n    }\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n\n    if (this.config.shouldActivateOnStart !== undefined) {\n      this.shouldActivateOnStart = this.config.shouldActivateOnStart;\n    }\n    if (this.config.disallowInterruption !== undefined) {\n      this.disallowInterruption = this.config.disallowInterruption;\n    }\n  }\n\n  protected resetConfig(): void {\n    super.resetConfig();\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n    this.newPointerAction();\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n    this.newPointerAction();\n  }\n\n  private newPointerAction(): void {\n    this.startX = this.tracker.getLastAvgX();\n    this.startY = this.tracker.getLastAvgY();\n\n    if (this.currentState !== State.UNDETERMINED) {\n      return;\n    }\n\n    this.begin();\n    if (this.buttonRole) {\n      this.activate();\n    }\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n\n    const dx = this.startX - this.tracker.getLastAvgX();\n    const dy = this.startY - this.tracker.getLastAvgY();\n    const distSq = dx * dx + dy * dy;\n\n    if (distSq >= this.minDistSq) {\n      if (this.buttonRole && this.currentState === State.ACTIVE) {\n        this.cancel();\n      } else if (!this.buttonRole && this.currentState === State.BEGAN) {\n        this.activate();\n      }\n    }\n  }\n\n  protected onPointerLeave(): void {\n    if (\n      this.currentState === State.BEGAN ||\n      this.currentState === State.ACTIVE\n    ) {\n      this.cancel();\n    }\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.onUp(event);\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.onUp(event);\n  }\n\n  private onUp(event: AdaptedEvent): void {\n    this.tracker.removeFromTracker(event.pointerId);\n\n    if (this.tracker.getTrackedPointersCount() === 0) {\n      if (this.currentState === State.ACTIVE) {\n        this.end();\n      } else {\n        this.fail();\n      }\n    }\n  }\n\n  public shouldRecognizeSimultaneously(handler: GestureHandler): boolean {\n    if (super.shouldRecognizeSimultaneously(handler)) {\n      return true;\n    }\n\n    if (\n      handler instanceof NativeViewGestureHandler &&\n      handler.getState() === State.ACTIVE &&\n      handler.disallowsInterruption()\n    ) {\n      return false;\n    }\n\n    const canBeInterrupted = !this.disallowInterruption;\n\n    if (\n      this.currentState === State.ACTIVE &&\n      handler.getState() === State.ACTIVE &&\n      canBeInterrupted\n    ) {\n      return false;\n    }\n\n    return (\n      this.currentState === State.ACTIVE &&\n      canBeInterrupted &&\n      handler.getTag() > 0\n    );\n  }\n\n  public shouldBeCancelledByOther(_handler: GestureHandler): boolean {\n    return !this.disallowInterruption;\n  }\n\n  public disallowsInterruption(): boolean {\n    return this.disallowInterruption;\n  }\n}\n"]}