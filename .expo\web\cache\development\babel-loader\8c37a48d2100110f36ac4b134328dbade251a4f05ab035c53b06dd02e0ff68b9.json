{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport { UnavailabilityError } from 'expo-modules-core';\nimport ExpoSplashScreen from \"./ExpoSplashScreen\";\nexport function preventAutoHideAsync() {\n  return _preventAutoHideAsync.apply(this, arguments);\n}\nfunction _preventAutoHideAsync() {\n  _preventAutoHideAsync = _asyncToGenerator(function* () {\n    if (!ExpoSplashScreen.preventAutoHideAsync) {\n      throw new UnavailabilityError('expo-splash-screen', 'preventAutoHideAsync');\n    }\n    return yield ExpoSplashScreen.preventAutoHideAsync();\n  });\n  return _preventAutoHideAsync.apply(this, arguments);\n}\nexport function hideAsync() {\n  return _hideAsync.apply(this, arguments);\n}\nfunction _hideAsync() {\n  _hideAsync = _asyncToGenerator(function* () {\n    if (!ExpoSplashScreen.hideAsync) {\n      throw new UnavailabilityError('expo-splash-screen', 'hideAsync');\n    }\n    return yield ExpoSplashScreen.hideAsync();\n  });\n  return _hideAsync.apply(this, arguments);\n}\nexport function hide() {\n  console.warn('SplashScreen.hide() is deprecated in favour of SplashScreen.hideAsync()');\n  hideAsync();\n}\nexport function preventAutoHide() {\n  console.warn('SplashScreen.preventAutoHide() is deprecated in favour of SplashScreen.preventAutoHideAsync()');\n  preventAutoHideAsync();\n}", "map": {"version": 3, "names": ["UnavailabilityError", "ExpoSplashScreen", "preventAutoHideAsync", "_preventAutoHideAsync", "apply", "arguments", "_asyncToGenerator", "<PERSON><PERSON><PERSON>", "_hideAsync", "hide", "console", "warn", "preventAutoHide"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-splash-screen\\src\\SplashScreen.ts"], "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport ExpoSplashScreen from './ExpoSplashScreen';\n\n// @needsAudit\n/**\n * Makes the native splash screen (configured in `app.json`) remain visible until `hideAsync` is called.\n *\n * > **Important note**: It is recommended to call this in global scope without awaiting, rather than\n * > inside React components or hooks, because otherwise this might be called too late,\n * > when the splash screen is already hidden.\n *\n * @example\n * ```ts\n * import * as SplashScreen from 'expo-splash-screen';\n *\n * SplashScreen.preventAutoHideAsync();\n *\n * export default function App() {\n *  // ...\n * }\n * ```\n */\nexport async function preventAutoHideAsync(): Promise<boolean> {\n  if (!ExpoSplashScreen.preventAutoHideAsync) {\n    throw new UnavailabilityError('expo-splash-screen', 'preventAutoHideAsync');\n  }\n  return await ExpoSplashScreen.preventAutoHideAsync();\n}\n\n// @needsAudit\n/**\n * Hides the native splash screen immediately. Be careful to ensure that your app has content ready\n * to display when you hide the splash screen, or you may see a blank screen briefly. See the\n * [\"Usage\"](#usage) section for an example.\n */\nexport async function hideAsync(): Promise<boolean> {\n  if (!ExpoSplashScreen.hideAsync) {\n    throw new UnavailabilityError('expo-splash-screen', 'hideAsync');\n  }\n  return await ExpoSplashScreen.hideAsync();\n}\n\n/**\n * @deprecated Use `SplashScreen.hideAsync()` instead\n * @ignore\n */\nexport function hide(): void {\n  console.warn('SplashScreen.hide() is deprecated in favour of SplashScreen.hideAsync()');\n  hideAsync();\n}\n\n/**\n * @deprecated Use `SplashScreen.preventAutoHideAsync()` instead\n * @ignore\n */\nexport function preventAutoHide(): void {\n  console.warn(\n    'SplashScreen.preventAutoHide() is deprecated in favour of SplashScreen.preventAutoHideAsync()'\n  );\n  preventAutoHideAsync();\n}\n"], "mappings": ";AAAA,SAASA,mBAAmB,QAAQ,mBAAmB;AAEvD,OAAOC,gBAAgB;AAqBvB,gBAAsBC,oBAAoBA,CAAA;EAAA,OAAAC,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAKzC,SAAAF,sBAAA;EAAAA,qBAAA,GAAAG,iBAAA,CALM,aAAmC;IACxC,IAAI,CAACL,gBAAgB,CAACC,oBAAoB,EAAE;MAC1C,MAAM,IAAIF,mBAAmB,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;;IAE7E,aAAaC,gBAAgB,CAACC,oBAAoB,EAAE;EACtD,CAAC;EAAA,OAAAC,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAQD,gBAAsBE,SAASA,CAAA;EAAA,OAAAC,UAAA,CAAAJ,KAAA,OAAAC,SAAA;AAAA;AAK9B,SAAAG,WAAA;EAAAA,UAAA,GAAAF,iBAAA,CALM,aAAwB;IAC7B,IAAI,CAACL,gBAAgB,CAACM,SAAS,EAAE;MAC/B,MAAM,IAAIP,mBAAmB,CAAC,oBAAoB,EAAE,WAAW,CAAC;;IAElE,aAAaC,gBAAgB,CAACM,SAAS,EAAE;EAC3C,CAAC;EAAA,OAAAC,UAAA,CAAAJ,KAAA,OAAAC,SAAA;AAAA;AAMD,OAAM,SAAUI,IAAIA,CAAA;EAClBC,OAAO,CAACC,IAAI,CAAC,yEAAyE,CAAC;EACvFJ,SAAS,EAAE;AACb;AAMA,OAAM,SAAUK,eAAeA,CAAA;EAC7BF,OAAO,CAACC,IAAI,CACV,+FAA+F,CAChG;EACDT,oBAAoB,EAAE;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}