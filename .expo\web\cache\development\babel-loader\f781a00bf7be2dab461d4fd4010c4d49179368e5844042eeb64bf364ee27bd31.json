{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Colors, Typography, Spacing } from \"../constants\";\nimport Button from \"./Button\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar ErrorBoundary = function (_React$Component) {\n  function ErrorBoundary(props) {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    _this = _callSuper(this, ErrorBoundary, [props]);\n    _this.handleRetry = function () {\n      _this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null\n      });\n    };\n    _this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n    return _this;\n  }\n  _inherits(ErrorBoundary, _React$Component);\n  return _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, errorInfo) {\n      console.error('ErrorBoundary caught an error:', error, errorInfo);\n      this.setState({\n        error: error,\n        errorInfo: errorInfo\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (this.state.hasError) {\n        return _jsx(View, {\n          style: styles.container,\n          children: _jsxs(ScrollView, {\n            contentContainerStyle: styles.content,\n            children: [_jsx(View, {\n              style: styles.iconContainer,\n              children: _jsx(MaterialIcons, {\n                name: \"error-outline\",\n                size: 64,\n                color: Colors.error\n              })\n            }), _jsx(Text, {\n              style: styles.title,\n              children: \"Oops! Terjadi Kesalahan\"\n            }), _jsx(Text, {\n              style: styles.message,\n              children: \"Aplikasi mengalami masalah yang tidak terduga. Tim kami akan segera memperbaikinya.\"\n            }), _jsx(View, {\n              style: styles.buttonContainer,\n              children: _jsx(Button, {\n                title: \"Coba Lagi\",\n                onPress: this.handleRetry,\n                icon: \"refresh\",\n                style: styles.retryButton\n              })\n            }), __DEV__ && this.state.error && _jsxs(View, {\n              style: styles.errorDetails,\n              children: [_jsx(Text, {\n                style: styles.errorTitle,\n                children: \"Error Details (Development Mode):\"\n              }), _jsx(Text, {\n                style: styles.errorText,\n                children: this.state.error.toString()\n              }), this.state.errorInfo && _jsx(Text, {\n                style: styles.errorText,\n                children: this.state.errorInfo.componentStack\n              })]\n            })]\n          })\n        });\n      }\n      return this.props.children;\n    }\n  }], [{\n    key: \"getDerivedStateFromError\",\n    value: function getDerivedStateFromError(error) {\n      return {\n        hasError: true\n      };\n    }\n  }]);\n}(React.Component);\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: Colors.background\n  },\n  content: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: Spacing.padding.xl\n  },\n  iconContainer: {\n    marginBottom: Spacing.lg\n  },\n  title: _objectSpread(_objectSpread({}, Typography.h2), {}, {\n    color: Colors.onSurface,\n    textAlign: 'center',\n    marginBottom: Spacing.md\n  }),\n  message: _objectSpread(_objectSpread({}, Typography.body1), {}, {\n    color: Colors.onSurfaceVariant,\n    textAlign: 'center',\n    marginBottom: Spacing.xl,\n    lineHeight: 24\n  }),\n  buttonContainer: {\n    width: '100%',\n    maxWidth: 200\n  },\n  retryButton: {\n    marginBottom: Spacing.lg\n  },\n  errorDetails: {\n    marginTop: Spacing.xl,\n    padding: Spacing.padding.md,\n    backgroundColor: Colors.errorLight,\n    borderRadius: Spacing.borderRadius.md,\n    width: '100%'\n  },\n  errorTitle: _objectSpread(_objectSpread({}, Typography.subtitle1), {}, {\n    color: Colors.error,\n    marginBottom: Spacing.sm\n  }),\n  errorText: _objectSpread(_objectSpread({}, Typography.caption), {}, {\n    color: Colors.error,\n    fontFamily: 'monospace'\n  })\n});\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "ScrollView", "MaterialIcons", "Colors", "Typography", "Spacing", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "props", "_this", "_classCallCheck", "_callSuper", "handleRetry", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "state", "_inherits", "_createClass", "key", "value", "componentDidCatch", "console", "render", "style", "styles", "container", "children", "contentContainerStyle", "content", "iconContainer", "name", "size", "color", "title", "message", "buttonContainer", "onPress", "icon", "retryButton", "__DEV__", "errorDetails", "errorTitle", "errorText", "toString", "componentStack", "getDerivedStateFromError", "Component", "create", "flex", "backgroundColor", "background", "justifyContent", "alignItems", "padding", "xl", "marginBottom", "lg", "_objectSpread", "h2", "onSurface", "textAlign", "md", "body1", "onSurfaceVariant", "lineHeight", "width", "max<PERSON><PERSON><PERSON>", "marginTop", "errorLight", "borderRadius", "subtitle1", "sm", "caption", "fontFamily"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/components/ErrorBoundary.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  ScrollView,\n} from 'react-native';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Colors, Typography, Spacing } from '../constants';\nimport Button from './Button';\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Log the error to an error reporting service\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({\n      error: error,\n      errorInfo: errorInfo,\n    });\n  }\n\n  handleRetry = () => {\n    this.setState({ hasError: false, error: null, errorInfo: null });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      return (\n        <View style={styles.container}>\n          <ScrollView contentContainerStyle={styles.content}>\n            <View style={styles.iconContainer}>\n              <MaterialIcons\n                name=\"error-outline\"\n                size={64}\n                color={Colors.error}\n              />\n            </View>\n            \n            <Text style={styles.title}>Oops! Terjadi Kesalahan</Text>\n            <Text style={styles.message}>\n              Aplikasi mengalami masalah yang tidak terduga. Tim kami akan segera memperbaikinya.\n            </Text>\n            \n            <View style={styles.buttonContainer}>\n              <Button\n                title=\"Coba Lagi\"\n                onPress={this.handleRetry}\n                icon=\"refresh\"\n                style={styles.retryButton}\n              />\n            </View>\n            \n            {__DEV__ && this.state.error && (\n              <View style={styles.errorDetails}>\n                <Text style={styles.errorTitle}>Error Details (Development Mode):</Text>\n                <Text style={styles.errorText}>\n                  {this.state.error.toString()}\n                </Text>\n                {this.state.errorInfo && (\n                  <Text style={styles.errorText}>\n                    {this.state.errorInfo.componentStack}\n                  </Text>\n                )}\n              </View>\n            )}\n          </ScrollView>\n        </View>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: Colors.background,\n  },\n  content: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: Spacing.padding.xl,\n  },\n  iconContainer: {\n    marginBottom: Spacing.lg,\n  },\n  title: {\n    ...Typography.h2,\n    color: Colors.onSurface,\n    textAlign: 'center',\n    marginBottom: Spacing.md,\n  },\n  message: {\n    ...Typography.body1,\n    color: Colors.onSurfaceVariant,\n    textAlign: 'center',\n    marginBottom: Spacing.xl,\n    lineHeight: 24,\n  },\n  buttonContainer: {\n    width: '100%',\n    maxWidth: 200,\n  },\n  retryButton: {\n    marginBottom: Spacing.lg,\n  },\n  errorDetails: {\n    marginTop: Spacing.xl,\n    padding: Spacing.padding.md,\n    backgroundColor: Colors.errorLight,\n    borderRadius: Spacing.borderRadius.md,\n    width: '100%',\n  },\n  errorTitle: {\n    ...Typography.subtitle1,\n    color: Colors.error,\n    marginBottom: Spacing.sm,\n  },\n  errorText: {\n    ...Typography.caption,\n    color: Colors.error,\n    fontFamily: 'monospace',\n  },\n});\n\nexport default ErrorBoundary;\n"], "mappings": ";;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAO1B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO;AACpC,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAAA,IAExBC,aAAa,aAAAC,gBAAA;EACjB,SAAAD,cAAYE,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAJ,aAAA;IACjBG,KAAA,GAAAE,UAAA,OAAAL,aAAA,GAAME,KAAK;IAAEC,KAAA,CAkBfG,WAAW,GAAG,YAAM;MAClBH,KAAA,CAAKI,QAAQ,CAAC;QAAEC,QAAQ,EAAE,KAAK;QAAEC,KAAK,EAAE,IAAI;QAAEC,SAAS,EAAE;MAAK,CAAC,CAAC;IAClE,CAAC;IAnBCP,KAAA,CAAKQ,KAAK,GAAG;MAAEH,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;IAAC,OAAAP,KAAA;EACjE;EAACS,SAAA,CAAAZ,aAAA,EAAAC,gBAAA;EAAA,OAAAY,YAAA,CAAAb,aAAA;IAAAc,GAAA;IAAAC,KAAA,EAOD,SAAAC,iBAAiBA,CAACP,KAAK,EAAEC,SAAS,EAAE;MAElCO,OAAO,CAACR,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEC,SAAS,CAAC;MACjE,IAAI,CAACH,QAAQ,CAAC;QACZE,KAAK,EAAEA,KAAK;QACZC,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAMD,SAAAG,MAAMA,CAAA,EAAG;MACP,IAAI,IAAI,CAACP,KAAK,CAACH,QAAQ,EAAE;QAEvB,OACEX,IAAA,CAACV,IAAI;UAACgC,KAAK,EAAEC,MAAM,CAACC,SAAU;UAAAC,QAAA,EAC5BvB,KAAA,CAACT,UAAU;YAACiC,qBAAqB,EAAEH,MAAM,CAACI,OAAQ;YAAAF,QAAA,GAChDzB,IAAA,CAACV,IAAI;cAACgC,KAAK,EAAEC,MAAM,CAACK,aAAc;cAAAH,QAAA,EAChCzB,IAAA,CAACN,aAAa;gBACZmC,IAAI,EAAC,eAAe;gBACpBC,IAAI,EAAE,EAAG;gBACTC,KAAK,EAAEpC,MAAM,CAACiB;cAAM,CACrB;YAAC,CACE,CAAC,EAEPZ,IAAA,CAACT,IAAI;cAAC+B,KAAK,EAAEC,MAAM,CAACS,KAAM;cAAAP,QAAA,EAAC;YAAuB,CAAM,CAAC,EACzDzB,IAAA,CAACT,IAAI;cAAC+B,KAAK,EAAEC,MAAM,CAACU,OAAQ;cAAAR,QAAA,EAAC;YAE7B,CAAM,CAAC,EAEPzB,IAAA,CAACV,IAAI;cAACgC,KAAK,EAAEC,MAAM,CAACW,eAAgB;cAAAT,QAAA,EAClCzB,IAAA,CAACF,MAAM;gBACLkC,KAAK,EAAC,WAAW;gBACjBG,OAAO,EAAE,IAAI,CAAC1B,WAAY;gBAC1B2B,IAAI,EAAC,SAAS;gBACdd,KAAK,EAAEC,MAAM,CAACc;cAAY,CAC3B;YAAC,CACE,CAAC,EAENC,OAAO,IAAI,IAAI,CAACxB,KAAK,CAACF,KAAK,IAC1BV,KAAA,CAACZ,IAAI;cAACgC,KAAK,EAAEC,MAAM,CAACgB,YAAa;cAAAd,QAAA,GAC/BzB,IAAA,CAACT,IAAI;gBAAC+B,KAAK,EAAEC,MAAM,CAACiB,UAAW;gBAAAf,QAAA,EAAC;cAAiC,CAAM,CAAC,EACxEzB,IAAA,CAACT,IAAI;gBAAC+B,KAAK,EAAEC,MAAM,CAACkB,SAAU;gBAAAhB,QAAA,EAC3B,IAAI,CAACX,KAAK,CAACF,KAAK,CAAC8B,QAAQ,CAAC;cAAC,CACxB,CAAC,EACN,IAAI,CAAC5B,KAAK,CAACD,SAAS,IACnBb,IAAA,CAACT,IAAI;gBAAC+B,KAAK,EAAEC,MAAM,CAACkB,SAAU;gBAAAhB,QAAA,EAC3B,IAAI,CAACX,KAAK,CAACD,SAAS,CAAC8B;cAAc,CAChC,CACP;YAAA,CACG,CACP;UAAA,CACS;QAAC,CACT,CAAC;MAEX;MAEA,OAAO,IAAI,CAACtC,KAAK,CAACoB,QAAQ;IAC5B;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAjED,SAAO0B,wBAAwBA,CAAChC,KAAK,EAAE;MAErC,OAAO;QAAED,QAAQ,EAAE;MAAK,CAAC;IAC3B;EAAC;AAAA,EATyBtB,KAAK,CAACwD,SAAS;AA0E3C,IAAMtB,MAAM,GAAG/B,UAAU,CAACsD,MAAM,CAAC;EAC/BtB,SAAS,EAAE;IACTuB,IAAI,EAAE,CAAC;IACPC,eAAe,EAAErD,MAAM,CAACsD;EAC1B,CAAC;EACDtB,OAAO,EAAE;IACPoB,IAAI,EAAE,CAAC;IACPG,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAEvD,OAAO,CAACuD,OAAO,CAACC;EAC3B,CAAC;EACDzB,aAAa,EAAE;IACb0B,YAAY,EAAEzD,OAAO,CAAC0D;EACxB,CAAC;EACDvB,KAAK,EAAAwB,aAAA,CAAAA,aAAA,KACA5D,UAAU,CAAC6D,EAAE;IAChB1B,KAAK,EAAEpC,MAAM,CAAC+D,SAAS;IACvBC,SAAS,EAAE,QAAQ;IACnBL,YAAY,EAAEzD,OAAO,CAAC+D;EAAE,EACzB;EACD3B,OAAO,EAAAuB,aAAA,CAAAA,aAAA,KACF5D,UAAU,CAACiE,KAAK;IACnB9B,KAAK,EAAEpC,MAAM,CAACmE,gBAAgB;IAC9BH,SAAS,EAAE,QAAQ;IACnBL,YAAY,EAAEzD,OAAO,CAACwD,EAAE;IACxBU,UAAU,EAAE;EAAE,EACf;EACD7B,eAAe,EAAE;IACf8B,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE;EACZ,CAAC;EACD5B,WAAW,EAAE;IACXiB,YAAY,EAAEzD,OAAO,CAAC0D;EACxB,CAAC;EACDhB,YAAY,EAAE;IACZ2B,SAAS,EAAErE,OAAO,CAACwD,EAAE;IACrBD,OAAO,EAAEvD,OAAO,CAACuD,OAAO,CAACQ,EAAE;IAC3BZ,eAAe,EAAErD,MAAM,CAACwE,UAAU;IAClCC,YAAY,EAAEvE,OAAO,CAACuE,YAAY,CAACR,EAAE;IACrCI,KAAK,EAAE;EACT,CAAC;EACDxB,UAAU,EAAAgB,aAAA,CAAAA,aAAA,KACL5D,UAAU,CAACyE,SAAS;IACvBtC,KAAK,EAAEpC,MAAM,CAACiB,KAAK;IACnB0C,YAAY,EAAEzD,OAAO,CAACyE;EAAE,EACzB;EACD7B,SAAS,EAAAe,aAAA,CAAAA,aAAA,KACJ5D,UAAU,CAAC2E,OAAO;IACrBxC,KAAK,EAAEpC,MAAM,CAACiB,KAAK;IACnB4D,UAAU,EAAE;EAAW;AAE3B,CAAC,CAAC;AAEF,eAAerE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}