{"version": 3, "sources": ["RotationGestureHandler.ts"], "names": ["createHandler", "baseGestureHandlerProps", "rotationHandlerName", "RotationGestureHandler", "name", "allowedProps", "config"], "mappings": "AAAA,OAAOA,aAAP,MAA0B,iBAA1B;AACA,SAEEC,uBAFF,QAGO,wBAHP;AAmCA,OAAO,MAAMC,mBAAmB,GAAG,wBAA5B;AAGP;AACA,OAAO,MAAMC,sBAAsB,GAAGH,aAAa,CAGjD;AACAI,EAAAA,IAAI,EAAEF,mBADN;AAEAG,EAAAA,YAAY,EAAEJ,uBAFd;AAGAK,EAAAA,MAAM,EAAE;AAHR,CAHiD,CAA5C", "sourcesContent": ["import createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\nexport type RotationGestureHandlerEventPayload = {\n  /**\n   * Amount rotated, expressed in radians, from the gesture's focal point\n   * (anchor).\n   */\n  rotation: number;\n\n  /**\n   * X coordinate, expressed in points, of the gesture's central focal point\n   * (anchor).\n   */\n  anchorX: number;\n\n  /**\n   * Y coordinate, expressed in points, of the gesture's central focal point\n   * (anchor).\n   */\n  anchorY: number;\n\n  /**\n   *\n   * Instantaneous velocity, expressed in point units per second, of the\n   * gesture.\n   */\n  velocity: number;\n};\n\nexport interface RotationGestureHandlerProps\n  extends BaseGestureHandlerProps<RotationGestureHandlerEventPayload> {}\n\nexport const rotationHandlerName = 'RotationGestureHandler';\n\nexport type RotationGestureHandler = typeof RotationGestureHandler;\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const RotationGestureHandler = createHandler<\n  RotationGestureHandlerProps,\n  RotationGestureHandlerEventPayload\n>({\n  name: rotationHandlerName,\n  allowedProps: baseGestureHandlerProps,\n  config: {},\n});\n"]}