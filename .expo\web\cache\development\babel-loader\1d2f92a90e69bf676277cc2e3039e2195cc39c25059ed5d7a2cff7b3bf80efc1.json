{"ast": null, "code": "export var Colors = {\n  primary: '#2196F3',\n  primaryVariant: '#1976D2',\n  primaryLight: '#BBDEFB',\n  background: '#F8F9FA',\n  surface: '#FFFFFF',\n  surfaceVariant: '#F5F5F5',\n  onSurface: '#212121',\n  onSurfaceVariant: '#757575',\n  onPrimary: '#FFFFFF',\n  success: '#4CAF50',\n  successLight: '#C8E6C9',\n  warning: '#FF9800',\n  warningLight: '#FFE0B2',\n  error: '#FF5252',\n  errorLight: '#FFCDD2',\n  outline: '#E0E0E0',\n  outlineVariant: '#F0F0F0',\n  shadow: '#000000',\n  info: '#2196F3',\n  infoLight: '#E3F2FD',\n  overlay: 'rgba(0, 0, 0, 0.5)',\n  overlayLight: 'rgba(0, 0, 0, 0.1)',\n  cardBackground: '#FFFFFF',\n  cardShadow: 'rgba(0, 0, 0, 0.1)',\n  bottomNavBackground: '#FFFFFF',\n  bottomNavActive: '#2196F3',\n  bottomNavInactive: '#757575',\n  online: '#4CAF50',\n  offline: '#9E9E9E',\n  pending: '#FF9800',\n  gradientStart: '#2196F3',\n  gradientEnd: '#1976D2'\n};", "map": {"version": 3, "names": ["Colors", "primary", "primaryVariant", "primaryLight", "background", "surface", "surfaceVariant", "onSurface", "onSurfaceVariant", "onPrimary", "success", "successLight", "warning", "warningLight", "error", "errorLight", "outline", "outlineVariant", "shadow", "info", "infoLight", "overlay", "overlayLight", "cardBackground", "cardShadow", "bottomNavBackground", "bottomNavActive", "bottomNavInactive", "online", "offline", "pending", "gradientStart", "gradientEnd"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/constants/Colors.js"], "sourcesContent": ["// Material Design 3 Color Palette for PSG-BMI Portal\nexport const Colors = {\n  // Primary Colors\n  primary: '#2196F3',\n  primaryVariant: '#1976D2',\n  primaryLight: '#BBDEFB',\n  \n  // Background Colors\n  background: '#F8F9FA',\n  surface: '#FFFFFF',\n  surfaceVariant: '#F5F5F5',\n  \n  // Text Colors\n  onSurface: '#212121',\n  onSurfaceVariant: '#757575',\n  onPrimary: '#FFFFFF',\n  \n  // Accent Colors\n  success: '#4CAF50',\n  successLight: '#C8E6C9',\n  warning: '#FF9800',\n  warningLight: '#FFE0B2',\n  error: '#FF5252',\n  errorLight: '#FFCDD2',\n  \n  // Neutral Colors\n  outline: '#E0E0E0',\n  outlineVariant: '#F0F0F0',\n  shadow: '#000000',\n  \n  // Semantic Colors\n  info: '#2196F3',\n  infoLight: '#E3F2FD',\n  \n  // Overlay Colors\n  overlay: 'rgba(0, 0, 0, 0.5)',\n  overlayLight: 'rgba(0, 0, 0, 0.1)',\n  \n  // Card Colors\n  cardBackground: '#FFFFFF',\n  cardShadow: 'rgba(0, 0, 0, 0.1)',\n  \n  // Navigation Colors\n  bottomNavBackground: '#FFFFFF',\n  bottomNavActive: '#2196F3',\n  bottomNavInactive: '#757575',\n  \n  // Status Colors\n  online: '#4CAF50',\n  offline: '#9E9E9E',\n  pending: '#FF9800',\n  \n  // Gradient Colors\n  gradientStart: '#2196F3',\n  gradientEnd: '#1976D2',\n};\n"], "mappings": "AACA,OAAO,IAAMA,MAAM,GAAG;EAEpBC,OAAO,EAAE,SAAS;EAClBC,cAAc,EAAE,SAAS;EACzBC,YAAY,EAAE,SAAS;EAGvBC,UAAU,EAAE,SAAS;EACrBC,OAAO,EAAE,SAAS;EAClBC,cAAc,EAAE,SAAS;EAGzBC,SAAS,EAAE,SAAS;EACpBC,gBAAgB,EAAE,SAAS;EAC3BC,SAAS,EAAE,SAAS;EAGpBC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,SAAS;EACvBC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,SAAS;EACvBC,KAAK,EAAE,SAAS;EAChBC,UAAU,EAAE,SAAS;EAGrBC,OAAO,EAAE,SAAS;EAClBC,cAAc,EAAE,SAAS;EACzBC,MAAM,EAAE,SAAS;EAGjBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE,SAAS;EAGpBC,OAAO,EAAE,oBAAoB;EAC7BC,YAAY,EAAE,oBAAoB;EAGlCC,cAAc,EAAE,SAAS;EACzBC,UAAU,EAAE,oBAAoB;EAGhCC,mBAAmB,EAAE,SAAS;EAC9BC,eAAe,EAAE,SAAS;EAC1BC,iBAAiB,EAAE,SAAS;EAG5BC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAGlBC,aAAa,EAAE,SAAS;EACxBC,WAAW,EAAE;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}