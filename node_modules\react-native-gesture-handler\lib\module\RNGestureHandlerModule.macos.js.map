{"version": 3, "sources": ["RNGestureHandlerModule.macos.ts"], "names": ["isNewWebImplementationEnabled", "InteractionManager", "NodeManager", "PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "NativeViewGestureHandler", "ManualGestureHandler", "HammerNodeManager", "HammerNativeViewGestureHandler", "HammerPanGestureHandler", "HammerTapGestureHandler", "HammerLongPressGestureHandler", "HammerPinchGestureHandler", "HammerRotationGestureHandler", "HammerFlingGestureHandler", "GestureHandlerWebDelegate", "Gestures", "HammerGestures", "handleSetJSResponder", "_tag", "_blockNativeResponder", "handleClearJSResponder", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "Error", "GestureClass", "getInstance", "configureInteractions", "<PERSON><PERSON><PERSON><PERSON>", "updateGestureHandler", "attachGestureHandler", "newView", "_actionType", "propsRef", "init", "<PERSON><PERSON><PERSON><PERSON>", "newConfig", "updateGestureConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "mappings": "AACA,SAASA,6BAAT,QAA8C,8BAA9C,C,CAEA;;AACA,OAAOC,kBAAP,MAA+B,gCAA/B;AACA,OAAOC,WAAP,MAAwB,yBAAxB;AACA,OAAOC,iBAAP,MAA8B,kCAA9B;AACA,OAAOC,iBAAP,MAA8B,kCAA9B;AACA,OAAOC,uBAAP,MAAoC,wCAApC;AACA,OAAOC,mBAAP,MAAgC,oCAAhC;AACA,OAAOC,sBAAP,MAAmC,uCAAnC;AACA,OAAOC,mBAAP,MAAgC,oCAAhC;AACA,OAAOC,wBAAP,MAAqC,yCAArC;AACA,OAAOC,oBAAP,MAAiC,qCAAjC,C,CAEA;;AACA,OAAO,KAAKC,iBAAZ,MAAmC,0BAAnC;AACA,OAAOC,8BAAP,MAA2C,uCAA3C;AACA,OAAOC,uBAAP,MAAoC,gCAApC;AACA,OAAOC,uBAAP,MAAoC,gCAApC;AACA,OAAOC,6BAAP,MAA0C,sCAA1C;AACA,OAAOC,yBAAP,MAAsC,kCAAtC;AACA,OAAOC,4BAAP,MAAyC,qCAAzC;AACA,OAAOC,yBAAP,MAAsC,kCAAtC;AAEA,SAASC,yBAAT,QAA0C,uCAA1C;AAEA,OAAO,MAAMC,QAAQ,GAAG;AACtBX,EAAAA,wBADsB;AAEtBN,EAAAA,iBAFsB;AAGtBC,EAAAA,iBAHsB;AAItBC,EAAAA,uBAJsB;AAKtBC,EAAAA,mBALsB;AAMtBC,EAAAA,sBANsB;AAOtBC,EAAAA,mBAPsB;AAQtBE,EAAAA;AARsB,CAAjB;AAWP,OAAO,MAAMW,cAAc,GAAG;AAC5BZ,EAAAA,wBAAwB,EAAEG,8BADE;AAE5BT,EAAAA,iBAAiB,EAAEU,uBAFS;AAG5BT,EAAAA,iBAAiB,EAAEU,uBAHS;AAI5BT,EAAAA,uBAAuB,EAAEU,6BAJG;AAK5BT,EAAAA,mBAAmB,EAAEU,yBALO;AAM5BT,EAAAA,sBAAsB,EAAEU,4BANI;AAO5BT,EAAAA,mBAAmB,EAAEU;AAPO,CAAvB;AAUP,eAAe;AACbI,EAAAA,oBAAoB,CAACC,IAAD,EAAeC,qBAAf,EAA+C,CACjE;AACD,GAHY;;AAIbC,EAAAA,sBAAsB,GAAG,CACvB;AACD,GANY;;AAObC,EAAAA,oBAAoB,CAClBC,WADkB,EAElBC,UAFkB,EAGlBC,MAHkB,EAIlB;AACA,QAAI7B,6BAA6B,EAAjC,EAAqC;AACnC,UAAI,EAAE2B,WAAW,IAAIP,QAAjB,CAAJ,EAAgC;AAC9B,cAAM,IAAIU,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD;;AAED,YAAMI,YAAY,GAAGX,QAAQ,CAACO,WAAD,CAA7B;AACAzB,MAAAA,WAAW,CAACwB,oBAAZ,CACEE,UADF,EAEE,IAAIG,YAAJ,CAAiB,IAAIZ,yBAAJ,EAAjB,CAFF;AAIAlB,MAAAA,kBAAkB,CAAC+B,WAAnB,GAAiCC,qBAAjC,CACE/B,WAAW,CAACgC,UAAZ,CAAuBN,UAAvB,CADF,EAEEC,MAFF;AAID,KAhBD,MAgBO;AACL,UAAI,EAAEF,WAAW,IAAIN,cAAjB,CAAJ,EAAsC;AACpC,cAAM,IAAIS,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD,OALI,CAOL;AACA;;;AACA,YAAMI,YAAY,GAAGV,cAAc,CAACM,WAAD,CAAnC,CATK,CAUL;;AACAhB,MAAAA,iBAAiB,CAACe,oBAAlB,CAAuCE,UAAvC,EAAmD,IAAIG,YAAJ,EAAnD;AACD;;AAED,SAAKI,oBAAL,CAA0BP,UAA1B,EAAsCC,MAAtC;AACD,GA3CY;;AA4CbO,EAAAA,oBAAoB,CAClBR,UADkB,EAElBS,OAFkB,EAGlBC,WAHkB,EAIlBC,QAJkB,EAKlB;AACA,QAAIvC,6BAA6B,EAAjC,EAAqC;AACnCE,MAAAA,WAAW,CAACgC,UAAZ,CAAuBN,UAAvB,EAAmCY,IAAnC,CAAwCH,OAAxC,EAAiDE,QAAjD;AACD,KAFD,MAEO;AACL5B,MAAAA,iBAAiB,CAACuB,UAAlB,CAA6BN,UAA7B,EAAyCa,OAAzC,CAAiDJ,OAAjD,EAA0DE,QAA1D;AACD;AACF,GAvDY;;AAwDbJ,EAAAA,oBAAoB,CAACP,UAAD,EAAqBc,SAArB,EAAwC;AAC1D,QAAI1C,6BAA6B,EAAjC,EAAqC;AACnCE,MAAAA,WAAW,CAACgC,UAAZ,CAAuBN,UAAvB,EAAmCe,mBAAnC,CAAuDD,SAAvD;AAEAzC,MAAAA,kBAAkB,CAAC+B,WAAnB,GAAiCC,qBAAjC,CACE/B,WAAW,CAACgC,UAAZ,CAAuBN,UAAvB,CADF,EAEEc,SAFF;AAID,KAPD,MAOO;AACL/B,MAAAA,iBAAiB,CAACuB,UAAlB,CAA6BN,UAA7B,EAAyCe,mBAAzC,CAA6DD,SAA7D;AACD;AACF,GAnEY;;AAoEbE,EAAAA,qBAAqB,CAAChB,UAAD,EAAqB;AACxC,QAAI5B,6BAA6B,EAAjC,EAAqC;AACnC,aAAOE,WAAW,CAACgC,UAAZ,CAAuBN,UAAvB,CAAP;AACD,KAFD,MAEO;AACL,aAAOjB,iBAAiB,CAACuB,UAAlB,CAA6BN,UAA7B,CAAP;AACD;AACF,GA1EY;;AA2EbiB,EAAAA,kBAAkB,CAACjB,UAAD,EAAqB;AACrC,QAAI5B,6BAA6B,EAAjC,EAAqC;AACnCE,MAAAA,WAAW,CAAC2C,kBAAZ,CAA+BjB,UAA/B;AACD,KAFD,MAEO;AACLjB,MAAAA,iBAAiB,CAACkC,kBAAlB,CAAqCjB,UAArC;AACD;AACF,GAjFY;;AAkFb;AACAkB,EAAAA,eAAe,GAAG,CAAE;;AAnFP,CAAf", "sourcesContent": ["import { ActionType } from './ActionType';\nimport { isNewWebImplementationEnabled } from './EnableNewWebImplementation';\n\n//GestureHandlers\nimport InteractionManager from './web/tools/InteractionManager';\nimport NodeManager from './web/tools/NodeManager';\nimport PanGestureHandler from './web/handlers/PanGestureHandler';\nimport TapGestureHandler from './web/handlers/TapGestureHandler';\nimport LongPressGestureHandler from './web/handlers/LongPressGestureHandler';\nimport PinchGestureHandler from './web/handlers/PinchGestureHandler';\nimport RotationGestureHandler from './web/handlers/RotationGestureHandler';\nimport FlingGestureHandler from './web/handlers/FlingGestureHandler';\nimport NativeViewGestureHandler from './web/handlers/NativeViewGestureHandler';\nimport ManualGestureHandler from './web/handlers/ManualGestureHandler';\n\n//Hammer Handlers\nimport * as HammerNodeManager from './web_hammer/NodeManager';\nimport HammerNativeViewGestureHandler from './web_hammer/NativeViewGestureHandler';\nimport HammerPanGestureHandler from './web_hammer/PanGestureHandler';\nimport HammerTapGestureHandler from './web_hammer/TapGestureHandler';\nimport HammerLongPressGestureHandler from './web_hammer/LongPressGestureHandler';\nimport HammerPinchGestureHandler from './web_hammer/PinchGestureHandler';\nimport HammerRotationGestureHandler from './web_hammer/RotationGestureHandler';\nimport HammerFlingGestureHandler from './web_hammer/FlingGestureHandler';\nimport { Config } from './web/interfaces';\nimport { GestureHandlerWebDelegate } from './web/tools/GestureHandlerWebDelegate';\n\nexport const Gestures = {\n  NativeViewGestureHandler,\n  PanGestureHandler,\n  TapGestureHandler,\n  LongPressGestureHandler,\n  PinchGestureHandler,\n  RotationGestureHandler,\n  FlingGestureHandler,\n  ManualGestureHandler,\n};\n\nexport const HammerGestures = {\n  NativeViewGestureHandler: HammerNativeViewGestureHandler,\n  PanGestureHandler: HammerPanGestureHandler,\n  TapGestureHandler: HammerTapGestureHandler,\n  LongPressGestureHandler: HammerLongPressGestureHandler,\n  PinchGestureHandler: HammerPinchGestureHandler,\n  RotationGestureHandler: HammerRotationGestureHandler,\n  FlingGestureHandler: HammerFlingGestureHandler,\n};\n\nexport default {\n  handleSetJSResponder(_tag: number, _blockNativeResponder: boolean) {\n    // NO-OP\n  },\n  handleClearJSResponder() {\n    // NO-OP\n  },\n  createGestureHandler<T>(\n    handlerName: keyof typeof Gestures,\n    handlerTag: number,\n    config: T\n  ) {\n    if (isNewWebImplementationEnabled()) {\n      if (!(handlerName in Gestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      const GestureClass = Gestures[handlerName];\n      NodeManager.createGestureHandler(\n        handlerTag,\n        new GestureClass(new GestureHandlerWebDelegate())\n      );\n      InteractionManager.getInstance().configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        config as unknown as Config\n      );\n    } else {\n      if (!(handlerName in HammerGestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      // @ts-ignore If it doesn't exist, the error is thrown\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      const GestureClass = HammerGestures[handlerName];\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n      HammerNodeManager.createGestureHandler(handlerTag, new GestureClass());\n    }\n\n    this.updateGestureHandler(handlerTag, config as unknown as Config);\n  },\n  attachGestureHandler(\n    handlerTag: number,\n    newView: number,\n    _actionType: ActionType,\n    propsRef: React.RefObject<unknown>\n  ) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.getHandler(handlerTag).init(newView, propsRef);\n    } else {\n      HammerNodeManager.getHandler(handlerTag).setView(newView, propsRef);\n    }\n  },\n  updateGestureHandler(handlerTag: number, newConfig: Config) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n\n      InteractionManager.getInstance().configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        newConfig\n      );\n    } else {\n      HammerNodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n    }\n  },\n  getGestureHandlerNode(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      return NodeManager.getHandler(handlerTag);\n    } else {\n      return HammerNodeManager.getHandler(handlerTag);\n    }\n  },\n  dropGestureHandler(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.dropGestureHandler(handlerTag);\n    } else {\n      HammerNodeManager.dropGestureHandler(handlerTag);\n    }\n  },\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  flushOperations() {},\n};\n"]}