{"version": 3, "sources": ["GestureHandlerOrchestrator.ts"], "names": ["State", "PointerType", "PointerTracker", "GestureHandlerOrchestrator", "constructor", "scheduleFinishedHandlersCleanup", "handlingChangeSemaphore", "cleanupFinishedHandlers", "<PERSON><PERSON><PERSON><PERSON>", "handler", "reset", "setActive", "setAwaiting", "setActivationIndex", "Number", "MAX_VALUE", "removeHandlerFromOrchestrator", "gestureHandlers", "splice", "indexOf", "awaitingHandlers", "i", "length", "isFinished", "getState", "isAwaiting", "hasOtherHandlerToWaitFor", "hasToWait", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "shouldHandlerWaitForOther", "tryActivate", "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CANCELLED", "FAILED", "shouldActivate", "makeActive", "ACTIVE", "fail", "BEGAN", "cancel", "shouldHandlerBeCancelledBy", "cleanupAwaitingHandlers", "onHandlerStateChange", "newState", "oldState", "sendIfDisabled", "isEnabled", "END", "sendEvent", "isActive", "UNDETERMINED", "currentState", "setShouldResetProgress", "activationIndex", "alreadyExists", "push", "recordHandlerIfNotPresent", "MAX_SAFE_INTEGER", "shouldWaitForHandlerFailure", "shouldRequireToWaitForFailure", "canRunSimultaneously", "gh1", "gh2", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "handlerPointers", "getTrackedPointersID", "otherPointers", "shareCommonPointers", "getDelegate", "get<PERSON>iew", "checkOverlap", "overlap", "pointer", "handlerX", "getTracker", "getLastX", "handlerY", "getLastY", "isPointerInBounds", "x", "y", "otherX", "otherY", "state", "cancelMouseAndPenGestures", "<PERSON><PERSON><PERSON><PERSON>", "getPointerType", "MOUSE", "PEN", "resetTracker", "getInstance", "instance"], "mappings": ";;AAAA,SAASA,KAAT,QAAsB,aAAtB;AACA,SAASC,WAAT,QAA4B,eAA5B;AAGA,OAAOC,cAAP,MAA2B,kBAA3B;AAEA,eAAe,MAAMC,0BAAN,CAAiC;AAS9C;AACA;AACQC,EAAAA,WAAW,GAAG;AAAA,6CARsB,EAQtB;;AAAA,8CAPuB,EAOvB;;AAAA,qDALY,CAKZ;;AAAA,6CAJI,CAIJ;AAAE;;AAEhBC,EAAAA,+BAA+B,GAAS;AAC9C,QAAI,KAAKC,uBAAL,KAAiC,CAArC,EAAwC;AACtC,WAAKC,uBAAL;AACD;AACF;;AAEOC,EAAAA,YAAY,CAACC,OAAD,EAAgC;AAClDA,IAAAA,OAAO,CAACC,KAAR;AACAD,IAAAA,OAAO,CAACE,SAAR,CAAkB,KAAlB;AACAF,IAAAA,OAAO,CAACG,WAAR,CAAoB,KAApB;AACAH,IAAAA,OAAO,CAACI,kBAAR,CAA2BC,MAAM,CAACC,SAAlC;AACD;;AAEMC,EAAAA,6BAA6B,CAACP,OAAD,EAAgC;AAClE,SAAKQ,eAAL,CAAqBC,MAArB,CAA4B,KAAKD,eAAL,CAAqBE,OAArB,CAA6BV,OAA7B,CAA5B,EAAmE,CAAnE;AACA,SAAKW,gBAAL,CAAsBF,MAAtB,CAA6B,KAAKE,gBAAL,CAAsBD,OAAtB,CAA8BV,OAA9B,CAA7B,EAAqE,CAArE;AACD;;AAEOF,EAAAA,uBAAuB,GAAS;AACtC,SAAK,IAAIc,CAAC,GAAG,KAAKJ,eAAL,CAAqBK,MAArB,GAA8B,CAA3C,EAA8CD,CAAC,IAAI,CAAnD,EAAsD,EAAEA,CAAxD,EAA2D;AACzD,YAAMZ,OAAO,GAAG,KAAKQ,eAAL,CAAqBI,CAArB,CAAhB;;AAEA,UAAI,CAACZ,OAAL,EAAc;AACZ;AACD;;AACD,UAAI,KAAKc,UAAL,CAAgBd,OAAO,CAACe,QAAR,EAAhB,KAAuC,CAACf,OAAO,CAACgB,UAAR,EAA5C,EAAkE;AAChE,aAAKR,eAAL,CAAqBC,MAArB,CAA4BG,CAA5B,EAA+B,CAA/B;AAEA,aAAKb,YAAL,CAAkBC,OAAlB;AACD;AACF;AACF;;AAEOiB,EAAAA,wBAAwB,CAACjB,OAAD,EAAmC;AACjE,QAAIkB,SAAS,GAAG,KAAhB;AACA,SAAKV,eAAL,CAAqBW,OAArB,CAA8BC,YAAD,IAAkB;AAC7C,UACEA,YAAY,IACZ,CAAC,KAAKN,UAAL,CAAgBM,YAAY,CAACL,QAAb,EAAhB,CADD,IAEA,KAAKM,yBAAL,CAA+BrB,OAA/B,EAAwCoB,YAAxC,CAHF,EAIE;AACAF,QAAAA,SAAS,GAAG,IAAZ;AACA;AACD;AACF,KATD;AAWA,WAAOA,SAAP;AACD;;AAEOI,EAAAA,WAAW,CAACtB,OAAD,EAAgC;AACjD,QAAI,KAAKiB,wBAAL,CAA8BjB,OAA9B,CAAJ,EAA4C;AAC1C,WAAKuB,kBAAL,CAAwBvB,OAAxB;AACD,KAFD,MAEO,IACLA,OAAO,CAACe,QAAR,OAAuBxB,KAAK,CAACiC,SAA7B,IACAxB,OAAO,CAACe,QAAR,OAAuBxB,KAAK,CAACkC,MAFxB,EAGL;AACA,UAAI,KAAKC,cAAL,CAAoB1B,OAApB,CAAJ,EAAkC;AAChC,aAAK2B,UAAL,CAAgB3B,OAAhB;AACD,OAFD,MAEO;AACL,gBAAQA,OAAO,CAACe,QAAR,EAAR;AACE,eAAKxB,KAAK,CAACqC,MAAX;AACE5B,YAAAA,OAAO,CAAC6B,IAAR;AACA;;AACF,eAAKtC,KAAK,CAACuC,KAAX;AACE9B,YAAAA,OAAO,CAAC+B,MAAR;AALJ;AAOD;AACF;AACF;;AAEOL,EAAAA,cAAc,CAAC1B,OAAD,EAAmC;AACvD,SAAK,MAAMoB,YAAX,IAA2B,KAAKZ,eAAhC,EAAiD;AAC/C,UAAI,KAAKwB,0BAAL,CAAgChC,OAAhC,EAAyCoB,YAAzC,CAAJ,EAA4D;AAC1D,eAAO,KAAP;AACD;AACF;;AAED,WAAO,IAAP;AACD;;AAEOa,EAAAA,uBAAuB,CAACjC,OAAD,EAAgC;AAC7D,SAAK,IAAIY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKD,gBAAL,CAAsBE,MAA1C,EAAkD,EAAED,CAApD,EAAuD;AACrD,UACE,CAAC,KAAKD,gBAAL,CAAsBC,CAAtB,EAAyBI,UAAzB,EAAD,IACA,KAAKK,yBAAL,CAA+B,KAAKV,gBAAL,CAAsBC,CAAtB,CAA/B,EAAyDZ,OAAzD,CAFF,EAGE;AACA,aAAKD,YAAL,CAAkB,KAAKY,gBAAL,CAAsBC,CAAtB,CAAlB;AACA,aAAKD,gBAAL,CAAsBF,MAAtB,CAA6BG,CAA7B,EAAgC,CAAhC;AACD;AACF;AACF;;AAEMsB,EAAAA,oBAAoB,CACzBlC,OADyB,EAEzBmC,QAFyB,EAGzBC,QAHyB,EAIzBC,cAJyB,EAKnB;AACN,QAAI,CAACrC,OAAO,CAACsC,SAAR,EAAD,IAAwB,CAACD,cAA7B,EAA6C;AAC3C;AACD;;AAED,SAAKxC,uBAAL,IAAgC,CAAhC;;AAEA,QAAI,KAAKiB,UAAL,CAAgBqB,QAAhB,CAAJ,EAA+B;AAC7B,WAAKxB,gBAAL,CAAsBQ,OAAtB,CAA+BC,YAAD,IAAkB;AAC9C,YAAI,KAAKC,yBAAL,CAA+BD,YAA/B,EAA6CpB,OAA7C,CAAJ,EAA2D;AACzD,cAAImC,QAAQ,KAAK5C,KAAK,CAACgD,GAAvB,EAA4B;AAC1BnB,YAAAA,YAAY,SAAZ,IAAAA,YAAY,WAAZ,YAAAA,YAAY,CAAEW,MAAd;;AACA,gBAAIX,YAAY,CAACL,QAAb,OAA4BxB,KAAK,CAACgD,GAAtC,EAA2C;AACzC;AACA;AACA;AACA;AACAnB,cAAAA,YAAY,CAACoB,SAAb,CAAuBjD,KAAK,CAACiC,SAA7B,EAAwCjC,KAAK,CAACuC,KAA9C;AACD;;AACDV,YAAAA,YAAY,SAAZ,IAAAA,YAAY,WAAZ,YAAAA,YAAY,CAAEjB,WAAd,CAA0B,KAA1B;AACD,WAVD,MAUO;AACL,iBAAKmB,WAAL,CAAiBF,YAAjB;AACD;AACF;AACF,OAhBD;AAiBD;;AAED,QAAIe,QAAQ,KAAK5C,KAAK,CAACqC,MAAvB,EAA+B;AAC7B,WAAKN,WAAL,CAAiBtB,OAAjB;AACD,KAFD,MAEO,IAAIoC,QAAQ,KAAK7C,KAAK,CAACqC,MAAnB,IAA6BQ,QAAQ,KAAK7C,KAAK,CAACgD,GAApD,EAAyD;AAC9D,UAAIvC,OAAO,CAACyC,QAAR,EAAJ,EAAwB;AACtBzC,QAAAA,OAAO,CAACwC,SAAR,CAAkBL,QAAlB,EAA4BC,QAA5B;AACD,OAFD,MAEO,IACLA,QAAQ,KAAK7C,KAAK,CAACqC,MAAnB,KACCO,QAAQ,KAAK5C,KAAK,CAACiC,SAAnB,IAAgCW,QAAQ,KAAK5C,KAAK,CAACkC,MADpD,CADK,EAGL;AACAzB,QAAAA,OAAO,CAACwC,SAAR,CAAkBL,QAAlB,EAA4B5C,KAAK,CAACuC,KAAlC;AACD;AACF,KATM,MASA,IACLM,QAAQ,KAAK7C,KAAK,CAACmD,YAAnB,IACAP,QAAQ,KAAK5C,KAAK,CAACiC,SAFd,EAGL;AACAxB,MAAAA,OAAO,CAACwC,SAAR,CAAkBL,QAAlB,EAA4BC,QAA5B;AACD;;AAED,SAAKvC,uBAAL,IAAgC,CAAhC;AAEA,SAAKD,+BAAL;;AAEA,QAAI,KAAKe,gBAAL,CAAsBD,OAAtB,CAA8BV,OAA9B,IAAyC,CAA7C,EAAgD;AAC9C,WAAKiC,uBAAL,CAA6BjC,OAA7B;AACD;AACF;;AAEO2B,EAAAA,UAAU,CAAC3B,OAAD,EAAgC;AAChD,UAAM2C,YAAY,GAAG3C,OAAO,CAACe,QAAR,EAArB;AAEAf,IAAAA,OAAO,CAACE,SAAR,CAAkB,IAAlB;AACAF,IAAAA,OAAO,CAAC4C,sBAAR,CAA+B,IAA/B;AACA5C,IAAAA,OAAO,CAACI,kBAAR,CAA2B,KAAKyC,eAAL,EAA3B;;AAEA,SAAK,IAAIjC,CAAC,GAAG,KAAKJ,eAAL,CAAqBK,MAArB,GAA8B,CAA3C,EAA8CD,CAAC,IAAI,CAAnD,EAAsD,EAAEA,CAAxD,EAA2D;AACzD,UAAI,KAAKoB,0BAAL,CAAgC,KAAKxB,eAAL,CAAqBI,CAArB,CAAhC,EAAyDZ,OAAzD,CAAJ,EAAuE;AACrE,aAAKQ,eAAL,CAAqBI,CAArB,EAAwBmB,MAAxB;AACD;AACF;;AAED,SAAKpB,gBAAL,CAAsBQ,OAAtB,CAA+BC,YAAD,IAAkB;AAC9C,UAAI,KAAKY,0BAAL,CAAgCZ,YAAhC,EAA8CpB,OAA9C,CAAJ,EAA4D;AAC1DoB,QAAAA,YAAY,SAAZ,IAAAA,YAAY,WAAZ,YAAAA,YAAY,CAAEW,MAAd;AACAX,QAAAA,YAAY,SAAZ,IAAAA,YAAY,WAAZ,YAAAA,YAAY,CAAEjB,WAAd,CAA0B,IAA1B;AACD;AACF,KALD;AAOAH,IAAAA,OAAO,CAACwC,SAAR,CAAkBjD,KAAK,CAACqC,MAAxB,EAAgCrC,KAAK,CAACuC,KAAtC;;AAEA,QAAIa,YAAY,KAAKpD,KAAK,CAACqC,MAA3B,EAAmC;AACjC5B,MAAAA,OAAO,CAACwC,SAAR,CAAkBjD,KAAK,CAACgD,GAAxB,EAA6BhD,KAAK,CAACqC,MAAnC;;AACA,UAAIe,YAAY,KAAKpD,KAAK,CAACgD,GAA3B,EAAgC;AAC9BvC,QAAAA,OAAO,CAACwC,SAAR,CAAkBjD,KAAK,CAACmD,YAAxB,EAAsCnD,KAAK,CAACgD,GAA5C;AACD;AACF;;AAED,QAAIvC,OAAO,CAACgB,UAAR,EAAJ,EAA0B;AACxBhB,MAAAA,OAAO,CAACG,WAAR,CAAoB,KAApB;;AACA,WAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKD,gBAAL,CAAsBE,MAA1C,EAAkD,EAAED,CAApD,EAAuD;AACrD,YAAI,KAAKD,gBAAL,CAAsBC,CAAtB,MAA6BZ,OAAjC,EAA0C;AACxC,eAAKW,gBAAL,CAAsBF,MAAtB,CAA6BG,CAA7B,EAAgC,CAAhC;AACD;AACF;AACF;AACF;;AAEOW,EAAAA,kBAAkB,CAACvB,OAAD,EAAgC;AACxD,QAAI8C,aAAa,GAAG,KAApB;AAEA,SAAKnC,gBAAL,CAAsBQ,OAAtB,CAA+BC,YAAD,IAAkB;AAC9C,UAAIA,YAAY,KAAKpB,OAArB,EAA8B;AAC5B8C,QAAAA,aAAa,GAAG,IAAhB;AACA;AACD;AACF,KALD;;AAOA,QAAIA,aAAJ,EAAmB;AACjB;AACD;;AAED,SAAKnC,gBAAL,CAAsBoC,IAAtB,CAA2B/C,OAA3B;AAEAA,IAAAA,OAAO,CAACG,WAAR,CAAoB,IAApB;AACAH,IAAAA,OAAO,CAACI,kBAAR,CAA2B,KAAKyC,eAAL,EAA3B;AACD;;AAEMG,EAAAA,yBAAyB,CAAChD,OAAD,EAAgC;AAC9D,QAAI8C,aAAa,GAAG,KAApB;AAEA,SAAKtC,eAAL,CAAqBW,OAArB,CAA8BC,YAAD,IAAkB;AAC7C,UAAIA,YAAY,KAAKpB,OAArB,EAA8B;AAC5B8C,QAAAA,aAAa,GAAG,IAAhB;AACA;AACD;AACF,KALD;;AAOA,QAAIA,aAAJ,EAAmB;AACjB;AACD;;AAED,SAAKtC,eAAL,CAAqBuC,IAArB,CAA0B/C,OAA1B;AAEAA,IAAAA,OAAO,CAACE,SAAR,CAAkB,KAAlB;AACAF,IAAAA,OAAO,CAACG,WAAR,CAAoB,KAApB;AACAH,IAAAA,OAAO,CAACI,kBAAR,CAA2BC,MAAM,CAAC4C,gBAAlC;AACD;;AAEO5B,EAAAA,yBAAyB,CAC/BrB,OAD+B,EAE/BoB,YAF+B,EAGtB;AACT,WACEpB,OAAO,KAAKoB,YAAZ,KACCpB,OAAO,CAACkD,2BAAR,CAAoC9B,YAApC,KACCA,YAAY,CAAC+B,6BAAb,CAA2CnD,OAA3C,CAFF,CADF;AAKD;;AAEOoD,EAAAA,oBAAoB,CAC1BC,GAD0B,EAE1BC,GAF0B,EAGjB;AACT,WACED,GAAG,KAAKC,GAAR,IACAD,GAAG,CAACE,6BAAJ,CAAkCD,GAAlC,CADA,IAEAA,GAAG,CAACC,6BAAJ,CAAkCF,GAAlC,CAHF;AAKD;;AAEOrB,EAAAA,0BAA0B,CAChChC,OADgC,EAEhCoB,YAFgC,EAGvB;AACT,QAAI,KAAKgC,oBAAL,CAA0BpD,OAA1B,EAAmCoB,YAAnC,CAAJ,EAAsD;AACpD,aAAO,KAAP;AACD;;AAED,QACEpB,OAAO,KAAKoB,YAAZ,KACCpB,OAAO,CAACgB,UAAR,MAAwBhB,OAAO,CAACe,QAAR,OAAuBxB,KAAK,CAACqC,MADtD,CADF,EAGE;AACA;AACA,aAAO5B,OAAO,CAACwD,wBAAR,CAAiCpC,YAAjC,CAAP;AACD;;AAED,UAAMqC,eAAyB,GAAGzD,OAAO,CAAC0D,oBAAR,EAAlC;AACA,UAAMC,aAAuB,GAAGvC,YAAY,CAACsC,oBAAb,EAAhC;;AAEA,QACE,CAACjE,cAAc,CAACmE,mBAAf,CAAmCH,eAAnC,EAAoDE,aAApD,CAAD,IACA3D,OAAO,CAAC6D,WAAR,GAAsBC,OAAtB,OAAoC1C,YAAY,CAACyC,WAAb,GAA2BC,OAA3B,EAFtC,EAGE;AACA,aAAO,KAAKC,YAAL,CAAkB/D,OAAlB,EAA2BoB,YAA3B,CAAP;AACD;;AAED,WAAO,IAAP;AACD;;AAEO2C,EAAAA,YAAY,CAClB/D,OADkB,EAElBoB,YAFkB,EAGT;AACT;AACA;AACA;AAEA;AAEA,UAAMqC,eAAyB,GAAGzD,OAAO,CAAC0D,oBAAR,EAAlC;AACA,UAAMC,aAAuB,GAAGvC,YAAY,CAACsC,oBAAb,EAAhC;AAEA,QAAIM,OAAO,GAAG,KAAd;AAEAP,IAAAA,eAAe,CAACtC,OAAhB,CAAyB8C,OAAD,IAAqB;AAC3C,YAAMC,QAAgB,GAAGlE,OAAO,CAACmE,UAAR,GAAqBC,QAArB,CAA8BH,OAA9B,CAAzB;AACA,YAAMI,QAAgB,GAAGrE,OAAO,CAACmE,UAAR,GAAqBG,QAArB,CAA8BL,OAA9B,CAAzB;;AAEA,UACEjE,OAAO,CAAC6D,WAAR,GAAsBU,iBAAtB,CAAwC;AAAEC,QAAAA,CAAC,EAAEN,QAAL;AAAeO,QAAAA,CAAC,EAAEJ;AAAlB,OAAxC,KACAjD,YAAY,CACTyC,WADH,GAEGU,iBAFH,CAEqB;AAAEC,QAAAA,CAAC,EAAEN,QAAL;AAAeO,QAAAA,CAAC,EAAEJ;AAAlB,OAFrB,CAFF,EAKE;AACAL,QAAAA,OAAO,GAAG,IAAV;AACD;AACF,KAZD;AAcAL,IAAAA,aAAa,CAACxC,OAAd,CAAuB8C,OAAD,IAAqB;AACzC,YAAMS,MAAc,GAAGtD,YAAY,CAAC+C,UAAb,GAA0BC,QAA1B,CAAmCH,OAAnC,CAAvB;AACA,YAAMU,MAAc,GAAGvD,YAAY,CAAC+C,UAAb,GAA0BG,QAA1B,CAAmCL,OAAnC,CAAvB;;AAEA,UACEjE,OAAO,CAAC6D,WAAR,GAAsBU,iBAAtB,CAAwC;AAAEC,QAAAA,CAAC,EAAEE,MAAL;AAAaD,QAAAA,CAAC,EAAEE;AAAhB,OAAxC,KACAvD,YAAY,CAACyC,WAAb,GAA2BU,iBAA3B,CAA6C;AAAEC,QAAAA,CAAC,EAAEE,MAAL;AAAaD,QAAAA,CAAC,EAAEE;AAAhB,OAA7C,CAFF,EAGE;AACAX,QAAAA,OAAO,GAAG,IAAV;AACD;AACF,KAVD;AAYA,WAAOA,OAAP;AACD;;AAEOlD,EAAAA,UAAU,CAAC8D,KAAD,EAAwB;AACxC,WACEA,KAAK,KAAKrF,KAAK,CAACgD,GAAhB,IAAuBqC,KAAK,KAAKrF,KAAK,CAACkC,MAAvC,IAAiDmD,KAAK,KAAKrF,KAAK,CAACiC,SADnE;AAGD,GAvV6C,CAyV9C;AACA;AACA;AACA;AACA;AACA;;;AACOqD,EAAAA,yBAAyB,CAACC,cAAD,EAAuC;AACrE,SAAKtE,eAAL,CAAqBW,OAArB,CAA8BnB,OAAD,IAA6B;AACxD,UACEA,OAAO,CAAC+E,cAAR,OAA6BvF,WAAW,CAACwF,KAAzC,IACAhF,OAAO,CAAC+E,cAAR,OAA6BvF,WAAW,CAACyF,GAF3C,EAGE;AACA;AACD;;AAED,UAAIjF,OAAO,KAAK8E,cAAhB,EAAgC;AAC9B9E,QAAAA,OAAO,CAAC+B,MAAR;AACD,OAFD,MAEO;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA/B,QAAAA,OAAO,CAACmE,UAAR,GAAqBe,YAArB;AACD;AACF,KApBD;AAqBD;;AAEwB,SAAXC,WAAW,GAA+B;AACtD,QAAI,CAACzF,0BAA0B,CAAC0F,QAAhC,EAA0C;AACxC1F,MAAAA,0BAA0B,CAAC0F,QAA3B,GAAsC,IAAI1F,0BAAJ,EAAtC;AACD;;AAED,WAAOA,0BAA0B,CAAC0F,QAAlC;AACD;;AA7X6C;;gBAA3B1F,0B", "sourcesContent": ["import { State } from '../../State';\nimport { PointerType } from '../interfaces';\n\nimport GestureHandler from '../handlers/GestureHandler';\nimport PointerTracker from './PointerTracker';\n\nexport default class GestureHandlerOrchestrator {\n  private static instance: GestureHandlerOrchestrator;\n\n  private gestureHandlers: GestureHandler[] = [];\n  private awaitingHandlers: GestureHandler[] = [];\n\n  private handlingChangeSemaphore = 0;\n  private activationIndex = 0;\n\n  // Private beacuse of Singleton\n  // eslint-disable-next-line no-useless-constructor, @typescript-eslint/no-empty-function\n  private constructor() {}\n\n  private scheduleFinishedHandlersCleanup(): void {\n    if (this.handlingChangeSemaphore === 0) {\n      this.cleanupFinishedHandlers();\n    }\n  }\n\n  private cleanHandler(handler: GestureHandler): void {\n    handler.reset();\n    handler.setActive(false);\n    handler.setAwaiting(false);\n    handler.setActivationIndex(Number.MAX_VALUE);\n  }\n\n  public removeHandlerFromOrchestrator(handler: GestureHandler): void {\n    this.gestureHandlers.splice(this.gestureHandlers.indexOf(handler), 1);\n    this.awaitingHandlers.splice(this.awaitingHandlers.indexOf(handler), 1);\n  }\n\n  private cleanupFinishedHandlers(): void {\n    for (let i = this.gestureHandlers.length - 1; i >= 0; --i) {\n      const handler = this.gestureHandlers[i];\n\n      if (!handler) {\n        continue;\n      }\n      if (this.isFinished(handler.getState()) && !handler.isAwaiting()) {\n        this.gestureHandlers.splice(i, 1);\n\n        this.cleanHandler(handler);\n      }\n    }\n  }\n\n  private hasOtherHandlerToWaitFor(handler: GestureHandler): boolean {\n    let hasToWait = false;\n    this.gestureHandlers.forEach((otherHandler) => {\n      if (\n        otherHandler &&\n        !this.isFinished(otherHandler.getState()) &&\n        this.shouldHandlerWaitForOther(handler, otherHandler)\n      ) {\n        hasToWait = true;\n        return;\n      }\n    });\n\n    return hasToWait;\n  }\n\n  private tryActivate(handler: GestureHandler): void {\n    if (this.hasOtherHandlerToWaitFor(handler)) {\n      this.addAwaitingHandler(handler);\n    } else if (\n      handler.getState() !== State.CANCELLED &&\n      handler.getState() !== State.FAILED\n    ) {\n      if (this.shouldActivate(handler)) {\n        this.makeActive(handler);\n      } else {\n        switch (handler.getState()) {\n          case State.ACTIVE:\n            handler.fail();\n            break;\n          case State.BEGAN:\n            handler.cancel();\n        }\n      }\n    }\n  }\n\n  private shouldActivate(handler: GestureHandler): boolean {\n    for (const otherHandler of this.gestureHandlers) {\n      if (this.shouldHandlerBeCancelledBy(handler, otherHandler)) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  private cleanupAwaitingHandlers(handler: GestureHandler): void {\n    for (let i = 0; i < this.awaitingHandlers.length; ++i) {\n      if (\n        !this.awaitingHandlers[i].isAwaiting() &&\n        this.shouldHandlerWaitForOther(this.awaitingHandlers[i], handler)\n      ) {\n        this.cleanHandler(this.awaitingHandlers[i]);\n        this.awaitingHandlers.splice(i, 1);\n      }\n    }\n  }\n\n  public onHandlerStateChange(\n    handler: GestureHandler,\n    newState: State,\n    oldState: State,\n    sendIfDisabled?: boolean\n  ): void {\n    if (!handler.isEnabled() && !sendIfDisabled) {\n      return;\n    }\n\n    this.handlingChangeSemaphore += 1;\n\n    if (this.isFinished(newState)) {\n      this.awaitingHandlers.forEach((otherHandler) => {\n        if (this.shouldHandlerWaitForOther(otherHandler, handler)) {\n          if (newState === State.END) {\n            otherHandler?.cancel();\n            if (otherHandler.getState() === State.END) {\n              // Handle edge case, where discrete gestures end immediately after activation thus\n              // their state is set to END and when the gesture they are waiting for activates they\n              // should be cancelled, however `cancel` was never sent as gestures were already in the END state.\n              // Send synthetic BEGAN -> CANCELLED to properly handle JS logic\n              otherHandler.sendEvent(State.CANCELLED, State.BEGAN);\n            }\n            otherHandler?.setAwaiting(false);\n          } else {\n            this.tryActivate(otherHandler);\n          }\n        }\n      });\n    }\n\n    if (newState === State.ACTIVE) {\n      this.tryActivate(handler);\n    } else if (oldState === State.ACTIVE || oldState === State.END) {\n      if (handler.isActive()) {\n        handler.sendEvent(newState, oldState);\n      } else if (\n        oldState === State.ACTIVE &&\n        (newState === State.CANCELLED || newState === State.FAILED)\n      ) {\n        handler.sendEvent(newState, State.BEGAN);\n      }\n    } else if (\n      oldState !== State.UNDETERMINED ||\n      newState !== State.CANCELLED\n    ) {\n      handler.sendEvent(newState, oldState);\n    }\n\n    this.handlingChangeSemaphore -= 1;\n\n    this.scheduleFinishedHandlersCleanup();\n\n    if (this.awaitingHandlers.indexOf(handler) < 0) {\n      this.cleanupAwaitingHandlers(handler);\n    }\n  }\n\n  private makeActive(handler: GestureHandler): void {\n    const currentState = handler.getState();\n\n    handler.setActive(true);\n    handler.setShouldResetProgress(true);\n    handler.setActivationIndex(this.activationIndex++);\n\n    for (let i = this.gestureHandlers.length - 1; i >= 0; --i) {\n      if (this.shouldHandlerBeCancelledBy(this.gestureHandlers[i], handler)) {\n        this.gestureHandlers[i].cancel();\n      }\n    }\n\n    this.awaitingHandlers.forEach((otherHandler) => {\n      if (this.shouldHandlerBeCancelledBy(otherHandler, handler)) {\n        otherHandler?.cancel();\n        otherHandler?.setAwaiting(true);\n      }\n    });\n\n    handler.sendEvent(State.ACTIVE, State.BEGAN);\n\n    if (currentState !== State.ACTIVE) {\n      handler.sendEvent(State.END, State.ACTIVE);\n      if (currentState !== State.END) {\n        handler.sendEvent(State.UNDETERMINED, State.END);\n      }\n    }\n\n    if (handler.isAwaiting()) {\n      handler.setAwaiting(false);\n      for (let i = 0; i < this.awaitingHandlers.length; ++i) {\n        if (this.awaitingHandlers[i] === handler) {\n          this.awaitingHandlers.splice(i, 1);\n        }\n      }\n    }\n  }\n\n  private addAwaitingHandler(handler: GestureHandler): void {\n    let alreadyExists = false;\n\n    this.awaitingHandlers.forEach((otherHandler) => {\n      if (otherHandler === handler) {\n        alreadyExists = true;\n        return;\n      }\n    });\n\n    if (alreadyExists) {\n      return;\n    }\n\n    this.awaitingHandlers.push(handler);\n\n    handler.setAwaiting(true);\n    handler.setActivationIndex(this.activationIndex++);\n  }\n\n  public recordHandlerIfNotPresent(handler: GestureHandler): void {\n    let alreadyExists = false;\n\n    this.gestureHandlers.forEach((otherHandler) => {\n      if (otherHandler === handler) {\n        alreadyExists = true;\n        return;\n      }\n    });\n\n    if (alreadyExists) {\n      return;\n    }\n\n    this.gestureHandlers.push(handler);\n\n    handler.setActive(false);\n    handler.setAwaiting(false);\n    handler.setActivationIndex(Number.MAX_SAFE_INTEGER);\n  }\n\n  private shouldHandlerWaitForOther(\n    handler: GestureHandler,\n    otherHandler: GestureHandler\n  ): boolean {\n    return (\n      handler !== otherHandler &&\n      (handler.shouldWaitForHandlerFailure(otherHandler) ||\n        otherHandler.shouldRequireToWaitForFailure(handler))\n    );\n  }\n\n  private canRunSimultaneously(\n    gh1: GestureHandler,\n    gh2: GestureHandler\n  ): boolean {\n    return (\n      gh1 === gh2 ||\n      gh1.shouldRecognizeSimultaneously(gh2) ||\n      gh2.shouldRecognizeSimultaneously(gh1)\n    );\n  }\n\n  private shouldHandlerBeCancelledBy(\n    handler: GestureHandler,\n    otherHandler: GestureHandler\n  ): boolean {\n    if (this.canRunSimultaneously(handler, otherHandler)) {\n      return false;\n    }\n\n    if (\n      handler !== otherHandler &&\n      (handler.isAwaiting() || handler.getState() === State.ACTIVE)\n    ) {\n      // For now it always returns false\n      return handler.shouldBeCancelledByOther(otherHandler);\n    }\n\n    const handlerPointers: number[] = handler.getTrackedPointersID();\n    const otherPointers: number[] = otherHandler.getTrackedPointersID();\n\n    if (\n      !PointerTracker.shareCommonPointers(handlerPointers, otherPointers) &&\n      handler.getDelegate().getView() !== otherHandler.getDelegate().getView()\n    ) {\n      return this.checkOverlap(handler, otherHandler);\n    }\n\n    return true;\n  }\n\n  private checkOverlap(\n    handler: GestureHandler,\n    otherHandler: GestureHandler\n  ): boolean {\n    // If handlers don't have common pointers, default return value is false.\n    // However, if at least on pointer overlaps with both handlers, we return true\n    // This solves issue in overlapping parents example\n\n    // TODO: Find better way to handle that issue, for example by activation order and handler cancelling\n\n    const handlerPointers: number[] = handler.getTrackedPointersID();\n    const otherPointers: number[] = otherHandler.getTrackedPointersID();\n\n    let overlap = false;\n\n    handlerPointers.forEach((pointer: number) => {\n      const handlerX: number = handler.getTracker().getLastX(pointer);\n      const handlerY: number = handler.getTracker().getLastY(pointer);\n\n      if (\n        handler.getDelegate().isPointerInBounds({ x: handlerX, y: handlerY }) &&\n        otherHandler\n          .getDelegate()\n          .isPointerInBounds({ x: handlerX, y: handlerY })\n      ) {\n        overlap = true;\n      }\n    });\n\n    otherPointers.forEach((pointer: number) => {\n      const otherX: number = otherHandler.getTracker().getLastX(pointer);\n      const otherY: number = otherHandler.getTracker().getLastY(pointer);\n\n      if (\n        handler.getDelegate().isPointerInBounds({ x: otherX, y: otherY }) &&\n        otherHandler.getDelegate().isPointerInBounds({ x: otherX, y: otherY })\n      ) {\n        overlap = true;\n      }\n    });\n\n    return overlap;\n  }\n\n  private isFinished(state: State): boolean {\n    return (\n      state === State.END || state === State.FAILED || state === State.CANCELLED\n    );\n  }\n\n  // This function is called when handler receives touchdown event\n  // If handler is using mouse or pen as a pointer and any handler receives touch event,\n  // mouse/pen event dissappears - it doesn't send onPointerCancel nor onPointerUp (and others)\n  // This became a problem because handler was left at active state without any signal to end or fail\n  // To handle this, when new touch event is received, we loop through active handlers and check which type of\n  // pointer they're using. If there are any handler with mouse/pen as a pointer, we cancel them\n  public cancelMouseAndPenGestures(currentHandler: GestureHandler): void {\n    this.gestureHandlers.forEach((handler: GestureHandler) => {\n      if (\n        handler.getPointerType() !== PointerType.MOUSE &&\n        handler.getPointerType() !== PointerType.PEN\n      ) {\n        return;\n      }\n\n      if (handler !== currentHandler) {\n        handler.cancel();\n      } else {\n        // Handler that received touch event should have its pointer tracker reset\n        // This allows handler to smoothly change from mouse/pen to touch\n        // The drawback is, that when we try to use mouse/pen one more time, it doesn't send onPointerDown at the first time\n        // so it is required to click two times to get handler to work\n        //\n        // However, handler will receive manually created onPointerEnter that is triggered in EventManager in onPointerMove method.\n        // There may be possibility to use that fact to make handler respond properly to first mouse click\n        handler.getTracker().resetTracker();\n      }\n    });\n  }\n\n  public static getInstance(): GestureHandlerOrchestrator {\n    if (!GestureHandlerOrchestrator.instance) {\n      GestureHandlerOrchestrator.instance = new GestureHandlerOrchestrator();\n    }\n\n    return GestureHandlerOrchestrator.instance;\n  }\n}\n"]}