{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"style\", \"onPress\", \"variant\", \"padding\", \"margin\", \"borderRadius\", \"disabled\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport { Colors, Spacing, Animations } from \"../constants\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar Card = function Card(_ref) {\n  var children = _ref.children,\n    style = _ref.style,\n    onPress = _ref.onPress,\n    _ref$variant = _ref.variant,\n    variant = _ref$variant === void 0 ? 'elevated' : _ref$variant,\n    _ref$padding = _ref.padding,\n    padding = _ref$padding === void 0 ? 'md' : _ref$padding,\n    _ref$margin = _ref.margin,\n    margin = _ref$margin === void 0 ? 'sm' : _ref$margin,\n    _ref$borderRadius = _ref.borderRadius,\n    borderRadius = _ref$borderRadius === void 0 ? 'lg' : _ref$borderRadius,\n    _ref$disabled = _ref.disabled,\n    disabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var scaleValue = React.useRef(new Animated.Value(1)).current;\n  var handlePressIn = function handlePressIn() {\n    if (onPress && !disabled) {\n      Animated.spring(scaleValue, _objectSpread(_objectSpread({\n        toValue: Animations.scale.pressed\n      }, Animations.spring.default), {}, {\n        useNativeDriver: true\n      })).start();\n    }\n  };\n  var handlePressOut = function handlePressOut() {\n    if (onPress && !disabled) {\n      Animated.spring(scaleValue, _objectSpread(_objectSpread({\n        toValue: Animations.scale.normal\n      }, Animations.spring.default), {}, {\n        useNativeDriver: true\n      })).start();\n    }\n  };\n  var getCardStyle = function getCardStyle() {\n    var baseStyle = [styles.card, styles[`padding_${padding}`], styles[`margin_${margin}`], styles[`borderRadius_${borderRadius}`]];\n    switch (variant) {\n      case 'elevated':\n        baseStyle.push(styles.elevatedCard);\n        break;\n      case 'outlined':\n        baseStyle.push(styles.outlinedCard);\n        break;\n      case 'filled':\n        baseStyle.push(styles.filledCard);\n        break;\n      default:\n        baseStyle.push(styles.elevatedCard);\n    }\n    if (disabled) {\n      baseStyle.push(styles.disabledCard);\n    }\n    return baseStyle;\n  };\n  var CardContent = function CardContent() {\n    return _jsx(View, _objectSpread(_objectSpread({\n      style: [getCardStyle(), style]\n    }, props), {}, {\n      children: children\n    }));\n  };\n  if (onPress && !disabled) {\n    return _jsx(Animated.View, {\n      style: {\n        transform: [{\n          scale: scaleValue\n        }]\n      },\n      children: _jsx(TouchableOpacity, {\n        onPress: onPress,\n        onPressIn: handlePressIn,\n        onPressOut: handlePressOut,\n        activeOpacity: 0.9,\n        disabled: disabled,\n        children: _jsx(CardContent, {})\n      })\n    });\n  }\n  return _jsx(CardContent, {});\n};\nvar styles = StyleSheet.create({\n  card: {\n    backgroundColor: Colors.cardBackground\n  },\n  padding_xs: {\n    padding: Spacing.padding.xs\n  },\n  padding_sm: {\n    padding: Spacing.padding.sm\n  },\n  padding_md: {\n    padding: Spacing.padding.md\n  },\n  padding_lg: {\n    padding: Spacing.padding.lg\n  },\n  padding_xl: {\n    padding: Spacing.padding.xl\n  },\n  margin_xs: {\n    margin: Spacing.margin.xs\n  },\n  margin_sm: {\n    margin: Spacing.margin.sm\n  },\n  margin_md: {\n    margin: Spacing.margin.md\n  },\n  margin_lg: {\n    margin: Spacing.margin.lg\n  },\n  margin_xl: {\n    margin: Spacing.margin.xl\n  },\n  borderRadius_sm: {\n    borderRadius: Spacing.borderRadius.sm\n  },\n  borderRadius_md: {\n    borderRadius: Spacing.borderRadius.md\n  },\n  borderRadius_lg: {\n    borderRadius: Spacing.borderRadius.lg\n  },\n  borderRadius_xl: {\n    borderRadius: Spacing.borderRadius.xl\n  },\n  elevatedCard: {\n    shadowColor: Colors.cardShadow,\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3\n  },\n  outlinedCard: {\n    borderWidth: 1,\n    borderColor: Colors.outline,\n    shadowOpacity: 0,\n    elevation: 0\n  },\n  filledCard: {\n    backgroundColor: Colors.surfaceVariant,\n    shadowOpacity: 0,\n    elevation: 0\n  },\n  disabledCard: {\n    opacity: Animations.opacity.disabled\n  }\n});\nexport default Card;", "map": {"version": 3, "names": ["React", "View", "StyleSheet", "TouchableOpacity", "Animated", "Colors", "Spacing", "Animations", "jsx", "_jsx", "Card", "_ref", "children", "style", "onPress", "_ref$variant", "variant", "_ref$padding", "padding", "_ref$margin", "margin", "_ref$borderRadius", "borderRadius", "_ref$disabled", "disabled", "props", "_objectWithoutProperties", "_excluded", "scaleValue", "useRef", "Value", "current", "handlePressIn", "spring", "_objectSpread", "toValue", "scale", "pressed", "default", "useNativeDriver", "start", "handlePressOut", "normal", "getCardStyle", "baseStyle", "styles", "card", "push", "elevatedCard", "outlinedCard", "filledCard", "disabledCard", "<PERSON><PERSON><PERSON><PERSON>", "transform", "onPressIn", "onPressOut", "activeOpacity", "create", "backgroundColor", "cardBackground", "padding_xs", "xs", "padding_sm", "sm", "padding_md", "md", "padding_lg", "lg", "padding_xl", "xl", "margin_xs", "margin_sm", "margin_md", "margin_lg", "margin_xl", "borderRadius_sm", "borderRadius_md", "borderRadius_lg", "borderRadius_xl", "shadowColor", "cardShadow", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation", "borderWidth", "borderColor", "outline", "surfaceVariant", "opacity"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/components/Card.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  StyleSheet,\n  TouchableOpacity,\n  Animated,\n} from 'react-native';\nimport { Colors, Spacing, Animations } from '../constants';\n\nconst Card = ({\n  children,\n  style,\n  onPress,\n  variant = 'elevated',\n  padding = 'md',\n  margin = 'sm',\n  borderRadius = 'lg',\n  disabled = false,\n  ...props\n}) => {\n  const scaleValue = React.useRef(new Animated.Value(1)).current;\n\n  const handlePressIn = () => {\n    if (onPress && !disabled) {\n      Animated.spring(scaleValue, {\n        toValue: Animations.scale.pressed,\n        ...Animations.spring.default,\n        useNativeDriver: true,\n      }).start();\n    }\n  };\n\n  const handlePressOut = () => {\n    if (onPress && !disabled) {\n      Animated.spring(scaleValue, {\n        toValue: Animations.scale.normal,\n        ...Animations.spring.default,\n        useNativeDriver: true,\n      }).start();\n    }\n  };\n\n  const getCardStyle = () => {\n    const baseStyle = [\n      styles.card,\n      styles[`padding_${padding}`],\n      styles[`margin_${margin}`],\n      styles[`borderRadius_${borderRadius}`],\n    ];\n\n    switch (variant) {\n      case 'elevated':\n        baseStyle.push(styles.elevatedCard);\n        break;\n      case 'outlined':\n        baseStyle.push(styles.outlinedCard);\n        break;\n      case 'filled':\n        baseStyle.push(styles.filledCard);\n        break;\n      default:\n        baseStyle.push(styles.elevatedCard);\n    }\n\n    if (disabled) {\n      baseStyle.push(styles.disabledCard);\n    }\n\n    return baseStyle;\n  };\n\n  const CardContent = () => (\n    <View style={[getCardStyle(), style]} {...props}>\n      {children}\n    </View>\n  );\n\n  if (onPress && !disabled) {\n    return (\n      <Animated.View style={{ transform: [{ scale: scaleValue }] }}>\n        <TouchableOpacity\n          onPress={onPress}\n          onPressIn={handlePressIn}\n          onPressOut={handlePressOut}\n          activeOpacity={0.9}\n          disabled={disabled}\n        >\n          <CardContent />\n        </TouchableOpacity>\n      </Animated.View>\n    );\n  }\n\n  return <CardContent />;\n};\n\nconst styles = StyleSheet.create({\n  card: {\n    backgroundColor: Colors.cardBackground,\n  },\n  \n  // Padding variants\n  padding_xs: {\n    padding: Spacing.padding.xs,\n  },\n  padding_sm: {\n    padding: Spacing.padding.sm,\n  },\n  padding_md: {\n    padding: Spacing.padding.md,\n  },\n  padding_lg: {\n    padding: Spacing.padding.lg,\n  },\n  padding_xl: {\n    padding: Spacing.padding.xl,\n  },\n  \n  // Margin variants\n  margin_xs: {\n    margin: Spacing.margin.xs,\n  },\n  margin_sm: {\n    margin: Spacing.margin.sm,\n  },\n  margin_md: {\n    margin: Spacing.margin.md,\n  },\n  margin_lg: {\n    margin: Spacing.margin.lg,\n  },\n  margin_xl: {\n    margin: Spacing.margin.xl,\n  },\n  \n  // Border radius variants\n  borderRadius_sm: {\n    borderRadius: Spacing.borderRadius.sm,\n  },\n  borderRadius_md: {\n    borderRadius: Spacing.borderRadius.md,\n  },\n  borderRadius_lg: {\n    borderRadius: Spacing.borderRadius.lg,\n  },\n  borderRadius_xl: {\n    borderRadius: Spacing.borderRadius.xl,\n  },\n  \n  // Card variants\n  elevatedCard: {\n    shadowColor: Colors.cardShadow,\n    shadowOffset: {\n      width: 0,\n      height: 2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3,\n  },\n  outlinedCard: {\n    borderWidth: 1,\n    borderColor: Colors.outline,\n    shadowOpacity: 0,\n    elevation: 0,\n  },\n  filledCard: {\n    backgroundColor: Colors.surfaceVariant,\n    shadowOpacity: 0,\n    elevation: 0,\n  },\n  disabledCard: {\n    opacity: Animations.opacity.disabled,\n  },\n});\n\nexport default Card;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,QAAA;AAO1B,SAASC,MAAM,EAAEC,OAAO,EAAEC,UAAU;AAAuB,SAAAC,GAAA,IAAAC,IAAA;AAE3D,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAAC,IAAA,EAUJ;EAAA,IATJC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IACRC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,OAAO,GAAAH,IAAA,CAAPG,OAAO;IAAAC,YAAA,GAAAJ,IAAA,CACPK,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,UAAU,GAAAA,YAAA;IAAAE,YAAA,GAAAN,IAAA,CACpBO,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;IAAAE,WAAA,GAAAR,IAAA,CACdS,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,IAAI,GAAAA,WAAA;IAAAE,iBAAA,GAAAV,IAAA,CACbW,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,IAAI,GAAAA,iBAAA;IAAAE,aAAA,GAAAZ,IAAA,CACnBa,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IACbE,KAAK,GAAAC,wBAAA,CAAAf,IAAA,EAAAgB,SAAA;EAER,IAAMC,UAAU,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAIzB,QAAQ,CAAC0B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAE9D,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1B,IAAIlB,OAAO,IAAI,CAACU,QAAQ,EAAE;MACxBpB,QAAQ,CAAC6B,MAAM,CAACL,UAAU,EAAAM,aAAA,CAAAA,aAAA;QACxBC,OAAO,EAAE5B,UAAU,CAAC6B,KAAK,CAACC;MAAO,GAC9B9B,UAAU,CAAC0B,MAAM,CAACK,OAAO;QAC5BC,eAAe,EAAE;MAAI,EACtB,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC;EAED,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAI3B,OAAO,IAAI,CAACU,QAAQ,EAAE;MACxBpB,QAAQ,CAAC6B,MAAM,CAACL,UAAU,EAAAM,aAAA,CAAAA,aAAA;QACxBC,OAAO,EAAE5B,UAAU,CAAC6B,KAAK,CAACM;MAAM,GAC7BnC,UAAU,CAAC0B,MAAM,CAACK,OAAO;QAC5BC,eAAe,EAAE;MAAI,EACtB,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC;EAED,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB,IAAMC,SAAS,GAAG,CAChBC,MAAM,CAACC,IAAI,EACXD,MAAM,CAAC,WAAW3B,OAAO,EAAE,CAAC,EAC5B2B,MAAM,CAAC,UAAUzB,MAAM,EAAE,CAAC,EAC1ByB,MAAM,CAAC,gBAAgBvB,YAAY,EAAE,CAAC,CACvC;IAED,QAAQN,OAAO;MACb,KAAK,UAAU;QACb4B,SAAS,CAACG,IAAI,CAACF,MAAM,CAACG,YAAY,CAAC;QACnC;MACF,KAAK,UAAU;QACbJ,SAAS,CAACG,IAAI,CAACF,MAAM,CAACI,YAAY,CAAC;QACnC;MACF,KAAK,QAAQ;QACXL,SAAS,CAACG,IAAI,CAACF,MAAM,CAACK,UAAU,CAAC;QACjC;MACF;QACEN,SAAS,CAACG,IAAI,CAACF,MAAM,CAACG,YAAY,CAAC;IACvC;IAEA,IAAIxB,QAAQ,EAAE;MACZoB,SAAS,CAACG,IAAI,CAACF,MAAM,CAACM,YAAY,CAAC;IACrC;IAEA,OAAOP,SAAS;EAClB,CAAC;EAED,IAAMQ,WAAW,GAAG,SAAdA,WAAWA,CAAA;IAAA,OACf3C,IAAA,CAACR,IAAI,EAAAiC,aAAA,CAAAA,aAAA;MAACrB,KAAK,EAAE,CAAC8B,YAAY,CAAC,CAAC,EAAE9B,KAAK;IAAE,GAAKY,KAAK;MAAAb,QAAA,EAC5CA;IAAQ,EACL,CAAC;EAAA,CACR;EAED,IAAIE,OAAO,IAAI,CAACU,QAAQ,EAAE;IACxB,OACEf,IAAA,CAACL,QAAQ,CAACH,IAAI;MAACY,KAAK,EAAE;QAAEwC,SAAS,EAAE,CAAC;UAAEjB,KAAK,EAAER;QAAW,CAAC;MAAE,CAAE;MAAAhB,QAAA,EAC3DH,IAAA,CAACN,gBAAgB;QACfW,OAAO,EAAEA,OAAQ;QACjBwC,SAAS,EAAEtB,aAAc;QACzBuB,UAAU,EAAEd,cAAe;QAC3Be,aAAa,EAAE,GAAI;QACnBhC,QAAQ,EAAEA,QAAS;QAAAZ,QAAA,EAEnBH,IAAA,CAAC2C,WAAW,IAAE;MAAC,CACC;IAAC,CACN,CAAC;EAEpB;EAEA,OAAO3C,IAAA,CAAC2C,WAAW,IAAE,CAAC;AACxB,CAAC;AAED,IAAMP,MAAM,GAAG3C,UAAU,CAACuD,MAAM,CAAC;EAC/BX,IAAI,EAAE;IACJY,eAAe,EAAErD,MAAM,CAACsD;EAC1B,CAAC;EAGDC,UAAU,EAAE;IACV1C,OAAO,EAAEZ,OAAO,CAACY,OAAO,CAAC2C;EAC3B,CAAC;EACDC,UAAU,EAAE;IACV5C,OAAO,EAAEZ,OAAO,CAACY,OAAO,CAAC6C;EAC3B,CAAC;EACDC,UAAU,EAAE;IACV9C,OAAO,EAAEZ,OAAO,CAACY,OAAO,CAAC+C;EAC3B,CAAC;EACDC,UAAU,EAAE;IACVhD,OAAO,EAAEZ,OAAO,CAACY,OAAO,CAACiD;EAC3B,CAAC;EACDC,UAAU,EAAE;IACVlD,OAAO,EAAEZ,OAAO,CAACY,OAAO,CAACmD;EAC3B,CAAC;EAGDC,SAAS,EAAE;IACTlD,MAAM,EAAEd,OAAO,CAACc,MAAM,CAACyC;EACzB,CAAC;EACDU,SAAS,EAAE;IACTnD,MAAM,EAAEd,OAAO,CAACc,MAAM,CAAC2C;EACzB,CAAC;EACDS,SAAS,EAAE;IACTpD,MAAM,EAAEd,OAAO,CAACc,MAAM,CAAC6C;EACzB,CAAC;EACDQ,SAAS,EAAE;IACTrD,MAAM,EAAEd,OAAO,CAACc,MAAM,CAAC+C;EACzB,CAAC;EACDO,SAAS,EAAE;IACTtD,MAAM,EAAEd,OAAO,CAACc,MAAM,CAACiD;EACzB,CAAC;EAGDM,eAAe,EAAE;IACfrD,YAAY,EAAEhB,OAAO,CAACgB,YAAY,CAACyC;EACrC,CAAC;EACDa,eAAe,EAAE;IACftD,YAAY,EAAEhB,OAAO,CAACgB,YAAY,CAAC2C;EACrC,CAAC;EACDY,eAAe,EAAE;IACfvD,YAAY,EAAEhB,OAAO,CAACgB,YAAY,CAAC6C;EACrC,CAAC;EACDW,eAAe,EAAE;IACfxD,YAAY,EAAEhB,OAAO,CAACgB,YAAY,CAAC+C;EACrC,CAAC;EAGDrB,YAAY,EAAE;IACZ+B,WAAW,EAAE1E,MAAM,CAAC2E,UAAU;IAC9BC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDrC,YAAY,EAAE;IACZsC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAEnF,MAAM,CAACoF,OAAO;IAC3BL,aAAa,EAAE,CAAC;IAChBE,SAAS,EAAE;EACb,CAAC;EACDpC,UAAU,EAAE;IACVQ,eAAe,EAAErD,MAAM,CAACqF,cAAc;IACtCN,aAAa,EAAE,CAAC;IAChBE,SAAS,EAAE;EACb,CAAC;EACDnC,YAAY,EAAE;IACZwC,OAAO,EAAEpF,UAAU,CAACoF,OAAO,CAACnE;EAC9B;AACF,CAAC,CAAC;AAEF,eAAed,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}