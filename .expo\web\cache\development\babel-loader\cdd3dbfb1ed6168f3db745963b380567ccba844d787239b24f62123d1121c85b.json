{"ast": null, "code": "import * as React from 'react';\nexport default function mergeRefs() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return function forwardRef(node) {\n    args.forEach(function (ref) {\n      if (ref == null) {\n        return;\n      }\n      if (typeof ref === 'function') {\n        ref(node);\n        return;\n      }\n      if (typeof ref === 'object') {\n        ref.current = node;\n        return;\n      }\n      console.error(\"mergeRefs cannot handle Refs of type boolean, number or string, received ref \" + String(ref));\n    });\n  };\n}", "map": {"version": 3, "names": ["React", "mergeRefs", "_len", "arguments", "length", "args", "Array", "_key", "forwardRef", "node", "for<PERSON>ach", "ref", "current", "console", "error", "String"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/modules/mergeRefs/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nexport default function mergeRefs() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return function forwardRef(node) {\n    args.forEach(ref => {\n      if (ref == null) {\n        return;\n      }\n      if (typeof ref === 'function') {\n        ref(node);\n        return;\n      }\n      if (typeof ref === 'object') {\n        ref.current = node;\n        return;\n      }\n      console.error(\"mergeRefs cannot handle Refs of type boolean, number or string, received ref \" + String(ref));\n    });\n  };\n}"], "mappings": "AASA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,SAASA,CAAA,EAAG;EAClC,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;IAC/BJ,IAAI,CAACK,OAAO,CAAC,UAAAC,GAAG,EAAI;MAClB,IAAIA,GAAG,IAAI,IAAI,EAAE;QACf;MACF;MACA,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;QAC7BA,GAAG,CAACF,IAAI,CAAC;QACT;MACF;MACA,IAAI,OAAOE,GAAG,KAAK,QAAQ,EAAE;QAC3BA,GAAG,CAACC,OAAO,GAAGH,IAAI;QAClB;MACF;MACAI,OAAO,CAACC,KAAK,CAAC,+EAA+E,GAAGC,MAAM,CAACJ,GAAG,CAAC,CAAC;IAC9G,CAAC,CAAC;EACJ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}