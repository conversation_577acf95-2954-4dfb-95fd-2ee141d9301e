{"version": 3, "sources": ["LongPressGestureHandler.ts"], "names": ["createHandler", "baseGestureHandlerProps", "longPressGestureHandlerProps", "longPressHandlerName", "LongPressGestureHandler", "name", "allowedProps", "config", "shouldCancelWhenOutside"], "mappings": "AAAA,OAAOA,aAAP,MAA0B,iBAA1B;AACA,SAEEC,uBAFF,QAGO,wBAHP;AAKA,OAAO,MAAMC,4BAA4B,GAAG,CAC1C,eAD0C,EAE1C,SAF0C,CAArC;AAiEP,OAAO,MAAMC,oBAAoB,GAAG,yBAA7B;AAGP;AACA,OAAO,MAAMC,uBAAuB,GAAGJ,aAAa,CAGlD;AACAK,EAAAA,IAAI,EAAEF,oBADN;AAEAG,EAAAA,YAAY,EAAE,CACZ,GAAGL,uBADS,EAEZ,GAAGC,4BAFS,CAFd;AAMAK,EAAAA,MAAM,EAAE;AACNC,IAAAA,uBAAuB,EAAE;AADnB;AANR,CAHkD,CAA7C", "sourcesContent": ["import createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\nexport const longPressGestureHandlerProps = [\n  'minDurationMs',\n  'maxDist',\n] as const;\n\nexport type LongPressGestureHandlerEventPayload = {\n  /**\n   * X coordinate, expressed in points, of the current position of the pointer\n   * (finger or a leading pointer when there are multiple fingers placed)\n   * relative to the view attached to the handler.\n   */\n  x: number;\n\n  /**\n   * Y coordinate, expressed in points, of the current position of the pointer\n   * (finger or a leading pointer when there are multiple fingers placed)\n   * relative to the view attached to the handler.\n   */\n  y: number;\n\n  /**\n   * X coordinate, expressed in points, of the current position of the pointer\n   * (finger or a leading pointer when there are multiple fingers placed)\n   * relative to the window. It is recommended to use `absoluteX` instead of\n   * `x` in cases when the view attached to the handler can be transformed as an\n   * effect of the gesture.\n   */\n  absoluteX: number;\n\n  /**\n   * Y coordinate, expressed in points, of the current position of the pointer\n   * (finger or a leading pointer when there are multiple fingers placed)\n   * relative to the window. It is recommended to use `absoluteY` instead of\n   * `y` in cases when the view attached to the handler can be transformed as an\n   * effect of the gesture.\n   */\n  absoluteY: number;\n\n  /**\n   * Duration of the long press (time since the start of the event), expressed\n   * in milliseconds.\n   */\n  duration: number;\n};\n\nexport interface LongPressGestureConfig {\n  /**\n   * Minimum time, expressed in milliseconds, that a finger must remain pressed on\n   * the corresponding view. The default value is 500.\n   */\n  minDurationMs?: number;\n\n  /**\n   * Maximum distance, expressed in points, that defines how far the finger is\n   * allowed to travel during a long press gesture. If the finger travels\n   * further than the defined distance and the handler hasn't yet activated, it\n   * will fail to recognize the gesture. The default value is 10.\n   */\n  maxDist?: number;\n}\n\nexport interface LongPressGestureHandlerProps\n  extends BaseGestureHandlerProps<LongPressGestureHandlerEventPayload>,\n    LongPressGestureConfig {}\n\nexport const longPressHandlerName = 'LongPressGestureHandler';\n\nexport type LongPressGestureHandler = typeof LongPressGestureHandler;\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const LongPressGestureHandler = createHandler<\n  LongPressGestureHandlerProps,\n  LongPressGestureHandlerEventPayload\n>({\n  name: longPressHandlerName,\n  allowedProps: [\n    ...baseGestureHandlerProps,\n    ...longPressGestureHandlerProps,\n  ] as const,\n  config: {\n    shouldCancelWhenOutside: true,\n  },\n});\n"]}