{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _createForOfIteratorHelperLoose from \"@babel/runtime/helpers/createForOfIteratorHelperLoose\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nvar _excluded = [\"ItemSeparatorComponent\", \"SectionSeparatorComponent\", \"renderItem\", \"renderSectionFooter\", \"renderSectionHeader\", \"sections\", \"stickySectionHeadersEnabled\"];\nimport View from \"../../../exports/View\";\nimport VirtualizedList from \"../VirtualizedList\";\nimport { keyExtractor as defaultKeyExtractor } from \"../VirtualizeUtils\";\nimport invariant from 'fbjs/lib/invariant';\nimport * as React from 'react';\nvar VirtualizedSectionList = function (_React$PureComponent) {\n  function VirtualizedSectionList() {\n    var _this;\n    _classCallCheck(this, VirtualizedSectionList);\n    _this = _callSuper(this, VirtualizedSectionList, arguments);\n    _this._keyExtractor = function (item, index) {\n      var info = _this._subExtractor(index);\n      return info && info.key || String(index);\n    };\n    _this._convertViewable = function (viewable) {\n      var _info$index;\n      invariant(viewable.index != null, 'Received a broken ViewToken');\n      var info = _this._subExtractor(viewable.index);\n      if (!info) {\n        return null;\n      }\n      var keyExtractorWithNullableIndex = info.section.keyExtractor;\n      var keyExtractorWithNonNullableIndex = _this.props.keyExtractor || defaultKeyExtractor;\n      var key = keyExtractorWithNullableIndex != null ? keyExtractorWithNullableIndex(viewable.item, info.index) : keyExtractorWithNonNullableIndex(viewable.item, (_info$index = info.index) !== null && _info$index !== void 0 ? _info$index : 0);\n      return _objectSpread(_objectSpread({}, viewable), {}, {\n        index: info.index,\n        key: key,\n        section: info.section\n      });\n    };\n    _this._onViewableItemsChanged = function (_ref) {\n      var viewableItems = _ref.viewableItems,\n        changed = _ref.changed;\n      var onViewableItemsChanged = _this.props.onViewableItemsChanged;\n      if (onViewableItemsChanged != null) {\n        onViewableItemsChanged({\n          viewableItems: viewableItems.map(_this._convertViewable, _this).filter(Boolean),\n          changed: changed.map(_this._convertViewable, _this).filter(Boolean)\n        });\n      }\n    };\n    _this._renderItem = function (listItemCount) {\n      return (function (_ref2) {\n          var item = _ref2.item,\n            index = _ref2.index;\n          var info = _this._subExtractor(index);\n          if (!info) {\n            return null;\n          }\n          var infoIndex = info.index;\n          if (infoIndex == null) {\n            var section = info.section;\n            if (info.header === true) {\n              var renderSectionHeader = _this.props.renderSectionHeader;\n              return renderSectionHeader ? renderSectionHeader({\n                section: section\n              }) : null;\n            } else {\n              var renderSectionFooter = _this.props.renderSectionFooter;\n              return renderSectionFooter ? renderSectionFooter({\n                section: section\n              }) : null;\n            }\n          } else {\n            var renderItem = info.section.renderItem || _this.props.renderItem;\n            var SeparatorComponent = _this._getSeparatorComponent(index, info, listItemCount);\n            invariant(renderItem, 'no renderItem!');\n            return React.createElement(ItemWithSeparator, {\n              SeparatorComponent: SeparatorComponent,\n              LeadingSeparatorComponent: infoIndex === 0 ? _this.props.SectionSeparatorComponent : undefined,\n              cellKey: info.key,\n              index: infoIndex,\n              item: item,\n              leadingItem: info.leadingItem,\n              leadingSection: info.leadingSection,\n              prevCellKey: (_this._subExtractor(index - 1) || {}).key,\n              setSelfHighlightCallback: _this._setUpdateHighlightFor,\n              setSelfUpdatePropsCallback: _this._setUpdatePropsFor,\n              updateHighlightFor: _this._updateHighlightFor,\n              updatePropsFor: _this._updatePropsFor,\n              renderItem: renderItem,\n              section: info.section,\n              trailingItem: info.trailingItem,\n              trailingSection: info.trailingSection,\n              inverted: !!_this.props.inverted\n            });\n          }\n        }\n      );\n    };\n    _this._updatePropsFor = function (cellKey, value) {\n      var updateProps = _this._updatePropsMap[cellKey];\n      if (updateProps != null) {\n        updateProps(value);\n      }\n    };\n    _this._updateHighlightFor = function (cellKey, value) {\n      var updateHighlight = _this._updateHighlightMap[cellKey];\n      if (updateHighlight != null) {\n        updateHighlight(value);\n      }\n    };\n    _this._setUpdateHighlightFor = function (cellKey, updateHighlightFn) {\n      if (updateHighlightFn != null) {\n        _this._updateHighlightMap[cellKey] = updateHighlightFn;\n      } else {\n        delete _this._updateHighlightFor[cellKey];\n      }\n    };\n    _this._setUpdatePropsFor = function (cellKey, updatePropsFn) {\n      if (updatePropsFn != null) {\n        _this._updatePropsMap[cellKey] = updatePropsFn;\n      } else {\n        delete _this._updatePropsMap[cellKey];\n      }\n    };\n    _this._updateHighlightMap = {};\n    _this._updatePropsMap = {};\n    _this._captureRef = function (ref) {\n      _this._listRef = ref;\n    };\n    return _this;\n  }\n  _inherits(VirtualizedSectionList, _React$PureComponent);\n  return _createClass(VirtualizedSectionList, [{\n    key: \"scrollToLocation\",\n    value: function scrollToLocation(params) {\n      var index = params.itemIndex;\n      for (var i = 0; i < params.sectionIndex; i++) {\n        index += this.props.getItemCount(this.props.sections[i].data) + 2;\n      }\n      var viewOffset = params.viewOffset || 0;\n      if (this._listRef == null) {\n        return;\n      }\n      if (params.itemIndex > 0 && this.props.stickySectionHeadersEnabled) {\n        var frame = this._listRef.__getFrameMetricsApprox(index - params.itemIndex, this._listRef.props);\n        viewOffset += frame.length;\n      }\n      var toIndexParams = _objectSpread(_objectSpread({}, params), {}, {\n        viewOffset: viewOffset,\n        index: index\n      });\n      this._listRef.scrollToIndex(toIndexParams);\n    }\n  }, {\n    key: \"getListRef\",\n    value: function getListRef() {\n      return this._listRef;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props = this.props,\n        ItemSeparatorComponent = _this$props.ItemSeparatorComponent,\n        SectionSeparatorComponent = _this$props.SectionSeparatorComponent,\n        _renderItem = _this$props.renderItem,\n        renderSectionFooter = _this$props.renderSectionFooter,\n        renderSectionHeader = _this$props.renderSectionHeader,\n        _sections = _this$props.sections,\n        stickySectionHeadersEnabled = _this$props.stickySectionHeadersEnabled,\n        passThroughProps = _objectWithoutPropertiesLoose(_this$props, _excluded);\n      var listHeaderOffset = this.props.ListHeaderComponent ? 1 : 0;\n      var stickyHeaderIndices = this.props.stickySectionHeadersEnabled ? [] : undefined;\n      var itemCount = 0;\n      for (var _iterator = _createForOfIteratorHelperLoose(this.props.sections), _step; !(_step = _iterator()).done;) {\n        var section = _step.value;\n        if (stickyHeaderIndices != null) {\n          stickyHeaderIndices.push(itemCount + listHeaderOffset);\n        }\n        itemCount += 2;\n        itemCount += this.props.getItemCount(section.data);\n      }\n      var renderItem = this._renderItem(itemCount);\n      return React.createElement(VirtualizedList, _extends({}, passThroughProps, {\n        keyExtractor: this._keyExtractor,\n        stickyHeaderIndices: stickyHeaderIndices,\n        renderItem: renderItem,\n        data: this.props.sections,\n        getItem: function getItem(sections, index) {\n          return _this2._getItem(_this2.props, sections, index);\n        },\n        getItemCount: function getItemCount() {\n          return itemCount;\n        },\n        onViewableItemsChanged: this.props.onViewableItemsChanged ? this._onViewableItemsChanged : undefined,\n        ref: this._captureRef\n      }));\n    }\n  }, {\n    key: \"_getItem\",\n    value: function _getItem(props, sections, index) {\n      if (!sections) {\n        return null;\n      }\n      var itemIdx = index - 1;\n      for (var i = 0; i < sections.length; i++) {\n        var section = sections[i];\n        var sectionData = section.data;\n        var itemCount = props.getItemCount(sectionData);\n        if (itemIdx === -1 || itemIdx === itemCount) {\n          return section;\n        } else if (itemIdx < itemCount) {\n          return props.getItem(sectionData, itemIdx);\n        } else {\n          itemIdx -= itemCount + 2;\n        }\n      }\n      return null;\n    }\n  }, {\n    key: \"_subExtractor\",\n    value: function _subExtractor(index) {\n      var itemIndex = index;\n      var _this$props2 = this.props,\n        getItem = _this$props2.getItem,\n        getItemCount = _this$props2.getItemCount,\n        keyExtractor = _this$props2.keyExtractor,\n        sections = _this$props2.sections;\n      for (var i = 0; i < sections.length; i++) {\n        var section = sections[i];\n        var sectionData = section.data;\n        var key = section.key || String(i);\n        itemIndex -= 1;\n        if (itemIndex >= getItemCount(sectionData) + 1) {\n          itemIndex -= getItemCount(sectionData) + 1;\n        } else if (itemIndex === -1) {\n          return {\n            section: section,\n            key: key + ':header',\n            index: null,\n            header: true,\n            trailingSection: sections[i + 1]\n          };\n        } else if (itemIndex === getItemCount(sectionData)) {\n          return {\n            section: section,\n            key: key + ':footer',\n            index: null,\n            header: false,\n            trailingSection: sections[i + 1]\n          };\n        } else {\n          var extractor = section.keyExtractor || keyExtractor || defaultKeyExtractor;\n          return {\n            section: section,\n            key: key + ':' + extractor(getItem(sectionData, itemIndex), itemIndex),\n            index: itemIndex,\n            leadingItem: getItem(sectionData, itemIndex - 1),\n            leadingSection: sections[i - 1],\n            trailingItem: getItem(sectionData, itemIndex + 1),\n            trailingSection: sections[i + 1]\n          };\n        }\n      }\n    }\n  }, {\n    key: \"_getSeparatorComponent\",\n    value: function _getSeparatorComponent(index, info, listItemCount) {\n      info = info || this._subExtractor(index);\n      if (!info) {\n        return null;\n      }\n      var ItemSeparatorComponent = info.section.ItemSeparatorComponent || this.props.ItemSeparatorComponent;\n      var SectionSeparatorComponent = this.props.SectionSeparatorComponent;\n      var isLastItemInList = index === listItemCount - 1;\n      var isLastItemInSection = info.index === this.props.getItemCount(info.section.data) - 1;\n      if (SectionSeparatorComponent && isLastItemInSection) {\n        return SectionSeparatorComponent;\n      }\n      if (ItemSeparatorComponent && !isLastItemInSection && !isLastItemInList) {\n        return ItemSeparatorComponent;\n      }\n      return null;\n    }\n  }]);\n}(React.PureComponent);\nfunction ItemWithSeparator(props) {\n  var LeadingSeparatorComponent = props.LeadingSeparatorComponent,\n    SeparatorComponent = props.SeparatorComponent,\n    cellKey = props.cellKey,\n    prevCellKey = props.prevCellKey,\n    setSelfHighlightCallback = props.setSelfHighlightCallback,\n    updateHighlightFor = props.updateHighlightFor,\n    setSelfUpdatePropsCallback = props.setSelfUpdatePropsCallback,\n    updatePropsFor = props.updatePropsFor,\n    item = props.item,\n    index = props.index,\n    section = props.section,\n    inverted = props.inverted;\n  var _React$useState = React.useState(false),\n    leadingSeparatorHiglighted = _React$useState[0],\n    setLeadingSeparatorHighlighted = _React$useState[1];\n  var _React$useState2 = React.useState(false),\n    separatorHighlighted = _React$useState2[0],\n    setSeparatorHighlighted = _React$useState2[1];\n  var _React$useState3 = React.useState({\n      leadingItem: props.leadingItem,\n      leadingSection: props.leadingSection,\n      section: props.section,\n      trailingItem: props.item,\n      trailingSection: props.trailingSection\n    }),\n    leadingSeparatorProps = _React$useState3[0],\n    setLeadingSeparatorProps = _React$useState3[1];\n  var _React$useState4 = React.useState({\n      leadingItem: props.item,\n      leadingSection: props.leadingSection,\n      section: props.section,\n      trailingItem: props.trailingItem,\n      trailingSection: props.trailingSection\n    }),\n    separatorProps = _React$useState4[0],\n    setSeparatorProps = _React$useState4[1];\n  React.useEffect(function () {\n    setSelfHighlightCallback(cellKey, setSeparatorHighlighted);\n    setSelfUpdatePropsCallback(cellKey, setSeparatorProps);\n    return function () {\n      setSelfUpdatePropsCallback(cellKey, null);\n      setSelfHighlightCallback(cellKey, null);\n    };\n  }, [cellKey, setSelfHighlightCallback, setSeparatorProps, setSelfUpdatePropsCallback]);\n  var separators = {\n    highlight: function highlight() {\n      setLeadingSeparatorHighlighted(true);\n      setSeparatorHighlighted(true);\n      if (prevCellKey != null) {\n        updateHighlightFor(prevCellKey, true);\n      }\n    },\n    unhighlight: function unhighlight() {\n      setLeadingSeparatorHighlighted(false);\n      setSeparatorHighlighted(false);\n      if (prevCellKey != null) {\n        updateHighlightFor(prevCellKey, false);\n      }\n    },\n    updateProps: function updateProps(select, newProps) {\n      if (select === 'leading') {\n        if (LeadingSeparatorComponent != null) {\n          setLeadingSeparatorProps(_objectSpread(_objectSpread({}, leadingSeparatorProps), newProps));\n        } else if (prevCellKey != null) {\n          updatePropsFor(prevCellKey, _objectSpread(_objectSpread({}, leadingSeparatorProps), newProps));\n        }\n      } else if (select === 'trailing' && SeparatorComponent != null) {\n        setSeparatorProps(_objectSpread(_objectSpread({}, separatorProps), newProps));\n      }\n    }\n  };\n  var element = props.renderItem({\n    item: item,\n    index: index,\n    section: section,\n    separators: separators\n  });\n  var leadingSeparator = LeadingSeparatorComponent != null && React.createElement(LeadingSeparatorComponent, _extends({\n    highlighted: leadingSeparatorHiglighted\n  }, leadingSeparatorProps));\n  var separator = SeparatorComponent != null && React.createElement(SeparatorComponent, _extends({\n    highlighted: separatorHighlighted\n  }, separatorProps));\n  return leadingSeparator || separator ? React.createElement(View, null, inverted === false ? leadingSeparator : separator, element, inverted === false ? separator : leadingSeparator) : element;\n}\nexport default VirtualizedSectionList;", "map": {"version": 3, "names": ["_extends", "_createForOfIteratorHelperLoose", "_objectWithoutPropertiesLoose", "_objectSpread", "_excluded", "View", "VirtualizedList", "keyExtractor", "defaultKeyExtractor", "invariant", "React", "VirtualizedSectionList", "_React$PureComponent", "_this", "_classCallCheck", "_callSuper", "arguments", "_keyExtractor", "item", "index", "info", "_subExtractor", "key", "String", "_convertViewable", "viewable", "_info$index", "keyExtractorWithNullableIndex", "section", "keyExtractorWithNonNullableIndex", "props", "_onViewableItemsChanged", "_ref", "viewableItems", "changed", "onViewableItemsChanged", "map", "filter", "Boolean", "_renderItem", "listItemCount", "_ref2", "infoIndex", "header", "renderSectionHeader", "renderSectionFooter", "renderItem", "SeparatorComponent", "_getSeparatorComponent", "createElement", "ItemWithSeparator", "LeadingSeparatorComponent", "SectionSeparatorComponent", "undefined", "cellKey", "leadingItem", "leadingSection", "prevCell<PERSON>ey", "setSelfHighlightCallback", "_setUpdateHighlightFor", "setSelfUpdatePropsCallback", "_setUpdatePropsFor", "updateHighlightFor", "_updateHighlightFor", "updatePropsFor", "_updatePropsFor", "trailingItem", "trailingSection", "inverted", "value", "updateProps", "_updatePropsMap", "updateHighlight", "_updateHighlightMap", "updateHighlightFn", "updatePropsFn", "_captureRef", "ref", "_listRef", "_inherits", "_createClass", "scrollToLocation", "params", "itemIndex", "i", "sectionIndex", "getItemCount", "sections", "data", "viewOffset", "stickySectionHeadersEnabled", "frame", "__getFrameMetricsApprox", "length", "toIndexParams", "scrollToIndex", "getListRef", "render", "_this2", "_this$props", "ItemSeparatorComponent", "_sections", "passThroughProps", "listHeaderOffset", "ListHeaderComponent", "stickyHeaderIndices", "itemCount", "_iterator", "_step", "done", "push", "getItem", "_getItem", "itemIdx", "sectionData", "_this$props2", "extractor", "isLastItemInList", "isLastItemInSection", "PureComponent", "_React$useState", "useState", "leadingSeparatorHiglighted", "setLeadingSeparatorHighlighted", "_React$useState2", "separatorHighlighted", "setSeparatorHighlighted", "_React$useState3", "leadingSeparatorProps", "setLeadingSeparatorProps", "_React$useState4", "separatorProps", "setSeparatorProps", "useEffect", "separators", "highlight", "unhighlight", "select", "newProps", "element", "leadingSeparator", "highlighted", "separator"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/vendor/react-native/VirtualizedSectionList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _createForOfIteratorHelperLoose from \"@babel/runtime/helpers/createForOfIteratorHelperLoose\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nvar _excluded = [\"ItemSeparatorComponent\", \"SectionSeparatorComponent\", \"renderItem\", \"renderSectionFooter\", \"renderSectionHeader\", \"sections\", \"stickySectionHeadersEnabled\"];\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport View from '../../../exports/View';\nimport VirtualizedList from '../VirtualizedList';\nimport { keyExtractor as defaultKeyExtractor } from '../VirtualizeUtils';\nimport invariant from 'fbjs/lib/invariant';\nimport * as React from 'react';\n/**\n * Right now this just flattens everything into one list and uses VirtualizedList under the\n * hood. The only operation that might not scale well is concatting the data arrays of all the\n * sections when new props are received, which should be plenty fast for up to ~10,000 items.\n */\nclass VirtualizedSectionList extends React.PureComponent {\n  constructor() {\n    super(...arguments);\n    this._keyExtractor = (item, index) => {\n      var info = this._subExtractor(index);\n      return info && info.key || String(index);\n    };\n    this._convertViewable = viewable => {\n      var _info$index;\n      invariant(viewable.index != null, 'Received a broken ViewToken');\n      var info = this._subExtractor(viewable.index);\n      if (!info) {\n        return null;\n      }\n      var keyExtractorWithNullableIndex = info.section.keyExtractor;\n      var keyExtractorWithNonNullableIndex = this.props.keyExtractor || defaultKeyExtractor;\n      var key = keyExtractorWithNullableIndex != null ? keyExtractorWithNullableIndex(viewable.item, info.index) : keyExtractorWithNonNullableIndex(viewable.item, (_info$index = info.index) !== null && _info$index !== void 0 ? _info$index : 0);\n      return _objectSpread(_objectSpread({}, viewable), {}, {\n        index: info.index,\n        key,\n        section: info.section\n      });\n    };\n    this._onViewableItemsChanged = _ref => {\n      var viewableItems = _ref.viewableItems,\n        changed = _ref.changed;\n      var onViewableItemsChanged = this.props.onViewableItemsChanged;\n      if (onViewableItemsChanged != null) {\n        onViewableItemsChanged({\n          viewableItems: viewableItems.map(this._convertViewable, this).filter(Boolean),\n          changed: changed.map(this._convertViewable, this).filter(Boolean)\n        });\n      }\n    };\n    this._renderItem = listItemCount =>\n    // eslint-disable-next-line react/no-unstable-nested-components\n    _ref2 => {\n      var item = _ref2.item,\n        index = _ref2.index;\n      var info = this._subExtractor(index);\n      if (!info) {\n        return null;\n      }\n      var infoIndex = info.index;\n      if (infoIndex == null) {\n        var section = info.section;\n        if (info.header === true) {\n          var renderSectionHeader = this.props.renderSectionHeader;\n          return renderSectionHeader ? renderSectionHeader({\n            section\n          }) : null;\n        } else {\n          var renderSectionFooter = this.props.renderSectionFooter;\n          return renderSectionFooter ? renderSectionFooter({\n            section\n          }) : null;\n        }\n      } else {\n        var renderItem = info.section.renderItem || this.props.renderItem;\n        var SeparatorComponent = this._getSeparatorComponent(index, info, listItemCount);\n        invariant(renderItem, 'no renderItem!');\n        return /*#__PURE__*/React.createElement(ItemWithSeparator, {\n          SeparatorComponent: SeparatorComponent,\n          LeadingSeparatorComponent: infoIndex === 0 ? this.props.SectionSeparatorComponent : undefined,\n          cellKey: info.key,\n          index: infoIndex,\n          item: item,\n          leadingItem: info.leadingItem,\n          leadingSection: info.leadingSection,\n          prevCellKey: (this._subExtractor(index - 1) || {}).key\n          // Callback to provide updateHighlight for this item\n          ,\n          setSelfHighlightCallback: this._setUpdateHighlightFor,\n          setSelfUpdatePropsCallback: this._setUpdatePropsFor\n          // Provide child ability to set highlight/updateProps for previous item using prevCellKey\n          ,\n          updateHighlightFor: this._updateHighlightFor,\n          updatePropsFor: this._updatePropsFor,\n          renderItem: renderItem,\n          section: info.section,\n          trailingItem: info.trailingItem,\n          trailingSection: info.trailingSection,\n          inverted: !!this.props.inverted\n        });\n      }\n    };\n    this._updatePropsFor = (cellKey, value) => {\n      var updateProps = this._updatePropsMap[cellKey];\n      if (updateProps != null) {\n        updateProps(value);\n      }\n    };\n    this._updateHighlightFor = (cellKey, value) => {\n      var updateHighlight = this._updateHighlightMap[cellKey];\n      if (updateHighlight != null) {\n        updateHighlight(value);\n      }\n    };\n    this._setUpdateHighlightFor = (cellKey, updateHighlightFn) => {\n      if (updateHighlightFn != null) {\n        this._updateHighlightMap[cellKey] = updateHighlightFn;\n      } else {\n        // $FlowFixMe[prop-missing]\n        delete this._updateHighlightFor[cellKey];\n      }\n    };\n    this._setUpdatePropsFor = (cellKey, updatePropsFn) => {\n      if (updatePropsFn != null) {\n        this._updatePropsMap[cellKey] = updatePropsFn;\n      } else {\n        delete this._updatePropsMap[cellKey];\n      }\n    };\n    this._updateHighlightMap = {};\n    this._updatePropsMap = {};\n    this._captureRef = ref => {\n      this._listRef = ref;\n    };\n  }\n  scrollToLocation(params) {\n    var index = params.itemIndex;\n    for (var i = 0; i < params.sectionIndex; i++) {\n      index += this.props.getItemCount(this.props.sections[i].data) + 2;\n    }\n    var viewOffset = params.viewOffset || 0;\n    if (this._listRef == null) {\n      return;\n    }\n    if (params.itemIndex > 0 && this.props.stickySectionHeadersEnabled) {\n      var frame = this._listRef.__getFrameMetricsApprox(index - params.itemIndex, this._listRef.props);\n      viewOffset += frame.length;\n    }\n    var toIndexParams = _objectSpread(_objectSpread({}, params), {}, {\n      viewOffset,\n      index\n    });\n    // $FlowFixMe[incompatible-use]\n    this._listRef.scrollToIndex(toIndexParams);\n  }\n  getListRef() {\n    return this._listRef;\n  }\n  render() {\n    var _this$props = this.props,\n      ItemSeparatorComponent = _this$props.ItemSeparatorComponent,\n      SectionSeparatorComponent = _this$props.SectionSeparatorComponent,\n      _renderItem = _this$props.renderItem,\n      renderSectionFooter = _this$props.renderSectionFooter,\n      renderSectionHeader = _this$props.renderSectionHeader,\n      _sections = _this$props.sections,\n      stickySectionHeadersEnabled = _this$props.stickySectionHeadersEnabled,\n      passThroughProps = _objectWithoutPropertiesLoose(_this$props, _excluded);\n    var listHeaderOffset = this.props.ListHeaderComponent ? 1 : 0;\n    var stickyHeaderIndices = this.props.stickySectionHeadersEnabled ? [] : undefined;\n    var itemCount = 0;\n    for (var _iterator = _createForOfIteratorHelperLoose(this.props.sections), _step; !(_step = _iterator()).done;) {\n      var section = _step.value;\n      // Track the section header indices\n      if (stickyHeaderIndices != null) {\n        stickyHeaderIndices.push(itemCount + listHeaderOffset);\n      }\n\n      // Add two for the section header and footer.\n      itemCount += 2;\n      itemCount += this.props.getItemCount(section.data);\n    }\n    var renderItem = this._renderItem(itemCount);\n    return /*#__PURE__*/React.createElement(VirtualizedList, _extends({}, passThroughProps, {\n      keyExtractor: this._keyExtractor,\n      stickyHeaderIndices: stickyHeaderIndices,\n      renderItem: renderItem,\n      data: this.props.sections,\n      getItem: (sections, index) => this._getItem(this.props, sections, index),\n      getItemCount: () => itemCount,\n      onViewableItemsChanged: this.props.onViewableItemsChanged ? this._onViewableItemsChanged : undefined,\n      ref: this._captureRef\n    }));\n  }\n  _getItem(props, sections, index) {\n    if (!sections) {\n      return null;\n    }\n    var itemIdx = index - 1;\n    for (var i = 0; i < sections.length; i++) {\n      var section = sections[i];\n      var sectionData = section.data;\n      var itemCount = props.getItemCount(sectionData);\n      if (itemIdx === -1 || itemIdx === itemCount) {\n        // We intend for there to be overflow by one on both ends of the list.\n        // This will be for headers and footers. When returning a header or footer\n        // item the section itself is the item.\n        return section;\n      } else if (itemIdx < itemCount) {\n        // If we are in the bounds of the list's data then return the item.\n        return props.getItem(sectionData, itemIdx);\n      } else {\n        itemIdx -= itemCount + 2; // Add two for the header and footer\n      }\n    }\n    return null;\n  }\n\n  // $FlowFixMe[missing-local-annot]\n\n  _subExtractor(index) {\n    var itemIndex = index;\n    var _this$props2 = this.props,\n      getItem = _this$props2.getItem,\n      getItemCount = _this$props2.getItemCount,\n      keyExtractor = _this$props2.keyExtractor,\n      sections = _this$props2.sections;\n    for (var i = 0; i < sections.length; i++) {\n      var section = sections[i];\n      var sectionData = section.data;\n      var key = section.key || String(i);\n      itemIndex -= 1; // The section adds an item for the header\n      if (itemIndex >= getItemCount(sectionData) + 1) {\n        itemIndex -= getItemCount(sectionData) + 1; // The section adds an item for the footer.\n      } else if (itemIndex === -1) {\n        return {\n          section,\n          key: key + ':header',\n          index: null,\n          header: true,\n          trailingSection: sections[i + 1]\n        };\n      } else if (itemIndex === getItemCount(sectionData)) {\n        return {\n          section,\n          key: key + ':footer',\n          index: null,\n          header: false,\n          trailingSection: sections[i + 1]\n        };\n      } else {\n        var extractor = section.keyExtractor || keyExtractor || defaultKeyExtractor;\n        return {\n          section,\n          key: key + ':' + extractor(getItem(sectionData, itemIndex), itemIndex),\n          index: itemIndex,\n          leadingItem: getItem(sectionData, itemIndex - 1),\n          leadingSection: sections[i - 1],\n          trailingItem: getItem(sectionData, itemIndex + 1),\n          trailingSection: sections[i + 1]\n        };\n      }\n    }\n  }\n  _getSeparatorComponent(index, info, listItemCount) {\n    info = info || this._subExtractor(index);\n    if (!info) {\n      return null;\n    }\n    var ItemSeparatorComponent = info.section.ItemSeparatorComponent || this.props.ItemSeparatorComponent;\n    var SectionSeparatorComponent = this.props.SectionSeparatorComponent;\n    var isLastItemInList = index === listItemCount - 1;\n    var isLastItemInSection = info.index === this.props.getItemCount(info.section.data) - 1;\n    if (SectionSeparatorComponent && isLastItemInSection) {\n      return SectionSeparatorComponent;\n    }\n    if (ItemSeparatorComponent && !isLastItemInSection && !isLastItemInList) {\n      return ItemSeparatorComponent;\n    }\n    return null;\n  }\n}\nfunction ItemWithSeparator(props) {\n  var LeadingSeparatorComponent = props.LeadingSeparatorComponent,\n    SeparatorComponent = props.SeparatorComponent,\n    cellKey = props.cellKey,\n    prevCellKey = props.prevCellKey,\n    setSelfHighlightCallback = props.setSelfHighlightCallback,\n    updateHighlightFor = props.updateHighlightFor,\n    setSelfUpdatePropsCallback = props.setSelfUpdatePropsCallback,\n    updatePropsFor = props.updatePropsFor,\n    item = props.item,\n    index = props.index,\n    section = props.section,\n    inverted = props.inverted;\n  var _React$useState = React.useState(false),\n    leadingSeparatorHiglighted = _React$useState[0],\n    setLeadingSeparatorHighlighted = _React$useState[1];\n  var _React$useState2 = React.useState(false),\n    separatorHighlighted = _React$useState2[0],\n    setSeparatorHighlighted = _React$useState2[1];\n  var _React$useState3 = React.useState({\n      leadingItem: props.leadingItem,\n      leadingSection: props.leadingSection,\n      section: props.section,\n      trailingItem: props.item,\n      trailingSection: props.trailingSection\n    }),\n    leadingSeparatorProps = _React$useState3[0],\n    setLeadingSeparatorProps = _React$useState3[1];\n  var _React$useState4 = React.useState({\n      leadingItem: props.item,\n      leadingSection: props.leadingSection,\n      section: props.section,\n      trailingItem: props.trailingItem,\n      trailingSection: props.trailingSection\n    }),\n    separatorProps = _React$useState4[0],\n    setSeparatorProps = _React$useState4[1];\n  React.useEffect(() => {\n    setSelfHighlightCallback(cellKey, setSeparatorHighlighted);\n    // $FlowFixMe[incompatible-call]\n    setSelfUpdatePropsCallback(cellKey, setSeparatorProps);\n    return () => {\n      setSelfUpdatePropsCallback(cellKey, null);\n      setSelfHighlightCallback(cellKey, null);\n    };\n  }, [cellKey, setSelfHighlightCallback, setSeparatorProps, setSelfUpdatePropsCallback]);\n  var separators = {\n    highlight: () => {\n      setLeadingSeparatorHighlighted(true);\n      setSeparatorHighlighted(true);\n      if (prevCellKey != null) {\n        updateHighlightFor(prevCellKey, true);\n      }\n    },\n    unhighlight: () => {\n      setLeadingSeparatorHighlighted(false);\n      setSeparatorHighlighted(false);\n      if (prevCellKey != null) {\n        updateHighlightFor(prevCellKey, false);\n      }\n    },\n    updateProps: (select, newProps) => {\n      if (select === 'leading') {\n        if (LeadingSeparatorComponent != null) {\n          setLeadingSeparatorProps(_objectSpread(_objectSpread({}, leadingSeparatorProps), newProps));\n        } else if (prevCellKey != null) {\n          // update the previous item's separator\n          updatePropsFor(prevCellKey, _objectSpread(_objectSpread({}, leadingSeparatorProps), newProps));\n        }\n      } else if (select === 'trailing' && SeparatorComponent != null) {\n        setSeparatorProps(_objectSpread(_objectSpread({}, separatorProps), newProps));\n      }\n    }\n  };\n  var element = props.renderItem({\n    item,\n    index,\n    section,\n    separators\n  });\n  var leadingSeparator = LeadingSeparatorComponent != null && /*#__PURE__*/React.createElement(LeadingSeparatorComponent, _extends({\n    highlighted: leadingSeparatorHiglighted\n  }, leadingSeparatorProps));\n  var separator = SeparatorComponent != null && /*#__PURE__*/React.createElement(SeparatorComponent, _extends({\n    highlighted: separatorHighlighted\n  }, separatorProps));\n  return leadingSeparator || separator ? /*#__PURE__*/React.createElement(View, null, inverted === false ? leadingSeparator : separator, element, inverted === false ? separator : leadingSeparator) : element;\n}\n\n/* $FlowFixMe[class-object-subtyping] added when improving typing for this\n * parameters */\n// $FlowFixMe[method-unbinding]\nexport default VirtualizedSectionList;"], "mappings": ";;;;;;;AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,+BAA+B,MAAM,uDAAuD;AACnG,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,OAAOC,aAAa,MAAM,sCAAsC;AAChE,IAAIC,SAAS,GAAG,CAAC,wBAAwB,EAAE,2BAA2B,EAAE,YAAY,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,UAAU,EAAE,6BAA6B,CAAC;AAW9K,OAAOC,IAAI;AACX,OAAOC,eAAe;AACtB,SAASC,YAAY,IAAIC,mBAAmB;AAC5C,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAC,IAMzBC,sBAAsB,aAAAC,oBAAA;EAC1B,SAAAD,uBAAA,EAAc;IAAA,IAAAE,KAAA;IAAAC,eAAA,OAAAH,sBAAA;IACZE,KAAA,GAAAE,UAAA,OAAAJ,sBAAA,EAASK,SAAS;IAClBH,KAAA,CAAKI,aAAa,GAAG,UAACC,IAAI,EAAEC,KAAK,EAAK;MACpC,IAAIC,IAAI,GAAGP,KAAA,CAAKQ,aAAa,CAACF,KAAK,CAAC;MACpC,OAAOC,IAAI,IAAIA,IAAI,CAACE,GAAG,IAAIC,MAAM,CAACJ,KAAK,CAAC;IAC1C,CAAC;IACDN,KAAA,CAAKW,gBAAgB,GAAG,UAAAC,QAAQ,EAAI;MAClC,IAAIC,WAAW;MACfjB,SAAS,CAACgB,QAAQ,CAACN,KAAK,IAAI,IAAI,EAAE,6BAA6B,CAAC;MAChE,IAAIC,IAAI,GAAGP,KAAA,CAAKQ,aAAa,CAACI,QAAQ,CAACN,KAAK,CAAC;MAC7C,IAAI,CAACC,IAAI,EAAE;QACT,OAAO,IAAI;MACb;MACA,IAAIO,6BAA6B,GAAGP,IAAI,CAACQ,OAAO,CAACrB,YAAY;MAC7D,IAAIsB,gCAAgC,GAAGhB,KAAA,CAAKiB,KAAK,CAACvB,YAAY,IAAIC,mBAAmB;MACrF,IAAIc,GAAG,GAAGK,6BAA6B,IAAI,IAAI,GAAGA,6BAA6B,CAACF,QAAQ,CAACP,IAAI,EAAEE,IAAI,CAACD,KAAK,CAAC,GAAGU,gCAAgC,CAACJ,QAAQ,CAACP,IAAI,EAAE,CAACQ,WAAW,GAAGN,IAAI,CAACD,KAAK,MAAM,IAAI,IAAIO,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,CAAC,CAAC;MAC7O,OAAOvB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsB,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;QACpDN,KAAK,EAAEC,IAAI,CAACD,KAAK;QACjBG,GAAG,EAAHA,GAAG;QACHM,OAAO,EAAER,IAAI,CAACQ;MAChB,CAAC,CAAC;IACJ,CAAC;IACDf,KAAA,CAAKkB,uBAAuB,GAAG,UAAAC,IAAI,EAAI;MACrC,IAAIC,aAAa,GAAGD,IAAI,CAACC,aAAa;QACpCC,OAAO,GAAGF,IAAI,CAACE,OAAO;MACxB,IAAIC,sBAAsB,GAAGtB,KAAA,CAAKiB,KAAK,CAACK,sBAAsB;MAC9D,IAAIA,sBAAsB,IAAI,IAAI,EAAE;QAClCA,sBAAsB,CAAC;UACrBF,aAAa,EAAEA,aAAa,CAACG,GAAG,CAACvB,KAAA,CAAKW,gBAAgB,EAAAX,KAAM,CAAC,CAACwB,MAAM,CAACC,OAAO,CAAC;UAC7EJ,OAAO,EAAEA,OAAO,CAACE,GAAG,CAACvB,KAAA,CAAKW,gBAAgB,EAAAX,KAAM,CAAC,CAACwB,MAAM,CAACC,OAAO;QAClE,CAAC,CAAC;MACJ;IACF,CAAC;IACDzB,KAAA,CAAK0B,WAAW,GAAG,UAAAC,aAAa;MAAA,QAEhC,UAAAC,KAAK,EAAI;UACP,IAAIvB,IAAI,GAAGuB,KAAK,CAACvB,IAAI;YACnBC,KAAK,GAAGsB,KAAK,CAACtB,KAAK;UACrB,IAAIC,IAAI,GAAGP,KAAA,CAAKQ,aAAa,CAACF,KAAK,CAAC;UACpC,IAAI,CAACC,IAAI,EAAE;YACT,OAAO,IAAI;UACb;UACA,IAAIsB,SAAS,GAAGtB,IAAI,CAACD,KAAK;UAC1B,IAAIuB,SAAS,IAAI,IAAI,EAAE;YACrB,IAAId,OAAO,GAAGR,IAAI,CAACQ,OAAO;YAC1B,IAAIR,IAAI,CAACuB,MAAM,KAAK,IAAI,EAAE;cACxB,IAAIC,mBAAmB,GAAG/B,KAAA,CAAKiB,KAAK,CAACc,mBAAmB;cACxD,OAAOA,mBAAmB,GAAGA,mBAAmB,CAAC;gBAC/ChB,OAAO,EAAPA;cACF,CAAC,CAAC,GAAG,IAAI;YACX,CAAC,MAAM;cACL,IAAIiB,mBAAmB,GAAGhC,KAAA,CAAKiB,KAAK,CAACe,mBAAmB;cACxD,OAAOA,mBAAmB,GAAGA,mBAAmB,CAAC;gBAC/CjB,OAAO,EAAPA;cACF,CAAC,CAAC,GAAG,IAAI;YACX;UACF,CAAC,MAAM;YACL,IAAIkB,UAAU,GAAG1B,IAAI,CAACQ,OAAO,CAACkB,UAAU,IAAIjC,KAAA,CAAKiB,KAAK,CAACgB,UAAU;YACjE,IAAIC,kBAAkB,GAAGlC,KAAA,CAAKmC,sBAAsB,CAAC7B,KAAK,EAAEC,IAAI,EAAEoB,aAAa,CAAC;YAChF/B,SAAS,CAACqC,UAAU,EAAE,gBAAgB,CAAC;YACvC,OAAoBpC,KAAK,CAACuC,aAAa,CAACC,iBAAiB,EAAE;cACzDH,kBAAkB,EAAEA,kBAAkB;cACtCI,yBAAyB,EAAET,SAAS,KAAK,CAAC,GAAG7B,KAAA,CAAKiB,KAAK,CAACsB,yBAAyB,GAAGC,SAAS;cAC7FC,OAAO,EAAElC,IAAI,CAACE,GAAG;cACjBH,KAAK,EAAEuB,SAAS;cAChBxB,IAAI,EAAEA,IAAI;cACVqC,WAAW,EAAEnC,IAAI,CAACmC,WAAW;cAC7BC,cAAc,EAAEpC,IAAI,CAACoC,cAAc;cACnCC,WAAW,EAAE,CAAC5C,KAAA,CAAKQ,aAAa,CAACF,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEG,GAAG;cAGtDoC,wBAAwB,EAAE7C,KAAA,CAAK8C,sBAAsB;cACrDC,0BAA0B,EAAE/C,KAAA,CAAKgD,kBAAkB;cAGnDC,kBAAkB,EAAEjD,KAAA,CAAKkD,mBAAmB;cAC5CC,cAAc,EAAEnD,KAAA,CAAKoD,eAAe;cACpCnB,UAAU,EAAEA,UAAU;cACtBlB,OAAO,EAAER,IAAI,CAACQ,OAAO;cACrBsC,YAAY,EAAE9C,IAAI,CAAC8C,YAAY;cAC/BC,eAAe,EAAE/C,IAAI,CAAC+C,eAAe;cACrCC,QAAQ,EAAE,CAAC,CAACvD,KAAA,CAAKiB,KAAK,CAACsC;YACzB,CAAC,CAAC;UACJ;QACF;MAAC;IAAA;IACDvD,KAAA,CAAKoD,eAAe,GAAG,UAACX,OAAO,EAAEe,KAAK,EAAK;MACzC,IAAIC,WAAW,GAAGzD,KAAA,CAAK0D,eAAe,CAACjB,OAAO,CAAC;MAC/C,IAAIgB,WAAW,IAAI,IAAI,EAAE;QACvBA,WAAW,CAACD,KAAK,CAAC;MACpB;IACF,CAAC;IACDxD,KAAA,CAAKkD,mBAAmB,GAAG,UAACT,OAAO,EAAEe,KAAK,EAAK;MAC7C,IAAIG,eAAe,GAAG3D,KAAA,CAAK4D,mBAAmB,CAACnB,OAAO,CAAC;MACvD,IAAIkB,eAAe,IAAI,IAAI,EAAE;QAC3BA,eAAe,CAACH,KAAK,CAAC;MACxB;IACF,CAAC;IACDxD,KAAA,CAAK8C,sBAAsB,GAAG,UAACL,OAAO,EAAEoB,iBAAiB,EAAK;MAC5D,IAAIA,iBAAiB,IAAI,IAAI,EAAE;QAC7B7D,KAAA,CAAK4D,mBAAmB,CAACnB,OAAO,CAAC,GAAGoB,iBAAiB;MACvD,CAAC,MAAM;QAEL,OAAO7D,KAAA,CAAKkD,mBAAmB,CAACT,OAAO,CAAC;MAC1C;IACF,CAAC;IACDzC,KAAA,CAAKgD,kBAAkB,GAAG,UAACP,OAAO,EAAEqB,aAAa,EAAK;MACpD,IAAIA,aAAa,IAAI,IAAI,EAAE;QACzB9D,KAAA,CAAK0D,eAAe,CAACjB,OAAO,CAAC,GAAGqB,aAAa;MAC/C,CAAC,MAAM;QACL,OAAO9D,KAAA,CAAK0D,eAAe,CAACjB,OAAO,CAAC;MACtC;IACF,CAAC;IACDzC,KAAA,CAAK4D,mBAAmB,GAAG,CAAC,CAAC;IAC7B5D,KAAA,CAAK0D,eAAe,GAAG,CAAC,CAAC;IACzB1D,KAAA,CAAK+D,WAAW,GAAG,UAAAC,GAAG,EAAI;MACxBhE,KAAA,CAAKiE,QAAQ,GAAGD,GAAG;IACrB,CAAC;IAAC,OAAAhE,KAAA;EACJ;EAACkE,SAAA,CAAApE,sBAAA,EAAAC,oBAAA;EAAA,OAAAoE,YAAA,CAAArE,sBAAA;IAAAW,GAAA;IAAA+C,KAAA,EACD,SAAAY,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAI/D,KAAK,GAAG+D,MAAM,CAACC,SAAS;MAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,YAAY,EAAED,CAAC,EAAE,EAAE;QAC5CjE,KAAK,IAAI,IAAI,CAACW,KAAK,CAACwD,YAAY,CAAC,IAAI,CAACxD,KAAK,CAACyD,QAAQ,CAACH,CAAC,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC;MACnE;MACA,IAAIC,UAAU,GAAGP,MAAM,CAACO,UAAU,IAAI,CAAC;MACvC,IAAI,IAAI,CAACX,QAAQ,IAAI,IAAI,EAAE;QACzB;MACF;MACA,IAAII,MAAM,CAACC,SAAS,GAAG,CAAC,IAAI,IAAI,CAACrD,KAAK,CAAC4D,2BAA2B,EAAE;QAClE,IAAIC,KAAK,GAAG,IAAI,CAACb,QAAQ,CAACc,uBAAuB,CAACzE,KAAK,GAAG+D,MAAM,CAACC,SAAS,EAAE,IAAI,CAACL,QAAQ,CAAChD,KAAK,CAAC;QAChG2D,UAAU,IAAIE,KAAK,CAACE,MAAM;MAC5B;MACA,IAAIC,aAAa,GAAG3F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+E,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/DO,UAAU,EAAVA,UAAU;QACVtE,KAAK,EAALA;MACF,CAAC,CAAC;MAEF,IAAI,CAAC2D,QAAQ,CAACiB,aAAa,CAACD,aAAa,CAAC;IAC5C;EAAC;IAAAxE,GAAA;IAAA+C,KAAA,EACD,SAAA2B,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAAClB,QAAQ;IACtB;EAAC;IAAAxD,GAAA;IAAA+C,KAAA,EACD,SAAA4B,MAAMA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACP,IAAIC,WAAW,GAAG,IAAI,CAACrE,KAAK;QAC1BsE,sBAAsB,GAAGD,WAAW,CAACC,sBAAsB;QAC3DhD,yBAAyB,GAAG+C,WAAW,CAAC/C,yBAAyB;QACjEb,WAAW,GAAG4D,WAAW,CAACrD,UAAU;QACpCD,mBAAmB,GAAGsD,WAAW,CAACtD,mBAAmB;QACrDD,mBAAmB,GAAGuD,WAAW,CAACvD,mBAAmB;QACrDyD,SAAS,GAAGF,WAAW,CAACZ,QAAQ;QAChCG,2BAA2B,GAAGS,WAAW,CAACT,2BAA2B;QACrEY,gBAAgB,GAAGpG,6BAA6B,CAACiG,WAAW,EAAE/F,SAAS,CAAC;MAC1E,IAAImG,gBAAgB,GAAG,IAAI,CAACzE,KAAK,CAAC0E,mBAAmB,GAAG,CAAC,GAAG,CAAC;MAC7D,IAAIC,mBAAmB,GAAG,IAAI,CAAC3E,KAAK,CAAC4D,2BAA2B,GAAG,EAAE,GAAGrC,SAAS;MACjF,IAAIqD,SAAS,GAAG,CAAC;MACjB,KAAK,IAAIC,SAAS,GAAG1G,+BAA+B,CAAC,IAAI,CAAC6B,KAAK,CAACyD,QAAQ,CAAC,EAAEqB,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAEE,IAAI,GAAG;QAC9G,IAAIjF,OAAO,GAAGgF,KAAK,CAACvC,KAAK;QAEzB,IAAIoC,mBAAmB,IAAI,IAAI,EAAE;UAC/BA,mBAAmB,CAACK,IAAI,CAACJ,SAAS,GAAGH,gBAAgB,CAAC;QACxD;QAGAG,SAAS,IAAI,CAAC;QACdA,SAAS,IAAI,IAAI,CAAC5E,KAAK,CAACwD,YAAY,CAAC1D,OAAO,CAAC4D,IAAI,CAAC;MACpD;MACA,IAAI1C,UAAU,GAAG,IAAI,CAACP,WAAW,CAACmE,SAAS,CAAC;MAC5C,OAAoBhG,KAAK,CAACuC,aAAa,CAAC3C,eAAe,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEsG,gBAAgB,EAAE;QACtF/F,YAAY,EAAE,IAAI,CAACU,aAAa;QAChCwF,mBAAmB,EAAEA,mBAAmB;QACxC3D,UAAU,EAAEA,UAAU;QACtB0C,IAAI,EAAE,IAAI,CAAC1D,KAAK,CAACyD,QAAQ;QACzBwB,OAAO,EAAE,SAATA,OAAOA,CAAGxB,QAAQ,EAAEpE,KAAK;UAAA,OAAK+E,MAAI,CAACc,QAAQ,CAACd,MAAI,CAACpE,KAAK,EAAEyD,QAAQ,EAAEpE,KAAK,CAAC;QAAA;QACxEmE,YAAY,EAAE,SAAdA,YAAYA,CAAA;UAAA,OAAQoB,SAAS;QAAA;QAC7BvE,sBAAsB,EAAE,IAAI,CAACL,KAAK,CAACK,sBAAsB,GAAG,IAAI,CAACJ,uBAAuB,GAAGsB,SAAS;QACpGwB,GAAG,EAAE,IAAI,CAACD;MACZ,CAAC,CAAC,CAAC;IACL;EAAC;IAAAtD,GAAA;IAAA+C,KAAA,EACD,SAAA2C,QAAQA,CAAClF,KAAK,EAAEyD,QAAQ,EAAEpE,KAAK,EAAE;MAC/B,IAAI,CAACoE,QAAQ,EAAE;QACb,OAAO,IAAI;MACb;MACA,IAAI0B,OAAO,GAAG9F,KAAK,GAAG,CAAC;MACvB,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,QAAQ,CAACM,MAAM,EAAET,CAAC,EAAE,EAAE;QACxC,IAAIxD,OAAO,GAAG2D,QAAQ,CAACH,CAAC,CAAC;QACzB,IAAI8B,WAAW,GAAGtF,OAAO,CAAC4D,IAAI;QAC9B,IAAIkB,SAAS,GAAG5E,KAAK,CAACwD,YAAY,CAAC4B,WAAW,CAAC;QAC/C,IAAID,OAAO,KAAK,CAAC,CAAC,IAAIA,OAAO,KAAKP,SAAS,EAAE;UAI3C,OAAO9E,OAAO;QAChB,CAAC,MAAM,IAAIqF,OAAO,GAAGP,SAAS,EAAE;UAE9B,OAAO5E,KAAK,CAACiF,OAAO,CAACG,WAAW,EAAED,OAAO,CAAC;QAC5C,CAAC,MAAM;UACLA,OAAO,IAAIP,SAAS,GAAG,CAAC;QAC1B;MACF;MACA,OAAO,IAAI;IACb;EAAC;IAAApF,GAAA;IAAA+C,KAAA,EAID,SAAAhD,aAAaA,CAACF,KAAK,EAAE;MACnB,IAAIgE,SAAS,GAAGhE,KAAK;MACrB,IAAIgG,YAAY,GAAG,IAAI,CAACrF,KAAK;QAC3BiF,OAAO,GAAGI,YAAY,CAACJ,OAAO;QAC9BzB,YAAY,GAAG6B,YAAY,CAAC7B,YAAY;QACxC/E,YAAY,GAAG4G,YAAY,CAAC5G,YAAY;QACxCgF,QAAQ,GAAG4B,YAAY,CAAC5B,QAAQ;MAClC,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,QAAQ,CAACM,MAAM,EAAET,CAAC,EAAE,EAAE;QACxC,IAAIxD,OAAO,GAAG2D,QAAQ,CAACH,CAAC,CAAC;QACzB,IAAI8B,WAAW,GAAGtF,OAAO,CAAC4D,IAAI;QAC9B,IAAIlE,GAAG,GAAGM,OAAO,CAACN,GAAG,IAAIC,MAAM,CAAC6D,CAAC,CAAC;QAClCD,SAAS,IAAI,CAAC;QACd,IAAIA,SAAS,IAAIG,YAAY,CAAC4B,WAAW,CAAC,GAAG,CAAC,EAAE;UAC9C/B,SAAS,IAAIG,YAAY,CAAC4B,WAAW,CAAC,GAAG,CAAC;QAC5C,CAAC,MAAM,IAAI/B,SAAS,KAAK,CAAC,CAAC,EAAE;UAC3B,OAAO;YACLvD,OAAO,EAAPA,OAAO;YACPN,GAAG,EAAEA,GAAG,GAAG,SAAS;YACpBH,KAAK,EAAE,IAAI;YACXwB,MAAM,EAAE,IAAI;YACZwB,eAAe,EAAEoB,QAAQ,CAACH,CAAC,GAAG,CAAC;UACjC,CAAC;QACH,CAAC,MAAM,IAAID,SAAS,KAAKG,YAAY,CAAC4B,WAAW,CAAC,EAAE;UAClD,OAAO;YACLtF,OAAO,EAAPA,OAAO;YACPN,GAAG,EAAEA,GAAG,GAAG,SAAS;YACpBH,KAAK,EAAE,IAAI;YACXwB,MAAM,EAAE,KAAK;YACbwB,eAAe,EAAEoB,QAAQ,CAACH,CAAC,GAAG,CAAC;UACjC,CAAC;QACH,CAAC,MAAM;UACL,IAAIgC,SAAS,GAAGxF,OAAO,CAACrB,YAAY,IAAIA,YAAY,IAAIC,mBAAmB;UAC3E,OAAO;YACLoB,OAAO,EAAPA,OAAO;YACPN,GAAG,EAAEA,GAAG,GAAG,GAAG,GAAG8F,SAAS,CAACL,OAAO,CAACG,WAAW,EAAE/B,SAAS,CAAC,EAAEA,SAAS,CAAC;YACtEhE,KAAK,EAAEgE,SAAS;YAChB5B,WAAW,EAAEwD,OAAO,CAACG,WAAW,EAAE/B,SAAS,GAAG,CAAC,CAAC;YAChD3B,cAAc,EAAE+B,QAAQ,CAACH,CAAC,GAAG,CAAC,CAAC;YAC/BlB,YAAY,EAAE6C,OAAO,CAACG,WAAW,EAAE/B,SAAS,GAAG,CAAC,CAAC;YACjDhB,eAAe,EAAEoB,QAAQ,CAACH,CAAC,GAAG,CAAC;UACjC,CAAC;QACH;MACF;IACF;EAAC;IAAA9D,GAAA;IAAA+C,KAAA,EACD,SAAArB,sBAAsBA,CAAC7B,KAAK,EAAEC,IAAI,EAAEoB,aAAa,EAAE;MACjDpB,IAAI,GAAGA,IAAI,IAAI,IAAI,CAACC,aAAa,CAACF,KAAK,CAAC;MACxC,IAAI,CAACC,IAAI,EAAE;QACT,OAAO,IAAI;MACb;MACA,IAAIgF,sBAAsB,GAAGhF,IAAI,CAACQ,OAAO,CAACwE,sBAAsB,IAAI,IAAI,CAACtE,KAAK,CAACsE,sBAAsB;MACrG,IAAIhD,yBAAyB,GAAG,IAAI,CAACtB,KAAK,CAACsB,yBAAyB;MACpE,IAAIiE,gBAAgB,GAAGlG,KAAK,KAAKqB,aAAa,GAAG,CAAC;MAClD,IAAI8E,mBAAmB,GAAGlG,IAAI,CAACD,KAAK,KAAK,IAAI,CAACW,KAAK,CAACwD,YAAY,CAAClE,IAAI,CAACQ,OAAO,CAAC4D,IAAI,CAAC,GAAG,CAAC;MACvF,IAAIpC,yBAAyB,IAAIkE,mBAAmB,EAAE;QACpD,OAAOlE,yBAAyB;MAClC;MACA,IAAIgD,sBAAsB,IAAI,CAACkB,mBAAmB,IAAI,CAACD,gBAAgB,EAAE;QACvE,OAAOjB,sBAAsB;MAC/B;MACA,OAAO,IAAI;IACb;EAAC;AAAA,EAxQkC1F,KAAK,CAAC6G,aAAa;AA0QxD,SAASrE,iBAAiBA,CAACpB,KAAK,EAAE;EAChC,IAAIqB,yBAAyB,GAAGrB,KAAK,CAACqB,yBAAyB;IAC7DJ,kBAAkB,GAAGjB,KAAK,CAACiB,kBAAkB;IAC7CO,OAAO,GAAGxB,KAAK,CAACwB,OAAO;IACvBG,WAAW,GAAG3B,KAAK,CAAC2B,WAAW;IAC/BC,wBAAwB,GAAG5B,KAAK,CAAC4B,wBAAwB;IACzDI,kBAAkB,GAAGhC,KAAK,CAACgC,kBAAkB;IAC7CF,0BAA0B,GAAG9B,KAAK,CAAC8B,0BAA0B;IAC7DI,cAAc,GAAGlC,KAAK,CAACkC,cAAc;IACrC9C,IAAI,GAAGY,KAAK,CAACZ,IAAI;IACjBC,KAAK,GAAGW,KAAK,CAACX,KAAK;IACnBS,OAAO,GAAGE,KAAK,CAACF,OAAO;IACvBwC,QAAQ,GAAGtC,KAAK,CAACsC,QAAQ;EAC3B,IAAIoD,eAAe,GAAG9G,KAAK,CAAC+G,QAAQ,CAAC,KAAK,CAAC;IACzCC,0BAA0B,GAAGF,eAAe,CAAC,CAAC,CAAC;IAC/CG,8BAA8B,GAAGH,eAAe,CAAC,CAAC,CAAC;EACrD,IAAII,gBAAgB,GAAGlH,KAAK,CAAC+G,QAAQ,CAAC,KAAK,CAAC;IAC1CI,oBAAoB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC1CE,uBAAuB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC/C,IAAIG,gBAAgB,GAAGrH,KAAK,CAAC+G,QAAQ,CAAC;MAClClE,WAAW,EAAEzB,KAAK,CAACyB,WAAW;MAC9BC,cAAc,EAAE1B,KAAK,CAAC0B,cAAc;MACpC5B,OAAO,EAAEE,KAAK,CAACF,OAAO;MACtBsC,YAAY,EAAEpC,KAAK,CAACZ,IAAI;MACxBiD,eAAe,EAAErC,KAAK,CAACqC;IACzB,CAAC,CAAC;IACF6D,qBAAqB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3CE,wBAAwB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChD,IAAIG,gBAAgB,GAAGxH,KAAK,CAAC+G,QAAQ,CAAC;MAClClE,WAAW,EAAEzB,KAAK,CAACZ,IAAI;MACvBsC,cAAc,EAAE1B,KAAK,CAAC0B,cAAc;MACpC5B,OAAO,EAAEE,KAAK,CAACF,OAAO;MACtBsC,YAAY,EAAEpC,KAAK,CAACoC,YAAY;MAChCC,eAAe,EAAErC,KAAK,CAACqC;IACzB,CAAC,CAAC;IACFgE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzCxH,KAAK,CAAC2H,SAAS,CAAC,YAAM;IACpB3E,wBAAwB,CAACJ,OAAO,EAAEwE,uBAAuB,CAAC;IAE1DlE,0BAA0B,CAACN,OAAO,EAAE8E,iBAAiB,CAAC;IACtD,OAAO,YAAM;MACXxE,0BAA0B,CAACN,OAAO,EAAE,IAAI,CAAC;MACzCI,wBAAwB,CAACJ,OAAO,EAAE,IAAI,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,CAACA,OAAO,EAAEI,wBAAwB,EAAE0E,iBAAiB,EAAExE,0BAA0B,CAAC,CAAC;EACtF,IAAI0E,UAAU,GAAG;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAQ;MACfZ,8BAA8B,CAAC,IAAI,CAAC;MACpCG,uBAAuB,CAAC,IAAI,CAAC;MAC7B,IAAIrE,WAAW,IAAI,IAAI,EAAE;QACvBK,kBAAkB,CAACL,WAAW,EAAE,IAAI,CAAC;MACvC;IACF,CAAC;IACD+E,WAAW,EAAE,SAAbA,WAAWA,CAAA,EAAQ;MACjBb,8BAA8B,CAAC,KAAK,CAAC;MACrCG,uBAAuB,CAAC,KAAK,CAAC;MAC9B,IAAIrE,WAAW,IAAI,IAAI,EAAE;QACvBK,kBAAkB,CAACL,WAAW,EAAE,KAAK,CAAC;MACxC;IACF,CAAC;IACDa,WAAW,EAAE,SAAbA,WAAWA,CAAGmE,MAAM,EAAEC,QAAQ,EAAK;MACjC,IAAID,MAAM,KAAK,SAAS,EAAE;QACxB,IAAItF,yBAAyB,IAAI,IAAI,EAAE;UACrC8E,wBAAwB,CAAC9H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6H,qBAAqB,CAAC,EAAEU,QAAQ,CAAC,CAAC;QAC7F,CAAC,MAAM,IAAIjF,WAAW,IAAI,IAAI,EAAE;UAE9BO,cAAc,CAACP,WAAW,EAAEtD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6H,qBAAqB,CAAC,EAAEU,QAAQ,CAAC,CAAC;QAChG;MACF,CAAC,MAAM,IAAID,MAAM,KAAK,UAAU,IAAI1F,kBAAkB,IAAI,IAAI,EAAE;QAC9DqF,iBAAiB,CAACjI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgI,cAAc,CAAC,EAAEO,QAAQ,CAAC,CAAC;MAC/E;IACF;EACF,CAAC;EACD,IAAIC,OAAO,GAAG7G,KAAK,CAACgB,UAAU,CAAC;IAC7B5B,IAAI,EAAJA,IAAI;IACJC,KAAK,EAALA,KAAK;IACLS,OAAO,EAAPA,OAAO;IACP0G,UAAU,EAAVA;EACF,CAAC,CAAC;EACF,IAAIM,gBAAgB,GAAGzF,yBAAyB,IAAI,IAAI,IAAiBzC,KAAK,CAACuC,aAAa,CAACE,yBAAyB,EAAEnD,QAAQ,CAAC;IAC/H6I,WAAW,EAAEnB;EACf,CAAC,EAAEM,qBAAqB,CAAC,CAAC;EAC1B,IAAIc,SAAS,GAAG/F,kBAAkB,IAAI,IAAI,IAAiBrC,KAAK,CAACuC,aAAa,CAACF,kBAAkB,EAAE/C,QAAQ,CAAC;IAC1G6I,WAAW,EAAEhB;EACf,CAAC,EAAEM,cAAc,CAAC,CAAC;EACnB,OAAOS,gBAAgB,IAAIE,SAAS,GAAgBpI,KAAK,CAACuC,aAAa,CAAC5C,IAAI,EAAE,IAAI,EAAE+D,QAAQ,KAAK,KAAK,GAAGwE,gBAAgB,GAAGE,SAAS,EAAEH,OAAO,EAAEvE,QAAQ,KAAK,KAAK,GAAG0E,SAAS,GAAGF,gBAAgB,CAAC,GAAGD,OAAO;AAC9M;AAKA,eAAehI,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}