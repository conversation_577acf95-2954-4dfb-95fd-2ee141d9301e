{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nvar _jsxFileName = \"D:\\\\aplikasi\\\\TRAE\\\\psg-bmi\\\\src\\\\screens\\\\HomeScreen.js\",\n  _this = this;\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport RefreshControl from \"react-native-web/dist/exports/RefreshControl\";\nimport StatusBar from \"react-native-web/dist/exports/StatusBar\";\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { Colors, Spacing } from '../constants';\nimport { useBottomNavigation } from '../hooks/useBottomNavigation';\nimport Header from '../components/Header';\nimport MenuGrid from '../components/MenuGrid';\nimport Carousel from '../components/Carousel';\nimport DashboardOverview from '../components/DashboardOverview';\nimport VideoGallery from '../components/VideoGallery';\nimport RecentActivities from '../components/RecentActivities';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nvar HomeScreen = function HomeScreen() {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    refreshing = _React$useState2[0],\n    setRefreshing = _React$useState2[1];\n  var _useBottomNavigation = useBottomNavigation(),\n    getContentPadding = _useBottomNavigation.getContentPadding;\n  var onRefresh = React.useCallback(function () {\n    setRefreshing(true);\n    setTimeout(function () {\n      setRefreshing(false);\n    }, 1000);\n  }, []);\n  var contentBottomPadding = getContentPadding(Spacing.lg);\n  return _jsxDEV(SafeAreaView, {\n    style: styles.container,\n    edges: ['top'],\n    children: [_jsxDEV(StatusBar, {\n      barStyle: \"light-content\",\n      backgroundColor: Colors.primary\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, _this), _jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, _this), _jsxDEV(ScrollView, {\n      style: styles.scrollView,\n      contentContainerStyle: [styles.scrollContent, {\n        paddingBottom: contentBottomPadding\n      }],\n      showsVerticalScrollIndicator: false,\n      refreshControl: _jsxDEV(RefreshControl, {\n        refreshing: refreshing,\n        onRefresh: onRefresh,\n        colors: [Colors.primary],\n        tintColor: Colors.primary\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, _this),\n      children: [_jsxDEV(View, {\n        style: styles.section,\n        children: _jsxDEV(Carousel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, _this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, _this), _jsxDEV(View, {\n        style: styles.section,\n        children: _jsxDEV(DashboardOverview, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, _this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, _this), _jsxDEV(View, {\n        style: styles.section,\n        children: _jsxDEV(MenuGrid, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, _this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, _this), _jsxDEV(View, {\n        style: styles.section,\n        children: _jsxDEV(VideoGallery, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, _this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, _this), _jsxDEV(View, {\n        style: styles.section,\n        children: _jsxDEV(RecentActivities, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, _this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, _this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, _this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, _this);\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: Colors.background\n  },\n  scrollView: {\n    flex: 1\n  },\n  scrollContent: {},\n  section: {\n    marginBottom: Spacing.lg\n  }\n});\nexport default HomeScreen;", "map": {"version": 3, "names": ["React", "View", "ScrollView", "StyleSheet", "RefreshControl", "StatusBar", "SafeAreaView", "Colors", "Spacing", "useBottomNavigation", "Header", "MenuGrid", "Carousel", "DashboardOverview", "VideoGallery", "RecentActivities", "jsxDEV", "_jsxDEV", "HomeScreen", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "refreshing", "setRefreshing", "_useBottomNavigation", "getContentPadding", "onRefresh", "useCallback", "setTimeout", "contentBottomPadding", "lg", "style", "styles", "container", "edges", "children", "barStyle", "backgroundColor", "primary", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_this", "scrollView", "contentContainerStyle", "scrollContent", "paddingBottom", "showsVerticalScrollIndicator", "refreshControl", "colors", "tintColor", "section", "create", "flex", "background", "marginBottom"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/screens/HomeScreen.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  ScrollView,\n  StyleSheet,\n  RefreshControl,\n  StatusBar,\n} from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { Colors, Spacing } from '../constants';\nimport { useBottomNavigation } from '../hooks/useBottomNavigation';\n\n// Import components that we'll create in subsequent tasks\nimport Header from '../components/Header';\nimport MenuGrid from '../components/MenuGrid';\nimport Carousel from '../components/Carousel';\nimport DashboardOverview from '../components/DashboardOverview';\nimport VideoGallery from '../components/VideoGallery';\nimport RecentActivities from '../components/RecentActivities';\n\nconst HomeScreen = () => {\n  const [refreshing, setRefreshing] = React.useState(false);\n  const { getContentPadding } = useBottomNavigation();\n\n  const onRefresh = React.useCallback(() => {\n    setRefreshing(true);\n    // Simulate refresh delay\n    setTimeout(() => {\n      setRefreshing(false);\n    }, 1000);\n  }, []);\n\n  // Calculate bottom padding to account for fixed navigation\n  const contentBottomPadding = getContentPadding(Spacing.lg);\n\n  return (\n    <SafeAreaView style={styles.container} edges={['top']}>\n      <StatusBar barStyle=\"light-content\" backgroundColor={Colors.primary} />\n      \n      <Header />\n      \n      <ScrollView\n        style={styles.scrollView}\n        contentContainerStyle={[\n          styles.scrollContent,\n          { paddingBottom: contentBottomPadding }\n        ]}\n        showsVerticalScrollIndicator={false}\n        refreshControl={\n          <RefreshControl\n            refreshing={refreshing}\n            onRefresh={onRefresh}\n            colors={[Colors.primary]}\n            tintColor={Colors.primary}\n          />\n        }\n      >\n        {/* Auto-scroll Carousel */}\n        <View style={styles.section}>\n          <Carousel />\n        </View>\n\n        {/* Dashboard Overview */}\n        <View style={styles.section}>\n          <DashboardOverview />\n        </View>\n\n        {/* Menu Grid */}\n        <View style={styles.section}>\n          <MenuGrid />\n        </View>\n\n        {/* Video Gallery */}\n        <View style={styles.section}>\n          <VideoGallery />\n        </View>\n\n        {/* Recent Activities */}\n        <View style={styles.section}>\n          <RecentActivities />\n        </View>\n      </ScrollView>\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: Colors.background,\n  },\n  scrollView: {\n    flex: 1,\n  },\n  scrollContent: {\n    // paddingBottom is now dynamically calculated and applied inline\n  },\n  section: {\n    marginBottom: Spacing.lg,\n  },\n});\n\nexport default HomeScreen;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,cAAA;AAAA,OAAAC,SAAA;AAQ1B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,MAAM,EAAEC,OAAO,QAAQ,cAAc;AAC9C,SAASC,mBAAmB,QAAQ,8BAA8B;AAGlE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,gBAAgB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;EACvB,IAAAC,eAAA,GAAoCnB,KAAK,CAACoB,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAlDI,UAAU,GAAAF,gBAAA;IAAEG,aAAa,GAAAH,gBAAA;EAChC,IAAAI,oBAAA,GAA8BhB,mBAAmB,CAAC,CAAC;IAA3CiB,iBAAiB,GAAAD,oBAAA,CAAjBC,iBAAiB;EAEzB,IAAMC,SAAS,GAAG3B,KAAK,CAAC4B,WAAW,CAAC,YAAM;IACxCJ,aAAa,CAAC,IAAI,CAAC;IAEnBK,UAAU,CAAC,YAAM;MACfL,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMM,oBAAoB,GAAGJ,iBAAiB,CAAClB,OAAO,CAACuB,EAAE,CAAC;EAE1D,OACEd,OAAA,CAACX,YAAY;IAAC0B,KAAK,EAAEC,MAAM,CAACC,SAAU;IAACC,KAAK,EAAE,CAAC,KAAK,CAAE;IAAAC,QAAA,GACpDnB,OAAA,CAACZ,SAAS;MAACgC,QAAQ,EAAC,eAAe;MAACC,eAAe,EAAE/B,MAAM,CAACgC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,GAAAC,KAAE,CAAC,EAEvE3B,OAAA,CAACP,MAAM;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,GAAAC,KAAE,CAAC,EAEV3B,OAAA,CAACf,UAAU;MACT8B,KAAK,EAAEC,MAAM,CAACY,UAAW;MACzBC,qBAAqB,EAAE,CACrBb,MAAM,CAACc,aAAa,EACpB;QAAEC,aAAa,EAAElB;MAAqB,CAAC,CACvC;MACFmB,4BAA4B,EAAE,KAAM;MACpCC,cAAc,EACZjC,OAAA,CAACb,cAAc;QACbmB,UAAU,EAAEA,UAAW;QACvBI,SAAS,EAAEA,SAAU;QACrBwB,MAAM,EAAE,CAAC5C,MAAM,CAACgC,OAAO,CAAE;QACzBa,SAAS,EAAE7C,MAAM,CAACgC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KAC3B,CACF;MAAAR,QAAA,GAGDnB,OAAA,CAAChB,IAAI;QAAC+B,KAAK,EAAEC,MAAM,CAACoB,OAAQ;QAAAjB,QAAA,EAC1BnB,OAAA,CAACL,QAAQ;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KAAE;MAAC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KACR,CAAC,EAGP3B,OAAA,CAAChB,IAAI;QAAC+B,KAAK,EAAEC,MAAM,CAACoB,OAAQ;QAAAjB,QAAA,EAC1BnB,OAAA,CAACJ,iBAAiB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KAAE;MAAC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KACjB,CAAC,EAGP3B,OAAA,CAAChB,IAAI;QAAC+B,KAAK,EAAEC,MAAM,CAACoB,OAAQ;QAAAjB,QAAA,EAC1BnB,OAAA,CAACN,QAAQ;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KAAE;MAAC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KACR,CAAC,EAGP3B,OAAA,CAAChB,IAAI;QAAC+B,KAAK,EAAEC,MAAM,CAACoB,OAAQ;QAAAjB,QAAA,EAC1BnB,OAAA,CAACH,YAAY;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KAAE;MAAC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KACZ,CAAC,EAGP3B,OAAA,CAAChB,IAAI;QAAC+B,KAAK,EAAEC,MAAM,CAACoB,OAAQ;QAAAjB,QAAA,EAC1BnB,OAAA,CAACF,gBAAgB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KAAE;MAAC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KAChB,CAAC;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,GAAAC,KACG,CAAC;EAAA;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,GAAAC,KACD,CAAC;AAEnB,CAAC;AAED,IAAMX,MAAM,GAAG9B,UAAU,CAACmD,MAAM,CAAC;EAC/BpB,SAAS,EAAE;IACTqB,IAAI,EAAE,CAAC;IACPjB,eAAe,EAAE/B,MAAM,CAACiD;EAC1B,CAAC;EACDX,UAAU,EAAE;IACVU,IAAI,EAAE;EACR,CAAC;EACDR,aAAa,EAAE,CAEf,CAAC;EACDM,OAAO,EAAE;IACPI,YAAY,EAAEjD,OAAO,CAACuB;EACxB;AACF,CAAC,CAAC;AAEF,eAAeb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}