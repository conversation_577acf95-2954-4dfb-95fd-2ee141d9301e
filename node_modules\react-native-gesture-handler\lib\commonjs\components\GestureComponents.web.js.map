{"version": 3, "sources": ["GestureComponents.web.tsx"], "names": ["ScrollView", "RNScrollView", "disallowInterruption", "Switch", "RNSwitch", "shouldCancelWhenOutside", "shouldActivateOnStart", "TextInput", "RNTextInput", "DrawerLayoutAndroid", "console", "warn", "RefreshControl", "View", "FlatList", "React", "forwardRef", "props", "ref", "scrollProps"], "mappings": ";;;;;;;AAAA;;AACA;;AASA;;;;;;;;;;AAEO,MAAMA,UAAU,GAAG,kCAAoBC,uBAApB,EAAkC;AAC1DC,EAAAA,oBAAoB,EAAE;AADoC,CAAlC,CAAnB;;AAIA,MAAMC,MAAM,GAAG,kCAAoBC,mBAApB,EAA8B;AAClDC,EAAAA,uBAAuB,EAAE,KADyB;AAElDC,EAAAA,qBAAqB,EAAE,IAF2B;AAGlDJ,EAAAA,oBAAoB,EAAE;AAH4B,CAA9B,CAAf;;AAKA,MAAMK,SAAS,GAAG,kCAAoBC,sBAApB,CAAlB;;;AACA,MAAMC,mBAAmB,GAAG,MAAM;AACvCC,EAAAA,OAAO,CAACC,IAAR,CAAa,8CAAb;AACA,sBAAO,oBAAC,iBAAD,OAAP;AACD,CAHM,C,CAKP;AACA;AACA;;;;AACO,MAAMC,cAAc,GAAG,kCAAoBC,iBAApB,CAAvB;;AAEA,MAAMC,QAAQ,gBAAGC,KAAK,CAACC,UAAN,CACtB,CAAoBC,KAApB,EAAiDC,GAAjD,kBACE,oBAAC,qBAAD;AACE,EAAA,GAAG,EAAEA;AADP,GAEMD,KAFN;AAGE,EAAA,qBAAqB,EAAGE,WAAD,iBAAiB,oBAAC,UAAD,EAAgBA,WAAhB;AAH1C,GAFoB,CAAjB", "sourcesContent": ["import * as React from 'react';\nimport {\n  FlatList as <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Switch as RNSwitch,\n  TextInput as RNTextInput,\n  ScrollView as RNScrollView,\n  FlatListProps,\n  View,\n} from 'react-native';\n\nimport createNativeWrapper from '../handlers/createNativeWrapper';\n\nexport const ScrollView = createNativeWrapper(RNScrollView, {\n  disallowInterruption: false,\n});\n\nexport const Switch = createNativeWrapper(RNSwitch, {\n  shouldCancelWhenOutside: false,\n  shouldActivateOnStart: true,\n  disallowInterruption: true,\n});\nexport const TextInput = createNativeWrapper(RNTextInput);\nexport const DrawerLayoutAndroid = () => {\n  console.warn('DrawerLayoutAndroid is not supported on web!');\n  return <View />;\n};\n\n// RefreshControl is implemented as a functional component, rendering a View\n// NativeViewGestureHandler needs to set a ref on its child, which cannot be done\n// on functional components\nexport const RefreshControl = createNativeWrapper(View);\n\nexport const FlatList = React.forwardRef(\n  <ItemT extends any>(props: FlatListProps<ItemT>, ref: any) => (\n    <RNFlatList\n      ref={ref}\n      {...props}\n      renderScrollComponent={(scrollProps) => <ScrollView {...scrollProps} />}\n    />\n  )\n);\n"]}