{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Colors, Typography, Spacing } from \"../constants\";\nimport { MenuCard } from \"./\";\nimport { MenuData } from \"../constants/MenuData\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar _Dimensions$get = Dimensions.get('window'),\n  screenWidth = _Dimensions$get.width;\nvar GRID_PADDING = Spacing.padding.md;\nvar CARD_MARGIN = Spacing.margin.sm;\nvar CARDS_PER_ROW = 2;\nvar CARD_WIDTH = (screenWidth - GRID_PADDING * 2 - CARD_MARGIN * (CARDS_PER_ROW - 1)) / CARDS_PER_ROW;\nvar MenuGrid = function MenuGrid() {\n  var handleMenuPress = function handleMenuPress(item) {\n    Alert.alert(item.title, item.description, [{\n      text: 'Batal',\n      style: 'cancel'\n    }, {\n      text: 'Buka Aplikasi',\n      onPress: function onPress() {\n        console.log(`Navigate to ${item.route}`);\n      }\n    }]);\n  };\n  var renderMenuItem = function renderMenuItem(_ref) {\n    var item = _ref.item,\n      index = _ref.index;\n    return _jsx(View, {\n      style: [styles.cardContainer, {\n        width: CARD_WIDTH\n      }],\n      children: _jsx(MenuCard, {\n        title: item.title,\n        subtitle: item.subtitle,\n        icon: item.icon,\n        iconType: item.iconType,\n        color: item.color,\n        onPress: function onPress() {\n          return handleMenuPress(item);\n        }\n      })\n    });\n  };\n  var renderHeader = function renderHeader() {\n    return _jsxs(View, {\n      style: styles.headerContainer,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Quick Actions\"\n      }), _jsx(Text, {\n        style: styles.sectionSubtitle,\n        children: \"Akses cepat ke aplikasi yang sering digunakan\"\n      })]\n    });\n  };\n  return _jsxs(View, {\n    style: styles.container,\n    children: [renderHeader(), _jsx(FlatList, {\n      data: MenuData,\n      renderItem: renderMenuItem,\n      keyExtractor: function keyExtractor(item) {\n        return item.id.toString();\n      },\n      numColumns: CARDS_PER_ROW,\n      scrollEnabled: false,\n      contentContainerStyle: styles.gridContainer,\n      columnWrapperStyle: styles.row,\n      showsVerticalScrollIndicator: false\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    paddingHorizontal: GRID_PADDING\n  },\n  headerContainer: {\n    marginBottom: Spacing.lg\n  },\n  sectionTitle: _objectSpread(_objectSpread({}, Typography.h3), {}, {\n    color: Colors.onSurface,\n    marginBottom: Spacing.xs\n  }),\n  sectionSubtitle: _objectSpread(_objectSpread({}, Typography.body2), {}, {\n    color: Colors.onSurfaceVariant\n  }),\n  gridContainer: {\n    paddingBottom: Spacing.sm\n  },\n  row: {\n    justifyContent: 'space-between',\n    marginBottom: CARD_MARGIN * 2\n  },\n  cardContainer: {\n    marginBottom: CARD_MARGIN\n  }\n});\nexport default MenuGrid;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "FlatList", "Dimensions", "<PERSON><PERSON>", "Colors", "Typography", "Spacing", "MenuCard", "MenuData", "jsx", "_jsx", "jsxs", "_jsxs", "_Dimensions$get", "get", "screenWidth", "width", "GRID_PADDING", "padding", "md", "CARD_MARGIN", "margin", "sm", "CARDS_PER_ROW", "CARD_WIDTH", "MenuGrid", "handleMenuPress", "item", "alert", "title", "description", "text", "style", "onPress", "console", "log", "route", "renderMenuItem", "_ref", "index", "styles", "cardContainer", "children", "subtitle", "icon", "iconType", "color", "renderHeader", "headerContainer", "sectionTitle", "sectionSubtitle", "container", "data", "renderItem", "keyExtractor", "id", "toString", "numColumns", "scrollEnabled", "contentContainerStyle", "gridContainer", "columnWrapperStyle", "row", "showsVerticalScrollIndicator", "create", "paddingHorizontal", "marginBottom", "lg", "_objectSpread", "h3", "onSurface", "xs", "body2", "onSurfaceVariant", "paddingBottom", "justifyContent"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/components/MenuGrid.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  FlatList,\n  Dimensions,\n  Alert,\n} from 'react-native';\nimport { Colors, Typography, Spacing } from '../constants';\nimport { MenuCard } from './';\nimport { MenuData } from '../constants/MenuData';\n\nconst { width: screenWidth } = Dimensions.get('window');\nconst GRID_PADDING = Spacing.padding.md;\nconst CARD_MARGIN = Spacing.margin.sm;\nconst CARDS_PER_ROW = 2;\nconst CARD_WIDTH = (screenWidth - (GRID_PADDING * 2) - (CARD_MARGIN * (CARDS_PER_ROW - 1))) / CARDS_PER_ROW;\n\nconst MenuGrid = () => {\n  const handleMenuPress = (item) => {\n    // For now, show an alert. In a real app, this would navigate to the respective screen\n    Alert.alert(\n      item.title,\n      item.description,\n      [\n        {\n          text: 'Batal',\n          style: 'cancel',\n        },\n        {\n          text: 'Buka Aplikasi',\n          onPress: () => {\n            console.log(`Navigate to ${item.route}`);\n            // Navigation logic would go here\n          },\n        },\n      ]\n    );\n  };\n\n  const renderMenuItem = ({ item, index }) => {\n    return (\n      <View style={[styles.cardContainer, { width: CARD_WIDTH }]}>\n        <MenuCard\n          title={item.title}\n          subtitle={item.subtitle}\n          icon={item.icon}\n          iconType={item.iconType}\n          color={item.color}\n          onPress={() => handleMenuPress(item)}\n        />\n      </View>\n    );\n  };\n\n  const renderHeader = () => (\n    <View style={styles.headerContainer}>\n      <Text style={styles.sectionTitle}>Quick Actions</Text>\n      <Text style={styles.sectionSubtitle}>\n        Akses cepat ke aplikasi yang sering digunakan\n      </Text>\n    </View>\n  );\n\n  return (\n    <View style={styles.container}>\n      {renderHeader()}\n      \n      <FlatList\n        data={MenuData}\n        renderItem={renderMenuItem}\n        keyExtractor={(item) => item.id.toString()}\n        numColumns={CARDS_PER_ROW}\n        scrollEnabled={false}\n        contentContainerStyle={styles.gridContainer}\n        columnWrapperStyle={styles.row}\n        showsVerticalScrollIndicator={false}\n      />\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    paddingHorizontal: GRID_PADDING,\n  },\n  headerContainer: {\n    marginBottom: Spacing.lg,\n  },\n  sectionTitle: {\n    ...Typography.h3,\n    color: Colors.onSurface,\n    marginBottom: Spacing.xs,\n  },\n  sectionSubtitle: {\n    ...Typography.body2,\n    color: Colors.onSurfaceVariant,\n  },\n  gridContainer: {\n    paddingBottom: Spacing.sm,\n  },\n  row: {\n    justifyContent: 'space-between',\n    marginBottom: CARD_MARGIN * 2,\n  },\n  cardContainer: {\n    marginBottom: CARD_MARGIN,\n  },\n});\n\nexport default MenuGrid;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAS1B,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO;AACpC,SAASC,QAAQ;AACjB,SAASC,QAAQ;AAAgC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEjD,IAAAC,eAAA,GAA+BX,UAAU,CAACY,GAAG,CAAC,QAAQ,CAAC;EAAxCC,WAAW,GAAAF,eAAA,CAAlBG,KAAK;AACb,IAAMC,YAAY,GAAGX,OAAO,CAACY,OAAO,CAACC,EAAE;AACvC,IAAMC,WAAW,GAAGd,OAAO,CAACe,MAAM,CAACC,EAAE;AACrC,IAAMC,aAAa,GAAG,CAAC;AACvB,IAAMC,UAAU,GAAG,CAACT,WAAW,GAAIE,YAAY,GAAG,CAAE,GAAIG,WAAW,IAAIG,aAAa,GAAG,CAAC,CAAE,IAAIA,aAAa;AAE3G,IAAME,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;EACrB,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,IAAI,EAAK;IAEhCxB,KAAK,CAACyB,KAAK,CACTD,IAAI,CAACE,KAAK,EACVF,IAAI,CAACG,WAAW,EAChB,CACE;MACEC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;IACT,CAAC,EACD;MACED,IAAI,EAAE,eAAe;MACrBE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;QACbC,OAAO,CAACC,GAAG,CAAC,eAAeR,IAAI,CAACS,KAAK,EAAE,CAAC;MAE1C;IACF,CAAC,CAEL,CAAC;EACH,CAAC;EAED,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,IAAA,EAAwB;IAAA,IAAlBX,IAAI,GAAAW,IAAA,CAAJX,IAAI;MAAEY,KAAK,GAAAD,IAAA,CAALC,KAAK;IACnC,OACE7B,IAAA,CAACZ,IAAI;MAACkC,KAAK,EAAE,CAACQ,MAAM,CAACC,aAAa,EAAE;QAAEzB,KAAK,EAAEQ;MAAW,CAAC,CAAE;MAAAkB,QAAA,EACzDhC,IAAA,CAACH,QAAQ;QACPsB,KAAK,EAAEF,IAAI,CAACE,KAAM;QAClBc,QAAQ,EAAEhB,IAAI,CAACgB,QAAS;QACxBC,IAAI,EAAEjB,IAAI,CAACiB,IAAK;QAChBC,QAAQ,EAAElB,IAAI,CAACkB,QAAS;QACxBC,KAAK,EAAEnB,IAAI,CAACmB,KAAM;QAClBb,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQP,eAAe,CAACC,IAAI,CAAC;QAAA;MAAC,CACtC;IAAC,CACE,CAAC;EAEX,CAAC;EAED,IAAMoB,YAAY,GAAG,SAAfA,YAAYA,CAAA;IAAA,OAChBnC,KAAA,CAACd,IAAI;MAACkC,KAAK,EAAEQ,MAAM,CAACQ,eAAgB;MAAAN,QAAA,GAClChC,IAAA,CAACX,IAAI;QAACiC,KAAK,EAAEQ,MAAM,CAACS,YAAa;QAAAP,QAAA,EAAC;MAAa,CAAM,CAAC,EACtDhC,IAAA,CAACX,IAAI;QAACiC,KAAK,EAAEQ,MAAM,CAACU,eAAgB;QAAAR,QAAA,EAAC;MAErC,CAAM,CAAC;IAAA,CACH,CAAC;EAAA,CACR;EAED,OACE9B,KAAA,CAACd,IAAI;IAACkC,KAAK,EAAEQ,MAAM,CAACW,SAAU;IAAAT,QAAA,GAC3BK,YAAY,CAAC,CAAC,EAEfrC,IAAA,CAACT,QAAQ;MACPmD,IAAI,EAAE5C,QAAS;MACf6C,UAAU,EAAEhB,cAAe;MAC3BiB,YAAY,EAAE,SAAdA,YAAYA,CAAG3B,IAAI;QAAA,OAAKA,IAAI,CAAC4B,EAAE,CAACC,QAAQ,CAAC,CAAC;MAAA,CAAC;MAC3CC,UAAU,EAAElC,aAAc;MAC1BmC,aAAa,EAAE,KAAM;MACrBC,qBAAqB,EAAEnB,MAAM,CAACoB,aAAc;MAC5CC,kBAAkB,EAAErB,MAAM,CAACsB,GAAI;MAC/BC,4BAA4B,EAAE;IAAM,CACrC,CAAC;EAAA,CACE,CAAC;AAEX,CAAC;AAED,IAAMvB,MAAM,GAAGxC,UAAU,CAACgE,MAAM,CAAC;EAC/Bb,SAAS,EAAE;IACTc,iBAAiB,EAAEhD;EACrB,CAAC;EACD+B,eAAe,EAAE;IACfkB,YAAY,EAAE5D,OAAO,CAAC6D;EACxB,CAAC;EACDlB,YAAY,EAAAmB,aAAA,CAAAA,aAAA,KACP/D,UAAU,CAACgE,EAAE;IAChBvB,KAAK,EAAE1C,MAAM,CAACkE,SAAS;IACvBJ,YAAY,EAAE5D,OAAO,CAACiE;EAAE,EACzB;EACDrB,eAAe,EAAAkB,aAAA,CAAAA,aAAA,KACV/D,UAAU,CAACmE,KAAK;IACnB1B,KAAK,EAAE1C,MAAM,CAACqE;EAAgB,EAC/B;EACDb,aAAa,EAAE;IACbc,aAAa,EAAEpE,OAAO,CAACgB;EACzB,CAAC;EACDwC,GAAG,EAAE;IACHa,cAAc,EAAE,eAAe;IAC/BT,YAAY,EAAE9C,WAAW,GAAG;EAC9B,CAAC;EACDqB,aAAa,EAAE;IACbyB,YAAY,EAAE9C;EAChB;AACF,CAAC,CAAC;AAEF,eAAeK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}