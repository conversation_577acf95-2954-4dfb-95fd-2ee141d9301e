{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Colors, Typography, Spacing } from \"../constants\";\nimport { StatCard } from \"./\";\nimport { DashboardStats } from \"../constants/MenuData\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar _Dimensions$get = Dimensions.get('window'),\n  screenWidth = _Dimensions$get.width;\nvar GRID_PADDING = Spacing.padding.md;\nvar CARD_MARGIN = Spacing.margin.sm;\nvar CARDS_PER_ROW = 2;\nvar CARD_WIDTH = (screenWidth - GRID_PADDING * 2 - CARD_MARGIN * (CARDS_PER_ROW - 1)) / CARDS_PER_ROW;\nvar DashboardOverview = function DashboardOverview() {\n  var handleStatPress = function handleStatPress(item) {\n    Alert.alert(item.title, `Detail informasi untuk ${item.title.toLowerCase()}`, [{\n      text: 'Tutup',\n      style: 'cancel'\n    }, {\n      text: 'Lihat Detail',\n      onPress: function onPress() {\n        console.log(`View details for ${item.title}`);\n      }\n    }]);\n  };\n  var renderStatItem = function renderStatItem(_ref) {\n    var item = _ref.item,\n      index = _ref.index;\n    return _jsx(View, {\n      style: [styles.cardContainer, {\n        width: CARD_WIDTH\n      }],\n      children: _jsx(StatCard, {\n        title: item.title,\n        value: item.value,\n        subtitle: item.subtitle,\n        icon: item.icon,\n        color: item.color,\n        trend: item.trend,\n        onPress: function onPress() {\n          return handleStatPress(item);\n        }\n      })\n    });\n  };\n  var renderHeader = function renderHeader() {\n    return _jsxs(View, {\n      style: styles.headerContainer,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Today's Overview\"\n      }), _jsx(Text, {\n        style: styles.sectionSubtitle,\n        children: \"Ringkasan aktivitas dan statistik hari ini\"\n      })]\n    });\n  };\n  var getCurrentDate = function getCurrentDate() {\n    var today = new Date();\n    var options = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    };\n    return today.toLocaleDateString('id-ID', options);\n  };\n  return _jsxs(View, {\n    style: styles.container,\n    children: [renderHeader(), _jsx(View, {\n      style: styles.dateContainer,\n      children: _jsx(Text, {\n        style: styles.dateText,\n        children: getCurrentDate()\n      })\n    }), _jsx(FlatList, {\n      data: DashboardStats,\n      renderItem: renderStatItem,\n      keyExtractor: function keyExtractor(item) {\n        return item.id.toString();\n      },\n      numColumns: CARDS_PER_ROW,\n      scrollEnabled: false,\n      contentContainerStyle: styles.gridContainer,\n      columnWrapperStyle: styles.row,\n      showsVerticalScrollIndicator: false\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    paddingHorizontal: GRID_PADDING\n  },\n  headerContainer: {\n    marginBottom: Spacing.md\n  },\n  sectionTitle: _objectSpread(_objectSpread({}, Typography.h3), {}, {\n    color: Colors.onSurface,\n    marginBottom: Spacing.xs\n  }),\n  sectionSubtitle: _objectSpread(_objectSpread({}, Typography.body2), {}, {\n    color: Colors.onSurfaceVariant\n  }),\n  dateContainer: {\n    backgroundColor: Colors.primaryLight,\n    paddingHorizontal: Spacing.padding.md,\n    paddingVertical: Spacing.padding.sm,\n    borderRadius: Spacing.borderRadius.md,\n    marginBottom: Spacing.lg\n  },\n  dateText: _objectSpread(_objectSpread({}, Typography.body2), {}, {\n    color: Colors.primary,\n    textAlign: 'center',\n    fontWeight: '500'\n  }),\n  gridContainer: {\n    paddingBottom: Spacing.sm\n  },\n  row: {\n    justifyContent: 'space-between',\n    marginBottom: CARD_MARGIN * 2\n  },\n  cardContainer: {\n    marginBottom: CARD_MARGIN\n  }\n});\nexport default DashboardOverview;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "FlatList", "Dimensions", "<PERSON><PERSON>", "Colors", "Typography", "Spacing", "StatCard", "DashboardStats", "jsx", "_jsx", "jsxs", "_jsxs", "_Dimensions$get", "get", "screenWidth", "width", "GRID_PADDING", "padding", "md", "CARD_MARGIN", "margin", "sm", "CARDS_PER_ROW", "CARD_WIDTH", "DashboardOverview", "handleStatPress", "item", "alert", "title", "toLowerCase", "text", "style", "onPress", "console", "log", "renderStatItem", "_ref", "index", "styles", "cardContainer", "children", "value", "subtitle", "icon", "color", "trend", "renderHeader", "headerContainer", "sectionTitle", "sectionSubtitle", "getCurrentDate", "today", "Date", "options", "weekday", "year", "month", "day", "toLocaleDateString", "container", "<PERSON><PERSON><PERSON><PERSON>", "dateText", "data", "renderItem", "keyExtractor", "id", "toString", "numColumns", "scrollEnabled", "contentContainerStyle", "gridContainer", "columnWrapperStyle", "row", "showsVerticalScrollIndicator", "create", "paddingHorizontal", "marginBottom", "_objectSpread", "h3", "onSurface", "xs", "body2", "onSurfaceVariant", "backgroundColor", "primaryLight", "paddingVertical", "borderRadius", "lg", "primary", "textAlign", "fontWeight", "paddingBottom", "justifyContent"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/components/DashboardOverview.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  FlatList,\n  Dimensions,\n  Alert,\n} from 'react-native';\nimport { Colors, Typography, Spacing } from '../constants';\nimport { StatCard } from './';\nimport { DashboardStats } from '../constants/MenuData';\n\nconst { width: screenWidth } = Dimensions.get('window');\nconst GRID_PADDING = Spacing.padding.md;\nconst CARD_MARGIN = Spacing.margin.sm;\nconst CARDS_PER_ROW = 2;\nconst CARD_WIDTH = (screenWidth - (GRID_PADDING * 2) - (CARD_MARGIN * (CARDS_PER_ROW - 1))) / CARDS_PER_ROW;\n\nconst DashboardOverview = () => {\n  const handleStatPress = (item) => {\n    Alert.alert(\n      item.title,\n      `Detail informasi untuk ${item.title.toLowerCase()}`,\n      [\n        {\n          text: 'Tutup',\n          style: 'cancel',\n        },\n        {\n          text: 'Lihat Detail',\n          onPress: () => {\n            console.log(`View details for ${item.title}`);\n            // Navigation to detail screen would go here\n          },\n        },\n      ]\n    );\n  };\n\n  const renderStatItem = ({ item, index }) => {\n    return (\n      <View style={[styles.cardContainer, { width: CARD_WIDTH }]}>\n        <StatCard\n          title={item.title}\n          value={item.value}\n          subtitle={item.subtitle}\n          icon={item.icon}\n          color={item.color}\n          trend={item.trend}\n          onPress={() => handleStatPress(item)}\n        />\n      </View>\n    );\n  };\n\n  const renderHeader = () => (\n    <View style={styles.headerContainer}>\n      <Text style={styles.sectionTitle}>Today's Overview</Text>\n      <Text style={styles.sectionSubtitle}>\n        Ringkasan aktivitas dan statistik hari ini\n      </Text>\n    </View>\n  );\n\n  const getCurrentDate = () => {\n    const today = new Date();\n    const options = { \n      weekday: 'long', \n      year: 'numeric', \n      month: 'long', \n      day: 'numeric' \n    };\n    return today.toLocaleDateString('id-ID', options);\n  };\n\n  return (\n    <View style={styles.container}>\n      {renderHeader()}\n      \n      <View style={styles.dateContainer}>\n        <Text style={styles.dateText}>{getCurrentDate()}</Text>\n      </View>\n      \n      <FlatList\n        data={DashboardStats}\n        renderItem={renderStatItem}\n        keyExtractor={(item) => item.id.toString()}\n        numColumns={CARDS_PER_ROW}\n        scrollEnabled={false}\n        contentContainerStyle={styles.gridContainer}\n        columnWrapperStyle={styles.row}\n        showsVerticalScrollIndicator={false}\n      />\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    paddingHorizontal: GRID_PADDING,\n  },\n  headerContainer: {\n    marginBottom: Spacing.md,\n  },\n  sectionTitle: {\n    ...Typography.h3,\n    color: Colors.onSurface,\n    marginBottom: Spacing.xs,\n  },\n  sectionSubtitle: {\n    ...Typography.body2,\n    color: Colors.onSurfaceVariant,\n  },\n  dateContainer: {\n    backgroundColor: Colors.primaryLight,\n    paddingHorizontal: Spacing.padding.md,\n    paddingVertical: Spacing.padding.sm,\n    borderRadius: Spacing.borderRadius.md,\n    marginBottom: Spacing.lg,\n  },\n  dateText: {\n    ...Typography.body2,\n    color: Colors.primary,\n    textAlign: 'center',\n    fontWeight: '500',\n  },\n  gridContainer: {\n    paddingBottom: Spacing.sm,\n  },\n  row: {\n    justifyContent: 'space-between',\n    marginBottom: CARD_MARGIN * 2,\n  },\n  cardContainer: {\n    marginBottom: CARD_MARGIN,\n  },\n});\n\nexport default DashboardOverview;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAS1B,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO;AACpC,SAASC,QAAQ;AACjB,SAASC,cAAc;AAAgC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEvD,IAAAC,eAAA,GAA+BX,UAAU,CAACY,GAAG,CAAC,QAAQ,CAAC;EAAxCC,WAAW,GAAAF,eAAA,CAAlBG,KAAK;AACb,IAAMC,YAAY,GAAGX,OAAO,CAACY,OAAO,CAACC,EAAE;AACvC,IAAMC,WAAW,GAAGd,OAAO,CAACe,MAAM,CAACC,EAAE;AACrC,IAAMC,aAAa,GAAG,CAAC;AACvB,IAAMC,UAAU,GAAG,CAACT,WAAW,GAAIE,YAAY,GAAG,CAAE,GAAIG,WAAW,IAAIG,aAAa,GAAG,CAAC,CAAE,IAAIA,aAAa;AAE3G,IAAME,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;EAC9B,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,IAAI,EAAK;IAChCxB,KAAK,CAACyB,KAAK,CACTD,IAAI,CAACE,KAAK,EACV,0BAA0BF,IAAI,CAACE,KAAK,CAACC,WAAW,CAAC,CAAC,EAAE,EACpD,CACE;MACEC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;IACT,CAAC,EACD;MACED,IAAI,EAAE,cAAc;MACpBE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;QACbC,OAAO,CAACC,GAAG,CAAC,oBAAoBR,IAAI,CAACE,KAAK,EAAE,CAAC;MAE/C;IACF,CAAC,CAEL,CAAC;EACH,CAAC;EAED,IAAMO,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,IAAA,EAAwB;IAAA,IAAlBV,IAAI,GAAAU,IAAA,CAAJV,IAAI;MAAEW,KAAK,GAAAD,IAAA,CAALC,KAAK;IACnC,OACE5B,IAAA,CAACZ,IAAI;MAACkC,KAAK,EAAE,CAACO,MAAM,CAACC,aAAa,EAAE;QAAExB,KAAK,EAAEQ;MAAW,CAAC,CAAE;MAAAiB,QAAA,EACzD/B,IAAA,CAACH,QAAQ;QACPsB,KAAK,EAAEF,IAAI,CAACE,KAAM;QAClBa,KAAK,EAAEf,IAAI,CAACe,KAAM;QAClBC,QAAQ,EAAEhB,IAAI,CAACgB,QAAS;QACxBC,IAAI,EAAEjB,IAAI,CAACiB,IAAK;QAChBC,KAAK,EAAElB,IAAI,CAACkB,KAAM;QAClBC,KAAK,EAAEnB,IAAI,CAACmB,KAAM;QAClBb,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQP,eAAe,CAACC,IAAI,CAAC;QAAA;MAAC,CACtC;IAAC,CACE,CAAC;EAEX,CAAC;EAED,IAAMoB,YAAY,GAAG,SAAfA,YAAYA,CAAA;IAAA,OAChBnC,KAAA,CAACd,IAAI;MAACkC,KAAK,EAAEO,MAAM,CAACS,eAAgB;MAAAP,QAAA,GAClC/B,IAAA,CAACX,IAAI;QAACiC,KAAK,EAAEO,MAAM,CAACU,YAAa;QAAAR,QAAA,EAAC;MAAgB,CAAM,CAAC,EACzD/B,IAAA,CAACX,IAAI;QAACiC,KAAK,EAAEO,MAAM,CAACW,eAAgB;QAAAT,QAAA,EAAC;MAErC,CAAM,CAAC;IAAA,CACH,CAAC;EAAA,CACR;EAED,IAAMU,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,IAAMC,OAAO,GAAG;MACdC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC;IACD,OAAON,KAAK,CAACO,kBAAkB,CAAC,OAAO,EAAEL,OAAO,CAAC;EACnD,CAAC;EAED,OACE1C,KAAA,CAACd,IAAI;IAACkC,KAAK,EAAEO,MAAM,CAACqB,SAAU;IAAAnB,QAAA,GAC3BM,YAAY,CAAC,CAAC,EAEfrC,IAAA,CAACZ,IAAI;MAACkC,KAAK,EAAEO,MAAM,CAACsB,aAAc;MAAApB,QAAA,EAChC/B,IAAA,CAACX,IAAI;QAACiC,KAAK,EAAEO,MAAM,CAACuB,QAAS;QAAArB,QAAA,EAAEU,cAAc,CAAC;MAAC,CAAO;IAAC,CACnD,CAAC,EAEPzC,IAAA,CAACT,QAAQ;MACP8D,IAAI,EAAEvD,cAAe;MACrBwD,UAAU,EAAE5B,cAAe;MAC3B6B,YAAY,EAAE,SAAdA,YAAYA,CAAGtC,IAAI;QAAA,OAAKA,IAAI,CAACuC,EAAE,CAACC,QAAQ,CAAC,CAAC;MAAA,CAAC;MAC3CC,UAAU,EAAE7C,aAAc;MAC1B8C,aAAa,EAAE,KAAM;MACrBC,qBAAqB,EAAE/B,MAAM,CAACgC,aAAc;MAC5CC,kBAAkB,EAAEjC,MAAM,CAACkC,GAAI;MAC/BC,4BAA4B,EAAE;IAAM,CACrC,CAAC;EAAA,CACE,CAAC;AAEX,CAAC;AAED,IAAMnC,MAAM,GAAGvC,UAAU,CAAC2E,MAAM,CAAC;EAC/Bf,SAAS,EAAE;IACTgB,iBAAiB,EAAE3D;EACrB,CAAC;EACD+B,eAAe,EAAE;IACf6B,YAAY,EAAEvE,OAAO,CAACa;EACxB,CAAC;EACD8B,YAAY,EAAA6B,aAAA,CAAAA,aAAA,KACPzE,UAAU,CAAC0E,EAAE;IAChBlC,KAAK,EAAEzC,MAAM,CAAC4E,SAAS;IACvBH,YAAY,EAAEvE,OAAO,CAAC2E;EAAE,EACzB;EACD/B,eAAe,EAAA4B,aAAA,CAAAA,aAAA,KACVzE,UAAU,CAAC6E,KAAK;IACnBrC,KAAK,EAAEzC,MAAM,CAAC+E;EAAgB,EAC/B;EACDtB,aAAa,EAAE;IACbuB,eAAe,EAAEhF,MAAM,CAACiF,YAAY;IACpCT,iBAAiB,EAAEtE,OAAO,CAACY,OAAO,CAACC,EAAE;IACrCmE,eAAe,EAAEhF,OAAO,CAACY,OAAO,CAACI,EAAE;IACnCiE,YAAY,EAAEjF,OAAO,CAACiF,YAAY,CAACpE,EAAE;IACrC0D,YAAY,EAAEvE,OAAO,CAACkF;EACxB,CAAC;EACD1B,QAAQ,EAAAgB,aAAA,CAAAA,aAAA,KACHzE,UAAU,CAAC6E,KAAK;IACnBrC,KAAK,EAAEzC,MAAM,CAACqF,OAAO;IACrBC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE;EAAK,EAClB;EACDpB,aAAa,EAAE;IACbqB,aAAa,EAAEtF,OAAO,CAACgB;EACzB,CAAC;EACDmD,GAAG,EAAE;IACHoB,cAAc,EAAE,eAAe;IAC/BhB,YAAY,EAAEzD,WAAW,GAAG;EAC9B,CAAC;EACDoB,aAAa,EAAE;IACbqC,YAAY,EAAEzD;EAChB;AACF,CAAC,CAAC;AAEF,eAAeK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}