{"ast": null, "code": "import bytesToUuid from './bytesToUuid';\nfunction uuidToBytes(uuid) {\n  var bytes = [];\n  uuid.replace(/[a-fA-F0-9]{2}/g, function (hex) {\n    bytes.push(parseInt(hex, 16));\n    return '';\n  });\n  return bytes;\n}\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str));\n  var bytes = new Array(str.length);\n  for (var i = 0; i < str.length; i++) {\n    bytes[i] = str.charCodeAt(i);\n  }\n  return bytes;\n}\nexport default function (name, version, hashfunc) {\n  var generateUUID = function generateUUID(value, namespace, buf, offset) {\n    var off = buf && offset || 0;\n    if (typeof value == 'string') value = stringToBytes(value);\n    if (typeof namespace == 'string') namespace = uuidToBytes(namespace);\n    if (!Array.isArray(value)) throw TypeError('value must be an array of bytes');\n    if (!Array.isArray(namespace) || namespace.length !== 16) throw TypeError('namespace must be uuid string or an Array of 16 byte values');\n    var bytes = hashfunc(namespace.concat(value));\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n    if (buf) {\n      for (var idx = 0; idx < 16; ++idx) {\n        buf[off + idx] = bytes[idx];\n      }\n    }\n    return bytesToUuid(bytes);\n  };\n  try {\n    generateUUID.name = name;\n  } catch (_unused) {}\n  generateUUID.DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\n  generateUUID.URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\n  return generateUUID;\n}", "map": {"version": 3, "names": ["bytesToUuid", "uuidToBytes", "uuid", "bytes", "replace", "hex", "push", "parseInt", "stringToBytes", "str", "unescape", "encodeURIComponent", "Array", "length", "i", "charCodeAt", "name", "version", "hashfunc", "generateUUID", "value", "namespace", "buf", "offset", "off", "isArray", "TypeError", "concat", "idx", "_unused", "DNS", "URL"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-modules-core\\src\\uuid\\lib\\v35.ts"], "sourcesContent": ["import bytesToUuid from './bytesToUuid';\n\nfunction uuidToBytes(uuid: string) {\n  // Note: We assume we're being passed a valid uuid string\n  const bytes: number[] = [];\n  uuid.replace(/[a-fA-F0-9]{2}/g, (hex: string) => {\n    bytes.push(parseInt(hex, 16));\n    return '';\n  });\n\n  return bytes;\n}\n\nfunction stringToBytes(str: string) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n  const bytes: number[] = new Array(str.length);\n  for (let i = 0; i < str.length; i++) {\n    bytes[i] = str.charCodeAt(i);\n  }\n  return bytes;\n}\n\nexport default function (\n  name: string,\n  version: number,\n  hashfunc: (bytes: number[] | string) => number[]\n) {\n  const generateUUID = function (\n    value: number[] | string,\n    namespace: number[] | string,\n    buf?: number[],\n    offset?: number\n  ): string {\n    const off = (buf && offset) || 0;\n\n    if (typeof value == 'string') value = stringToBytes(value);\n    if (typeof namespace == 'string') namespace = uuidToBytes(namespace);\n\n    if (!Array.isArray(value)) throw TypeError('value must be an array of bytes');\n    if (!Array.isArray(namespace) || namespace.length !== 16)\n      throw TypeError('namespace must be uuid string or an Array of 16 byte values');\n\n    // Per 4.3\n    const bytes = hashfunc(namespace.concat(value));\n    bytes[6] = (bytes[6] & 0x0f) | version;\n    bytes[8] = (bytes[8] & 0x3f) | 0x80;\n\n    if (buf) {\n      for (let idx = 0; idx < 16; ++idx) {\n        buf[off + idx] = bytes[idx];\n      }\n    }\n\n    return bytesToUuid(bytes);\n  };\n\n  // Function#name is not settable on some platforms (#270)\n  try {\n    generateUUID.name = name;\n  } catch {}\n\n  // Pre-defined namespaces, per Appendix C\n  generateUUID.DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\n  generateUUID.URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\n\n  return generateUUID;\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AAEvC,SAASC,WAAWA,CAACC,IAAY;EAE/B,IAAMC,KAAK,GAAa,EAAE;EAC1BD,IAAI,CAACE,OAAO,CAAC,iBAAiB,EAAE,UAACC,GAAW,EAAI;IAC9CF,KAAK,CAACG,IAAI,CAACC,QAAQ,CAACF,GAAG,EAAE,EAAE,CAAC,CAAC;IAC7B,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,OAAOF,KAAK;AACd;AAEA,SAASK,aAAaA,CAACC,GAAW;EAChCA,GAAG,GAAGC,QAAQ,CAACC,kBAAkB,CAACF,GAAG,CAAC,CAAC;EACvC,IAAMN,KAAK,GAAa,IAAIS,KAAK,CAACH,GAAG,CAACI,MAAM,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;IACnCX,KAAK,CAACW,CAAC,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACD,CAAC,CAAC;;EAE9B,OAAOX,KAAK;AACd;AAEA,eAAc,UACZa,IAAY,EACZC,OAAe,EACfC,QAAgD;EAEhD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAChBC,KAAwB,EACxBC,SAA4B,EAC5BC,GAAc,EACdC,MAAe;IAEf,IAAMC,GAAG,GAAIF,GAAG,IAAIC,MAAM,IAAK,CAAC;IAEhC,IAAI,OAAOH,KAAK,IAAI,QAAQ,EAAEA,KAAK,GAAGZ,aAAa,CAACY,KAAK,CAAC;IAC1D,IAAI,OAAOC,SAAS,IAAI,QAAQ,EAAEA,SAAS,GAAGpB,WAAW,CAACoB,SAAS,CAAC;IAEpE,IAAI,CAACT,KAAK,CAACa,OAAO,CAACL,KAAK,CAAC,EAAE,MAAMM,SAAS,CAAC,iCAAiC,CAAC;IAC7E,IAAI,CAACd,KAAK,CAACa,OAAO,CAACJ,SAAS,CAAC,IAAIA,SAAS,CAACR,MAAM,KAAK,EAAE,EACtD,MAAMa,SAAS,CAAC,6DAA6D,CAAC;IAGhF,IAAMvB,KAAK,GAAGe,QAAQ,CAACG,SAAS,CAACM,MAAM,CAACP,KAAK,CAAC,CAAC;IAC/CjB,KAAK,CAAC,CAAC,CAAC,GAAIA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAIc,OAAO;IACtCd,KAAK,CAAC,CAAC,CAAC,GAAIA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI;IAEnC,IAAImB,GAAG,EAAE;MACP,KAAK,IAAIM,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,EAAE,EAAE,EAAEA,GAAG,EAAE;QACjCN,GAAG,CAACE,GAAG,GAAGI,GAAG,CAAC,GAAGzB,KAAK,CAACyB,GAAG,CAAC;;;IAI/B,OAAO5B,WAAW,CAACG,KAAK,CAAC;EAC3B,CAAC;EAGD,IAAI;IACFgB,YAAY,CAACH,IAAI,GAAGA,IAAI;GACzB,CAAC,OAAAa,OAAA,EAAM;EAGRV,YAAY,CAACW,GAAG,GAAG,sCAAsC;EACzDX,YAAY,CAACY,GAAG,GAAG,sCAAsC;EAEzD,OAAOZ,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}