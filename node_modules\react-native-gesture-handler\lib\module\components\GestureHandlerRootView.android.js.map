{"version": 3, "sources": ["GestureHandlerRootView.android.tsx"], "names": ["React", "maybeInitializeFabric", "GestureHandlerRootViewContext", "GestureHandlerRootViewNativeComponent", "GestureHandlerRootView", "props"], "mappings": "AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAGA,SAASC,qBAAT,QAAsC,SAAtC;AACA,OAAOC,6BAAP,MAA0C,kCAA1C;AACA,OAAOC,qCAAP,MAAkD,kDAAlD;AAKA,eAAe,SAASC,sBAAT,CACbC,KADa,EAEb;AACA;AACA;AACA;AACAJ,EAAAA,qBAAqB;AAErB,sBACE,oBAAC,6BAAD,CAA+B,QAA/B;AAAwC,IAAA,KAAK;AAA7C,kBACE,oBAAC,qCAAD,EAA2CI,KAA3C,CADF,CADF;AAKD", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport { ViewProps } from 'react-native';\nimport { maybeInitializeFabric } from '../init';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\nimport GestureHandlerRootViewNativeComponent from '../specs/RNGestureHandlerRootViewNativeComponent';\n\nexport interface GestureHandlerRootViewProps\n  extends PropsWithChildren<ViewProps> {}\n\nexport default function GestureHandlerRootView(\n  props: GestureHandlerRootViewProps\n) {\n  // try initialize fabric on the first render, at this point we can\n  // reliably check if fabric is enabled (the function contains a flag\n  // to make sure it's called only once)\n  maybeInitializeFabric();\n\n  return (\n    <GestureHandlerRootViewContext.Provider value>\n      <GestureHandlerRootViewNativeComponent {...props} />\n    </GestureHandlerRootViewContext.Provider>\n  );\n}\n"]}