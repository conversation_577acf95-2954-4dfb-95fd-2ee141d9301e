{"version": 3, "sources": ["CircularBuffer.ts"], "names": ["Circular<PERSON><PERSON>er", "constructor", "size", "bufferSize", "buffer", "Array", "index", "actualSize", "push", "element", "Math", "min", "get", "at", "clear"], "mappings": ";;;;;;;;;AAAe,MAAMA,cAAN,CAAwB;AAMrCC,EAAAA,WAAW,CAACC,IAAD,EAAe;AAAA;;AAAA;;AAAA;;AAAA;;AACxB,SAAKC,UAAL,GAAkBD,IAAlB;AACA,SAAKE,MAAL,GAAc,IAAIC,KAAJ,CAAaH,IAAb,CAAd;AACA,SAAKI,KAAL,GAAa,CAAb;AACA,SAAKC,UAAL,GAAkB,CAAlB;AACD;;AAEc,MAAJL,IAAI,GAAW;AACxB,WAAO,KAAKK,UAAZ;AACD;;AAEMC,EAAAA,IAAI,CAACC,OAAD,EAAmB;AAC5B,SAAKL,MAAL,CAAY,KAAKE,KAAjB,IAA0BG,OAA1B;AACA,SAAKH,KAAL,GAAa,CAAC,KAAKA,KAAL,GAAa,CAAd,IAAmB,KAAKH,UAArC;AACA,SAAKI,UAAL,GAAkBG,IAAI,CAACC,GAAL,CAAS,KAAKJ,UAAL,GAAkB,CAA3B,EAA8B,KAAKJ,UAAnC,CAAlB;AACD;;AAEMS,EAAAA,GAAG,CAACC,EAAD,EAAgB;AACxB,QAAI,KAAKN,UAAL,KAAoB,KAAKJ,UAA7B,EAAyC;AACvC,UAAIG,KAAK,GAAG,CAAC,KAAKA,KAAL,GAAaO,EAAd,IAAoB,KAAKV,UAArC;;AACA,UAAIG,KAAK,GAAG,CAAZ,EAAe;AACbA,QAAAA,KAAK,IAAI,KAAKH,UAAd;AACD;;AAED,aAAO,KAAKC,MAAL,CAAYE,KAAZ,CAAP;AACD,KAPD,MAOO;AACL,aAAO,KAAKF,MAAL,CAAYS,EAAZ,CAAP;AACD;AACF;;AAEMC,EAAAA,KAAK,GAAS;AACnB,SAAKV,MAAL,GAAc,IAAIC,KAAJ,CAAa,KAAKF,UAAlB,CAAd;AACA,SAAKG,KAAL,GAAa,CAAb;AACA,SAAKC,UAAL,GAAkB,CAAlB;AACD;;AAxCoC", "sourcesContent": ["export default class CircularBuffer<T> {\n  private bufferSize: number;\n  private buffer: T[];\n  private index: number;\n  private actualSize: number;\n\n  constructor(size: number) {\n    this.bufferSize = size;\n    this.buffer = new Array<T>(size);\n    this.index = 0;\n    this.actualSize = 0;\n  }\n\n  public get size(): number {\n    return this.actualSize;\n  }\n\n  public push(element: T): void {\n    this.buffer[this.index] = element;\n    this.index = (this.index + 1) % this.bufferSize;\n    this.actualSize = Math.min(this.actualSize + 1, this.bufferSize);\n  }\n\n  public get(at: number): T {\n    if (this.actualSize === this.bufferSize) {\n      let index = (this.index + at) % this.bufferSize;\n      if (index < 0) {\n        index += this.bufferSize;\n      }\n\n      return this.buffer[index];\n    } else {\n      return this.buffer[at];\n    }\n  }\n\n  public clear(): void {\n    this.buffer = new Array<T>(this.bufferSize);\n    this.index = 0;\n    this.actualSize = 0;\n  }\n}\n"]}