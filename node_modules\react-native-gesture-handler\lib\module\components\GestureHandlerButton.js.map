{"version": 3, "sources": ["GestureHandlerButton.tsx"], "names": ["RNGestureHandlerButtonNativeComponent"], "mappings": "AAEA,OAAOA,qCAAP,MAAkD,gDAAlD;AAEA,eAAeA,qCAAf", "sourcesContent": ["import { HostComponent } from 'react-native';\nimport { RawButtonProps } from './GestureButtons';\nimport RNGestureHandlerButtonNativeComponent from '../specs/RNGestureHandlerButtonNativeComponent';\n\nexport default RNGestureHandlerButtonNativeComponent as HostComponent<RawButtonProps>;\n"]}