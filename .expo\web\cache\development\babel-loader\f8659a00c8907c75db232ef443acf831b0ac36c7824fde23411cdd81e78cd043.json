{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Colors, Typography, Spacing } from \"../constants\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar _Dimensions$get = Dimensions.get('window'),\n  screenWidth = _Dimensions$get.width;\nvar VIDEO_CARD_WIDTH = screenWidth * 0.7;\nvar VIDEO_CARD_HEIGHT = 200;\nvar videoData = [{\n  id: 1,\n  title: 'Safety Training - Keselamatan Kerja',\n  duration: '15:30',\n  thumbnail: 'https://via.placeholder.com/300x200/4CAF50/FFFFFF?text=Safety+Training',\n  category: 'Training',\n  uploadDate: '2024-01-15',\n  views: 1250\n}, {\n  id: 2,\n  title: 'Company Update - Q1 2024',\n  duration: '8:45',\n  thumbnail: 'https://via.placeholder.com/300x200/2196F3/FFFFFF?text=Company+Update',\n  category: 'Update',\n  uploadDate: '2024-01-10',\n  views: 890\n}, {\n  id: 3,\n  title: 'New Employee Orientation',\n  duration: '22:15',\n  thumbnail: 'https://via.placeholder.com/300x200/9C27B0/FFFFFF?text=Orientation',\n  category: 'Orientation',\n  uploadDate: '2024-01-08',\n  views: 567\n}, {\n  id: 4,\n  title: 'Production Excellence Workshop',\n  duration: '18:20',\n  thumbnail: 'https://via.placeholder.com/300x200/FF9800/FFFFFF?text=Workshop',\n  category: 'Workshop',\n  uploadDate: '2024-01-05',\n  views: 432\n}];\nvar VideoGallery = function VideoGallery() {\n  var handleVideoPress = function handleVideoPress(video) {\n    Alert.alert(video.title, `Durasi: ${video.duration}\\nKategori: ${video.category}\\nDilihat: ${video.views} kali`, [{\n      text: 'Batal',\n      style: 'cancel'\n    }, {\n      text: 'Putar Video',\n      onPress: function onPress() {\n        console.log('Play video:', video.title);\n      }\n    }]);\n  };\n  var handleViewAll = function handleViewAll() {\n    console.log('View all videos');\n  };\n  var formatViews = function formatViews(views) {\n    if (views >= 1000) {\n      return `${(views / 1000).toFixed(1)}k`;\n    }\n    return views.toString();\n  };\n  var renderVideoItem = function renderVideoItem(_ref) {\n    var item = _ref.item;\n    return _jsxs(TouchableOpacity, {\n      style: styles.videoCard,\n      onPress: function onPress() {\n        return handleVideoPress(item);\n      },\n      activeOpacity: 0.9,\n      children: [_jsxs(View, {\n        style: styles.thumbnailContainer,\n        children: [_jsx(Image, {\n          source: {\n            uri: item.thumbnail\n          },\n          style: styles.thumbnail\n        }), _jsx(View, {\n          style: styles.playButton,\n          children: _jsx(MaterialIcons, {\n            name: \"play-arrow\",\n            size: 32,\n            color: Colors.onPrimary\n          })\n        }), _jsx(View, {\n          style: styles.durationBadge,\n          children: _jsx(Text, {\n            style: styles.durationText,\n            children: item.duration\n          })\n        })]\n      }), _jsxs(View, {\n        style: styles.videoInfo,\n        children: [_jsx(Text, {\n          style: styles.videoTitle,\n          numberOfLines: 2,\n          children: item.title\n        }), _jsxs(View, {\n          style: styles.videoMeta,\n          children: [_jsx(View, {\n            style: styles.categoryBadge,\n            children: _jsx(Text, {\n              style: styles.categoryText,\n              children: item.category\n            })\n          }), _jsxs(View, {\n            style: styles.metaInfo,\n            children: [_jsx(MaterialIcons, {\n              name: \"visibility\",\n              size: 14,\n              color: Colors.onSurfaceVariant\n            }), _jsx(Text, {\n              style: styles.viewsText,\n              children: formatViews(item.views)\n            })]\n          })]\n        })]\n      })]\n    });\n  };\n  var renderHeader = function renderHeader() {\n    return _jsxs(View, {\n      style: styles.headerContainer,\n      children: [_jsxs(View, {\n        style: styles.titleContainer,\n        children: [_jsx(Text, {\n          style: styles.sectionTitle,\n          children: \"Featured Videos\"\n        }), _jsx(Text, {\n          style: styles.sectionSubtitle,\n          children: \"Video pelatihan dan update terbaru\"\n        })]\n      }), _jsxs(TouchableOpacity, {\n        onPress: handleViewAll,\n        style: styles.viewAllButton,\n        children: [_jsx(Text, {\n          style: styles.viewAllText,\n          children: \"Lihat Semua\"\n        }), _jsx(MaterialIcons, {\n          name: \"arrow-forward\",\n          size: 16,\n          color: Colors.primary\n        })]\n      })]\n    });\n  };\n  return _jsxs(View, {\n    style: styles.container,\n    children: [renderHeader(), _jsx(FlatList, {\n      data: videoData,\n      renderItem: renderVideoItem,\n      keyExtractor: function keyExtractor(item) {\n        return item.id.toString();\n      },\n      horizontal: true,\n      showsHorizontalScrollIndicator: false,\n      contentContainerStyle: styles.listContainer,\n      snapToInterval: VIDEO_CARD_WIDTH + Spacing.margin.md,\n      decelerationRate: \"fast\"\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    paddingVertical: Spacing.padding.sm\n  },\n  headerContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'flex-start',\n    paddingHorizontal: Spacing.padding.md,\n    marginBottom: Spacing.md\n  },\n  titleContainer: {\n    flex: 1\n  },\n  sectionTitle: _objectSpread(_objectSpread({}, Typography.h3), {}, {\n    color: Colors.onSurface,\n    marginBottom: Spacing.xs\n  }),\n  sectionSubtitle: _objectSpread(_objectSpread({}, Typography.body2), {}, {\n    color: Colors.onSurfaceVariant\n  }),\n  viewAllButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    paddingVertical: Spacing.xs\n  },\n  viewAllText: _objectSpread(_objectSpread({}, Typography.body2), {}, {\n    color: Colors.primary,\n    marginRight: Spacing.xs,\n    fontWeight: '500'\n  }),\n  listContainer: {\n    paddingHorizontal: Spacing.padding.md\n  },\n  videoCard: {\n    width: VIDEO_CARD_WIDTH,\n    marginRight: Spacing.margin.md,\n    backgroundColor: Colors.surface,\n    borderRadius: Spacing.borderRadius.lg,\n    overflow: 'hidden',\n    shadowColor: Colors.cardShadow,\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3\n  },\n  thumbnailContainer: {\n    position: 'relative',\n    height: VIDEO_CARD_HEIGHT * 0.6\n  },\n  thumbnail: {\n    width: '100%',\n    height: '100%',\n    resizeMode: 'cover'\n  },\n  playButton: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: [{\n      translateX: -20\n    }, {\n      translateY: -20\n    }],\n    width: 40,\n    height: 40,\n    borderRadius: 20,\n    backgroundColor: 'rgba(0, 0, 0, 0.7)',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  durationBadge: {\n    position: 'absolute',\n    bottom: Spacing.xs,\n    right: Spacing.xs,\n    backgroundColor: 'rgba(0, 0, 0, 0.8)',\n    paddingHorizontal: Spacing.xs,\n    paddingVertical: 2,\n    borderRadius: Spacing.borderRadius.sm\n  },\n  durationText: _objectSpread(_objectSpread({}, Typography.caption), {}, {\n    color: Colors.onPrimary,\n    fontSize: 10\n  }),\n  videoInfo: {\n    padding: Spacing.padding.md\n  },\n  videoTitle: _objectSpread(_objectSpread({}, Typography.subtitle2), {}, {\n    color: Colors.onSurface,\n    marginBottom: Spacing.sm\n  }),\n  videoMeta: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center'\n  },\n  categoryBadge: {\n    backgroundColor: Colors.primaryLight,\n    paddingHorizontal: Spacing.xs,\n    paddingVertical: 2,\n    borderRadius: Spacing.borderRadius.sm\n  },\n  categoryText: _objectSpread(_objectSpread({}, Typography.caption), {}, {\n    color: Colors.primary,\n    fontSize: 10,\n    fontWeight: '500'\n  }),\n  metaInfo: {\n    flexDirection: 'row',\n    alignItems: 'center'\n  },\n  viewsText: _objectSpread(_objectSpread({}, Typography.caption), {}, {\n    color: Colors.onSurfaceVariant,\n    marginLeft: Spacing.xs / 2\n  })\n});\nexport default VideoGallery;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "FlatList", "TouchableOpacity", "Image", "Dimensions", "<PERSON><PERSON>", "MaterialIcons", "Colors", "Typography", "Spacing", "jsx", "_jsx", "jsxs", "_jsxs", "_Dimensions$get", "get", "screenWidth", "width", "VIDEO_CARD_WIDTH", "VIDEO_CARD_HEIGHT", "videoData", "id", "title", "duration", "thumbnail", "category", "uploadDate", "views", "VideoGallery", "handleVideoPress", "video", "alert", "text", "style", "onPress", "console", "log", "handleViewAll", "formatViews", "toFixed", "toString", "renderVideoItem", "_ref", "item", "styles", "videoCard", "activeOpacity", "children", "thumbnail<PERSON><PERSON><PERSON>", "source", "uri", "playButton", "name", "size", "color", "onPrimary", "durationBadge", "durationText", "videoInfo", "videoTitle", "numberOfLines", "videoMeta", "categoryBadge", "categoryText", "metaInfo", "onSurfaceVariant", "viewsText", "renderHeader", "headerContainer", "<PERSON><PERSON><PERSON><PERSON>", "sectionTitle", "sectionSubtitle", "viewAllButton", "viewAllText", "primary", "container", "data", "renderItem", "keyExtractor", "horizontal", "showsHorizontalScrollIndicator", "contentContainerStyle", "listContainer", "snapToInterval", "margin", "md", "decelerationRate", "create", "paddingVertical", "padding", "sm", "flexDirection", "justifyContent", "alignItems", "paddingHorizontal", "marginBottom", "flex", "_objectSpread", "h3", "onSurface", "xs", "body2", "marginRight", "fontWeight", "backgroundColor", "surface", "borderRadius", "lg", "overflow", "shadowColor", "cardShadow", "shadowOffset", "height", "shadowOpacity", "shadowRadius", "elevation", "position", "resizeMode", "top", "left", "transform", "translateX", "translateY", "bottom", "right", "caption", "fontSize", "subtitle2", "primaryLight", "marginLeft"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/components/VideoGallery.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  FlatList,\n  TouchableOpacity,\n  Image,\n  Dimensions,\n  Alert,\n} from 'react-native';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Colors, Typography, Spacing } from '../constants';\n\nconst { width: screenWidth } = Dimensions.get('window');\nconst VIDEO_CARD_WIDTH = screenWidth * 0.7;\nconst VIDEO_CARD_HEIGHT = 200;\n\n// Sample video data\nconst videoData = [\n  {\n    id: 1,\n    title: 'Safety Training - Keselamatan Kerja',\n    duration: '15:30',\n    thumbnail: 'https://via.placeholder.com/300x200/4CAF50/FFFFFF?text=Safety+Training',\n    category: 'Training',\n    uploadDate: '2024-01-15',\n    views: 1250,\n  },\n  {\n    id: 2,\n    title: 'Company Update - Q1 2024',\n    duration: '8:45',\n    thumbnail: 'https://via.placeholder.com/300x200/2196F3/FFFFFF?text=Company+Update',\n    category: 'Update',\n    uploadDate: '2024-01-10',\n    views: 890,\n  },\n  {\n    id: 3,\n    title: 'New Employee Orientation',\n    duration: '22:15',\n    thumbnail: 'https://via.placeholder.com/300x200/9C27B0/FFFFFF?text=Orientation',\n    category: 'Orientation',\n    uploadDate: '2024-01-08',\n    views: 567,\n  },\n  {\n    id: 4,\n    title: 'Production Excellence Workshop',\n    duration: '18:20',\n    thumbnail: 'https://via.placeholder.com/300x200/FF9800/FFFFFF?text=Workshop',\n    category: 'Workshop',\n    uploadDate: '2024-01-05',\n    views: 432,\n  },\n];\n\nconst VideoGallery = () => {\n  const handleVideoPress = (video) => {\n    Alert.alert(\n      video.title,\n      `Durasi: ${video.duration}\\nKategori: ${video.category}\\nDilihat: ${video.views} kali`,\n      [\n        {\n          text: 'Batal',\n          style: 'cancel',\n        },\n        {\n          text: 'Putar Video',\n          onPress: () => {\n            console.log('Play video:', video.title);\n            // Video player logic would go here\n          },\n        },\n      ]\n    );\n  };\n\n  const handleViewAll = () => {\n    console.log('View all videos');\n    // Navigate to full video gallery\n  };\n\n  const formatViews = (views) => {\n    if (views >= 1000) {\n      return `${(views / 1000).toFixed(1)}k`;\n    }\n    return views.toString();\n  };\n\n  const renderVideoItem = ({ item }) => {\n    return (\n      <TouchableOpacity\n        style={styles.videoCard}\n        onPress={() => handleVideoPress(item)}\n        activeOpacity={0.9}\n      >\n        <View style={styles.thumbnailContainer}>\n          <Image source={{ uri: item.thumbnail }} style={styles.thumbnail} />\n          <View style={styles.playButton}>\n            <MaterialIcons\n              name=\"play-arrow\"\n              size={32}\n              color={Colors.onPrimary}\n            />\n          </View>\n          <View style={styles.durationBadge}>\n            <Text style={styles.durationText}>{item.duration}</Text>\n          </View>\n        </View>\n        \n        <View style={styles.videoInfo}>\n          <Text style={styles.videoTitle} numberOfLines={2}>\n            {item.title}\n          </Text>\n          <View style={styles.videoMeta}>\n            <View style={styles.categoryBadge}>\n              <Text style={styles.categoryText}>{item.category}</Text>\n            </View>\n            <View style={styles.metaInfo}>\n              <MaterialIcons\n                name=\"visibility\"\n                size={14}\n                color={Colors.onSurfaceVariant}\n              />\n              <Text style={styles.viewsText}>{formatViews(item.views)}</Text>\n            </View>\n          </View>\n        </View>\n      </TouchableOpacity>\n    );\n  };\n\n  const renderHeader = () => (\n    <View style={styles.headerContainer}>\n      <View style={styles.titleContainer}>\n        <Text style={styles.sectionTitle}>Featured Videos</Text>\n        <Text style={styles.sectionSubtitle}>\n          Video pelatihan dan update terbaru\n        </Text>\n      </View>\n      <TouchableOpacity onPress={handleViewAll} style={styles.viewAllButton}>\n        <Text style={styles.viewAllText}>Lihat Semua</Text>\n        <MaterialIcons\n          name=\"arrow-forward\"\n          size={16}\n          color={Colors.primary}\n        />\n      </TouchableOpacity>\n    </View>\n  );\n\n  return (\n    <View style={styles.container}>\n      {renderHeader()}\n      \n      <FlatList\n        data={videoData}\n        renderItem={renderVideoItem}\n        keyExtractor={(item) => item.id.toString()}\n        horizontal\n        showsHorizontalScrollIndicator={false}\n        contentContainerStyle={styles.listContainer}\n        snapToInterval={VIDEO_CARD_WIDTH + Spacing.margin.md}\n        decelerationRate=\"fast\"\n      />\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    paddingVertical: Spacing.padding.sm,\n  },\n  headerContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'flex-start',\n    paddingHorizontal: Spacing.padding.md,\n    marginBottom: Spacing.md,\n  },\n  titleContainer: {\n    flex: 1,\n  },\n  sectionTitle: {\n    ...Typography.h3,\n    color: Colors.onSurface,\n    marginBottom: Spacing.xs,\n  },\n  sectionSubtitle: {\n    ...Typography.body2,\n    color: Colors.onSurfaceVariant,\n  },\n  viewAllButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    paddingVertical: Spacing.xs,\n  },\n  viewAllText: {\n    ...Typography.body2,\n    color: Colors.primary,\n    marginRight: Spacing.xs,\n    fontWeight: '500',\n  },\n  listContainer: {\n    paddingHorizontal: Spacing.padding.md,\n  },\n  videoCard: {\n    width: VIDEO_CARD_WIDTH,\n    marginRight: Spacing.margin.md,\n    backgroundColor: Colors.surface,\n    borderRadius: Spacing.borderRadius.lg,\n    overflow: 'hidden',\n    shadowColor: Colors.cardShadow,\n    shadowOffset: {\n      width: 0,\n      height: 2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3,\n  },\n  thumbnailContainer: {\n    position: 'relative',\n    height: VIDEO_CARD_HEIGHT * 0.6,\n  },\n  thumbnail: {\n    width: '100%',\n    height: '100%',\n    resizeMode: 'cover',\n  },\n  playButton: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: [{ translateX: -20 }, { translateY: -20 }],\n    width: 40,\n    height: 40,\n    borderRadius: 20,\n    backgroundColor: 'rgba(0, 0, 0, 0.7)',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  durationBadge: {\n    position: 'absolute',\n    bottom: Spacing.xs,\n    right: Spacing.xs,\n    backgroundColor: 'rgba(0, 0, 0, 0.8)',\n    paddingHorizontal: Spacing.xs,\n    paddingVertical: 2,\n    borderRadius: Spacing.borderRadius.sm,\n  },\n  durationText: {\n    ...Typography.caption,\n    color: Colors.onPrimary,\n    fontSize: 10,\n  },\n  videoInfo: {\n    padding: Spacing.padding.md,\n  },\n  videoTitle: {\n    ...Typography.subtitle2,\n    color: Colors.onSurface,\n    marginBottom: Spacing.sm,\n  },\n  videoMeta: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n  },\n  categoryBadge: {\n    backgroundColor: Colors.primaryLight,\n    paddingHorizontal: Spacing.xs,\n    paddingVertical: 2,\n    borderRadius: Spacing.borderRadius.sm,\n  },\n  categoryText: {\n    ...Typography.caption,\n    color: Colors.primary,\n    fontSize: 10,\n    fontWeight: '500',\n  },\n  metaInfo: {\n    flexDirection: 'row',\n    alignItems: 'center',\n  },\n  viewsText: {\n    ...Typography.caption,\n    color: Colors.onSurfaceVariant,\n    marginLeft: Spacing.xs / 2,\n  },\n});\n\nexport default VideoGallery;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAW1B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO;AAAuB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE3D,IAAAC,eAAA,GAA+BV,UAAU,CAACW,GAAG,CAAC,QAAQ,CAAC;EAAxCC,WAAW,GAAAF,eAAA,CAAlBG,KAAK;AACb,IAAMC,gBAAgB,GAAGF,WAAW,GAAG,GAAG;AAC1C,IAAMG,iBAAiB,GAAG,GAAG;AAG7B,IAAMC,SAAS,GAAG,CAChB;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,qCAAqC;EAC5CC,QAAQ,EAAE,OAAO;EACjBC,SAAS,EAAE,wEAAwE;EACnFC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,YAAY;EACxBC,KAAK,EAAE;AACT,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,0BAA0B;EACjCC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE,uEAAuE;EAClFC,QAAQ,EAAE,QAAQ;EAClBC,UAAU,EAAE,YAAY;EACxBC,KAAK,EAAE;AACT,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,0BAA0B;EACjCC,QAAQ,EAAE,OAAO;EACjBC,SAAS,EAAE,oEAAoE;EAC/EC,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,YAAY;EACxBC,KAAK,EAAE;AACT,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,gCAAgC;EACvCC,QAAQ,EAAE,OAAO;EACjBC,SAAS,EAAE,iEAAiE;EAC5EC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,YAAY;EACxBC,KAAK,EAAE;AACT,CAAC,CACF;AAED,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EACzB,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,KAAK,EAAK;IAClCzB,KAAK,CAAC0B,KAAK,CACTD,KAAK,CAACR,KAAK,EACX,WAAWQ,KAAK,CAACP,QAAQ,eAAeO,KAAK,CAACL,QAAQ,cAAcK,KAAK,CAACH,KAAK,OAAO,EACtF,CACE;MACEK,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;IACT,CAAC,EACD;MACED,IAAI,EAAE,aAAa;MACnBE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;QACbC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEN,KAAK,CAACR,KAAK,CAAC;MAEzC;IACF,CAAC,CAEL,CAAC;EACH,CAAC;EAED,IAAMe,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1BF,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAEhC,CAAC;EAED,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIX,KAAK,EAAK;IAC7B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEY,OAAO,CAAC,CAAC,CAAC,GAAG;IACxC;IACA,OAAOZ,KAAK,CAACa,QAAQ,CAAC,CAAC;EACzB,CAAC;EAED,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,IAAA,EAAiB;IAAA,IAAXC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAC7B,OACE9B,KAAA,CAACX,gBAAgB;MACf+B,KAAK,EAAEW,MAAM,CAACC,SAAU;MACxBX,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQL,gBAAgB,CAACc,IAAI,CAAC;MAAA,CAAC;MACtCG,aAAa,EAAE,GAAI;MAAAC,QAAA,GAEnBlC,KAAA,CAACf,IAAI;QAACmC,KAAK,EAAEW,MAAM,CAACI,kBAAmB;QAAAD,QAAA,GACrCpC,IAAA,CAACR,KAAK;UAAC8C,MAAM,EAAE;YAAEC,GAAG,EAAEP,IAAI,CAACnB;UAAU,CAAE;UAACS,KAAK,EAAEW,MAAM,CAACpB;QAAU,CAAE,CAAC,EACnEb,IAAA,CAACb,IAAI;UAACmC,KAAK,EAAEW,MAAM,CAACO,UAAW;UAAAJ,QAAA,EAC7BpC,IAAA,CAACL,aAAa;YACZ8C,IAAI,EAAC,YAAY;YACjBC,IAAI,EAAE,EAAG;YACTC,KAAK,EAAE/C,MAAM,CAACgD;UAAU,CACzB;QAAC,CACE,CAAC,EACP5C,IAAA,CAACb,IAAI;UAACmC,KAAK,EAAEW,MAAM,CAACY,aAAc;UAAAT,QAAA,EAChCpC,IAAA,CAACZ,IAAI;YAACkC,KAAK,EAAEW,MAAM,CAACa,YAAa;YAAAV,QAAA,EAAEJ,IAAI,CAACpB;UAAQ,CAAO;QAAC,CACpD,CAAC;MAAA,CACH,CAAC,EAEPV,KAAA,CAACf,IAAI;QAACmC,KAAK,EAAEW,MAAM,CAACc,SAAU;QAAAX,QAAA,GAC5BpC,IAAA,CAACZ,IAAI;UAACkC,KAAK,EAAEW,MAAM,CAACe,UAAW;UAACC,aAAa,EAAE,CAAE;UAAAb,QAAA,EAC9CJ,IAAI,CAACrB;QAAK,CACP,CAAC,EACPT,KAAA,CAACf,IAAI;UAACmC,KAAK,EAAEW,MAAM,CAACiB,SAAU;UAAAd,QAAA,GAC5BpC,IAAA,CAACb,IAAI;YAACmC,KAAK,EAAEW,MAAM,CAACkB,aAAc;YAAAf,QAAA,EAChCpC,IAAA,CAACZ,IAAI;cAACkC,KAAK,EAAEW,MAAM,CAACmB,YAAa;cAAAhB,QAAA,EAAEJ,IAAI,CAAClB;YAAQ,CAAO;UAAC,CACpD,CAAC,EACPZ,KAAA,CAACf,IAAI;YAACmC,KAAK,EAAEW,MAAM,CAACoB,QAAS;YAAAjB,QAAA,GAC3BpC,IAAA,CAACL,aAAa;cACZ8C,IAAI,EAAC,YAAY;cACjBC,IAAI,EAAE,EAAG;cACTC,KAAK,EAAE/C,MAAM,CAAC0D;YAAiB,CAChC,CAAC,EACFtD,IAAA,CAACZ,IAAI;cAACkC,KAAK,EAAEW,MAAM,CAACsB,SAAU;cAAAnB,QAAA,EAAET,WAAW,CAACK,IAAI,CAAChB,KAAK;YAAC,CAAO,CAAC;UAAA,CAC3D,CAAC;QAAA,CACH,CAAC;MAAA,CACH,CAAC;IAAA,CACS,CAAC;EAEvB,CAAC;EAED,IAAMwC,YAAY,GAAG,SAAfA,YAAYA,CAAA;IAAA,OAChBtD,KAAA,CAACf,IAAI;MAACmC,KAAK,EAAEW,MAAM,CAACwB,eAAgB;MAAArB,QAAA,GAClClC,KAAA,CAACf,IAAI;QAACmC,KAAK,EAAEW,MAAM,CAACyB,cAAe;QAAAtB,QAAA,GACjCpC,IAAA,CAACZ,IAAI;UAACkC,KAAK,EAAEW,MAAM,CAAC0B,YAAa;UAAAvB,QAAA,EAAC;QAAe,CAAM,CAAC,EACxDpC,IAAA,CAACZ,IAAI;UAACkC,KAAK,EAAEW,MAAM,CAAC2B,eAAgB;UAAAxB,QAAA,EAAC;QAErC,CAAM,CAAC;MAAA,CACH,CAAC,EACPlC,KAAA,CAACX,gBAAgB;QAACgC,OAAO,EAAEG,aAAc;QAACJ,KAAK,EAAEW,MAAM,CAAC4B,aAAc;QAAAzB,QAAA,GACpEpC,IAAA,CAACZ,IAAI;UAACkC,KAAK,EAAEW,MAAM,CAAC6B,WAAY;UAAA1B,QAAA,EAAC;QAAW,CAAM,CAAC,EACnDpC,IAAA,CAACL,aAAa;UACZ8C,IAAI,EAAC,eAAe;UACpBC,IAAI,EAAE,EAAG;UACTC,KAAK,EAAE/C,MAAM,CAACmE;QAAQ,CACvB,CAAC;MAAA,CACc,CAAC;IAAA,CACf,CAAC;EAAA,CACR;EAED,OACE7D,KAAA,CAACf,IAAI;IAACmC,KAAK,EAAEW,MAAM,CAAC+B,SAAU;IAAA5B,QAAA,GAC3BoB,YAAY,CAAC,CAAC,EAEfxD,IAAA,CAACV,QAAQ;MACP2E,IAAI,EAAExD,SAAU;MAChByD,UAAU,EAAEpC,eAAgB;MAC5BqC,YAAY,EAAE,SAAdA,YAAYA,CAAGnC,IAAI;QAAA,OAAKA,IAAI,CAACtB,EAAE,CAACmB,QAAQ,CAAC,CAAC;MAAA,CAAC;MAC3CuC,UAAU;MACVC,8BAA8B,EAAE,KAAM;MACtCC,qBAAqB,EAAErC,MAAM,CAACsC,aAAc;MAC5CC,cAAc,EAAEjE,gBAAgB,GAAGT,OAAO,CAAC2E,MAAM,CAACC,EAAG;MACrDC,gBAAgB,EAAC;IAAM,CACxB,CAAC;EAAA,CACE,CAAC;AAEX,CAAC;AAED,IAAM1C,MAAM,GAAG5C,UAAU,CAACuF,MAAM,CAAC;EAC/BZ,SAAS,EAAE;IACTa,eAAe,EAAE/E,OAAO,CAACgF,OAAO,CAACC;EACnC,CAAC;EACDtB,eAAe,EAAE;IACfuB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,YAAY;IACxBC,iBAAiB,EAAErF,OAAO,CAACgF,OAAO,CAACJ,EAAE;IACrCU,YAAY,EAAEtF,OAAO,CAAC4E;EACxB,CAAC;EACDhB,cAAc,EAAE;IACd2B,IAAI,EAAE;EACR,CAAC;EACD1B,YAAY,EAAA2B,aAAA,CAAAA,aAAA,KACPzF,UAAU,CAAC0F,EAAE;IAChB5C,KAAK,EAAE/C,MAAM,CAAC4F,SAAS;IACvBJ,YAAY,EAAEtF,OAAO,CAAC2F;EAAE,EACzB;EACD7B,eAAe,EAAA0B,aAAA,CAAAA,aAAA,KACVzF,UAAU,CAAC6F,KAAK;IACnB/C,KAAK,EAAE/C,MAAM,CAAC0D;EAAgB,EAC/B;EACDO,aAAa,EAAE;IACbmB,aAAa,EAAE,KAAK;IACpBE,UAAU,EAAE,QAAQ;IACpBL,eAAe,EAAE/E,OAAO,CAAC2F;EAC3B,CAAC;EACD3B,WAAW,EAAAwB,aAAA,CAAAA,aAAA,KACNzF,UAAU,CAAC6F,KAAK;IACnB/C,KAAK,EAAE/C,MAAM,CAACmE,OAAO;IACrB4B,WAAW,EAAE7F,OAAO,CAAC2F,EAAE;IACvBG,UAAU,EAAE;EAAK,EAClB;EACDrB,aAAa,EAAE;IACbY,iBAAiB,EAAErF,OAAO,CAACgF,OAAO,CAACJ;EACrC,CAAC;EACDxC,SAAS,EAAE;IACT5B,KAAK,EAAEC,gBAAgB;IACvBoF,WAAW,EAAE7F,OAAO,CAAC2E,MAAM,CAACC,EAAE;IAC9BmB,eAAe,EAAEjG,MAAM,CAACkG,OAAO;IAC/BC,YAAY,EAAEjG,OAAO,CAACiG,YAAY,CAACC,EAAE;IACrCC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAEtG,MAAM,CAACuG,UAAU;IAC9BC,YAAY,EAAE;MACZ9F,KAAK,EAAE,CAAC;MACR+F,MAAM,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDnE,kBAAkB,EAAE;IAClBoE,QAAQ,EAAE,UAAU;IACpBJ,MAAM,EAAE7F,iBAAiB,GAAG;EAC9B,CAAC;EACDK,SAAS,EAAE;IACTP,KAAK,EAAE,MAAM;IACb+F,MAAM,EAAE,MAAM;IACdK,UAAU,EAAE;EACd,CAAC;EACDlE,UAAU,EAAE;IACViE,QAAQ,EAAE,UAAU;IACpBE,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;IAAG,CAAC,EAAE;MAAEC,UAAU,EAAE,CAAC;IAAG,CAAC,CAAC;IACrDzG,KAAK,EAAE,EAAE;IACT+F,MAAM,EAAE,EAAE;IACVN,YAAY,EAAE,EAAE;IAChBF,eAAe,EAAE,oBAAoB;IACrCX,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE;EAClB,CAAC;EACDpC,aAAa,EAAE;IACb4D,QAAQ,EAAE,UAAU;IACpBO,MAAM,EAAElH,OAAO,CAAC2F,EAAE;IAClBwB,KAAK,EAAEnH,OAAO,CAAC2F,EAAE;IACjBI,eAAe,EAAE,oBAAoB;IACrCV,iBAAiB,EAAErF,OAAO,CAAC2F,EAAE;IAC7BZ,eAAe,EAAE,CAAC;IAClBkB,YAAY,EAAEjG,OAAO,CAACiG,YAAY,CAAChB;EACrC,CAAC;EACDjC,YAAY,EAAAwC,aAAA,CAAAA,aAAA,KACPzF,UAAU,CAACqH,OAAO;IACrBvE,KAAK,EAAE/C,MAAM,CAACgD,SAAS;IACvBuE,QAAQ,EAAE;EAAE,EACb;EACDpE,SAAS,EAAE;IACT+B,OAAO,EAAEhF,OAAO,CAACgF,OAAO,CAACJ;EAC3B,CAAC;EACD1B,UAAU,EAAAsC,aAAA,CAAAA,aAAA,KACLzF,UAAU,CAACuH,SAAS;IACvBzE,KAAK,EAAE/C,MAAM,CAAC4F,SAAS;IACvBJ,YAAY,EAAEtF,OAAO,CAACiF;EAAE,EACzB;EACD7B,SAAS,EAAE;IACT8B,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE;EACd,CAAC;EACD/B,aAAa,EAAE;IACb0C,eAAe,EAAEjG,MAAM,CAACyH,YAAY;IACpClC,iBAAiB,EAAErF,OAAO,CAAC2F,EAAE;IAC7BZ,eAAe,EAAE,CAAC;IAClBkB,YAAY,EAAEjG,OAAO,CAACiG,YAAY,CAAChB;EACrC,CAAC;EACD3B,YAAY,EAAAkC,aAAA,CAAAA,aAAA,KACPzF,UAAU,CAACqH,OAAO;IACrBvE,KAAK,EAAE/C,MAAM,CAACmE,OAAO;IACrBoD,QAAQ,EAAE,EAAE;IACZvB,UAAU,EAAE;EAAK,EAClB;EACDvC,QAAQ,EAAE;IACR2B,aAAa,EAAE,KAAK;IACpBE,UAAU,EAAE;EACd,CAAC;EACD3B,SAAS,EAAA+B,aAAA,CAAAA,aAAA,KACJzF,UAAU,CAACqH,OAAO;IACrBvE,KAAK,EAAE/C,MAAM,CAAC0D,gBAAgB;IAC9BgE,UAAU,EAAExH,OAAO,CAAC2F,EAAE,GAAG;EAAC;AAE9B,CAAC,CAAC;AAEF,eAAexE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}