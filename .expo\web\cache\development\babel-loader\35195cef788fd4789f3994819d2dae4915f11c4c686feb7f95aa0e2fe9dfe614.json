{"ast": null, "code": "export var Spacing = {\n  unit: 8,\n  xs: 4,\n  sm: 8,\n  md: 16,\n  lg: 24,\n  xl: 32,\n  xxl: 40,\n  xxxl: 48,\n  padding: {\n    xs: 4,\n    sm: 8,\n    md: 16,\n    lg: 24,\n    xl: 32\n  },\n  margin: {\n    xs: 4,\n    sm: 8,\n    md: 16,\n    lg: 24,\n    xl: 32\n  },\n  container: 16,\n  section: 24,\n  component: 16,\n  borderRadius: {\n    sm: 4,\n    md: 8,\n    lg: 12,\n    xl: 16,\n    xxl: 20,\n    round: 50\n  },\n  iconSize: {\n    xs: 16,\n    sm: 20,\n    md: 24,\n    lg: 32,\n    xl: 40\n  },\n  buttonHeight: {\n    sm: 32,\n    md: 40,\n    lg: 48,\n    xl: 56\n  },\n  card: {\n    padding: 16,\n    margin: 8,\n    borderRadius: 12\n  }\n};", "map": {"version": 3, "names": ["Spacing", "unit", "xs", "sm", "md", "lg", "xl", "xxl", "xxxl", "padding", "margin", "container", "section", "component", "borderRadius", "round", "iconSize", "buttonHeight", "card"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/constants/Spacing.js"], "sourcesContent": ["// 8px Grid System for consistent spacing\nexport const Spacing = {\n  // Base unit (8px)\n  unit: 8,\n  \n  // Spacing scale\n  xs: 4,   // 0.5 * unit\n  sm: 8,   // 1 * unit\n  md: 16,  // 2 * unit\n  lg: 24,  // 3 * unit\n  xl: 32,  // 4 * unit\n  xxl: 40, // 5 * unit\n  xxxl: 48, // 6 * unit\n  \n  // Component specific spacing\n  padding: {\n    xs: 4,\n    sm: 8,\n    md: 16,\n    lg: 24,\n    xl: 32,\n  },\n  \n  margin: {\n    xs: 4,\n    sm: 8,\n    md: 16,\n    lg: 24,\n    xl: 32,\n  },\n  \n  // Layout spacing\n  container: 16,\n  section: 24,\n  component: 16,\n  \n  // Border radius\n  borderRadius: {\n    sm: 4,\n    md: 8,\n    lg: 12,\n    xl: 16,\n    xxl: 20,\n    round: 50,\n  },\n  \n  // Icon sizes\n  iconSize: {\n    xs: 16,\n    sm: 20,\n    md: 24,\n    lg: 32,\n    xl: 40,\n  },\n  \n  // Button heights\n  buttonHeight: {\n    sm: 32,\n    md: 40,\n    lg: 48,\n    xl: 56,\n  },\n  \n  // Card spacing\n  card: {\n    padding: 16,\n    margin: 8,\n    borderRadius: 12,\n  },\n};\n"], "mappings": "AACA,OAAO,IAAMA,OAAO,GAAG;EAErBC,IAAI,EAAE,CAAC;EAGPC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE;EACNC,GAAG,EAAE,EAAE;EACPC,IAAI,EAAE,EAAE;EAGRC,OAAO,EAAE;IACPP,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE;EACN,CAAC;EAEDI,MAAM,EAAE;IACNR,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE;EACN,CAAC;EAGDK,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,EAAE;EAGbC,YAAY,EAAE;IACZX,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,GAAG,EAAE,EAAE;IACPQ,KAAK,EAAE;EACT,CAAC;EAGDC,QAAQ,EAAE;IACRd,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE;EACN,CAAC;EAGDW,YAAY,EAAE;IACZd,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE;EACN,CAAC;EAGDY,IAAI,EAAE;IACJT,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,CAAC;IACTI,YAAY,EAAE;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}