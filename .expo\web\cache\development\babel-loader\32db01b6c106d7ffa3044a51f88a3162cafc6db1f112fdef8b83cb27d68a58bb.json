{"ast": null, "code": "import React, { createContext, useContext } from 'react';\nimport { isLocaleRTL } from \"./isLocaleRTL\";\nvar defaultLocale = {\n  direction: 'ltr',\n  locale: 'en-US'\n};\nvar LocaleContext = createContext(defaultLocale);\nexport function getLocaleDirection(locale) {\n  return isLocaleRTL(locale) ? 'rtl' : 'ltr';\n}\nexport function LocaleProvider(props) {\n  var direction = props.direction,\n    locale = props.locale,\n    children = props.children;\n  var needsContext = direction || locale;\n  return needsContext ? React.createElement(LocaleContext.Provider, {\n    children: children,\n    value: {\n      direction: locale ? getLocaleDirection(locale) : direction,\n      locale: locale\n    }\n  }) : children;\n}\nexport function useLocaleContext() {\n  return useContext(LocaleContext);\n}", "map": {"version": 3, "names": ["React", "createContext", "useContext", "isLocaleRTL", "defaultLocale", "direction", "locale", "LocaleContext", "getLocaleDirection", "LocaleProvider", "props", "children", "needsContext", "createElement", "Provider", "value", "useLocaleContext"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/modules/useLocale/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport React, { createContext, useContext } from 'react';\nimport { isLocaleRTL } from './isLocaleRTL';\nvar defaultLocale = {\n  direction: 'ltr',\n  locale: 'en-US'\n};\nvar LocaleContext = /*#__PURE__*/createContext(defaultLocale);\nexport function getLocaleDirection(locale) {\n  return isLocaleRTL(locale) ? 'rtl' : 'ltr';\n}\nexport function LocaleProvider(props) {\n  var direction = props.direction,\n    locale = props.locale,\n    children = props.children;\n  var needsContext = direction || locale;\n  return needsContext ? /*#__PURE__*/React.createElement(LocaleContext.Provider, {\n    children: children,\n    value: {\n      direction: locale ? getLocaleDirection(locale) : direction,\n      locale\n    }\n  }) : children;\n}\nexport function useLocaleContext() {\n  return useContext(LocaleContext);\n}"], "mappings": "AASA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACxD,SAASC,WAAW;AACpB,IAAIC,aAAa,GAAG;EAClBC,SAAS,EAAE,KAAK;EAChBC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,aAAa,GAAgBN,aAAa,CAACG,aAAa,CAAC;AAC7D,OAAO,SAASI,kBAAkBA,CAACF,MAAM,EAAE;EACzC,OAAOH,WAAW,CAACG,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK;AAC5C;AACA,OAAO,SAASG,cAAcA,CAACC,KAAK,EAAE;EACpC,IAAIL,SAAS,GAAGK,KAAK,CAACL,SAAS;IAC7BC,MAAM,GAAGI,KAAK,CAACJ,MAAM;IACrBK,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC3B,IAAIC,YAAY,GAAGP,SAAS,IAAIC,MAAM;EACtC,OAAOM,YAAY,GAAgBZ,KAAK,CAACa,aAAa,CAACN,aAAa,CAACO,QAAQ,EAAE;IAC7EH,QAAQ,EAAEA,QAAQ;IAClBI,KAAK,EAAE;MACLV,SAAS,EAAEC,MAAM,GAAGE,kBAAkB,CAACF,MAAM,CAAC,GAAGD,SAAS;MAC1DC,MAAM,EAANA;IACF;EACF,CAAC,CAAC,GAAGK,QAAQ;AACf;AACA,OAAO,SAASK,gBAAgBA,CAAA,EAAG;EACjC,OAAOd,UAAU,CAACK,aAAa,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}