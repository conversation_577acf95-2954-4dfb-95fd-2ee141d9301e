{"version": 3, "sources": ["longPressGesture.ts"], "names": ["BaseGesture", "LongPressGesture", "constructor", "handler<PERSON>ame", "shouldCancelWhenOutside", "minDuration", "duration", "config", "minDurationMs", "maxDistance", "distance", "maxDist"], "mappings": ";;AAAA,SAASA,WAAT,QAA+C,WAA/C;AAMA,OAAO,MAAMC,gBAAN,SAA+BD,WAA/B,CAAgF;AAGrFE,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAF8C,EAE9C;;AAGZ,SAAKC,WAAL,GAAmB,yBAAnB;AACA,SAAKC,uBAAL,CAA6B,IAA7B;AACD;;AAEDC,EAAAA,WAAW,CAACC,QAAD,EAAmB;AAC5B,SAAKC,MAAL,CAAYC,aAAZ,GAA4BF,QAA5B;AACA,WAAO,IAAP;AACD;;AAEDG,EAAAA,WAAW,CAACC,QAAD,EAAmB;AAC5B,SAAKH,MAAL,CAAYI,OAAZ,GAAsBD,QAAtB;AACA,WAAO,IAAP;AACD;;AAlBoF", "sourcesContent": ["import { BaseGesture, BaseGestureConfig } from './gesture';\nimport {\n  LongPressGestureConfig,\n  LongPressGestureHandlerEventPayload,\n} from '../LongPressGestureHandler';\n\nexport class LongPressGesture extends BaseGesture<LongPressGestureHandlerEventPayload> {\n  public config: BaseGestureConfig & LongPressGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'LongPressGestureHandler';\n    this.shouldCancelWhenOutside(true);\n  }\n\n  minDuration(duration: number) {\n    this.config.minDurationMs = duration;\n    return this;\n  }\n\n  maxDistance(distance: number) {\n    this.config.maxDist = distance;\n    return this;\n  }\n}\n\nexport type LongPressGestureType = InstanceType<typeof LongPressGesture>;\n"]}