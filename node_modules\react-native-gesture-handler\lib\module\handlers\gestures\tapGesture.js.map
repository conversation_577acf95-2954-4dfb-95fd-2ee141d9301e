{"version": 3, "sources": ["tapGesture.ts"], "names": ["BaseGesture", "TapGesture", "constructor", "handler<PERSON>ame", "shouldCancelWhenOutside", "minPointers", "config", "numberOfTaps", "count", "maxDistance", "maxDist", "maxDuration", "duration", "maxDurationMs", "max<PERSON><PERSON><PERSON>", "delay", "max<PERSON>elay<PERSON>", "maxDeltaX", "delta", "maxDeltaY"], "mappings": ";;AAAA,SAA4BA,WAA5B,QAA+C,WAA/C;AAMA,OAAO,MAAMC,UAAN,SAAyBD,WAAzB,CAAoE;AAGzEE,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAFwC,EAExC;;AAGZ,SAAKC,WAAL,GAAmB,mBAAnB;AACA,SAAKC,uBAAL,CAA6B,IAA7B;AACD;;AAEDC,EAAAA,WAAW,CAACA,WAAD,EAAsB;AAC/B,SAAKC,MAAL,CAAYD,WAAZ,GAA0BA,WAA1B;AACA,WAAO,IAAP;AACD;;AAEDE,EAAAA,YAAY,CAACC,KAAD,EAAgB;AAC1B,SAAKF,MAAL,CAAYC,YAAZ,GAA2BC,KAA3B;AACA,WAAO,IAAP;AACD;;AAEDC,EAAAA,WAAW,CAACC,OAAD,EAAkB;AAC3B,SAAKJ,MAAL,CAAYI,OAAZ,GAAsBA,OAAtB;AACA,WAAO,IAAP;AACD;;AAEDC,EAAAA,WAAW,CAACC,QAAD,EAAmB;AAC5B,SAAKN,MAAL,CAAYO,aAAZ,GAA4BD,QAA5B;AACA,WAAO,IAAP;AACD;;AAEDE,EAAAA,QAAQ,CAACC,KAAD,EAAgB;AACtB,SAAKT,MAAL,CAAYU,UAAZ,GAAyBD,KAAzB;AACA,WAAO,IAAP;AACD;;AAEDE,EAAAA,SAAS,CAACC,KAAD,EAAgB;AACvB,SAAKZ,MAAL,CAAYW,SAAZ,GAAwBC,KAAxB;AACA,WAAO,IAAP;AACD;;AAEDC,EAAAA,SAAS,CAACD,KAAD,EAAgB;AACvB,SAAKZ,MAAL,CAAYa,SAAZ,GAAwBD,KAAxB;AACA,WAAO,IAAP;AACD;;AA3CwE", "sourcesContent": ["import { BaseGestureConfig, BaseGesture } from './gesture';\nimport {\n  TapGestureConfig,\n  TapGestureHandlerEventPayload,\n} from '../TapGestureHandler';\n\nexport class TapGesture extends BaseGesture<TapGestureHandlerEventPayload> {\n  public config: BaseGestureConfig & TapGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'TapGestureHandler';\n    this.shouldCancelWhenOutside(true);\n  }\n\n  minPointers(minPointers: number) {\n    this.config.minPointers = minPointers;\n    return this;\n  }\n\n  numberOfTaps(count: number) {\n    this.config.numberOfTaps = count;\n    return this;\n  }\n\n  maxDistance(maxDist: number) {\n    this.config.maxDist = maxDist;\n    return this;\n  }\n\n  maxDuration(duration: number) {\n    this.config.maxDurationMs = duration;\n    return this;\n  }\n\n  maxDelay(delay: number) {\n    this.config.maxDelayMs = delay;\n    return this;\n  }\n\n  maxDeltaX(delta: number) {\n    this.config.maxDeltaX = delta;\n    return this;\n  }\n\n  maxDeltaY(delta: number) {\n    this.config.maxDeltaY = delta;\n    return this;\n  }\n}\n\nexport type TapGestureType = InstanceType<typeof TapGesture>;\n"]}