{"version": 3, "sources": ["handlersRegistry.ts"], "names": ["handlerIDToTag", "gestures", "Map", "oldHandlers", "testIDs", "handlerTag", "getNextHandlerTag", "registerHandler", "handler", "testID", "set", "registerOldGestureHandler", "unregister<PERSON><PERSON><PERSON>", "delete", "<PERSON><PERSON><PERSON><PERSON>", "get", "findOldGestureHandler", "findHandlerByTestID", "undefined"], "mappings": ";;;;;;;;;;;;;;AAAA;;AAIO,MAAMA,cAAsC,GAAG,EAA/C;;AACP,MAAMC,QAAQ,GAAG,IAAIC,GAAJ,EAAjB;AACA,MAAMC,WAAW,GAAG,IAAID,GAAJ,EAApB;AACA,MAAME,OAAO,GAAG,IAAIF,GAAJ,EAAhB;AAEA,IAAIG,UAAU,GAAG,CAAjB;;AAEO,SAASC,iBAAT,GAAqC;AAC1C,SAAOD,UAAU,EAAjB;AACD;;AAEM,SAASE,eAAT,CACLF,UADK,EAELG,OAFK,EAGLC,MAHK,EAIL;AACAR,EAAAA,QAAQ,CAACS,GAAT,CAAaL,UAAb,EAAyBG,OAAzB;;AACA,MAAI,2BAAeC,MAAnB,EAA2B;AACzBL,IAAAA,OAAO,CAACM,GAAR,CAAYD,MAAZ,EAAoBJ,UAApB;AACD;AACF;;AAEM,SAASM,yBAAT,CACLN,UADK,EAELG,OAFK,EAGL;AACAL,EAAAA,WAAW,CAACO,GAAZ,CAAgBL,UAAhB,EAA4BG,OAA5B;AACD;;AAEM,SAASI,iBAAT,CAA2BP,UAA3B,EAA+CI,MAA/C,EAAgE;AACrER,EAAAA,QAAQ,CAACY,MAAT,CAAgBR,UAAhB;;AACA,MAAI,2BAAeI,MAAnB,EAA2B;AACzBL,IAAAA,OAAO,CAACS,MAAR,CAAeJ,MAAf;AACD;AACF;;AAEM,SAASK,WAAT,CAAqBT,UAArB,EAAyC;AAC9C,SAAOJ,QAAQ,CAACc,GAAT,CAAaV,UAAb,CAAP;AACD;;AAEM,SAASW,qBAAT,CAA+BX,UAA/B,EAAmD;AACxD,SAAOF,WAAW,CAACY,GAAZ,CAAgBV,UAAhB,CAAP;AACD;;AAEM,SAASY,mBAAT,CAA6BR,MAA7B,EAA6C;AAClD,QAAMJ,UAAU,GAAGD,OAAO,CAACW,GAAR,CAAYN,MAAZ,CAAnB;;AACA,MAAIJ,UAAU,KAAKa,SAAnB,EAA8B;AAAA;;AAC5B,2BAAOJ,WAAW,CAACT,UAAD,CAAlB,uDAAkC,IAAlC;AACD;;AACD,SAAO,IAAP;AACD", "sourcesContent": ["import { isJestEnv } from '../utils';\nimport { GestureType } from './gestures/gesture';\nimport { GestureEvent, HandlerStateChangeEvent } from './gestureHandlerCommon';\n\nexport const handlerIDToTag: Record<string, number> = {};\nconst gestures = new Map<number, GestureType>();\nconst oldHandlers = new Map<number, GestureHandlerCallbacks>();\nconst testIDs = new Map<string, number>();\n\nlet handlerTag = 1;\n\nexport function getNextHandlerTag(): number {\n  return handlerTag++;\n}\n\nexport function registerHandler(\n  handlerTag: number,\n  handler: GestureType,\n  testID?: string\n) {\n  gestures.set(handlerTag, handler);\n  if (isJestEnv() && testID) {\n    testIDs.set(testID, handlerTag);\n  }\n}\n\nexport function registerOldGestureHandler(\n  handlerTag: number,\n  handler: GestureHandlerCallbacks\n) {\n  oldHandlers.set(handlerTag, handler);\n}\n\nexport function unregisterHandler(handlerTag: number, testID?: string) {\n  gestures.delete(handlerTag);\n  if (isJestEnv() && testID) {\n    testIDs.delete(testID);\n  }\n}\n\nexport function findHandler(handlerTag: number) {\n  return gestures.get(handlerTag);\n}\n\nexport function findOldGestureHandler(handlerTag: number) {\n  return oldHandlers.get(handlerTag);\n}\n\nexport function findHandlerByTestID(testID: string) {\n  const handlerTag = testIDs.get(testID);\n  if (handlerTag !== undefined) {\n    return findHandler(handlerTag) ?? null;\n  }\n  return null;\n}\n\nexport interface GestureHandlerCallbacks {\n  onGestureEvent: (event: GestureEvent<any>) => void;\n  onGestureStateChange: (event: HandlerStateChangeEvent<any>) => void;\n}\n"]}