{"ast": null, "code": "export var BLUR = 'blur';\nexport var CONTEXT_MENU = 'contextmenu';\nexport var FOCUS_OUT = 'focusout';\nexport var MOUSE_DOWN = 'mousedown';\nexport var MOUSE_MOVE = 'mousemove';\nexport var MOUSE_UP = 'mouseup';\nexport var MOUSE_CANCEL = 'dragstart';\nexport var TOUCH_START = 'touchstart';\nexport var TOUCH_MOVE = 'touchmove';\nexport var TOUCH_END = 'touchend';\nexport var TOUCH_CANCEL = 'touchcancel';\nexport var SCROLL = 'scroll';\nexport var SELECT = 'select';\nexport var SELECTION_CHANGE = 'selectionchange';\nexport function isStartish(eventType) {\n  return eventType === TOUCH_START || eventType === MOUSE_DOWN;\n}\nexport function isMoveish(eventType) {\n  return eventType === TOUCH_MOVE || eventType === MOUSE_MOVE;\n}\nexport function isEndish(eventType) {\n  return eventType === TOUCH_END || eventType === MOUSE_UP || isCancelish(eventType);\n}\nexport function isCancelish(eventType) {\n  return eventType === TOUCH_CANCEL || eventType === MOUSE_CANCEL;\n}\nexport function isScroll(eventType) {\n  return eventType === SCROLL;\n}\nexport function isSelectionChange(eventType) {\n  return eventType === SELECT || eventType === SELECTION_CHANGE;\n}", "map": {"version": 3, "names": ["BLUR", "CONTEXT_MENU", "FOCUS_OUT", "MOUSE_DOWN", "MOUSE_MOVE", "MOUSE_UP", "MOUSE_CANCEL", "TOUCH_START", "TOUCH_MOVE", "TOUCH_END", "TOUCH_CANCEL", "SCROLL", "SELECT", "SELECTION_CHANGE", "isStartish", "eventType", "isMoveish", "<PERSON><PERSON><PERSON><PERSON>", "isCancelish", "isScroll", "isSelectionChange"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/modules/useResponderEvents/ResponderEventTypes.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nexport var BLUR = 'blur';\nexport var CONTEXT_MENU = 'contextmenu';\nexport var FOCUS_OUT = 'focusout';\nexport var MOUSE_DOWN = 'mousedown';\nexport var MOUSE_MOVE = 'mousemove';\nexport var MOUSE_UP = 'mouseup';\nexport var MOUSE_CANCEL = 'dragstart';\nexport var TOUCH_START = 'touchstart';\nexport var TOUCH_MOVE = 'touchmove';\nexport var TOUCH_END = 'touchend';\nexport var TOUCH_CANCEL = 'touchcancel';\nexport var SCROLL = 'scroll';\nexport var SELECT = 'select';\nexport var SELECTION_CHANGE = 'selectionchange';\nexport function isStartish(eventType) {\n  return eventType === TOUCH_START || eventType === MOUSE_DOWN;\n}\nexport function isMoveish(eventType) {\n  return eventType === TOUCH_MOVE || eventType === MOUSE_MOVE;\n}\nexport function isEndish(eventType) {\n  return eventType === TOUCH_END || eventType === MOUSE_UP || isCancelish(eventType);\n}\nexport function isCancelish(eventType) {\n  return eventType === TOUCH_CANCEL || eventType === MOUSE_CANCEL;\n}\nexport function isScroll(eventType) {\n  return eventType === SCROLL;\n}\nexport function isSelectionChange(eventType) {\n  return eventType === SELECT || eventType === SELECTION_CHANGE;\n}"], "mappings": "AASA,OAAO,IAAIA,IAAI,GAAG,MAAM;AACxB,OAAO,IAAIC,YAAY,GAAG,aAAa;AACvC,OAAO,IAAIC,SAAS,GAAG,UAAU;AACjC,OAAO,IAAIC,UAAU,GAAG,WAAW;AACnC,OAAO,IAAIC,UAAU,GAAG,WAAW;AACnC,OAAO,IAAIC,QAAQ,GAAG,SAAS;AAC/B,OAAO,IAAIC,YAAY,GAAG,WAAW;AACrC,OAAO,IAAIC,WAAW,GAAG,YAAY;AACrC,OAAO,IAAIC,UAAU,GAAG,WAAW;AACnC,OAAO,IAAIC,SAAS,GAAG,UAAU;AACjC,OAAO,IAAIC,YAAY,GAAG,aAAa;AACvC,OAAO,IAAIC,MAAM,GAAG,QAAQ;AAC5B,OAAO,IAAIC,MAAM,GAAG,QAAQ;AAC5B,OAAO,IAAIC,gBAAgB,GAAG,iBAAiB;AAC/C,OAAO,SAASC,UAAUA,CAACC,SAAS,EAAE;EACpC,OAAOA,SAAS,KAAKR,WAAW,IAAIQ,SAAS,KAAKZ,UAAU;AAC9D;AACA,OAAO,SAASa,SAASA,CAACD,SAAS,EAAE;EACnC,OAAOA,SAAS,KAAKP,UAAU,IAAIO,SAAS,KAAKX,UAAU;AAC7D;AACA,OAAO,SAASa,QAAQA,CAACF,SAAS,EAAE;EAClC,OAAOA,SAAS,KAAKN,SAAS,IAAIM,SAAS,KAAKV,QAAQ,IAAIa,WAAW,CAACH,SAAS,CAAC;AACpF;AACA,OAAO,SAASG,WAAWA,CAACH,SAAS,EAAE;EACrC,OAAOA,SAAS,KAAKL,YAAY,IAAIK,SAAS,KAAKT,YAAY;AACjE;AACA,OAAO,SAASa,QAAQA,CAACJ,SAAS,EAAE;EAClC,OAAOA,SAAS,KAAKJ,MAAM;AAC7B;AACA,OAAO,SAASS,iBAAiBA,CAACL,SAAS,EAAE;EAC3C,OAAOA,SAAS,KAAKH,MAAM,IAAIG,SAAS,KAAKF,gBAAgB;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}