{"version": 3, "sources": ["LongPressGestureHandler.ts"], "names": ["Platform", "State", "Gesture<PERSON>andler", "DEFAULT_MIN_DURATION_MS", "DEFAULT_MAX_DIST_DP", "SCALING_FACTOR", "LongPressGestureHandler", "defaultMaxDistSq", "init", "ref", "propsRef", "OS", "delegate", "get<PERSON>iew", "oncontextmenu", "transformNativeEvent", "duration", "Date", "now", "startTime", "updateGestureConfig", "enabled", "props", "config", "minDurationMs", "undefined", "maxDist", "maxDistSq", "resetConfig", "onStateChange", "_newState", "_oldState", "clearTimeout", "activationTimeout", "onPointerDown", "event", "tracker", "addToTracker", "tryBegin", "tryActivate", "checkDistanceFail", "onPointerMove", "track", "onPointerUp", "removeFromTracker", "pointerId", "currentState", "ACTIVE", "end", "fail", "UNDETERMINED", "previousTime", "begin", "startX", "x", "startY", "y", "setTimeout", "activate", "dx", "dy", "distSq", "cancel"], "mappings": ";;AAAA,SAASA,QAAT,QAAyB,cAAzB;AACA,SAASC,KAAT,QAAsB,aAAtB;AAGA,OAAOC,cAAP,MAA2B,kBAA3B;AAEA,MAAMC,uBAAuB,GAAG,GAAhC;AACA,MAAMC,mBAAmB,GAAG,EAA5B;AACA,MAAMC,cAAc,GAAG,EAAvB;AAEA,eAAe,MAAMC,uBAAN,SAAsCJ,cAAtC,CAAqD;AAAA;AAAA;;AAAA,2CAC1CC,uBAD0C;;AAAA,8CAEvCC,mBAAmB,GAAGC,cAFiB;;AAAA,uCAI9C,KAAKE,gBAJyC;;AAAA,oCAKjD,CALiD;;AAAA,oCAMjD,CANiD;;AAAA,uCAQ9C,CAR8C;;AAAA,0CAS3C,CAT2C;;AAAA;AAAA;;AAa3DC,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAkD;AAC3D,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;;AAEA,QAAIV,QAAQ,CAACW,EAAT,KAAgB,KAApB,EAA2B;AACxB,WAAKC,QAAL,CAAcC,OAAd,EAAD,CAAyCC,aAAzC,GAAyD,MAAM,KAA/D;AACD;AACF;;AAESC,EAAAA,oBAAoB,GAAG;AAC/B,WAAO,EACL,GAAG,MAAMA,oBAAN,EADE;AAELC,MAAAA,QAAQ,EAAEC,IAAI,CAACC,GAAL,KAAa,KAAKC;AAFvB,KAAP;AAID;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;;AAEA,QAAI,KAAKC,MAAL,CAAYC,aAAZ,KAA8BC,SAAlC,EAA6C;AAC3C,WAAKD,aAAL,GAAqB,KAAKD,MAAL,CAAYC,aAAjC;AACD;;AAED,QAAI,KAAKD,MAAL,CAAYG,OAAZ,KAAwBD,SAA5B,EAAuC;AACrC,WAAKE,SAAL,GAAiB,KAAKJ,MAAL,CAAYG,OAAZ,GAAsB,KAAKH,MAAL,CAAYG,OAAnD;AACD;AACF;;AAESE,EAAAA,WAAW,GAAS;AAC5B,UAAMA,WAAN;AACA,SAAKJ,aAAL,GAAqBrB,uBAArB;AACA,SAAKwB,SAAL,GAAiB,KAAKpB,gBAAtB;AACD;;AAESsB,EAAAA,aAAa,CAACC,SAAD,EAAmBC,SAAnB,EAA2C;AAChEC,IAAAA,YAAY,CAAC,KAAKC,iBAAN,CAAZ;AACD;;AAESC,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AACA,SAAKG,QAAL,CAAcH,KAAd;AACA,SAAKI,WAAL;AACA,SAAKC,iBAAL,CAAuBL,KAAvB;AACD;;AAESM,EAAAA,aAAa,CAACN,KAAD,EAA4B;AACjD,UAAMM,aAAN,CAAoBN,KAApB;AACA,SAAKC,OAAL,CAAaM,KAAb,CAAmBP,KAAnB;AACA,SAAKK,iBAAL,CAAuBL,KAAvB;AACD;;AAESQ,EAAAA,WAAW,CAACR,KAAD,EAA4B;AAC/C,UAAMQ,WAAN,CAAkBR,KAAlB;AACA,SAAKC,OAAL,CAAaQ,iBAAb,CAA+BT,KAAK,CAACU,SAArC;;AAEA,QAAI,KAAKC,YAAL,KAAsB7C,KAAK,CAAC8C,MAAhC,EAAwC;AACtC,WAAKC,GAAL;AACD,KAFD,MAEO;AACL,WAAKC,IAAL;AACD;AACF;;AAEOX,EAAAA,QAAQ,CAACH,KAAD,EAA4B;AAC1C,QAAI,KAAKW,YAAL,KAAsB7C,KAAK,CAACiD,YAAhC,EAA8C;AAC5C;AACD;;AAED,SAAKC,YAAL,GAAoBlC,IAAI,CAACC,GAAL,EAApB;AACA,SAAKC,SAAL,GAAiB,KAAKgC,YAAtB;AAEA,SAAKC,KAAL;AAEA,SAAKC,MAAL,GAAclB,KAAK,CAACmB,CAApB;AACA,SAAKC,MAAL,GAAcpB,KAAK,CAACqB,CAApB;AACD;;AAEOjB,EAAAA,WAAW,GAAS;AAC1B,QAAI,KAAKf,aAAL,GAAqB,CAAzB,EAA4B;AAC1B,WAAKS,iBAAL,GAAyBwB,UAAU,CAAC,MAAM;AACxC,aAAKC,QAAL;AACD,OAFkC,EAEhC,KAAKlC,aAF2B,CAAnC;AAGD,KAJD,MAIO,IAAI,KAAKA,aAAL,KAAuB,CAA3B,EAA8B;AACnC,WAAKkC,QAAL;AACD;AACF;;AAEOlB,EAAAA,iBAAiB,CAACL,KAAD,EAA4B;AACnD,UAAMwB,EAAE,GAAGxB,KAAK,CAACmB,CAAN,GAAU,KAAKD,MAA1B;AACA,UAAMO,EAAE,GAAGzB,KAAK,CAACqB,CAAN,GAAU,KAAKD,MAA1B;AACA,UAAMM,MAAM,GAAGF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAA9B;;AAEA,QAAIC,MAAM,IAAI,KAAKlC,SAAnB,EAA8B;AAC5B;AACD;;AAED,QAAI,KAAKmB,YAAL,KAAsB7C,KAAK,CAAC8C,MAAhC,EAAwC;AACtC,WAAKe,MAAL;AACD,KAFD,MAEO;AACL,WAAKb,IAAL;AACD;AACF;;AAjHiE", "sourcesContent": ["import { Platform } from 'react-native';\nimport { State } from '../../State';\nimport { AdaptedEvent, Config } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\n\nconst DEFAULT_MIN_DURATION_MS = 500;\nconst DEFAULT_MAX_DIST_DP = 10;\nconst SCALING_FACTOR = 10;\n\nexport default class LongPressGestureHandler extends GestureHandler {\n  private minDurationMs = DEFAULT_MIN_DURATION_MS;\n  private defaultMaxDistSq = DEFAULT_MAX_DIST_DP * SCALING_FACTOR;\n\n  private maxDistSq = this.defaultMaxDistSq;\n  private startX = 0;\n  private startY = 0;\n\n  private startTime = 0;\n  private previousTime = 0;\n\n  private activationTimeout: number | undefined;\n\n  public init(ref: number, propsRef: React.RefObject<unknown>) {\n    super.init(ref, propsRef);\n\n    if (Platform.OS === 'web') {\n      (this.delegate.getView() as HTMLElement).oncontextmenu = () => false;\n    }\n  }\n\n  protected transformNativeEvent() {\n    return {\n      ...super.transformNativeEvent(),\n      duration: Date.now() - this.startTime,\n    };\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n\n    if (this.config.minDurationMs !== undefined) {\n      this.minDurationMs = this.config.minDurationMs;\n    }\n\n    if (this.config.maxDist !== undefined) {\n      this.maxDistSq = this.config.maxDist * this.config.maxDist;\n    }\n  }\n\n  protected resetConfig(): void {\n    super.resetConfig();\n    this.minDurationMs = DEFAULT_MIN_DURATION_MS;\n    this.maxDistSq = this.defaultMaxDistSq;\n  }\n\n  protected onStateChange(_newState: State, _oldState: State): void {\n    clearTimeout(this.activationTimeout);\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n    this.tryBegin(event);\n    this.tryActivate();\n    this.checkDistanceFail(event);\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    super.onPointerMove(event);\n    this.tracker.track(event);\n    this.checkDistanceFail(event);\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.tracker.removeFromTracker(event.pointerId);\n\n    if (this.currentState === State.ACTIVE) {\n      this.end();\n    } else {\n      this.fail();\n    }\n  }\n\n  private tryBegin(event: AdaptedEvent): void {\n    if (this.currentState !== State.UNDETERMINED) {\n      return;\n    }\n\n    this.previousTime = Date.now();\n    this.startTime = this.previousTime;\n\n    this.begin();\n\n    this.startX = event.x;\n    this.startY = event.y;\n  }\n\n  private tryActivate(): void {\n    if (this.minDurationMs > 0) {\n      this.activationTimeout = setTimeout(() => {\n        this.activate();\n      }, this.minDurationMs);\n    } else if (this.minDurationMs === 0) {\n      this.activate();\n    }\n  }\n\n  private checkDistanceFail(event: AdaptedEvent): void {\n    const dx = event.x - this.startX;\n    const dy = event.y - this.startY;\n    const distSq = dx * dx + dy * dy;\n\n    if (distSq <= this.maxDistSq) {\n      return;\n    }\n\n    if (this.currentState === State.ACTIVE) {\n      this.cancel();\n    } else {\n      this.fail();\n    }\n  }\n}\n"]}