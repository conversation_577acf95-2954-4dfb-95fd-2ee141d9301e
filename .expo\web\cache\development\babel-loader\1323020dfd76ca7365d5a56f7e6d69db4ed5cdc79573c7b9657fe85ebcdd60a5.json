{"ast": null, "code": "export function getLocalAssetUri(hash, type) {\n  return null;\n}", "map": {"version": 3, "names": ["getLocalAssetUri", "hash", "type"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-asset\\src\\LocalAssets.web.ts"], "sourcesContent": ["export function getLocalAssetUri(hash: string, type: string | null): string | null {\n  // noop on web\n  return null;\n}\n"], "mappings": "AAAA,OAAM,SAAUA,gBAAgBA,CAACC,IAAY,EAAEC,IAAmB;EAEhE,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}