{"version": 3, "sources": ["GestureDetector.tsx"], "names": ["React", "useContext", "useEffect", "useRef", "useState", "BaseGesture", "CALLBACK_TYPE", "Reanimated", "registerHandler", "unregister<PERSON><PERSON><PERSON>", "RNGestureHandlerModule", "baseGestureHandlerWithMonitorProps", "filterConfig", "findNodeHandle", "scheduleFlushOperations", "GestureStateManager", "flingGestureHandlerProps", "forceTouchGestureHandlerProps", "longPressGestureHandlerProps", "panGestureHandlerProps", "panGestureHandlerCustomNativeProps", "tapGestureHandlerProps", "hoverGestureHandlerProps", "State", "TouchEventType", "ActionType", "isF<PERSON><PERSON>", "isJestEnv", "tagMessage", "getReactNativeVersion", "getShadowNodeFromRef", "Platform", "onGestureHandlerEvent", "<PERSON><PERSON><PERSON><PERSON>", "isNewWebImplementationEnabled", "nativeViewGestureHandlerProps", "GestureHandlerRootViewContext", "ghQueueMicrotask", "ALLOWED_PROPS", "convertToHandlerTag", "ref", "handlerTag", "current", "extractValidHandlerTags", "interactionGroup", "map", "filter", "tag", "dropHandlers", "preparedGesture", "handler", "config", "dropGestureHandler", "testId", "checkGestureCallbacksForWorklets", "gesture", "runOnJS", "areSomeNotWorklets", "handlers", "isWorklet", "includes", "areSomeWorklets", "console", "error", "attachHandlers", "gestureConfig", "viewTag", "webEventHandlersRef", "mountedRef", "firstExecution", "initialize", "prepare", "createGestureHandler", "handler<PERSON>ame", "requireToFail", "simultaneousWith", "blocksHandlers", "updateGestureHandler", "simultaneousHandlers", "waitFor", "actionType", "shouldUseReanimated", "REANIMATED_WORKLET", "JS_FUNCTION_NEW_API", "OS", "attachGestureHandler", "JS_FUNCTION_OLD_API", "animatedHandlers", "isAnimatedGesture", "g", "value", "updateHandlers", "i", "length", "previousHandlersValue", "newHandlersValue", "shouldUpdateSharedValue", "gestureId", "needsToReattach", "isStateChangeEvent", "event", "oldState", "isTouchEvent", "eventType", "<PERSON><PERSON><PERSON><PERSON>", "type", "BEGAN", "onBegin", "START", "onStart", "UPDATE", "onUpdate", "CHANGE", "onChange", "END", "onEnd", "FINALIZE", "onFinalize", "TOUCHES_DOWN", "onTouchesDown", "TOUCHES_MOVE", "onTouchesMove", "TOUCHES_UP", "onTouchesUp", "TOUCHES_CANCELLED", "onTouchesCancelled", "touchEventTypeToCallbackType", "UNDEFINED", "runWorklet", "args", "warn", "useAnimatedGesture", "needsRebuild", "sharedHandlersCallbacks", "useSharedValue", "lastUpdateEvent", "stateControllers", "callback", "currentCallback", "UNDETERMINED", "state", "ACTIVE", "undefined", "FAILED", "CANCELLED", "create", "changeEventCalculator", "useEvent", "animatedEventHandler", "validateDetectorChildren", "__DEV__", "REACT_NATIVE_VERSION", "wrapType", "minor", "major", "_reactInternals", "elementType", "_reactInternalFiber", "instance", "findHostInstance_DEPRECATED", "_internalFiberInstanceHandleDEV", "sibling", "Error", "return", "applyUserSelectProp", "userSelect", "toGestureArray", "GestureDetector", "props", "rootViewContext", "useReanimatedHook", "some", "firstRender", "viewRef", "previousViewTag", "forceReattach", "e", "nativeEvent", "onGestureHandlerStateChange", "renderState", "setRenderState", "forceRender", "onHandlersUpdate", "skipConfigUpdate", "needsToRebuildReanimatedEvent", "refFunction", "node", "global", "isFormsStackingContext", "children", "Wrap", "Component", "render", "child", "Children", "only", "cloneElement", "collapsable", "AnimatedWrap", "default", "createAnimatedComponent"], "mappings": ";;AAAA,OAAOA,KAAP,IAAgBC,UAAhB,EAA4BC,SAA5B,EAAuCC,MAAvC,EAA+CC,QAA/C,QAA+D,OAA/D;AACA,SAGEC,WAHF,EAKEC,aALF,QAMO,WANP;AAOA,SAASC,UAAT,QAAwC,qBAAxC;AACA,SAASC,eAAT,EAA0BC,iBAA1B,QAAmD,qBAAnD;AACA,OAAOC,sBAAP,MAAmC,8BAAnC;AACA,SACEC,kCADF,EAEEC,YAFF,EAGEC,cAHF,EAQEC,uBARF,QAUO,yBAVP;AAWA,SACEC,mBADF,QAGO,uBAHP;AAIA,SAASC,wBAAT,QAAyC,wBAAzC;AACA,SAASC,6BAAT,QAA8C,6BAA9C;AACA,SAASC,4BAAT,QAA6C,4BAA7C;AACA,SACEC,sBADF,EAEEC,kCAFF,QAGO,sBAHP;AAIA,SAASC,sBAAT,QAAuC,sBAAvC;AACA,SAASC,wBAAT,QAAyC,gBAAzC;AACA,SAASC,KAAT,QAAsB,aAAtB;AACA,SAASC,cAAT,QAA+B,sBAA/B;AAEA,SAASC,UAAT,QAA2B,kBAA3B;AACA,SAASC,QAAT,EAAmBC,SAAnB,EAA8BC,UAA9B,QAAgD,aAAhD;AACA,SAASC,qBAAT,QAAsC,6BAAtC;AACA,SAASC,oBAAT,QAAqC,4BAArC;AACA,SAASC,QAAT,QAAyB,cAAzB;AAEA,SAASC,qBAAT,QAAsC,iBAAtC;AACA,SAASC,UAAT,QAA2B,kBAA3B;AACA,SAASC,6BAAT,QAA8C,kCAA9C;AACA,SAASC,6BAAT,QAA8C,6BAA9C;AACA,OAAOC,6BAAP,MAA0C,qCAA1C;AACA,SAASC,gBAAT,QAAiC,wBAAjC;AAMA,MAAMC,aAAa,GAAG,CACpB,GAAG3B,kCADiB,EAEpB,GAAGU,sBAFiB,EAGpB,GAAGF,sBAHiB,EAIpB,GAAGC,kCAJiB,EAKpB,GAAGF,4BALiB,EAMpB,GAAGD,6BANiB,EAOpB,GAAGD,wBAPiB,EAQpB,GAAGM,wBARiB,EASpB,GAAGa,6BATiB,CAAtB;;AAsBA,SAASI,mBAAT,CAA6BC,GAA7B,EAAsD;AACpD,MAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;AAC3B,WAAOA,GAAP;AACD,GAFD,MAEO,IAAIA,GAAG,YAAYnC,WAAnB,EAAgC;AACrC,WAAOmC,GAAG,CAACC,UAAX;AACD,GAFM,MAEA;AAAA;;AACL;AACA;AACA,oDAAOD,GAAG,CAACE,OAAX,iDAAO,aAAaD,UAApB,yEAAkC,CAAC,CAAnC;AACD;AACF;;AAED,SAASE,uBAAT,CAAiCC,gBAAjC,EAA6E;AAAA;;AAC3E,kCACEA,gBADF,aACEA,gBADF,iDACEA,gBAAgB,CAAEC,GAAlB,CAAsBN,mBAAtB,CADF,2DACE,uBAA4CO,MAA5C,CAAoDC,GAAD,IAASA,GAAG,GAAG,CAAlE,CADF,yEAC0E,EAD1E;AAGD;;AAED,SAASC,YAAT,CAAsBC,eAAtB,EAA+D;AAC7D,OAAK,MAAMC,OAAX,IAAsBD,eAAe,CAACE,MAAtC,EAA8C;AAC5CzC,IAAAA,sBAAsB,CAAC0C,kBAAvB,CAA0CF,OAAO,CAACT,UAAlD;AAEAhC,IAAAA,iBAAiB,CAACyC,OAAO,CAACT,UAAT,EAAqBS,OAAO,CAACC,MAAR,CAAeE,MAApC,CAAjB;AACD;;AAEDvC,EAAAA,uBAAuB;AACxB;;AAED,SAASwC,gCAAT,CAA0CC,OAA1C,EAAgE;AAC9D;AACA;AACA,MAAIA,OAAO,CAACJ,MAAR,CAAeK,OAAnB,EAA4B;AAC1B;AACD;;AAED,QAAMC,kBAAkB,GAAGF,OAAO,CAACG,QAAR,CAAiBC,SAAjB,CAA2BC,QAA3B,CAAoC,KAApC,CAA3B;AACA,QAAMC,eAAe,GAAGN,OAAO,CAACG,QAAR,CAAiBC,SAAjB,CAA2BC,QAA3B,CAAoC,IAApC,CAAxB,CAR8D,CAU9D;AACA;;AACA,MAAIH,kBAAkB,IAAII,eAA1B,EAA2C;AACzCC,IAAAA,OAAO,CAACC,KAAR,CACEnC,UAAU,CACP,2QADO,CADZ;AAKD;AACF;;AAkBD,SAASoC,cAAT,CAAwB;AACtBf,EAAAA,eADsB;AAEtBgB,EAAAA,aAFsB;AAGtBV,EAAAA,OAHsB;AAItBW,EAAAA,OAJsB;AAKtBC,EAAAA,mBALsB;AAMtBC,EAAAA;AANsB,CAAxB,EAOyB;AACvB,MAAI,CAACnB,eAAe,CAACoB,cAArB,EAAqC;AACnCJ,IAAAA,aAAa,CAACK,UAAd;AACD,GAFD,MAEO;AACLrB,IAAAA,eAAe,CAACoB,cAAhB,GAAiC,KAAjC;AACD,GALsB,CAOvB;AACA;;;AACAhC,EAAAA,gBAAgB,CAAC,MAAM;AACrB,QAAI,CAAC+B,UAAU,CAAC1B,OAAhB,EAAyB;AACvB;AACD;;AACDuB,IAAAA,aAAa,CAACM,OAAd;AACD,GALe,CAAhB;;AAOA,OAAK,MAAMrB,OAAX,IAAsBK,OAAtB,EAA+B;AAC7BD,IAAAA,gCAAgC,CAACJ,OAAD,CAAhC;AACAxC,IAAAA,sBAAsB,CAAC8D,oBAAvB,CACEtB,OAAO,CAACuB,WADV,EAEEvB,OAAO,CAACT,UAFV,EAGE7B,YAAY,CAACsC,OAAO,CAACC,MAAT,EAAiBb,aAAjB,CAHd;AAMA9B,IAAAA,eAAe,CAAC0C,OAAO,CAACT,UAAT,EAAqBS,OAArB,EAA8BA,OAAO,CAACC,MAAR,CAAeE,MAA7C,CAAf;AACD,GAzBsB,CA2BvB;AACA;;;AACAhB,EAAAA,gBAAgB,CAAC,MAAM;AACrB,QAAI,CAAC+B,UAAU,CAAC1B,OAAhB,EAAyB;AACvB;AACD;;AACD,SAAK,MAAMQ,OAAX,IAAsBK,OAAtB,EAA+B;AAC7B,UAAImB,aAAuB,GAAG,EAA9B;;AACA,UAAIxB,OAAO,CAACC,MAAR,CAAeuB,aAAnB,EAAkC;AAChCA,QAAAA,aAAa,GAAG/B,uBAAuB,CAACO,OAAO,CAACC,MAAR,CAAeuB,aAAhB,CAAvC;AACD;;AAED,UAAIC,gBAA0B,GAAG,EAAjC;;AACA,UAAIzB,OAAO,CAACC,MAAR,CAAewB,gBAAnB,EAAqC;AACnCA,QAAAA,gBAAgB,GAAGhC,uBAAuB,CACxCO,OAAO,CAACC,MAAR,CAAewB,gBADyB,CAA1C;AAGD;;AAED,UAAIC,cAAwB,GAAG,EAA/B;;AACA,UAAI1B,OAAO,CAACC,MAAR,CAAeyB,cAAnB,EAAmC;AACjCA,QAAAA,cAAc,GAAGjC,uBAAuB,CAACO,OAAO,CAACC,MAAR,CAAeyB,cAAhB,CAAxC;AACD;;AAEDlE,MAAAA,sBAAsB,CAACmE,oBAAvB,CACE3B,OAAO,CAACT,UADV,EAEE7B,YAAY,CAACsC,OAAO,CAACC,MAAT,EAAiBb,aAAjB,EAAgC;AAC1CwC,QAAAA,oBAAoB,EAAEH,gBADoB;AAE1CI,QAAAA,OAAO,EAAEL,aAFiC;AAG1CE,QAAAA,cAAc,EAAEA;AAH0B,OAAhC,CAFd;AAQD;;AAED9D,IAAAA,uBAAuB;AACxB,GAjCe,CAAhB;AAmCAmC,EAAAA,eAAe,CAACE,MAAhB,GAAyBI,OAAzB;;AAEA,OAAK,MAAMA,OAAX,IAAsBN,eAAe,CAACE,MAAtC,EAA8C;AAC5C,UAAM6B,UAAU,GAAGzB,OAAO,CAAC0B,mBAAR,GACfxD,UAAU,CAACyD,kBADI,GAEfzD,UAAU,CAAC0D,mBAFf;;AAIA,QAAIpD,QAAQ,CAACqD,EAAT,KAAgB,KAApB,EAA2B;AAEvB1E,MAAAA,sBAAsB,CAAC2E,oBADzB,CAGE9B,OAAO,CAACd,UAHV,EAIEyB,OAJF,EAKEzC,UAAU,CAAC6D,mBALb,EAKkC;AAChCnB,MAAAA,mBANF;AAQD,KATD,MASO;AACLzD,MAAAA,sBAAsB,CAAC2E,oBAAvB,CACE9B,OAAO,CAACd,UADV,EAEEyB,OAFF,EAGEc,UAHF;AAKD;AACF;;AAED,MAAI/B,eAAe,CAACsC,gBAApB,EAAsC;AACpC,UAAMC,iBAAiB,GAAIC,CAAD,IAAoBA,CAAC,CAACR,mBAAhD;;AAEAhC,IAAAA,eAAe,CAACsC,gBAAhB,CAAiCG,KAAjC,GAAyCnC,OAAO,CAC7CT,MADsC,CAC/B0C,iBAD+B,EAEtC3C,GAFsC,CAEjC4C,CAAD,IAAOA,CAAC,CAAC/B,QAFyB,CAAzC;AAKD;AACF;;AAED,SAASiC,cAAT,CACE1C,eADF,EAEEgB,aAFF,EAGEV,OAHF,EAIEa,UAJF,EAKE;AACAH,EAAAA,aAAa,CAACM,OAAd;;AAEA,OAAK,IAAIqB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGrC,OAAO,CAACsC,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACvC,UAAM1C,OAAO,GAAGD,eAAe,CAACE,MAAhB,CAAuByC,CAAvB,CAAhB;AACAtC,IAAAA,gCAAgC,CAACJ,OAAD,CAAhC,CAFuC,CAIvC;AACA;;AACA,QAAIK,OAAO,CAACqC,CAAD,CAAP,CAAWnD,UAAX,KAA0BS,OAAO,CAACT,UAAtC,EAAkD;AAChDc,MAAAA,OAAO,CAACqC,CAAD,CAAP,CAAWnD,UAAX,GAAwBS,OAAO,CAACT,UAAhC;AACAc,MAAAA,OAAO,CAACqC,CAAD,CAAP,CAAWlC,QAAX,CAAoBjB,UAApB,GAAiCS,OAAO,CAACT,UAAzC;AACD;AACF,GAbD,CAeA;AACA;AACA;;;AACAJ,EAAAA,gBAAgB,CAAC,MAAM;AACrB,QAAI,CAAC+B,UAAU,CAAC1B,OAAhB,EAAyB;AACvB;AACD;;AACD,SAAK,IAAIkD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGrC,OAAO,CAACsC,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACvC,YAAM1C,OAAO,GAAGD,eAAe,CAACE,MAAhB,CAAuByC,CAAvB,CAAhB;AAEA1C,MAAAA,OAAO,CAACC,MAAR,GAAiBI,OAAO,CAACqC,CAAD,CAAP,CAAWzC,MAA5B;AACAD,MAAAA,OAAO,CAACQ,QAAR,GAAmBH,OAAO,CAACqC,CAAD,CAAP,CAAWlC,QAA9B;AAEA,YAAMgB,aAAa,GAAG/B,uBAAuB,CAC3CO,OAAO,CAACC,MAAR,CAAeuB,aAD4B,CAA7C;AAIA,YAAMC,gBAAgB,GAAGhC,uBAAuB,CAC9CO,OAAO,CAACC,MAAR,CAAewB,gBAD+B,CAAhD;AAIAjE,MAAAA,sBAAsB,CAACmE,oBAAvB,CACE3B,OAAO,CAACT,UADV,EAEE7B,YAAY,CAACsC,OAAO,CAACC,MAAT,EAAiBb,aAAjB,EAAgC;AAC1CwC,QAAAA,oBAAoB,EAAEH,gBADoB;AAE1CI,QAAAA,OAAO,EAAEL;AAFiC,OAAhC,CAFd;AAQAlE,MAAAA,eAAe,CAAC0C,OAAO,CAACT,UAAT,EAAqBS,OAArB,EAA8BA,OAAO,CAACC,MAAR,CAAeE,MAA7C,CAAf;AACD;;AAED,QAAIJ,eAAe,CAACsC,gBAApB,EAAsC;AAAA;;AACpC,YAAMO,qBAAqB,4BACzB7C,eAAe,CAACsC,gBAAhB,CAAiCG,KADR,yEACiB,EAD5C;AAEA,YAAMK,gBAAgB,GAAG9C,eAAe,CAACE,MAAhB,CACtBL,MADsB,CACd2C,CAAD,IAAOA,CAAC,CAACR,mBADM,EACe;AADf,OAEtBpC,GAFsB,CAEjB4C,CAAD,IAAOA,CAAC,CAAC/B,QAFS,CAAzB,CAHoC,CASpC;;AACA,UAAIsC,uBAAuB,GACzBF,qBAAqB,CAACD,MAAtB,KAAiCE,gBAAgB,CAACF,MADpD;;AAGA,UAAI,CAACG,uBAAL,EAA8B;AAC5B;AACA,aAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGG,gBAAgB,CAACF,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAChD,eACE;AACAG,UAAAA,gBAAgB,CAACH,CAAD,CAAhB,CAAoBK,SAApB,KAAkCH,qBAAqB,CAACF,CAAD,CAArB,CAAyBK,SAF7D,EAGE;AACAD,YAAAA,uBAAuB,GAAG,IAA1B;AACA;AACD;AACF;AACF;;AAED,UAAIA,uBAAJ,EAA6B;AAC3B/C,QAAAA,eAAe,CAACsC,gBAAhB,CAAiCG,KAAjC,GAAyCK,gBAAzC;AACD;AACF;;AAEDjF,IAAAA,uBAAuB;AACxB,GA7De,CAAhB;AA8DD;;AAED,SAASoF,eAAT,CACEjD,eADF,EAEEM,OAFF,EAGE;AACA,MAAIA,OAAO,CAACsC,MAAR,KAAmB5C,eAAe,CAACE,MAAhB,CAAuB0C,MAA9C,EAAsD;AACpD,WAAO,IAAP;AACD;;AACD,OAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGrC,OAAO,CAACsC,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACvC,QACErC,OAAO,CAACqC,CAAD,CAAP,CAAWnB,WAAX,KAA2BxB,eAAe,CAACE,MAAhB,CAAuByC,CAAvB,EAA0BnB,WAArD,IACAlB,OAAO,CAACqC,CAAD,CAAP,CAAWX,mBAAX,KACEhC,eAAe,CAACE,MAAhB,CAAuByC,CAAvB,EAA0BX,mBAH9B,EAIE;AACA,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD;;AAED,SAASkB,kBAAT,CACEC,KADF,EAEoC;AAClC,YADkC,CAElC;;AACA,SAAOA,KAAK,CAACC,QAAN,IAAkB,IAAzB;AACD;;AAED,SAASC,YAAT,CACEF,KADF,EAE8B;AAC5B;;AACA,SAAOA,KAAK,CAACG,SAAN,IAAmB,IAA1B;AACD;;AAED,SAASC,UAAT,CACEC,IADF,EAEElD,OAFF,EAGE;AACA;;AACA,UAAQkD,IAAR;AACE,SAAKnG,aAAa,CAACoG,KAAnB;AACE,aAAOnD,OAAO,CAACoD,OAAf;;AACF,SAAKrG,aAAa,CAACsG,KAAnB;AACE,aAAOrD,OAAO,CAACsD,OAAf;;AACF,SAAKvG,aAAa,CAACwG,MAAnB;AACE,aAAOvD,OAAO,CAACwD,QAAf;;AACF,SAAKzG,aAAa,CAAC0G,MAAnB;AACE,aAAOzD,OAAO,CAAC0D,QAAf;;AACF,SAAK3G,aAAa,CAAC4G,GAAnB;AACE,aAAO3D,OAAO,CAAC4D,KAAf;;AACF,SAAK7G,aAAa,CAAC8G,QAAnB;AACE,aAAO7D,OAAO,CAAC8D,UAAf;;AACF,SAAK/G,aAAa,CAACgH,YAAnB;AACE,aAAO/D,OAAO,CAACgE,aAAf;;AACF,SAAKjH,aAAa,CAACkH,YAAnB;AACE,aAAOjE,OAAO,CAACkE,aAAf;;AACF,SAAKnH,aAAa,CAACoH,UAAnB;AACE,aAAOnE,OAAO,CAACoE,WAAf;;AACF,SAAKrH,aAAa,CAACsH,iBAAnB;AACE,aAAOrE,OAAO,CAACsE,kBAAf;AApBJ;AAsBD;;AAED,SAASC,4BAAT,CACEvB,SADF,EAEiB;AACf;;AACA,UAAQA,SAAR;AACE,SAAK/E,cAAc,CAAC8F,YAApB;AACE,aAAOhH,aAAa,CAACgH,YAArB;;AACF,SAAK9F,cAAc,CAACgG,YAApB;AACE,aAAOlH,aAAa,CAACkH,YAArB;;AACF,SAAKhG,cAAc,CAACkG,UAApB;AACE,aAAOpH,aAAa,CAACoH,UAArB;;AACF,SAAKlG,cAAc,CAACoG,iBAApB;AACE,aAAOtH,aAAa,CAACsH,iBAArB;AARJ;;AAUA,SAAOtH,aAAa,CAACyH,SAArB;AACD;;AAED,SAASC,UAAT,CACEvB,IADF,EAEElD,OAFF,EAGE6C,KAHF,EAIE,GAAG6B,IAJL,EAKE;AACA;;AACA,QAAM/E,OAAO,GAAGsD,UAAU,CAACC,IAAD,EAAOlD,OAAP,CAA1B;;AACA,MAAIA,OAAO,CAACI,SAAR,CAAkB8C,IAAlB,CAAJ,EAA6B;AAC3B;AACA;AACAvD,IAAAA,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAGkD,KAAH,EAAU,GAAG6B,IAAb,CAAP;AACD,GAJD,MAIO,IAAI/E,OAAJ,EAAa;AAClBY,IAAAA,OAAO,CAACoE,IAAR,CAAatG,UAAU,CAAC,6CAAD,CAAvB;AACD;AACF;;AAED,SAASuG,kBAAT,CACElF,eADF,EAEEmF,YAFF,EAGE;AACA,MAAI,CAAC7H,UAAL,EAAiB;AACf;AACD,GAHD,CAKA;AACA;AACA;;;AACA,QAAM8H,uBAAuB,GAAG9H,UAAU,CAAC+H,cAAX,CAE9B,IAF8B,CAAhC,CARA,CAYA;;AACA,QAAMC,eAAe,GAAGhI,UAAU,CAAC+H,cAAX,CAEtB,EAFsB,CAAxB,CAbA,CAiBA;;AACA,QAAME,gBAA2C,GAAG,EAApD;;AAEA,QAAMC,QAAQ,GACZrC,KADe,IAEZ;AACH;;AAEA,UAAMsC,eAAe,GAAGL,uBAAuB,CAAC3C,KAAhD;;AACA,QAAI,CAACgD,eAAL,EAAsB;AACpB;AACD;;AAED,SAAK,IAAI9C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8C,eAAe,CAAC7C,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;AAC/C,YAAMrC,OAAO,GAAGmF,eAAe,CAAC9C,CAAD,CAA/B;;AAEA,UAAIQ,KAAK,CAAC3D,UAAN,KAAqBc,OAAO,CAACd,UAAjC,EAA6C;AAC3C,YAAI0D,kBAAkB,CAACC,KAAD,CAAtB,EAA+B;AAC7B,cACEA,KAAK,CAACC,QAAN,KAAmB9E,KAAK,CAACoH,YAAzB,IACAvC,KAAK,CAACwC,KAAN,KAAgBrH,KAAK,CAACmF,KAFxB,EAGE;AACAsB,YAAAA,UAAU,CAAC1H,aAAa,CAACoG,KAAf,EAAsBnD,OAAtB,EAA+B6C,KAA/B,CAAV;AACD,WALD,MAKO,IACL,CAACA,KAAK,CAACC,QAAN,KAAmB9E,KAAK,CAACmF,KAAzB,IACCN,KAAK,CAACC,QAAN,KAAmB9E,KAAK,CAACoH,YAD3B,KAEAvC,KAAK,CAACwC,KAAN,KAAgBrH,KAAK,CAACsH,MAHjB,EAIL;AACAb,YAAAA,UAAU,CAAC1H,aAAa,CAACsG,KAAf,EAAsBrD,OAAtB,EAA+B6C,KAA/B,CAAV;AACAmC,YAAAA,eAAe,CAAC7C,KAAhB,CAAsBnC,OAAO,CAACd,UAA9B,IAA4CqG,SAA5C;AACD,WAPM,MAOA,IACL1C,KAAK,CAACC,QAAN,KAAmBD,KAAK,CAACwC,KAAzB,IACAxC,KAAK,CAACwC,KAAN,KAAgBrH,KAAK,CAAC2F,GAFjB,EAGL;AACA,gBAAId,KAAK,CAACC,QAAN,KAAmB9E,KAAK,CAACsH,MAA7B,EAAqC;AACnCb,cAAAA,UAAU,CAAC1H,aAAa,CAAC4G,GAAf,EAAoB3D,OAApB,EAA6B6C,KAA7B,EAAoC,IAApC,CAAV;AACD;;AACD4B,YAAAA,UAAU,CAAC1H,aAAa,CAAC8G,QAAf,EAAyB7D,OAAzB,EAAkC6C,KAAlC,EAAyC,IAAzC,CAAV;AACD,WARM,MAQA,IACL,CAACA,KAAK,CAACwC,KAAN,KAAgBrH,KAAK,CAACwH,MAAtB,IAAgC3C,KAAK,CAACwC,KAAN,KAAgBrH,KAAK,CAACyH,SAAvD,KACA5C,KAAK,CAACwC,KAAN,KAAgBxC,KAAK,CAACC,QAFjB,EAGL;AACA,gBAAID,KAAK,CAACC,QAAN,KAAmB9E,KAAK,CAACsH,MAA7B,EAAqC;AACnCb,cAAAA,UAAU,CAAC1H,aAAa,CAAC4G,GAAf,EAAoB3D,OAApB,EAA6B6C,KAA7B,EAAoC,KAApC,CAAV;AACD;;AACD4B,YAAAA,UAAU,CAAC1H,aAAa,CAAC8G,QAAf,EAAyB7D,OAAzB,EAAkC6C,KAAlC,EAAyC,KAAzC,CAAV;AACD;AACF,SA9BD,MA8BO,IAAIE,YAAY,CAACF,KAAD,CAAhB,EAAyB;AAC9B,cAAI,CAACoC,gBAAgB,CAAC5C,CAAD,CAArB,EAA0B;AACxB4C,YAAAA,gBAAgB,CAAC5C,CAAD,CAAhB,GAAsB7E,mBAAmB,CAACkI,MAApB,CAA2B7C,KAAK,CAAC3D,UAAjC,CAAtB;AACD;;AAED,cAAI2D,KAAK,CAACG,SAAN,KAAoB/E,cAAc,CAACmH,YAAvC,EAAqD;AACnDX,YAAAA,UAAU,CACRF,4BAA4B,CAAC1B,KAAK,CAACG,SAAP,CADpB,EAERhD,OAFQ,EAGR6C,KAHQ,EAIRoC,gBAAgB,CAAC5C,CAAD,CAJR,CAAV;AAMD;AACF,SAbM,MAaA;AACLoC,UAAAA,UAAU,CAAC1H,aAAa,CAACwG,MAAf,EAAuBvD,OAAvB,EAAgC6C,KAAhC,CAAV;;AAEA,cAAI7C,OAAO,CAAC0D,QAAR,IAAoB1D,OAAO,CAAC2F,qBAAhC,EAAuD;AAAA;;AACrDlB,YAAAA,UAAU,CACR1H,aAAa,CAAC0G,MADN,EAERzD,OAFQ,2BAGRA,OAAO,CAAC2F,qBAHA,0DAGR,2BAAA3F,OAAO,EACL6C,KADK,EAELmC,eAAe,CAAC7C,KAAhB,CAAsBnC,OAAO,CAACd,UAA9B,CAFK,CAHC,CAAV;AASA8F,YAAAA,eAAe,CAAC7C,KAAhB,CAAsBnC,OAAO,CAACd,UAA9B,IAA4C2D,KAA5C;AACD;AACF;AACF;AACF;AACF,GA3ED,CApBA,CAiGA;;;AACA,QAAMA,KAAK,GAAG7F,UAAU,CAAC4I,QAAX,CACZV,QADY,EAEZ,CAAC,6BAAD,EAAgC,uBAAhC,CAFY,EAGZL,YAHY,CAAd;AAMAnF,EAAAA,eAAe,CAACmG,oBAAhB,GAAuChD,KAAvC;AACAnD,EAAAA,eAAe,CAACsC,gBAAhB,GAAmC8C,uBAAnC;AACD,C,CAED;;;AACA,SAASgB,wBAAT,CAAkC7G,GAAlC,EAA4C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAI8G,OAAO,IAAIvH,QAAQ,CAACqD,EAAT,KAAgB,KAA/B,EAAsC;AACpC,UAAMmE,oBAAoB,GAAG1H,qBAAqB,EAAlD,CADoC,CAEpC;;AACA,UAAM2H,QAAQ,GACZD,oBAAoB,CAACE,KAArB,GAA6B,EAA7B,IAAmCF,oBAAoB,CAACG,KAArB,GAA6B,CAAhE,GACI;AACAlH,IAAAA,GAAG,CAACmH,eAAJ,CAAoBC,WAFxB,GAGI;AACApH,IAAAA,GAAG,CAACqH,mBAAJ,CAAwBD,WAL9B,CAHoC,CASpC;;AACA,QAAIE,QAAQ,GACV7H,UAAU,CAAC8H,2BAAX,CACEvH,GADF,EAEEwH,+BAHJ,CAVoC,CAepC;;;AACA,WAAOF,QAAQ,IAAIA,QAAQ,CAACF,WAAT,KAAyBJ,QAA5C,EAAsD;AACpD;AACA,UAAIM,QAAQ,CAACG,OAAb,EAAsB;AACpB,cAAM,IAAIC,KAAJ,CACJ,mPADI,CAAN;AAGD,OANmD,CAQpD;;;AACAJ,MAAAA,QAAQ,GAAGA,QAAQ,CAACK,MAApB;AACD;AACF;AACF;;AAED,MAAMC,mBAAmB,GAAG,CAC1BC,UAD0B,EAE1B9G,OAF0B,KAGjB;AACT,OAAK,MAAMkC,CAAX,IAAgBlC,OAAO,CAAC+G,cAAR,EAAhB,EAA0C;AACxC7E,IAAAA,CAAC,CAACtC,MAAF,CAASkH,UAAT,GAAsBA,UAAtB;AACD;AACF,CAPD;;AAoBA,OAAO,MAAME,eAAe,GAAIC,KAAD,IAAiC;AAC9D,QAAMC,eAAe,GAAGxK,UAAU,CAACmC,6BAAD,CAAlC;;AACA,MAAIkH,OAAO,IAAI,CAACmB,eAAZ,IAA+B,CAAC9I,SAAS,EAAzC,IAA+CI,QAAQ,CAACqD,EAAT,KAAgB,KAAnE,EAA0E;AACxE,UAAM,IAAI8E,KAAJ,CACJ,wNADI,CAAN;AAGD;;AAED,QAAMjG,aAAa,GAAGuG,KAAK,CAACjH,OAA5B;;AAEA,MAAIiH,KAAK,CAACH,UAAV,EAAsB;AACpBD,IAAAA,mBAAmB,CAACI,KAAK,CAACH,UAAP,EAAmBpG,aAAnB,CAAnB;AACD;;AAED,QAAMV,OAAO,GAAGU,aAAa,CAACqG,cAAd,EAAhB;AACA,QAAMI,iBAAiB,GAAGnH,OAAO,CAACoH,IAAR,CAAclF,CAAD,IAAOA,CAAC,CAACR,mBAAtB,CAA1B,CAf8D,CAiB9D;;AACA,QAAM2D,KAAK,GAAGzI,MAAM,CAAuB;AACzCyK,IAAAA,WAAW,EAAE,IAD4B;AAEzCC,IAAAA,OAAO,EAAE,IAFgC;AAGzCC,IAAAA,eAAe,EAAE,CAAC,CAHuB;AAIzCC,IAAAA,aAAa,EAAE;AAJ0B,GAAvB,CAAN,CAKXrI,OALH;AAMA,QAAM0B,UAAU,GAAGjE,MAAM,CAAC,KAAD,CAAzB;AACA,QAAMgE,mBAAmB,GAAGhE,MAAM,CAAkB;AAClD6B,IAAAA,qBAAqB,EAAGgJ,CAAD,IAAyC;AAC9DhJ,MAAAA,qBAAqB,CAACgJ,CAAC,CAACC,WAAH,CAArB;AACD,KAHiD;AAIlDC,IAAAA,2BAA2B,EAAEhJ,6BAA6B,KACrD8I,CAAD,IAAyC;AACvChJ,MAAAA,qBAAqB,CAACgJ,CAAC,CAACC,WAAH,CAArB;AACD,KAHqD,GAItDnC;AAR8C,GAAlB,CAAlC;AAWA,QAAM,CAACqC,WAAD,EAAcC,cAAd,IAAgChL,QAAQ,CAAC,KAAD,CAA9C;;AACA,WAASiL,WAAT,GAAuB;AACrBD,IAAAA,cAAc,CAAC,CAACD,WAAF,CAAd;AACD;;AAED,QAAMlI,eAAe,GAAGjD,KAAK,CAACG,MAAN,CAAqC;AAC3DgD,IAAAA,MAAM,EAAEI,OADmD;AAE3D6F,IAAAA,oBAAoB,EAAE,IAFqC;AAG3D7D,IAAAA,gBAAgB,EAAE,IAHyC;AAI3DlB,IAAAA,cAAc,EAAE,IAJ2C;AAK3DqG,IAAAA,iBAAiB,EAAEA;AALwC,GAArC,EAMrBhI,OANH;;AAQA,MAAIgI,iBAAiB,KAAKzH,eAAe,CAACyH,iBAA1C,EAA6D;AAC3D,UAAM,IAAIR,KAAJ,CACJtI,UAAU,CACR,gFADQ,CADN,CAAN;AAKD;;AAED,WAAS0J,gBAAT,CAA0BC,gBAA1B,EAAsD;AACpD;AACA,UAAMrH,OAAO,GAAGrD,cAAc,CAAC+H,KAAK,CAACiC,OAAP,CAA9B;AACA,UAAME,aAAa,GAAG7G,OAAO,KAAK0E,KAAK,CAACkC,eAAxC;;AAEA,QAAIC,aAAa,IAAI7E,eAAe,CAACjD,eAAD,EAAkBM,OAAlB,CAApC,EAAgE;AAC9D8F,MAAAA,wBAAwB,CAACT,KAAK,CAACiC,OAAP,CAAxB;AACA7H,MAAAA,YAAY,CAACC,eAAD,CAAZ;AACAe,MAAAA,cAAc,CAAC;AACbf,QAAAA,eADa;AAEbgB,QAAAA,aAFa;AAGbV,QAAAA,OAHa;AAIbY,QAAAA,mBAJa;AAKbD,QAAAA,OALa;AAMbE,QAAAA;AANa,OAAD,CAAd;AASAwE,MAAAA,KAAK,CAACkC,eAAN,GAAwB5G,OAAxB;AACA0E,MAAAA,KAAK,CAACmC,aAAN,GAAsBA,aAAtB;;AACA,UAAIA,aAAJ,EAAmB;AACjBM,QAAAA,WAAW;AACZ;AACF,KAjBD,MAiBO,IAAI,CAACE,gBAAL,EAAuB;AAC5B5F,MAAAA,cAAc,CAAC1C,eAAD,EAAkBgB,aAAlB,EAAiCV,OAAjC,EAA0Ca,UAA1C,CAAd;AACD;AACF,GAlF6D,CAoF9D;AACA;;;AACA,QAAMoH,6BAA6B,GACjCvI,eAAe,CAACoB,cAAhB,IACA6B,eAAe,CAACjD,eAAD,EAAkBM,OAAlB,CADf,IAEAqF,KAAK,CAACmC,aAHR;AAKAnC,EAAAA,KAAK,CAACmC,aAAN,GAAsB,KAAtB;;AAEA,MAAI9H,eAAe,CAACoB,cAApB,EAAoC;AAClCJ,IAAAA,aAAa,CAACK,UAAd;AACD;;AAED,MAAIoG,iBAAJ,EAAuB;AACrB;AACA;AACAvC,IAAAA,kBAAkB,CAAClF,eAAD,EAAkBuI,6BAAlB,CAAlB;AACD;;AAEDtL,EAAAA,SAAS,CAAC,MAAM;AACd,UAAMgE,OAAO,GAAGrD,cAAc,CAAC+H,KAAK,CAACiC,OAAP,CAA9B;AACAjC,IAAAA,KAAK,CAACgC,WAAN,GAAoB,IAApB;AACAxG,IAAAA,UAAU,CAAC1B,OAAX,GAAqB,IAArB;AAEA2G,IAAAA,wBAAwB,CAACT,KAAK,CAACiC,OAAP,CAAxB;AAEA7G,IAAAA,cAAc,CAAC;AACbf,MAAAA,eADa;AAEbgB,MAAAA,aAFa;AAGbV,MAAAA,OAHa;AAIbY,MAAAA,mBAJa;AAKbD,MAAAA,OALa;AAMbE,MAAAA;AANa,KAAD,CAAd;AASA,WAAO,MAAM;AACXA,MAAAA,UAAU,CAAC1B,OAAX,GAAqB,KAArB;AACAM,MAAAA,YAAY,CAACC,eAAD,CAAZ;AACD,KAHD;AAID,GApBQ,EAoBN,EApBM,CAAT;AAsBA/C,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI,CAAC0I,KAAK,CAACgC,WAAX,EAAwB;AACtBU,MAAAA,gBAAgB;AACjB,KAFD,MAEO;AACL1C,MAAAA,KAAK,CAACgC,WAAN,GAAoB,KAApB;AACD;AACF,GANQ,EAMN,CAACJ,KAAD,CANM,CAAT;;AAQA,QAAMiB,WAAW,GAAIjJ,GAAD,IAAkB;AACpC,QAAIA,GAAG,KAAK,IAAZ,EAAkB;AAChB;AACAoG,MAAAA,KAAK,CAACiC,OAAN,GAAgBrI,GAAhB,CAFgB,CAIhB;;AACA,UAAIoG,KAAK,CAACkC,eAAN,KAA0B,CAAC,CAA/B,EAAkC;AAChClC,QAAAA,KAAK,CAACkC,eAAN,GAAwBjK,cAAc,CAAC+H,KAAK,CAACiC,OAAP,CAAtC;AACD,OAPe,CAShB;AACA;;;AACAS,MAAAA,gBAAgB,CAAC,IAAD,CAAhB;;AAEA,UAAI5J,QAAQ,EAAZ,EAAgB;AACd,cAAMgK,IAAI,GAAG5J,oBAAoB,CAACU,GAAD,CAAjC;;AACA,YAAImJ,MAAM,CAACC,sBAAP,CAA8BF,IAA9B,MAAwC,KAA5C,EAAmD;AACjD5H,UAAAA,OAAO,CAACC,KAAR,CACEnC,UAAU,CACR,uEACE,kGAFM,CADZ;AAMD;AACF;AACF;AACF,GA1BD;;AA4BA,MAAI8I,iBAAJ,EAAuB;AACrB,wBACE,oBAAC,YAAD;AACE,MAAA,GAAG,EAAEe,WADP;AAEE,MAAA,qBAAqB,EAAExI,eAAe,CAACmG;AAFzC,OAGGoB,KAAK,CAACqB,QAHT,CADF;AAOD,GARD,MAQO;AACL,wBAAO,oBAAC,IAAD;AAAM,MAAA,GAAG,EAAEJ;AAAX,OAAyBjB,KAAK,CAACqB,QAA/B,CAAP;AACD;AACF,CA5KM;;AA8KP,MAAMC,IAAN,SAAmB9L,KAAK,CAAC+L,SAAzB,CAIG;AACDC,EAAAA,MAAM,GAAG;AACP,QAAI;AACF;AACA;AACA;AACA;AACA;AACA,YAAMC,KAAU,GAAGjM,KAAK,CAACkM,QAAN,CAAeC,IAAf,CAAoB,KAAK3B,KAAL,CAAWqB,QAA/B,CAAnB;AACA,0BAAO7L,KAAK,CAACoM,YAAN,CACLH,KADK,EAEL;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAFK,EAGL;AACAJ,MAAAA,KAAK,CAACzB,KAAN,CAAYqB,QAJP,CAAP;AAMD,KAbD,CAaE,OAAOb,CAAP,EAAU;AACV,YAAM,IAAId,KAAJ,CACJtI,UAAU,CACP,2KADO,CADN,CAAN;AAKD;AACF;;AAtBA;;AAyBH,MAAM0K,YAAY,4BAAG/L,UAAH,aAAGA,UAAH,8CAAGA,UAAU,CAAEgM,OAAf,wDAAG,oBAAqBC,uBAArB,CAA6CV,IAA7C,CAAH,yEAAyDA,IAA3E", "sourcesContent": ["import React, { useContext, useEffect, useRef, useState } from 'react';\nimport {\n  GestureType,\n  HandlerCallbacks,\n  BaseGesture,\n  GestureRef,\n  CALLBACK_TYPE,\n} from './gesture';\nimport { Reanimated, SharedValue } from './reanimatedWrapper';\nimport { registerHandler, unregisterHandler } from '../handlersRegistry';\nimport RNGestureHandlerModule from '../../RNGestureHandlerModule';\nimport {\n  baseGestureHandlerWithMonitorProps,\n  filterConfig,\n  findNodeHandle,\n  GestureTouchEvent,\n  GestureUpdateEvent,\n  GestureStateChangeEvent,\n  HandlerStateChangeEvent,\n  scheduleFlushOperations,\n  UserSelect,\n} from '../gestureHandlerCommon';\nimport {\n  GestureStateManager,\n  GestureStateManagerType,\n} from './gestureStateManager';\nimport { flingGestureHandlerProps } from '../FlingGestureHandler';\nimport { forceTouchGestureHandlerProps } from '../ForceTouchGestureHandler';\nimport { longPressGestureHandlerProps } from '../LongPressGestureHandler';\nimport {\n  panGestureHandlerProps,\n  panGestureHandlerCustomNativeProps,\n} from '../PanGestureHandler';\nimport { tapGestureHandlerProps } from '../TapGestureHandler';\nimport { hoverGestureHandlerProps } from './hoverGesture';\nimport { State } from '../../State';\nimport { TouchEventType } from '../../TouchEventType';\nimport { ComposedGesture } from './gestureComposition';\nimport { ActionType } from '../../ActionType';\nimport { isFabric, isJestEnv, tagMessage } from '../../utils';\nimport { getReactNativeVersion } from '../../getReactNativeVersion';\nimport { getShadowNodeFromRef } from '../../getShadowNodeFromRef';\nimport { Platform } from 'react-native';\nimport type RNGestureHandlerModuleWeb from '../../RNGestureHandlerModule.web';\nimport { onGestureHandlerEvent } from './eventReceiver';\nimport { RNRenderer } from '../../RNRenderer';\nimport { isNewWebImplementationEnabled } from '../../EnableNewWebImplementation';\nimport { nativeViewGestureHandlerProps } from '../NativeViewGestureHandler';\nimport GestureHandlerRootViewContext from '../../GestureHandlerRootViewContext';\nimport { ghQueueMicrotask } from '../../ghQueueMicrotask';\n\ndeclare const global: {\n  isFormsStackingContext: (node: unknown) => boolean | null; // JSI function\n};\n\nconst ALLOWED_PROPS = [\n  ...baseGestureHandlerWithMonitorProps,\n  ...tapGestureHandlerProps,\n  ...panGestureHandlerProps,\n  ...panGestureHandlerCustomNativeProps,\n  ...longPressGestureHandlerProps,\n  ...forceTouchGestureHandlerProps,\n  ...flingGestureHandlerProps,\n  ...hoverGestureHandlerProps,\n  ...nativeViewGestureHandlerProps,\n];\n\nexport type GestureConfigReference = {\n  config: GestureType[];\n  animatedEventHandler: unknown;\n  animatedHandlers: SharedValue<\n    HandlerCallbacks<Record<string, unknown>>[] | null\n  > | null;\n  firstExecution: boolean;\n  useReanimatedHook: boolean;\n};\n\nfunction convertToHandlerTag(ref: GestureRef): number {\n  if (typeof ref === 'number') {\n    return ref;\n  } else if (ref instanceof BaseGesture) {\n    return ref.handlerTag;\n  } else {\n    // @ts-ignore in this case it should be a ref either to gesture object or\n    // a gesture handler component, in both cases handlerTag property exists\n    return ref.current?.handlerTag ?? -1;\n  }\n}\n\nfunction extractValidHandlerTags(interactionGroup: GestureRef[] | undefined) {\n  return (\n    interactionGroup?.map(convertToHandlerTag)?.filter((tag) => tag > 0) ?? []\n  );\n}\n\nfunction dropHandlers(preparedGesture: GestureConfigReference) {\n  for (const handler of preparedGesture.config) {\n    RNGestureHandlerModule.dropGestureHandler(handler.handlerTag);\n\n    unregisterHandler(handler.handlerTag, handler.config.testId);\n  }\n\n  scheduleFlushOperations();\n}\n\nfunction checkGestureCallbacksForWorklets(gesture: GestureType) {\n  // if a gesture is explicitly marked to run on the JS thread there is no need to check\n  // if callbacks are worklets as the user is aware they will be ran on the JS thread\n  if (gesture.config.runOnJS) {\n    return;\n  }\n\n  const areSomeNotWorklets = gesture.handlers.isWorklet.includes(false);\n  const areSomeWorklets = gesture.handlers.isWorklet.includes(true);\n\n  // if some of the callbacks are worklets and some are not, and the gesture is not\n  // explicitly marked with `.runOnJS(true)` show an error\n  if (areSomeNotWorklets && areSomeWorklets) {\n    console.error(\n      tagMessage(\n        `Some of the callbacks in the gesture are worklets and some are not. Either make sure that all calbacks are marked as 'worklet' if you wish to run them on the UI thread or use '.runOnJS(true)' modifier on the gesture explicitly to run all callbacks on the JS thread.`\n      )\n    );\n  }\n}\n\ninterface WebEventHandler {\n  onGestureHandlerEvent: (event: HandlerStateChangeEvent<unknown>) => void;\n  onGestureHandlerStateChange?: (\n    event: HandlerStateChangeEvent<unknown>\n  ) => void;\n}\n\ninterface AttachHandlersConfig {\n  preparedGesture: GestureConfigReference;\n  gestureConfig: ComposedGesture | GestureType;\n  gesture: GestureType[];\n  viewTag: number;\n  webEventHandlersRef: React.RefObject<WebEventHandler>;\n  mountedRef: React.RefObject<boolean>;\n}\n\nfunction attachHandlers({\n  preparedGesture,\n  gestureConfig,\n  gesture,\n  viewTag,\n  webEventHandlersRef,\n  mountedRef,\n}: AttachHandlersConfig) {\n  if (!preparedGesture.firstExecution) {\n    gestureConfig.initialize();\n  } else {\n    preparedGesture.firstExecution = false;\n  }\n\n  // use queueMicrotask to extract handlerTags, because all refs should be initialized\n  // when it's ran\n  ghQueueMicrotask(() => {\n    if (!mountedRef.current) {\n      return;\n    }\n    gestureConfig.prepare();\n  });\n\n  for (const handler of gesture) {\n    checkGestureCallbacksForWorklets(handler);\n    RNGestureHandlerModule.createGestureHandler(\n      handler.handlerName,\n      handler.handlerTag,\n      filterConfig(handler.config, ALLOWED_PROPS)\n    );\n\n    registerHandler(handler.handlerTag, handler, handler.config.testId);\n  }\n\n  // use queueMicrotask to extract handlerTags, because all refs should be initialized\n  // when it's ran\n  ghQueueMicrotask(() => {\n    if (!mountedRef.current) {\n      return;\n    }\n    for (const handler of gesture) {\n      let requireToFail: number[] = [];\n      if (handler.config.requireToFail) {\n        requireToFail = extractValidHandlerTags(handler.config.requireToFail);\n      }\n\n      let simultaneousWith: number[] = [];\n      if (handler.config.simultaneousWith) {\n        simultaneousWith = extractValidHandlerTags(\n          handler.config.simultaneousWith\n        );\n      }\n\n      let blocksHandlers: number[] = [];\n      if (handler.config.blocksHandlers) {\n        blocksHandlers = extractValidHandlerTags(handler.config.blocksHandlers);\n      }\n\n      RNGestureHandlerModule.updateGestureHandler(\n        handler.handlerTag,\n        filterConfig(handler.config, ALLOWED_PROPS, {\n          simultaneousHandlers: simultaneousWith,\n          waitFor: requireToFail,\n          blocksHandlers: blocksHandlers,\n        })\n      );\n    }\n\n    scheduleFlushOperations();\n  });\n\n  preparedGesture.config = gesture;\n\n  for (const gesture of preparedGesture.config) {\n    const actionType = gesture.shouldUseReanimated\n      ? ActionType.REANIMATED_WORKLET\n      : ActionType.JS_FUNCTION_NEW_API;\n\n    if (Platform.OS === 'web') {\n      (\n        RNGestureHandlerModule.attachGestureHandler as typeof RNGestureHandlerModuleWeb.attachGestureHandler\n      )(\n        gesture.handlerTag,\n        viewTag,\n        ActionType.JS_FUNCTION_OLD_API, // ignored on web\n        webEventHandlersRef\n      );\n    } else {\n      RNGestureHandlerModule.attachGestureHandler(\n        gesture.handlerTag,\n        viewTag,\n        actionType\n      );\n    }\n  }\n\n  if (preparedGesture.animatedHandlers) {\n    const isAnimatedGesture = (g: GestureType) => g.shouldUseReanimated;\n\n    preparedGesture.animatedHandlers.value = gesture\n      .filter(isAnimatedGesture)\n      .map((g) => g.handlers) as unknown as HandlerCallbacks<\n      Record<string, unknown>\n    >[];\n  }\n}\n\nfunction updateHandlers(\n  preparedGesture: GestureConfigReference,\n  gestureConfig: ComposedGesture | GestureType,\n  gesture: GestureType[],\n  mountedRef: React.RefObject<boolean>\n) {\n  gestureConfig.prepare();\n\n  for (let i = 0; i < gesture.length; i++) {\n    const handler = preparedGesture.config[i];\n    checkGestureCallbacksForWorklets(handler);\n\n    // only update handlerTag when it's actually different, it may be the same\n    // if gesture config object is wrapped with useMemo\n    if (gesture[i].handlerTag !== handler.handlerTag) {\n      gesture[i].handlerTag = handler.handlerTag;\n      gesture[i].handlers.handlerTag = handler.handlerTag;\n    }\n  }\n\n  // use queueMicrotask to extract handlerTags, because when it's ran, all refs should be updated\n  // and handlerTags in BaseGesture references should be updated in the loop above (we need to wait\n  // in case of external relations)\n  ghQueueMicrotask(() => {\n    if (!mountedRef.current) {\n      return;\n    }\n    for (let i = 0; i < gesture.length; i++) {\n      const handler = preparedGesture.config[i];\n\n      handler.config = gesture[i].config;\n      handler.handlers = gesture[i].handlers;\n\n      const requireToFail = extractValidHandlerTags(\n        handler.config.requireToFail\n      );\n\n      const simultaneousWith = extractValidHandlerTags(\n        handler.config.simultaneousWith\n      );\n\n      RNGestureHandlerModule.updateGestureHandler(\n        handler.handlerTag,\n        filterConfig(handler.config, ALLOWED_PROPS, {\n          simultaneousHandlers: simultaneousWith,\n          waitFor: requireToFail,\n        })\n      );\n\n      registerHandler(handler.handlerTag, handler, handler.config.testId);\n    }\n\n    if (preparedGesture.animatedHandlers) {\n      const previousHandlersValue =\n        preparedGesture.animatedHandlers.value ?? [];\n      const newHandlersValue = preparedGesture.config\n        .filter((g) => g.shouldUseReanimated) // ignore gestures that shouldn't run on UI\n        .map((g) => g.handlers) as unknown as HandlerCallbacks<\n        Record<string, unknown>\n      >[];\n\n      // if amount of gesture configs changes, we need to update the callbacks in shared value\n      let shouldUpdateSharedValue =\n        previousHandlersValue.length !== newHandlersValue.length;\n\n      if (!shouldUpdateSharedValue) {\n        // if the amount is the same, we need to check if any of the configs inside has changed\n        for (let i = 0; i < newHandlersValue.length; i++) {\n          if (\n            // we can use the `gestureId` prop as it's unique for every config instance\n            newHandlersValue[i].gestureId !== previousHandlersValue[i].gestureId\n          ) {\n            shouldUpdateSharedValue = true;\n            break;\n          }\n        }\n      }\n\n      if (shouldUpdateSharedValue) {\n        preparedGesture.animatedHandlers.value = newHandlersValue;\n      }\n    }\n\n    scheduleFlushOperations();\n  });\n}\n\nfunction needsToReattach(\n  preparedGesture: GestureConfigReference,\n  gesture: GestureType[]\n) {\n  if (gesture.length !== preparedGesture.config.length) {\n    return true;\n  }\n  for (let i = 0; i < gesture.length; i++) {\n    if (\n      gesture[i].handlerName !== preparedGesture.config[i].handlerName ||\n      gesture[i].shouldUseReanimated !==\n        preparedGesture.config[i].shouldUseReanimated\n    ) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction isStateChangeEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureStateChangeEvent {\n  'worklet';\n  // @ts-ignore Yes, the oldState prop is missing on GestureTouchEvent, that's the point\n  return event.oldState != null;\n}\n\nfunction isTouchEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureTouchEvent {\n  'worklet';\n  return event.eventType != null;\n}\n\nfunction getHandler(\n  type: CALLBACK_TYPE,\n  gesture: HandlerCallbacks<Record<string, unknown>>\n) {\n  'worklet';\n  switch (type) {\n    case CALLBACK_TYPE.BEGAN:\n      return gesture.onBegin;\n    case CALLBACK_TYPE.START:\n      return gesture.onStart;\n    case CALLBACK_TYPE.UPDATE:\n      return gesture.onUpdate;\n    case CALLBACK_TYPE.CHANGE:\n      return gesture.onChange;\n    case CALLBACK_TYPE.END:\n      return gesture.onEnd;\n    case CALLBACK_TYPE.FINALIZE:\n      return gesture.onFinalize;\n    case CALLBACK_TYPE.TOUCHES_DOWN:\n      return gesture.onTouchesDown;\n    case CALLBACK_TYPE.TOUCHES_MOVE:\n      return gesture.onTouchesMove;\n    case CALLBACK_TYPE.TOUCHES_UP:\n      return gesture.onTouchesUp;\n    case CALLBACK_TYPE.TOUCHES_CANCELLED:\n      return gesture.onTouchesCancelled;\n  }\n}\n\nfunction touchEventTypeToCallbackType(\n  eventType: TouchEventType\n): CALLBACK_TYPE {\n  'worklet';\n  switch (eventType) {\n    case TouchEventType.TOUCHES_DOWN:\n      return CALLBACK_TYPE.TOUCHES_DOWN;\n    case TouchEventType.TOUCHES_MOVE:\n      return CALLBACK_TYPE.TOUCHES_MOVE;\n    case TouchEventType.TOUCHES_UP:\n      return CALLBACK_TYPE.TOUCHES_UP;\n    case TouchEventType.TOUCHES_CANCELLED:\n      return CALLBACK_TYPE.TOUCHES_CANCELLED;\n  }\n  return CALLBACK_TYPE.UNDEFINED;\n}\n\nfunction runWorklet(\n  type: CALLBACK_TYPE,\n  gesture: HandlerCallbacks<Record<string, unknown>>,\n  event: GestureStateChangeEvent | GestureUpdateEvent | GestureTouchEvent,\n  ...args: any[]\n) {\n  'worklet';\n  const handler = getHandler(type, gesture);\n  if (gesture.isWorklet[type]) {\n    // @ts-ignore Logic below makes sure the correct event is send to the\n    // correct handler.\n    handler?.(event, ...args);\n  } else if (handler) {\n    console.warn(tagMessage('Animated gesture callback must be a worklet'));\n  }\n}\n\nfunction useAnimatedGesture(\n  preparedGesture: GestureConfigReference,\n  needsRebuild: boolean\n) {\n  if (!Reanimated) {\n    return;\n  }\n\n  // Hooks are called conditionally, but the condition is whether the\n  // react-native-reanimated is installed, which shouldn't change while running\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const sharedHandlersCallbacks = Reanimated.useSharedValue<\n    HandlerCallbacks<Record<string, unknown>>[] | null\n  >(null);\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const lastUpdateEvent = Reanimated.useSharedValue<\n    (GestureUpdateEvent | undefined)[]\n  >([]);\n\n  // not every gesture needs a state controller, init them lazily\n  const stateControllers: GestureStateManagerType[] = [];\n\n  const callback = (\n    event: GestureStateChangeEvent | GestureUpdateEvent | GestureTouchEvent\n  ) => {\n    'worklet';\n\n    const currentCallback = sharedHandlersCallbacks.value;\n    if (!currentCallback) {\n      return;\n    }\n\n    for (let i = 0; i < currentCallback.length; i++) {\n      const gesture = currentCallback[i];\n\n      if (event.handlerTag === gesture.handlerTag) {\n        if (isStateChangeEvent(event)) {\n          if (\n            event.oldState === State.UNDETERMINED &&\n            event.state === State.BEGAN\n          ) {\n            runWorklet(CALLBACK_TYPE.BEGAN, gesture, event);\n          } else if (\n            (event.oldState === State.BEGAN ||\n              event.oldState === State.UNDETERMINED) &&\n            event.state === State.ACTIVE\n          ) {\n            runWorklet(CALLBACK_TYPE.START, gesture, event);\n            lastUpdateEvent.value[gesture.handlerTag] = undefined;\n          } else if (\n            event.oldState !== event.state &&\n            event.state === State.END\n          ) {\n            if (event.oldState === State.ACTIVE) {\n              runWorklet(CALLBACK_TYPE.END, gesture, event, true);\n            }\n            runWorklet(CALLBACK_TYPE.FINALIZE, gesture, event, true);\n          } else if (\n            (event.state === State.FAILED || event.state === State.CANCELLED) &&\n            event.state !== event.oldState\n          ) {\n            if (event.oldState === State.ACTIVE) {\n              runWorklet(CALLBACK_TYPE.END, gesture, event, false);\n            }\n            runWorklet(CALLBACK_TYPE.FINALIZE, gesture, event, false);\n          }\n        } else if (isTouchEvent(event)) {\n          if (!stateControllers[i]) {\n            stateControllers[i] = GestureStateManager.create(event.handlerTag);\n          }\n\n          if (event.eventType !== TouchEventType.UNDETERMINED) {\n            runWorklet(\n              touchEventTypeToCallbackType(event.eventType),\n              gesture,\n              event,\n              stateControllers[i]\n            );\n          }\n        } else {\n          runWorklet(CALLBACK_TYPE.UPDATE, gesture, event);\n\n          if (gesture.onChange && gesture.changeEventCalculator) {\n            runWorklet(\n              CALLBACK_TYPE.CHANGE,\n              gesture,\n              gesture.changeEventCalculator?.(\n                event,\n                lastUpdateEvent.value[gesture.handlerTag]\n              )\n            );\n\n            lastUpdateEvent.value[gesture.handlerTag] = event;\n          }\n        }\n      }\n    }\n  };\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const event = Reanimated.useEvent(\n    callback,\n    ['onGestureHandlerStateChange', 'onGestureHandlerEvent'],\n    needsRebuild\n  );\n\n  preparedGesture.animatedEventHandler = event;\n  preparedGesture.animatedHandlers = sharedHandlersCallbacks;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction validateDetectorChildren(ref: any) {\n  // finds the first native view under the Wrap component and traverses the fiber tree upwards\n  // to check whether there is more than one native view as a pseudo-direct child of GestureDetector\n  // i.e. this is not ok:\n  //            Wrap\n  //             |\n  //            / \\\n  //           /   \\\n  //          /     \\\n  //         /       \\\n  //   NativeView  NativeView\n  //\n  // but this is fine:\n  //            Wrap\n  //             |\n  //         NativeView\n  //             |\n  //            / \\\n  //           /   \\\n  //          /     \\\n  //         /       \\\n  //   NativeView  NativeView\n  if (__DEV__ && Platform.OS !== 'web') {\n    const REACT_NATIVE_VERSION = getReactNativeVersion();\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n    const wrapType =\n      REACT_NATIVE_VERSION.minor > 63 || REACT_NATIVE_VERSION.major > 0\n        ? // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          ref._reactInternals.elementType\n        : // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          ref._reactInternalFiber.elementType;\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n    let instance =\n      RNRenderer.findHostInstance_DEPRECATED(\n        ref\n      )._internalFiberInstanceHandleDEV;\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    while (instance && instance.elementType !== wrapType) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      if (instance.sibling) {\n        throw new Error(\n          'GestureDetector has more than one native view as its children. This can happen if you are using a custom component that renders multiple views, like React.Fragment. You should wrap content of GestureDetector with a <View> or <Animated.View>.'\n        );\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n      instance = instance.return;\n    }\n  }\n}\n\nconst applyUserSelectProp = (\n  userSelect: UserSelect,\n  gesture: ComposedGesture | GestureType\n): void => {\n  for (const g of gesture.toGestureArray()) {\n    g.config.userSelect = userSelect;\n  }\n};\n\ninterface GestureDetectorProps {\n  gesture: ComposedGesture | GestureType;\n  userSelect?: UserSelect;\n  children?: React.ReactNode;\n}\ninterface GestureDetectorState {\n  firstRender: boolean;\n  viewRef: React.Component | null;\n  previousViewTag: number;\n  forceReattach: boolean;\n}\nexport const GestureDetector = (props: GestureDetectorProps) => {\n  const rootViewContext = useContext(GestureHandlerRootViewContext);\n  if (__DEV__ && !rootViewContext && !isJestEnv() && Platform.OS !== 'web') {\n    throw new Error(\n      'GestureDetector must be used as a descendant of GestureHandlerRootView. Otherwise the gestures will not be recognized. See https://docs.swmansion.com/react-native-gesture-handler/docs/installation for more details.'\n    );\n  }\n\n  const gestureConfig = props.gesture;\n\n  if (props.userSelect) {\n    applyUserSelectProp(props.userSelect, gestureConfig);\n  }\n\n  const gesture = gestureConfig.toGestureArray();\n  const useReanimatedHook = gesture.some((g) => g.shouldUseReanimated);\n\n  // store state in ref to prevent unnecessary renders\n  const state = useRef<GestureDetectorState>({\n    firstRender: true,\n    viewRef: null,\n    previousViewTag: -1,\n    forceReattach: false,\n  }).current;\n  const mountedRef = useRef(false);\n  const webEventHandlersRef = useRef<WebEventHandler>({\n    onGestureHandlerEvent: (e: HandlerStateChangeEvent<unknown>) => {\n      onGestureHandlerEvent(e.nativeEvent);\n    },\n    onGestureHandlerStateChange: isNewWebImplementationEnabled()\n      ? (e: HandlerStateChangeEvent<unknown>) => {\n          onGestureHandlerEvent(e.nativeEvent);\n        }\n      : undefined,\n  });\n\n  const [renderState, setRenderState] = useState(false);\n  function forceRender() {\n    setRenderState(!renderState);\n  }\n\n  const preparedGesture = React.useRef<GestureConfigReference>({\n    config: gesture,\n    animatedEventHandler: null,\n    animatedHandlers: null,\n    firstExecution: true,\n    useReanimatedHook: useReanimatedHook,\n  }).current;\n\n  if (useReanimatedHook !== preparedGesture.useReanimatedHook) {\n    throw new Error(\n      tagMessage(\n        'You cannot change the thread the callbacks are ran on while the app is running'\n      )\n    );\n  }\n\n  function onHandlersUpdate(skipConfigUpdate?: boolean) {\n    // if the underlying view has changed we need to reattach handlers to the new view\n    const viewTag = findNodeHandle(state.viewRef) as number;\n    const forceReattach = viewTag !== state.previousViewTag;\n\n    if (forceReattach || needsToReattach(preparedGesture, gesture)) {\n      validateDetectorChildren(state.viewRef);\n      dropHandlers(preparedGesture);\n      attachHandlers({\n        preparedGesture,\n        gestureConfig,\n        gesture,\n        webEventHandlersRef,\n        viewTag,\n        mountedRef,\n      });\n\n      state.previousViewTag = viewTag;\n      state.forceReattach = forceReattach;\n      if (forceReattach) {\n        forceRender();\n      }\n    } else if (!skipConfigUpdate) {\n      updateHandlers(preparedGesture, gestureConfig, gesture, mountedRef);\n    }\n  }\n\n  // Reanimated event should be rebuilt only when gestures are reattached, otherwise\n  // config update will be enough as all necessary items are stored in shared values anyway\n  const needsToRebuildReanimatedEvent =\n    preparedGesture.firstExecution ||\n    needsToReattach(preparedGesture, gesture) ||\n    state.forceReattach;\n\n  state.forceReattach = false;\n\n  if (preparedGesture.firstExecution) {\n    gestureConfig.initialize();\n  }\n\n  if (useReanimatedHook) {\n    // Whether animatedGesture or gesture is used shouldn't change while the app is running\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useAnimatedGesture(preparedGesture, needsToRebuildReanimatedEvent);\n  }\n\n  useEffect(() => {\n    const viewTag = findNodeHandle(state.viewRef) as number;\n    state.firstRender = true;\n    mountedRef.current = true;\n\n    validateDetectorChildren(state.viewRef);\n\n    attachHandlers({\n      preparedGesture,\n      gestureConfig,\n      gesture,\n      webEventHandlersRef,\n      viewTag,\n      mountedRef,\n    });\n\n    return () => {\n      mountedRef.current = false;\n      dropHandlers(preparedGesture);\n    };\n  }, []);\n\n  useEffect(() => {\n    if (!state.firstRender) {\n      onHandlersUpdate();\n    } else {\n      state.firstRender = false;\n    }\n  }, [props]);\n\n  const refFunction = (ref: unknown) => {\n    if (ref !== null) {\n      // @ts-ignore Just setting the view ref\n      state.viewRef = ref;\n\n      // if it's the first render, also set the previousViewTag to prevent reattaching gestures when not needed\n      if (state.previousViewTag === -1) {\n        state.previousViewTag = findNodeHandle(state.viewRef) as number;\n      }\n\n      // pass true as `skipConfigUpdate`, here we only want to trigger the eventual reattaching of handlers\n      // in case the view has changed, while config update would be handled be the `useEffect` above\n      onHandlersUpdate(true);\n\n      if (isFabric()) {\n        const node = getShadowNodeFromRef(ref);\n        if (global.isFormsStackingContext(node) === false) {\n          console.error(\n            tagMessage(\n              'GestureDetector has received a child that may get view-flattened. ' +\n                '\\nTo prevent it from misbehaving you need to wrap the child with a `<View collapsable={false}>`.'\n            )\n          );\n        }\n      }\n    }\n  };\n\n  if (useReanimatedHook) {\n    return (\n      <AnimatedWrap\n        ref={refFunction}\n        onGestureHandlerEvent={preparedGesture.animatedEventHandler}>\n        {props.children}\n      </AnimatedWrap>\n    );\n  } else {\n    return <Wrap ref={refFunction}>{props.children}</Wrap>;\n  }\n};\n\nclass Wrap extends React.Component<{\n  onGestureHandlerEvent?: unknown;\n  // implicit `children` prop has been removed in @types/react^18.0.0\n  children?: React.ReactNode;\n}> {\n  render() {\n    try {\n      // I don't think that fighting with types over such a simple function is worth it\n      // The only thing it does is add 'collapsable: false' to the child component\n      // to make sure it is in the native view hierarchy so the detector can find\n      // correct viewTag to attach to.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const child: any = React.Children.only(this.props.children);\n      return React.cloneElement(\n        child,\n        { collapsable: false },\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        child.props.children\n      );\n    } catch (e) {\n      throw new Error(\n        tagMessage(\n          `GestureDetector got more than one view as a child. If you want the gesture to work on multiple views, wrap them with a common parent and attach the gesture to that view.`\n        )\n      );\n    }\n  }\n}\n\nconst AnimatedWrap = Reanimated?.default?.createAnimatedComponent(Wrap) ?? Wrap;\n"]}