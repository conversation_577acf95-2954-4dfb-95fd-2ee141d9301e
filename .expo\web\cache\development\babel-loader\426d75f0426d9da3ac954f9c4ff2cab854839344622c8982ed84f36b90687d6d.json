{"ast": null, "code": "import { CodedError } from 'expo-modules-core';\nimport ExpoFontLoader from './ExpoFontLoader';\nimport { getAssetForSource, loadSingleFontAsync } from './FontLoader';\nexport function getServerResources() {\n  return ExpoFontLoader.getServerResources();\n}\nexport function resetServerContext() {\n  return ExpoFontLoader.resetServerContext();\n}\nexport function registerStaticFont(fontFamily, source) {\n  if (!source) {\n    throw new CodedError(`ERR_FONT_SOURCE`, `Cannot load null or undefined font source: { \"${fontFamily}\": ${source} }. Expected asset of type \\`FontSource\\` for fontFamily of name: \"${fontFamily}\"`);\n  }\n  var asset = getAssetForSource(source);\n  loadSingleFontAsync(fontFamily, asset);\n}", "map": {"version": 3, "names": ["CodedError", "ExpoFontLoader", "getAssetForSource", "loadSingleFontAsync", "getServerResources", "resetServerContext", "registerStaticFont", "fontFamily", "source", "asset"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-font\\src\\server.ts"], "sourcesContent": ["import { CodedError } from 'expo-modules-core';\n\nimport ExpoFontLoader from './ExpoFontLoader';\nimport { FontSource } from './Font.types';\nimport { getAssetForSource, loadSingleFontAsync } from './FontLoader';\n\n/**\n * @returns the server resources that should be statically extracted.\n * @private\n */\nexport function getServerResources(): string[] {\n  return ExpoFontLoader.getServerResources();\n}\n\n/**\n * @returns clear the server resources from the global scope.\n * @private\n */\nexport function resetServerContext() {\n  return ExpoFontLoader.resetServerContext();\n}\n\nexport function registerStaticFont(fontFamily: string, source?: FontSource | null) {\n  // MUST BE A SYNC FUNCTION!\n  if (!source) {\n    throw new CodedError(\n      `ERR_FONT_SOURCE`,\n      `Cannot load null or undefined font source: { \"${fontFamily}\": ${source} }. Expected asset of type \\`FontSource\\` for fontFamily of name: \"${fontFamily}\"`\n    );\n  }\n  const asset = getAssetForSource(source);\n\n  loadSingleFontAsync(fontFamily, asset);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAE9C,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,SAASC,iBAAiB,EAAEC,mBAAmB,QAAQ,cAAc;AAMrE,OAAM,SAAUC,kBAAkBA,CAAA;EAChC,OAAOH,cAAc,CAACG,kBAAkB,EAAE;AAC5C;AAMA,OAAM,SAAUC,kBAAkBA,CAAA;EAChC,OAAOJ,cAAc,CAACI,kBAAkB,EAAE;AAC5C;AAEA,OAAM,SAAUC,kBAAkBA,CAACC,UAAkB,EAAEC,MAA0B;EAE/E,IAAI,CAACA,MAAM,EAAE;IACX,MAAM,IAAIR,UAAU,CAClB,iBAAiB,EACjB,iDAAiDO,UAAU,MAAMC,MAAM,sEAAsED,UAAU,GAAG,CAC3J;;EAEH,IAAME,KAAK,GAAGP,iBAAiB,CAACM,MAAM,CAAC;EAEvCL,mBAAmB,CAACI,UAAU,EAAEE,KAAK,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}