{"version": 3, "sources": ["GestureButtons.tsx"], "names": ["React", "Animated", "Platform", "processColor", "StyleSheet", "createNativeWrapper", "GestureHandlerButton", "State", "RawButton", "shouldCancelWhenOutside", "shouldActivateOnStart", "BaseButton", "Component", "constructor", "props", "nativeEvent", "state", "oldState", "pointerInside", "active", "ACTIVE", "lastActive", "onActiveStateChange", "longPressDetected", "CANCELLED", "onPress", "OS", "BEGAN", "onLongPress", "longPressTimeout", "setTimeout", "delayLongPress", "undefined", "clearTimeout", "END", "FAILED", "e", "onHandlerStateChange", "handleEvent", "onGestureEvent", "render", "rippleColor", "rest", "AnimatedBaseButton", "createAnimatedComponent", "btnStyles", "create", "underlay", "position", "left", "right", "bottom", "top", "RectButton", "opacity", "setValue", "activeOpacity", "Value", "children", "style", "resolvedStyle", "flatten", "backgroundColor", "underlayColor", "borderRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "BorderlessButton", "borderless", "default", "PureNativeButton"], "mappings": ";;;;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,SACEC,QADF,EAEEC,QAFF,EAGEC,YAHF,EAIEC,UAJF,QAOO,cAPP;AASA,OAAOC,mBAAP,MAAgC,iCAAhC;AACA,OAAOC,oBAAP,MAAiC,wBAAjC;AACA,SAASC,KAAT,QAAsB,UAAtB;AA2GA,OAAO,MAAMC,SAAS,GAAGH,mBAAmB,CAACC,oBAAD,EAAuB;AACjEG,EAAAA,uBAAuB,EAAE,KADwC;AAEjEC,EAAAA,qBAAqB,EAAE;AAF0C,CAAvB,CAArC;AAKP,OAAO,MAAMC,UAAN,SAAyBX,KAAK,CAACY,SAA/B,CAA0D;AAS/DC,EAAAA,WAAW,CAACC,KAAD,EAAyB;AAClC,UAAMA,KAAN;;AADkC;;AAAA;;AAAA;;AAAA,yCAMd,CAAC;AACrBC,MAAAA;AADqB,KAAD,KAE0C;AAC9D,YAAM;AAAEC,QAAAA,KAAF;AAASC,QAAAA,QAAT;AAAmBC,QAAAA;AAAnB,UAAqCH,WAA3C;AACA,YAAMI,MAAM,GAAGD,aAAa,IAAIF,KAAK,KAAKT,KAAK,CAACa,MAAhD;;AAEA,UAAID,MAAM,KAAK,KAAKE,UAAhB,IAA8B,KAAKP,KAAL,CAAWQ,mBAA7C,EAAkE;AAChE,aAAKR,KAAL,CAAWQ,mBAAX,CAA+BH,MAA/B;AACD;;AAED,UACE,CAAC,KAAKI,iBAAN,IACAN,QAAQ,KAAKV,KAAK,CAACa,MADnB,IAEAJ,KAAK,KAAKT,KAAK,CAACiB,SAFhB,IAGA,KAAKH,UAHL,IAIA,KAAKP,KAAL,CAAWW,OALb,EAME;AACA,aAAKX,KAAL,CAAWW,OAAX,CAAmBN,MAAnB;AACD;;AAED,UACE,CAAC,KAAKE,UAAN,IACA;AACAL,MAAAA,KAAK,MAAMd,QAAQ,CAACwB,EAAT,KAAgB,SAAhB,GAA4BnB,KAAK,CAACa,MAAlC,GAA2Cb,KAAK,CAACoB,KAAvD,CAFL,IAGAT,aAJF,EAKE;AACA,aAAKK,iBAAL,GAAyB,KAAzB;;AACA,YAAI,KAAKT,KAAL,CAAWc,WAAf,EAA4B;AAC1B,eAAKC,gBAAL,GAAwBC,UAAU,CAChC,KAAKF,WAD2B,EAEhC,KAAKd,KAAL,CAAWiB,cAFqB,CAAlC;AAID;AACF,OAbD,MAaO,KACL;AACAf,MAAAA,KAAK,KAAKT,KAAK,CAACa,MAAhB,IACA,CAACF,aADD,IAEA,KAAKW,gBAAL,KAA0BG,SAJrB,EAKL;AACAC,QAAAA,YAAY,CAAC,KAAKJ,gBAAN,CAAZ;AACA,aAAKA,gBAAL,GAAwBG,SAAxB;AACD,OARM,MAQA,KACL;AACA,WAAKH,gBAAL,KAA0BG,SAA1B,KACChB,KAAK,KAAKT,KAAK,CAAC2B,GAAhB,IACClB,KAAK,KAAKT,KAAK,CAACiB,SADjB,IAECR,KAAK,KAAKT,KAAK,CAAC4B,MAHlB,CAFK,EAML;AACAF,QAAAA,YAAY,CAAC,KAAKJ,gBAAN,CAAZ;AACA,aAAKA,gBAAL,GAAwBG,SAAxB;AACD;;AAED,WAAKX,UAAL,GAAkBF,MAAlB;AACD,KA3DmC;;AAAA,yCA6Dd,MAAM;AAAA;;AAC1B,WAAKI,iBAAL,GAAyB,IAAzB;AACA,mDAAKT,KAAL,EAAWc,WAAX;AACD,KAhEmC;;AAAA,kDAuElCQ,CAD6B,IAE1B;AAAA;;AACH,oDAAKtB,KAAL,EAAWuB,oBAAX,mGAAkCD,CAAlC;AACA,WAAKE,WAAL,CAAiBF,CAAjB;AACD,KA3EmC;;AAAA,4CA8ElCA,CADuB,IAEpB;AAAA;;AACH,oDAAKtB,KAAL,EAAWyB,cAAX,mGAA4BH,CAA5B;AACA,WAAKE,WAAL,CACEF,CADF,EAFG,CAIA;AACJ,KApFmC;;AAElC,SAAKf,UAAL,GAAkB,KAAlB;AACA,SAAKE,iBAAL,GAAyB,KAAzB;AACD;;AAkFDiB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEC,MAAAA,WAAF;AAAe,SAAGC;AAAlB,QAA2B,KAAK5B,KAAtC;AAEA,wBACE,oBAAC,SAAD;AACE,MAAA,WAAW,EAAEX,YAAY,CAACsC,WAAD;AAD3B,OAEMC,IAFN;AAGE,MAAA,cAAc,EAAE,KAAKH,cAHvB;AAIE,MAAA,oBAAoB,EAAE,KAAKF;AAJ7B,OADF;AAQD;;AA1G8D;;gBAApD1B,U,kBACW;AACpBoB,EAAAA,cAAc,EAAE;AADI,C;;AA4GxB,MAAMY,kBAAkB,GAAG1C,QAAQ,CAAC2C,uBAAT,CAAiCjC,UAAjC,CAA3B;AAEA,MAAMkC,SAAS,GAAGzC,UAAU,CAAC0C,MAAX,CAAkB;AAClCC,EAAAA,QAAQ,EAAE;AACRC,IAAAA,QAAQ,EAAE,UADF;AAERC,IAAAA,IAAI,EAAE,CAFE;AAGRC,IAAAA,KAAK,EAAE,CAHC;AAIRC,IAAAA,MAAM,EAAE,CAJA;AAKRC,IAAAA,GAAG,EAAE;AALG;AADwB,CAAlB,CAAlB;AAUA,OAAO,MAAMC,UAAN,SAAyBrD,KAAK,CAACY,SAA/B,CAA0D;AAQ/DC,EAAAA,WAAW,CAACC,KAAD,EAAyB;AAClC,UAAMA,KAAN;;AADkC;;AAAA,iDAKLK,MAAD,IAAqB;AAAA;;AACjD,UAAIjB,QAAQ,CAACwB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,aAAK4B,OAAL,CAAaC,QAAb,CAAsBpC,MAAM,GAAG,KAAKL,KAAL,CAAW0C,aAAd,GAA+B,CAA3D;AACD;;AAED,oDAAK1C,KAAL,EAAWQ,mBAAX,mGAAiCH,MAAjC;AACD,KAXmC;;AAElC,SAAKmC,OAAL,GAAe,IAAIrD,QAAQ,CAACwD,KAAb,CAAmB,CAAnB,CAAf;AACD;;AAUDjB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEkB,MAAAA,QAAF;AAAYC,MAAAA,KAAZ;AAAmB,SAAGjB;AAAtB,QAA+B,KAAK5B,KAA1C;AAEA,UAAM8C,aAAa,GAAGxD,UAAU,CAACyD,OAAX,CAAmBF,KAAnB,aAAmBA,KAAnB,cAAmBA,KAAnB,GAA4B,EAA5B,CAAtB;AAEA,wBACE,oBAAC,UAAD,eACMjB,IADN;AAEE,MAAA,KAAK,EAAEkB,aAFT;AAGE,MAAA,mBAAmB,EAAE,KAAKtC;AAH5B,qBAIE,oBAAC,QAAD,CAAU,IAAV;AACE,MAAA,KAAK,EAAE,CACLuB,SAAS,CAACE,QADL,EAEL;AACEO,QAAAA,OAAO,EAAE,KAAKA,OADhB;AAEEQ,QAAAA,eAAe,EAAE,KAAKhD,KAAL,CAAWiD,aAF9B;AAGEC,QAAAA,YAAY,EAAEJ,aAAa,CAACI,YAH9B;AAIEC,QAAAA,mBAAmB,EAAEL,aAAa,CAACK,mBAJrC;AAKEC,QAAAA,oBAAoB,EAAEN,aAAa,CAACM,oBALtC;AAMEC,QAAAA,sBAAsB,EAAEP,aAAa,CAACO,sBANxC;AAOEC,QAAAA,uBAAuB,EAAER,aAAa,CAACQ;AAPzC,OAFK;AADT,MAJF,EAkBGV,QAlBH,CADF;AAsBD;;AAhD8D;;gBAApDL,U,kBACW;AACpBG,EAAAA,aAAa,EAAE,KADK;AAEpBO,EAAAA,aAAa,EAAE;AAFK,C;;AAkDxB,OAAO,MAAMM,gBAAN,SAA+BrE,KAAK,CAACY,SAArC,CAAsE;AAQ3EC,EAAAA,WAAW,CAACC,KAAD,EAA+B;AACxC,UAAMA,KAAN;;AADwC;;AAAA,iDAKXK,MAAD,IAAqB;AAAA;;AACjD,UAAIjB,QAAQ,CAACwB,EAAT,KAAgB,SAApB,EAA+B;AAC7B,aAAK4B,OAAL,CAAaC,QAAb,CAAsBpC,MAAM,GAAG,KAAKL,KAAL,CAAW0C,aAAd,GAA+B,CAA3D;AACD;;AAED,qDAAK1C,KAAL,EAAWQ,mBAAX,qGAAiCH,MAAjC;AACD,KAXyC;;AAExC,SAAKmC,OAAL,GAAe,IAAIrD,QAAQ,CAACwD,KAAb,CAAmB,CAAnB,CAAf;AACD;;AAUDjB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEkB,MAAAA,QAAF;AAAYC,MAAAA,KAAZ;AAAmB,SAAGjB;AAAtB,QAA+B,KAAK5B,KAA1C;AAEA,wBACE,oBAAC,kBAAD,eACM4B,IADN;AAEE,MAAA,mBAAmB,EAAE,KAAKpB,mBAF5B;AAGE,MAAA,KAAK,EAAE,CAACqC,KAAD,EAAQzD,QAAQ,CAACwB,EAAT,KAAgB,KAAhB,IAAyB;AAAE4B,QAAAA,OAAO,EAAE,KAAKA;AAAhB,OAAjC;AAHT,QAIGI,QAJH,CADF;AAQD;;AAhC0E;;gBAAhEW,gB,kBACW;AACpBb,EAAAA,aAAa,EAAE,GADK;AAEpBc,EAAAA,UAAU,EAAE;AAFQ,C;;AAkCxB,SAASC,OAAO,IAAIC,gBAApB,QAA4C,wBAA5C", "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  Platform,\n  processColor,\n  StyleSheet,\n  StyleProp,\n  ViewStyle,\n} from 'react-native';\n\nimport createNativeWrapper from '../handlers/createNativeWrapper';\nimport GestureHandlerButton from './GestureHandlerButton';\nimport { State } from '../State';\n\nimport {\n  GestureEvent,\n  HandlerStateChangeEvent,\n} from '../handlers/gestureHandlerCommon';\nimport {\n  NativeViewGestureHandlerPayload,\n  NativeViewGestureHandlerProps,\n} from '../handlers/NativeViewGestureHandler';\n\nexport interface RawButtonProps extends NativeViewGestureHandlerProps {\n  /**\n   * Defines if more than one button could be pressed simultaneously. By default\n   * set true.\n   */\n  exclusive?: boolean;\n  // TODO: we should transform props in `createNativeWrapper`\n\n  /**\n   * Android only.\n   *\n   * Defines color of native ripple animation used since API level 21.\n   */\n  rippleColor?: any; // it was present in BaseButtonProps before but is used here in code\n\n  /**\n   * Android only.\n   *\n   * Defines radius of native ripple animation used since API level 21.\n   */\n  rippleRadius?: number | null;\n\n  /**\n   * Android only.\n   *\n   * Set this to true if you want the ripple animation to render outside the view bounds.\n   */\n  borderless?: boolean;\n\n  /**\n   * Android only.\n   *\n   * Defines whether the ripple animation should be drawn on the foreground of the view.\n   */\n  foreground?: boolean;\n\n  /**\n   * Android only.\n   *\n   * Set this to true if you don't want the system to play sound when the button is pressed.\n   */\n  touchSoundDisabled?: boolean;\n}\n\nexport interface BaseButtonProps extends RawButtonProps {\n  /**\n   * Called when the button gets pressed (analogous to `onPress` in\n   * `TouchableHighlight` from RN core).\n   */\n  onPress?: (pointerInside: boolean) => void;\n\n  /**\n   * Called when the button gets pressed and is held for `delayLongPress`\n   * milliseconds.\n   */\n  onLongPress?: () => void;\n\n  /**\n   * Called when button changes from inactive to active and vice versa. It\n   * passes active state as a boolean variable as a first parameter for that\n   * method.\n   */\n  onActiveStateChange?: (active: boolean) => void;\n  style?: StyleProp<ViewStyle>;\n  testID?: string;\n\n  /**\n   * Delay, in milliseconds, after which the `onLongPress` callback gets called.\n   * Defaults to 600.\n   */\n  delayLongPress?: number;\n}\n\nexport interface RectButtonProps extends BaseButtonProps {\n  /**\n   * Background color that will be dimmed when button is in active state.\n   */\n  underlayColor?: string;\n\n  /**\n   * iOS only.\n   *\n   * Opacity applied to the underlay when button is in active state.\n   */\n  activeOpacity?: number;\n}\n\nexport interface BorderlessButtonProps extends BaseButtonProps {\n  /**\n   * iOS only.\n   *\n   * Opacity applied to the button when it is in an active state.\n   */\n  activeOpacity?: number;\n}\n\nexport const RawButton = createNativeWrapper(GestureHandlerButton, {\n  shouldCancelWhenOutside: false,\n  shouldActivateOnStart: false,\n});\n\nexport class BaseButton extends React.Component<BaseButtonProps> {\n  static defaultProps = {\n    delayLongPress: 600,\n  };\n\n  private lastActive: boolean;\n  private longPressTimeout: ReturnType<typeof setTimeout> | undefined;\n  private longPressDetected: boolean;\n\n  constructor(props: BaseButtonProps) {\n    super(props);\n    this.lastActive = false;\n    this.longPressDetected = false;\n  }\n\n  private handleEvent = ({\n    nativeEvent,\n  }: HandlerStateChangeEvent<NativeViewGestureHandlerPayload>) => {\n    const { state, oldState, pointerInside } = nativeEvent;\n    const active = pointerInside && state === State.ACTIVE;\n\n    if (active !== this.lastActive && this.props.onActiveStateChange) {\n      this.props.onActiveStateChange(active);\n    }\n\n    if (\n      !this.longPressDetected &&\n      oldState === State.ACTIVE &&\n      state !== State.CANCELLED &&\n      this.lastActive &&\n      this.props.onPress\n    ) {\n      this.props.onPress(active);\n    }\n\n    if (\n      !this.lastActive &&\n      // NativeViewGestureHandler sends different events based on platform\n      state === (Platform.OS !== 'android' ? State.ACTIVE : State.BEGAN) &&\n      pointerInside\n    ) {\n      this.longPressDetected = false;\n      if (this.props.onLongPress) {\n        this.longPressTimeout = setTimeout(\n          this.onLongPress,\n          this.props.delayLongPress\n        );\n      }\n    } else if (\n      // cancel longpress timeout if it's set and the finger moved out of the view\n      state === State.ACTIVE &&\n      !pointerInside &&\n      this.longPressTimeout !== undefined\n    ) {\n      clearTimeout(this.longPressTimeout);\n      this.longPressTimeout = undefined;\n    } else if (\n      // cancel longpress timeout if it's set and the gesture has finished\n      this.longPressTimeout !== undefined &&\n      (state === State.END ||\n        state === State.CANCELLED ||\n        state === State.FAILED)\n    ) {\n      clearTimeout(this.longPressTimeout);\n      this.longPressTimeout = undefined;\n    }\n\n    this.lastActive = active;\n  };\n\n  private onLongPress = () => {\n    this.longPressDetected = true;\n    this.props.onLongPress?.();\n  };\n\n  // Normally, the parent would execute it's handler first, then forward the\n  // event to listeners. However, here our handler is virtually only forwarding\n  // events to listeners, so we reverse the order to keep the proper order of\n  // the callbacks (from \"raw\" ones to \"processed\").\n  private onHandlerStateChange = (\n    e: HandlerStateChangeEvent<NativeViewGestureHandlerPayload>\n  ) => {\n    this.props.onHandlerStateChange?.(e);\n    this.handleEvent(e);\n  };\n\n  private onGestureEvent = (\n    e: GestureEvent<NativeViewGestureHandlerPayload>\n  ) => {\n    this.props.onGestureEvent?.(e);\n    this.handleEvent(\n      e as HandlerStateChangeEvent<NativeViewGestureHandlerPayload>\n    ); // TODO: maybe it is not correct\n  };\n\n  render() {\n    const { rippleColor, ...rest } = this.props;\n\n    return (\n      <RawButton\n        rippleColor={processColor(rippleColor)}\n        {...rest}\n        onGestureEvent={this.onGestureEvent}\n        onHandlerStateChange={this.onHandlerStateChange}\n      />\n    );\n  }\n}\n\nconst AnimatedBaseButton = Animated.createAnimatedComponent(BaseButton);\n\nconst btnStyles = StyleSheet.create({\n  underlay: {\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    bottom: 0,\n    top: 0,\n  },\n});\n\nexport class RectButton extends React.Component<RectButtonProps> {\n  static defaultProps = {\n    activeOpacity: 0.105,\n    underlayColor: 'black',\n  };\n\n  private opacity: Animated.Value;\n\n  constructor(props: RectButtonProps) {\n    super(props);\n    this.opacity = new Animated.Value(0);\n  }\n\n  private onActiveStateChange = (active: boolean) => {\n    if (Platform.OS !== 'android') {\n      this.opacity.setValue(active ? this.props.activeOpacity! : 0);\n    }\n\n    this.props.onActiveStateChange?.(active);\n  };\n\n  render() {\n    const { children, style, ...rest } = this.props;\n\n    const resolvedStyle = StyleSheet.flatten(style ?? {});\n\n    return (\n      <BaseButton\n        {...rest}\n        style={resolvedStyle}\n        onActiveStateChange={this.onActiveStateChange}>\n        <Animated.View\n          style={[\n            btnStyles.underlay,\n            {\n              opacity: this.opacity,\n              backgroundColor: this.props.underlayColor,\n              borderRadius: resolvedStyle.borderRadius,\n              borderTopLeftRadius: resolvedStyle.borderTopLeftRadius,\n              borderTopRightRadius: resolvedStyle.borderTopRightRadius,\n              borderBottomLeftRadius: resolvedStyle.borderBottomLeftRadius,\n              borderBottomRightRadius: resolvedStyle.borderBottomRightRadius,\n            },\n          ]}\n        />\n        {children}\n      </BaseButton>\n    );\n  }\n}\n\nexport class BorderlessButton extends React.Component<BorderlessButtonProps> {\n  static defaultProps = {\n    activeOpacity: 0.3,\n    borderless: true,\n  };\n\n  private opacity: Animated.Value;\n\n  constructor(props: BorderlessButtonProps) {\n    super(props);\n    this.opacity = new Animated.Value(1);\n  }\n\n  private onActiveStateChange = (active: boolean) => {\n    if (Platform.OS !== 'android') {\n      this.opacity.setValue(active ? this.props.activeOpacity! : 1);\n    }\n\n    this.props.onActiveStateChange?.(active);\n  };\n\n  render() {\n    const { children, style, ...rest } = this.props;\n\n    return (\n      <AnimatedBaseButton\n        {...rest}\n        onActiveStateChange={this.onActiveStateChange}\n        style={[style, Platform.OS === 'ios' && { opacity: this.opacity }]}>\n        {children}\n      </AnimatedBaseButton>\n    );\n  }\n}\n\nexport { default as PureNativeButton } from './GestureHandlerButton';\n"]}