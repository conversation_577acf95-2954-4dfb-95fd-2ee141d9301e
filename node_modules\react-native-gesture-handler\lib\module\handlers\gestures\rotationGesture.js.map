{"version": 3, "sources": ["rotationGesture.ts"], "names": ["ContinousBaseGesture", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "rotationChange", "rotation", "RotationGesture", "constructor", "handler<PERSON>ame", "onChange", "callback", "handlers"], "mappings": "AAAA,SAASA,oBAAT,QAAqC,WAArC;;AAQA,SAASC,qBAAT,CACEC,OADF,EAEEC,QAFF,EAGE;AACA;;AACA,MAAIC,aAAJ;;AACA,MAAID,QAAQ,KAAKE,SAAjB,EAA4B;AAC1BD,IAAAA,aAAa,GAAG;AACdE,MAAAA,cAAc,EAAEJ,OAAO,CAACK;AADV,KAAhB;AAGD,GAJD,MAIO;AACLH,IAAAA,aAAa,GAAG;AACdE,MAAAA,cAAc,EAAEJ,OAAO,CAACK,QAAR,GAAmBJ,QAAQ,CAACI;AAD9B,KAAhB;AAGD;;AAED,SAAO,EAAE,GAAGL,OAAL;AAAc,OAAGE;AAAjB,GAAP;AACD;;AAED,OAAO,MAAMI,eAAN,SAA8BR,oBAA9B,CAGL;AACAS,EAAAA,WAAW,GAAG;AACZ;AAEA,SAAKC,WAAL,GAAmB,wBAAnB;AACD;;AAEDC,EAAAA,QAAQ,CACNC,QADM,EAMN;AACA;AACA,SAAKC,QAAL,CAAcZ,qBAAd,GAAsCA,qBAAtC;AACA,WAAO,MAAMU,QAAN,CAAeC,QAAf,CAAP;AACD;;AAjBD", "sourcesContent": ["import { ContinousBaseGesture } from './gesture';\nimport { RotationGestureHandlerEventPayload } from '../RotationGestureHandler';\nimport { GestureUpdateEvent } from '../gestureHandlerCommon';\n\ntype RotationGestureChangeEventPayload = {\n  rotationChange: number;\n};\n\nfunction changeEventCalculator(\n  current: GestureUpdateEvent<RotationGestureHandlerEventPayload>,\n  previous?: GestureUpdateEvent<RotationGestureHandlerEventPayload>\n) {\n  'worklet';\n  let changePayload: RotationGestureChangeEventPayload;\n  if (previous === undefined) {\n    changePayload = {\n      rotationChange: current.rotation,\n    };\n  } else {\n    changePayload = {\n      rotationChange: current.rotation - previous.rotation,\n    };\n  }\n\n  return { ...current, ...changePayload };\n}\n\nexport class RotationGesture extends ContinousBaseGesture<\n  RotationGestureHandlerEventPayload,\n  RotationGestureChangeEventPayload\n> {\n  constructor() {\n    super();\n\n    this.handlerName = 'RotationGestureHandler';\n  }\n\n  onChange(\n    callback: (\n      event: GestureUpdateEvent<\n        RotationGestureHandlerEventPayload & RotationGestureChangeEventPayload\n      >\n    ) => void\n  ) {\n    // @ts-ignore TS being overprotective, RotationGestureHandlerEventPayload is Record\n    this.handlers.changeEventCalculator = changeEventCalculator;\n    return super.onChange(callback);\n  }\n}\n\nexport type RotationGestureType = InstanceType<typeof RotationGesture>;\n"]}