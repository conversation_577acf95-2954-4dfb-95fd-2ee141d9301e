{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport AnimatedValue from \"../nodes/AnimatedValue\";\nimport AnimatedValueXY from \"../nodes/AnimatedValueXY\";\nimport AnimatedInterpolation from \"../nodes/AnimatedInterpolation\";\nimport Easing from \"../../../../exports/Easing\";\nimport Animation from \"./Animation\";\nimport { shouldUseNativeDriver } from \"../NativeAnimatedHelper\";\nimport AnimatedColor from \"../nodes/AnimatedColor\";\nvar _easeInOut;\nfunction easeInOut() {\n  if (!_easeInOut) {\n    _easeInOut = Easing.inOut(Easing.ease);\n  }\n  return _easeInOut;\n}\nvar TimingAnimation = function (_Animation) {\n  function TimingAnimation(config) {\n    var _this;\n    _classCallCheck(this, TimingAnimation);\n    var _config$easing, _config$duration, _config$delay, _config$iterations, _config$isInteraction;\n    _this = _callSuper(this, TimingAnimation);\n    _this._toValue = config.toValue;\n    _this._easing = (_config$easing = config.easing) !== null && _config$easing !== void 0 ? _config$easing : easeInOut();\n    _this._duration = (_config$duration = config.duration) !== null && _config$duration !== void 0 ? _config$duration : 500;\n    _this._delay = (_config$delay = config.delay) !== null && _config$delay !== void 0 ? _config$delay : 0;\n    _this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;\n    _this._useNativeDriver = shouldUseNativeDriver(config);\n    _this._platformConfig = config.platformConfig;\n    _this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !_this._useNativeDriver;\n    return _this;\n  }\n  _inherits(TimingAnimation, _Animation);\n  return _createClass(TimingAnimation, [{\n    key: \"__getNativeAnimationConfig\",\n    value: function __getNativeAnimationConfig() {\n      var frameDuration = 1000.0 / 60.0;\n      var frames = [];\n      var numFrames = Math.round(this._duration / frameDuration);\n      for (var frame = 0; frame < numFrames; frame++) {\n        frames.push(this._easing(frame / numFrames));\n      }\n      frames.push(this._easing(1));\n      return {\n        type: 'frames',\n        frames: frames,\n        toValue: this._toValue,\n        iterations: this.__iterations,\n        platformConfig: this._platformConfig\n      };\n    }\n  }, {\n    key: \"start\",\n    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n      var _this2 = this;\n      this.__active = true;\n      this._fromValue = fromValue;\n      this._onUpdate = onUpdate;\n      this.__onEnd = onEnd;\n      var start = function start() {\n        if (_this2._duration === 0 && !_this2._useNativeDriver) {\n          _this2._onUpdate(_this2._toValue);\n          _this2.__debouncedOnEnd({\n            finished: true\n          });\n        } else {\n          _this2._startTime = Date.now();\n          if (_this2._useNativeDriver) {\n            _this2.__startNativeAnimation(animatedValue);\n          } else {\n            _this2._animationFrame = requestAnimationFrame(_this2.onUpdate.bind(_this2));\n          }\n        }\n      };\n      if (this._delay) {\n        this._timeout = setTimeout(start, this._delay);\n      } else {\n        start();\n      }\n    }\n  }, {\n    key: \"onUpdate\",\n    value: function onUpdate() {\n      var now = Date.now();\n      if (now >= this._startTime + this._duration) {\n        if (this._duration === 0) {\n          this._onUpdate(this._toValue);\n        } else {\n          this._onUpdate(this._fromValue + this._easing(1) * (this._toValue - this._fromValue));\n        }\n        this.__debouncedOnEnd({\n          finished: true\n        });\n        return;\n      }\n      this._onUpdate(this._fromValue + this._easing((now - this._startTime) / this._duration) * (this._toValue - this._fromValue));\n      if (this.__active) {\n        this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n      }\n    }\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      _superPropGet(TimingAnimation, \"stop\", this, 3)([]);\n      this.__active = false;\n      clearTimeout(this._timeout);\n      global.cancelAnimationFrame(this._animationFrame);\n      this.__debouncedOnEnd({\n        finished: false\n      });\n    }\n  }]);\n}(Animation);\nexport default TimingAnimation;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedValue", "AnimatedValueXY", "AnimatedInterpolation", "Easing", "Animation", "shouldUseNativeDriver", "AnimatedColor", "_easeInOut", "easeInOut", "inOut", "ease", "TimingAnimation", "_Animation", "config", "_this", "_config$easing", "_config$duration", "_config$delay", "_config$iterations", "_config$isInteraction", "_toValue", "toValue", "_easing", "easing", "_duration", "duration", "_delay", "delay", "__iterations", "iterations", "_useNativeDriver", "_platformConfig", "platformConfig", "__isInteraction", "isInteraction", "key", "value", "__getNativeAnimationConfig", "frameDuration", "frames", "numFrames", "Math", "round", "frame", "push", "type", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "_this2", "__active", "_fromValue", "_onUpdate", "__onEnd", "__debouncedOnEnd", "finished", "_startTime", "Date", "now", "__startNativeAnimation", "_animationFrame", "requestAnimationFrame", "bind", "_timeout", "setTimeout", "stop", "clearTimeout", "global", "cancelAnimationFrame"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/vendor/react-native/Animated/animations/TimingAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport AnimatedValue from '../nodes/AnimatedValue';\nimport AnimatedValueXY from '../nodes/AnimatedValueXY';\nimport AnimatedInterpolation from '../nodes/AnimatedInterpolation';\nimport Easing from '../../../../exports/Easing';\nimport Animation from './Animation';\nimport { shouldUseNativeDriver } from '../NativeAnimatedHelper';\nimport AnimatedColor from '../nodes/AnimatedColor';\nvar _easeInOut;\nfunction easeInOut() {\n  if (!_easeInOut) {\n    _easeInOut = Easing.inOut(Easing.ease);\n  }\n  return _easeInOut;\n}\nclass TimingAnimation extends Animation {\n  constructor(config) {\n    var _config$easing, _config$duration, _config$delay, _config$iterations, _config$isInteraction;\n    super();\n    this._toValue = config.toValue;\n    this._easing = (_config$easing = config.easing) !== null && _config$easing !== void 0 ? _config$easing : easeInOut();\n    this._duration = (_config$duration = config.duration) !== null && _config$duration !== void 0 ? _config$duration : 500;\n    this._delay = (_config$delay = config.delay) !== null && _config$delay !== void 0 ? _config$delay : 0;\n    this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;\n    this._useNativeDriver = shouldUseNativeDriver(config);\n    this._platformConfig = config.platformConfig;\n    this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !this._useNativeDriver;\n  }\n  __getNativeAnimationConfig() {\n    var frameDuration = 1000.0 / 60.0;\n    var frames = [];\n    var numFrames = Math.round(this._duration / frameDuration);\n    for (var frame = 0; frame < numFrames; frame++) {\n      frames.push(this._easing(frame / numFrames));\n    }\n    frames.push(this._easing(1));\n    return {\n      type: 'frames',\n      frames,\n      toValue: this._toValue,\n      iterations: this.__iterations,\n      platformConfig: this._platformConfig\n    };\n  }\n  start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n    this.__active = true;\n    this._fromValue = fromValue;\n    this._onUpdate = onUpdate;\n    this.__onEnd = onEnd;\n    var start = () => {\n      // Animations that sometimes have 0 duration and sometimes do not\n      // still need to use the native driver when duration is 0 so as to\n      // not cause intermixed JS and native animations.\n      if (this._duration === 0 && !this._useNativeDriver) {\n        this._onUpdate(this._toValue);\n        this.__debouncedOnEnd({\n          finished: true\n        });\n      } else {\n        this._startTime = Date.now();\n        if (this._useNativeDriver) {\n          this.__startNativeAnimation(animatedValue);\n        } else {\n          this._animationFrame = requestAnimationFrame(\n          // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n          this.onUpdate.bind(this));\n        }\n      }\n    };\n    if (this._delay) {\n      this._timeout = setTimeout(start, this._delay);\n    } else {\n      start();\n    }\n  }\n  onUpdate() {\n    var now = Date.now();\n    if (now >= this._startTime + this._duration) {\n      if (this._duration === 0) {\n        this._onUpdate(this._toValue);\n      } else {\n        this._onUpdate(this._fromValue + this._easing(1) * (this._toValue - this._fromValue));\n      }\n      this.__debouncedOnEnd({\n        finished: true\n      });\n      return;\n    }\n    this._onUpdate(this._fromValue + this._easing((now - this._startTime) / this._duration) * (this._toValue - this._fromValue));\n    if (this.__active) {\n      // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n    }\n  }\n  stop() {\n    super.stop();\n    this.__active = false;\n    clearTimeout(this._timeout);\n    global.cancelAnimationFrame(this._animationFrame);\n    this.__debouncedOnEnd({\n      finished: false\n    });\n  }\n}\nexport default TimingAnimation;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,aAAa;AACpB,OAAOC,eAAe;AACtB,OAAOC,qBAAqB;AAC5B,OAAOC,MAAM;AACb,OAAOC,SAAS;AAChB,SAASC,qBAAqB;AAC9B,OAAOC,aAAa;AACpB,IAAIC,UAAU;AACd,SAASC,SAASA,CAAA,EAAG;EACnB,IAAI,CAACD,UAAU,EAAE;IACfA,UAAU,GAAGJ,MAAM,CAACM,KAAK,CAACN,MAAM,CAACO,IAAI,CAAC;EACxC;EACA,OAAOH,UAAU;AACnB;AAAC,IACKI,eAAe,aAAAC,UAAA;EACnB,SAAAD,gBAAYE,MAAM,EAAE;IAAA,IAAAC,KAAA;IAAApC,eAAA,OAAAiC,eAAA;IAClB,IAAII,cAAc,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,qBAAqB;IAC9FL,KAAA,GAAA9B,UAAA,OAAA2B,eAAA;IACAG,KAAA,CAAKM,QAAQ,GAAGP,MAAM,CAACQ,OAAO;IAC9BP,KAAA,CAAKQ,OAAO,GAAG,CAACP,cAAc,GAAGF,MAAM,CAACU,MAAM,MAAM,IAAI,IAAIR,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGP,SAAS,CAAC,CAAC;IACpHM,KAAA,CAAKU,SAAS,GAAG,CAACR,gBAAgB,GAAGH,MAAM,CAACY,QAAQ,MAAM,IAAI,IAAIT,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,GAAG;IACtHF,KAAA,CAAKY,MAAM,GAAG,CAACT,aAAa,GAAGJ,MAAM,CAACc,KAAK,MAAM,IAAI,IAAIV,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC;IACrGH,KAAA,CAAKc,YAAY,GAAG,CAACV,kBAAkB,GAAGL,MAAM,CAACgB,UAAU,MAAM,IAAI,IAAIX,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC;IAC/HJ,KAAA,CAAKgB,gBAAgB,GAAGzB,qBAAqB,CAACQ,MAAM,CAAC;IACrDC,KAAA,CAAKiB,eAAe,GAAGlB,MAAM,CAACmB,cAAc;IAC5ClB,KAAA,CAAKmB,eAAe,GAAG,CAACd,qBAAqB,GAAGN,MAAM,CAACqB,aAAa,MAAM,IAAI,IAAIf,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAACL,KAAA,CAAKgB,gBAAgB;IAAC,OAAAhB,KAAA;EACtK;EAAC/B,SAAA,CAAA4B,eAAA,EAAAC,UAAA;EAAA,OAAAjC,YAAA,CAAAgC,eAAA;IAAAwB,GAAA;IAAAC,KAAA,EACD,SAAAC,0BAA0BA,CAAA,EAAG;MAC3B,IAAIC,aAAa,GAAG,MAAM,GAAG,IAAI;MACjC,IAAIC,MAAM,GAAG,EAAE;MACf,IAAIC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAClB,SAAS,GAAGc,aAAa,CAAC;MAC1D,KAAK,IAAIK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,SAAS,EAAEG,KAAK,EAAE,EAAE;QAC9CJ,MAAM,CAACK,IAAI,CAAC,IAAI,CAACtB,OAAO,CAACqB,KAAK,GAAGH,SAAS,CAAC,CAAC;MAC9C;MACAD,MAAM,CAACK,IAAI,CAAC,IAAI,CAACtB,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5B,OAAO;QACLuB,IAAI,EAAE,QAAQ;QACdN,MAAM,EAANA,MAAM;QACNlB,OAAO,EAAE,IAAI,CAACD,QAAQ;QACtBS,UAAU,EAAE,IAAI,CAACD,YAAY;QAC7BI,cAAc,EAAE,IAAI,CAACD;MACvB,CAAC;IACH;EAAC;IAAAI,GAAA;IAAAC,KAAA,EACD,SAAAU,KAAKA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa,EAAE;MAAA,IAAAC,MAAA;MAClE,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,UAAU,GAAGP,SAAS;MAC3B,IAAI,CAACQ,SAAS,GAAGP,QAAQ;MACzB,IAAI,CAACQ,OAAO,GAAGP,KAAK;MACpB,IAAIH,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;QAIhB,IAAIM,MAAI,CAAC5B,SAAS,KAAK,CAAC,IAAI,CAAC4B,MAAI,CAACtB,gBAAgB,EAAE;UAClDsB,MAAI,CAACG,SAAS,CAACH,MAAI,CAAChC,QAAQ,CAAC;UAC7BgC,MAAI,CAACK,gBAAgB,CAAC;YACpBC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLN,MAAI,CAACO,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;UAC5B,IAAIT,MAAI,CAACtB,gBAAgB,EAAE;YACzBsB,MAAI,CAACU,sBAAsB,CAACX,aAAa,CAAC;UAC5C,CAAC,MAAM;YACLC,MAAI,CAACW,eAAe,GAAGC,qBAAqB,CAE5CZ,MAAI,CAACJ,QAAQ,CAACiB,IAAI,CAACb,MAAI,CAAC,CAAC;UAC3B;QACF;MACF,CAAC;MACD,IAAI,IAAI,CAAC1B,MAAM,EAAE;QACf,IAAI,CAACwC,QAAQ,GAAGC,UAAU,CAACrB,KAAK,EAAE,IAAI,CAACpB,MAAM,CAAC;MAChD,CAAC,MAAM;QACLoB,KAAK,CAAC,CAAC;MACT;IACF;EAAC;IAAAX,GAAA;IAAAC,KAAA,EACD,SAAAY,QAAQA,CAAA,EAAG;MACT,IAAIa,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACpB,IAAIA,GAAG,IAAI,IAAI,CAACF,UAAU,GAAG,IAAI,CAACnC,SAAS,EAAE;QAC3C,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC+B,SAAS,CAAC,IAAI,CAACnC,QAAQ,CAAC;QAC/B,CAAC,MAAM;UACL,IAAI,CAACmC,SAAS,CAAC,IAAI,CAACD,UAAU,GAAG,IAAI,CAAChC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAACF,QAAQ,GAAG,IAAI,CAACkC,UAAU,CAAC,CAAC;QACvF;QACA,IAAI,CAACG,gBAAgB,CAAC;UACpBC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;MACF;MACA,IAAI,CAACH,SAAS,CAAC,IAAI,CAACD,UAAU,GAAG,IAAI,CAAChC,OAAO,CAAC,CAACuC,GAAG,GAAG,IAAI,CAACF,UAAU,IAAI,IAAI,CAACnC,SAAS,CAAC,IAAI,IAAI,CAACJ,QAAQ,GAAG,IAAI,CAACkC,UAAU,CAAC,CAAC;MAC5H,IAAI,IAAI,CAACD,QAAQ,EAAE;QAEjB,IAAI,CAACU,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAAChB,QAAQ,CAACiB,IAAI,CAAC,IAAI,CAAC,CAAC;MACxE;IACF;EAAC;IAAA9B,GAAA;IAAAC,KAAA,EACD,SAAAgC,IAAIA,CAAA,EAAG;MACLvE,aAAA,CAAAc,eAAA;MACA,IAAI,CAAC0C,QAAQ,GAAG,KAAK;MACrBgB,YAAY,CAAC,IAAI,CAACH,QAAQ,CAAC;MAC3BI,MAAM,CAACC,oBAAoB,CAAC,IAAI,CAACR,eAAe,CAAC;MACjD,IAAI,CAACN,gBAAgB,CAAC;QACpBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EAAC;AAAA,EAvF2BtD,SAAS;AAyFvC,eAAeO,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}