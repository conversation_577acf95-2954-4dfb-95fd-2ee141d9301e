{"version": 3, "sources": ["ScaleGestureDetector.ts"], "names": ["ScaleGestureDetector", "constructor", "callbacks", "onScaleBegin", "onScale", "onScaleEnd", "spanSlop", "DEFAULT_TOUCH_SLOP", "minSpan", "onTouchEvent", "event", "tracker", "currentTime", "time", "action", "eventType", "numOfPointers", "getTrackedPointersCount", "streamComplete", "EventTypes", "UP", "ADDITIONAL_POINTER_UP", "CANCEL", "DOWN", "inProgress", "initialSpan", "config<PERSON><PERSON><PERSON>", "ADDITIONAL_POINTER_DOWN", "pointerUp", "ignoredPointer", "pointerId", "undefined", "div", "sumX", "getSumX", "sumY", "getSumY", "focusX", "focusY", "devSumX", "devSumY", "getData", "for<PERSON>ach", "value", "key", "Math", "abs", "lastX", "lastY", "devX", "devY", "spanX", "spanY", "span", "hypot", "wasInProgress", "prevSpan", "currentSpan", "prevTime", "MOVE", "getCurrentSpan", "getFocusX", "getFocusY", "getTimeDelta", "getScaleFactor"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAUe,MAAMA,oBAAN,CAA2D;AAoBjEC,EAAAA,WAAW,CAACC,SAAD,EAAkC;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,wCAL/B,KAK+B;;AAAA;;AAAA;;AAClD,SAAKC,YAAL,GAAoBD,SAAS,CAACC,YAA9B;AACA,SAAKC,OAAL,GAAeF,SAAS,CAACE,OAAzB;AACA,SAAKC,UAAL,GAAkBH,SAAS,CAACG,UAA5B;AAEA,SAAKC,QAAL,GAAgBC,gCAAqB,CAArC;AACA,SAAKC,OAAL,GAAe,CAAf;AACD;;AAEMC,EAAAA,YAAY,CAACC,KAAD,EAAsBC,OAAtB,EAAwD;AACzE,SAAKC,WAAL,GAAmBF,KAAK,CAACG,IAAzB;AAEA,UAAMC,MAAkB,GAAGJ,KAAK,CAACK,SAAjC;AACA,UAAMC,aAAa,GAAGL,OAAO,CAACM,uBAAR,EAAtB;AAEA,UAAMC,cAAuB,GAC3BJ,MAAM,KAAKK,uBAAWC,EAAtB,IACAN,MAAM,KAAKK,uBAAWE,qBADtB,IAEAP,MAAM,KAAKK,uBAAWG,MAHxB;;AAKA,QAAIR,MAAM,KAAKK,uBAAWI,IAAtB,IAA8BL,cAAlC,EAAkD;AAChD,UAAI,KAAKM,UAAT,EAAqB;AACnB,aAAKnB,UAAL,CAAgB,IAAhB;AACA,aAAKmB,UAAL,GAAkB,KAAlB;AACA,aAAKC,WAAL,GAAmB,CAAnB;AACD;;AAED,UAAIP,cAAJ,EAAoB;AAClB,eAAO,IAAP;AACD;AACF;;AAED,UAAMQ,aAAsB,GAC1BZ,MAAM,KAAKK,uBAAWI,IAAtB,IACAT,MAAM,KAAKK,uBAAWE,qBADtB,IAEAP,MAAM,KAAKK,uBAAWQ,uBAHxB;AAKA,UAAMC,SAAS,GAAGd,MAAM,KAAKK,uBAAWE,qBAAxC;AAEA,UAAMQ,cAAkC,GAAGD,SAAS,GAChDlB,KAAK,CAACoB,SAD0C,GAEhDC,SAFJ,CA9ByE,CAkCzE;;AAEA,UAAMC,GAAW,GAAGJ,SAAS,GAAGZ,aAAa,GAAG,CAAnB,GAAuBA,aAApD;AAEA,UAAMiB,IAAI,GAAGtB,OAAO,CAACuB,OAAR,CAAgBL,cAAhB,CAAb;AACA,UAAMM,IAAI,GAAGxB,OAAO,CAACyB,OAAR,CAAgBP,cAAhB,CAAb;AAEA,UAAMQ,MAAM,GAAGJ,IAAI,GAAGD,GAAtB;AACA,UAAMM,MAAM,GAAGH,IAAI,GAAGH,GAAtB,CA1CyE,CA4CzE;;AAEA,QAAIO,OAAO,GAAG,CAAd;AACA,QAAIC,OAAO,GAAG,CAAd;AAEA7B,IAAAA,OAAO,CAAC8B,OAAR,GAAkBC,OAAlB,CAA0B,CAACC,KAAD,EAAQC,GAAR,KAAgB;AACxC,UAAIA,GAAG,KAAKf,cAAZ,EAA4B;AAC1B;AACD;;AAEDU,MAAAA,OAAO,IAAIM,IAAI,CAACC,GAAL,CAASH,KAAK,CAACI,KAAN,GAAcV,MAAvB,CAAX;AACAG,MAAAA,OAAO,IAAIK,IAAI,CAACC,GAAL,CAASH,KAAK,CAACK,KAAN,GAAcV,MAAvB,CAAX;AACD,KAPD;AASA,UAAMW,IAAY,GAAGV,OAAO,GAAGP,GAA/B;AACA,UAAMkB,IAAY,GAAGV,OAAO,GAAGR,GAA/B;AAEA,UAAMmB,KAAa,GAAGF,IAAI,GAAG,CAA7B;AACA,UAAMG,KAAa,GAAGF,IAAI,GAAG,CAA7B;AAEA,UAAMG,IAAI,GAAGR,IAAI,CAACS,KAAL,CAAWH,KAAX,EAAkBC,KAAlB,CAAb,CAhEyE,CAkEzE;;AACA,UAAMG,aAAsB,GAAG,KAAK/B,UAApC;AACA,SAAKa,MAAL,GAAcA,MAAd;AACA,SAAKC,MAAL,GAAcA,MAAd;;AAEA,QAAI,KAAKd,UAAL,KAAoB6B,IAAI,GAAG,KAAK7C,OAAZ,IAAuBkB,aAA3C,CAAJ,EAA+D;AAC7D,WAAKrB,UAAL,CAAgB,IAAhB;AACA,WAAKmB,UAAL,GAAkB,KAAlB;AACA,WAAKC,WAAL,GAAmB4B,IAAnB;AACD;;AAED,QAAI3B,aAAJ,EAAmB;AACjB,WAAKD,WAAL,GAAmB,KAAK+B,QAAL,GAAgB,KAAKC,WAAL,GAAmBJ,IAAtD;AACD;;AAED,QACE,CAAC,KAAK7B,UAAN,IACA6B,IAAI,IAAI,KAAK7C,OADb,KAEC+C,aAAa,IAAIV,IAAI,CAACC,GAAL,CAASO,IAAI,GAAG,KAAK5B,WAArB,IAAoC,KAAKnB,QAF3D,CADF,EAIE;AACA,WAAKkD,QAAL,GAAgB,KAAKC,WAAL,GAAmBJ,IAAnC;AACA,WAAKK,QAAL,GAAgB,KAAK9C,WAArB;AACA,WAAKY,UAAL,GAAkB,KAAKrB,YAAL,CAAkB,IAAlB,CAAlB;AACD,KAzFwE,CA2FzE;;;AACA,QAAIW,MAAM,KAAKK,uBAAWwC,IAA1B,EAAgC;AAC9B,aAAO,IAAP;AACD;;AAED,SAAKF,WAAL,GAAmBJ,IAAnB;;AAEA,QAAI,KAAK7B,UAAL,IAAmB,CAAC,KAAKpB,OAAL,CAAa,IAAb,CAAxB,EAA4C;AAC1C,aAAO,IAAP;AACD;;AAED,SAAKoD,QAAL,GAAgB,KAAKC,WAArB;AACA,SAAKC,QAAL,GAAgB,KAAK9C,WAArB;AAEA,WAAO,IAAP;AACD;;AAEMgD,EAAAA,cAAc,GAAW;AAC9B,WAAO,KAAKH,WAAZ;AACD;;AAEMI,EAAAA,SAAS,GAAW;AACzB,WAAO,KAAKxB,MAAZ;AACD;;AAEMyB,EAAAA,SAAS,GAAW;AACzB,WAAO,KAAKxB,MAAZ;AACD;;AAEMyB,EAAAA,YAAY,GAAW;AAC5B,WAAO,KAAKnD,WAAL,GAAmB,KAAK8C,QAA/B;AACD;;AAEMM,EAAAA,cAAc,CAAChD,aAAD,EAAgC;AACnD,QAAIA,aAAa,GAAG,CAApB,EAAuB;AACrB,aAAO,CAAP;AACD;;AAED,WAAO,KAAKwC,QAAL,GAAgB,CAAhB,GAAoB,KAAKC,WAAL,GAAmB,KAAKD,QAA5C,GAAuD,CAA9D;AACD;;AA/JuE", "sourcesContent": ["import { DEFAULT_TOUCH_SLOP } from '../constants';\nimport { AdaptedEvent, EventTypes } from '../interfaces';\n\nimport PointerTracker from '../tools/PointerTracker';\n\nexport interface ScaleGestureListener {\n  onScaleBegin: (detector: ScaleGestureDetector) => boolean;\n  onScale: (detector: ScaleGestureDetector) => boolean;\n  onScaleEnd: (detector: ScaleGestureDetector) => void;\n}\n\nexport default class ScaleGestureDetector implements ScaleGestureListener {\n  public onScaleBegin: (detector: ScaleGestureDetector) => boolean;\n  public onScale: (detector: ScaleGestureDetector) => boolean;\n  public onScaleEnd: (detector: ScaleGestureDetector) => void;\n\n  private focusX!: number;\n  private focusY!: number;\n\n  private currentSpan!: number;\n  private prevSpan!: number;\n  private initialSpan!: number;\n\n  private currentTime!: number;\n  private prevTime!: number;\n\n  private inProgress = false;\n\n  private spanSlop: number;\n  private minSpan: number;\n\n  public constructor(callbacks: ScaleGestureListener) {\n    this.onScaleBegin = callbacks.onScaleBegin;\n    this.onScale = callbacks.onScale;\n    this.onScaleEnd = callbacks.onScaleEnd;\n\n    this.spanSlop = DEFAULT_TOUCH_SLOP * 2;\n    this.minSpan = 0;\n  }\n\n  public onTouchEvent(event: AdaptedEvent, tracker: PointerTracker): boolean {\n    this.currentTime = event.time;\n\n    const action: EventTypes = event.eventType;\n    const numOfPointers = tracker.getTrackedPointersCount();\n\n    const streamComplete: boolean =\n      action === EventTypes.UP ||\n      action === EventTypes.ADDITIONAL_POINTER_UP ||\n      action === EventTypes.CANCEL;\n\n    if (action === EventTypes.DOWN || streamComplete) {\n      if (this.inProgress) {\n        this.onScaleEnd(this);\n        this.inProgress = false;\n        this.initialSpan = 0;\n      }\n\n      if (streamComplete) {\n        return true;\n      }\n    }\n\n    const configChanged: boolean =\n      action === EventTypes.DOWN ||\n      action === EventTypes.ADDITIONAL_POINTER_UP ||\n      action === EventTypes.ADDITIONAL_POINTER_DOWN;\n\n    const pointerUp = action === EventTypes.ADDITIONAL_POINTER_UP;\n\n    const ignoredPointer: number | undefined = pointerUp\n      ? event.pointerId\n      : undefined;\n\n    //Determine focal point\n\n    const div: number = pointerUp ? numOfPointers - 1 : numOfPointers;\n\n    const sumX = tracker.getSumX(ignoredPointer);\n    const sumY = tracker.getSumY(ignoredPointer);\n\n    const focusX = sumX / div;\n    const focusY = sumY / div;\n\n    //Determine average deviation from focal point\n\n    let devSumX = 0;\n    let devSumY = 0;\n\n    tracker.getData().forEach((value, key) => {\n      if (key === ignoredPointer) {\n        return;\n      }\n\n      devSumX += Math.abs(value.lastX - focusX);\n      devSumY += Math.abs(value.lastY - focusY);\n    });\n\n    const devX: number = devSumX / div;\n    const devY: number = devSumY / div;\n\n    const spanX: number = devX * 2;\n    const spanY: number = devY * 2;\n\n    const span = Math.hypot(spanX, spanY);\n\n    //Begin/end events\n    const wasInProgress: boolean = this.inProgress;\n    this.focusX = focusX;\n    this.focusY = focusY;\n\n    if (this.inProgress && (span < this.minSpan || configChanged)) {\n      this.onScaleEnd(this);\n      this.inProgress = false;\n      this.initialSpan = span;\n    }\n\n    if (configChanged) {\n      this.initialSpan = this.prevSpan = this.currentSpan = span;\n    }\n\n    if (\n      !this.inProgress &&\n      span >= this.minSpan &&\n      (wasInProgress || Math.abs(span - this.initialSpan) > this.spanSlop)\n    ) {\n      this.prevSpan = this.currentSpan = span;\n      this.prevTime = this.currentTime;\n      this.inProgress = this.onScaleBegin(this);\n    }\n\n    //Handle motion\n    if (action !== EventTypes.MOVE) {\n      return true;\n    }\n\n    this.currentSpan = span;\n\n    if (this.inProgress && !this.onScale(this)) {\n      return true;\n    }\n\n    this.prevSpan = this.currentSpan;\n    this.prevTime = this.currentTime;\n\n    return true;\n  }\n\n  public getCurrentSpan(): number {\n    return this.currentSpan;\n  }\n\n  public getFocusX(): number {\n    return this.focusX;\n  }\n\n  public getFocusY(): number {\n    return this.focusY;\n  }\n\n  public getTimeDelta(): number {\n    return this.currentTime - this.prevTime;\n  }\n\n  public getScaleFactor(numOfPointers: number): number {\n    if (numOfPointers < 2) {\n      return 1;\n    }\n\n    return this.prevSpan > 0 ? this.currentSpan / this.prevSpan : 1;\n  }\n}\n"]}