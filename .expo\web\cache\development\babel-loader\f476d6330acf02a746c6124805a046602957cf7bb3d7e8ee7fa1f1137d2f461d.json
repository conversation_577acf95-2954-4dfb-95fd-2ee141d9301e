{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"stickySectionHeadersEnabled\"];\nimport Platform from '../../../exports/Platform';\nimport * as React from 'react';\nimport VirtualizedSectionList from '../VirtualizedSectionList';\nvar SectionList = function (_React$PureComponent) {\n  function SectionList() {\n    var _this;\n    _classCallCheck(this, SectionList);\n    _this = _callSuper(this, SectionList, arguments);\n    _this._captureRef = function (ref) {\n      _this._wrapperListRef = ref;\n    };\n    return _this;\n  }\n  _inherits(SectionList, _React$PureComponent);\n  return _createClass(SectionList, [{\n    key: \"scrollToLocation\",\n    value: function scrollToLocation(params) {\n      if (this._wrapperListRef != null) {\n        this._wrapperListRef.scrollToLocation(params);\n      }\n    }\n  }, {\n    key: \"recordInteraction\",\n    value: function recordInteraction() {\n      var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n      listRef && listRef.recordInteraction();\n    }\n  }, {\n    key: \"flashScrollIndicators\",\n    value: function flashScrollIndicators() {\n      var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n      listRef && listRef.flashScrollIndicators();\n    }\n  }, {\n    key: \"getScrollResponder\",\n    value: function getScrollResponder() {\n      var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n      if (listRef) {\n        return listRef.getScrollResponder();\n      }\n    }\n  }, {\n    key: \"getScrollableNode\",\n    value: function getScrollableNode() {\n      var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n      if (listRef) {\n        return listRef.getScrollableNode();\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        _stickySectionHeadersEnabled = _this$props.stickySectionHeadersEnabled,\n        restProps = _objectWithoutPropertiesLoose(_this$props, _excluded);\n      var stickySectionHeadersEnabled = _stickySectionHeadersEnabled !== null && _stickySectionHeadersEnabled !== void 0 ? _stickySectionHeadersEnabled : Platform.OS === 'ios';\n      return React.createElement(VirtualizedSectionList, _extends({}, restProps, {\n        stickySectionHeadersEnabled: stickySectionHeadersEnabled,\n        ref: this._captureRef,\n        getItemCount: function getItemCount(items) {\n          return items.length;\n        },\n        getItem: function getItem(items, index) {\n          return items[index];\n        }\n      }));\n    }\n  }]);\n}(React.PureComponent);\nexport { SectionList as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_extends", "_objectWithoutPropertiesLoose", "_excluded", "Platform", "React", "VirtualizedSectionList", "SectionList", "_React$PureComponent", "_this", "arguments", "_captureRef", "ref", "_wrapperListRef", "key", "value", "scrollToLocation", "params", "recordInteraction", "listRef", "getListRef", "flashScrollIndicators", "getScrollResponder", "getScrollableNode", "render", "_this$props", "props", "_stickySectionHeadersEnabled", "stickySectionHeadersEnabled", "restProps", "OS", "createElement", "getItemCount", "items", "length", "getItem", "index", "PureComponent", "default"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/vendor/react-native/SectionList/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"stickySectionHeadersEnabled\"];\nimport Platform from '../../../exports/Platform';\nimport * as React from 'react';\nimport VirtualizedSectionList from '../VirtualizedSectionList';\n/**\n * A performant interface for rendering sectioned lists, supporting the most handy features:\n *\n *  - Fully cross-platform.\n *  - Configurable viewability callbacks.\n *  - List header support.\n *  - List footer support.\n *  - Item separator support.\n *  - Section header support.\n *  - Section separator support.\n *  - Heterogeneous data and item rendering support.\n *  - Pull to Refresh.\n *  - Scroll loading.\n *\n * If you don't need section support and want a simpler interface, use\n * [`<FlatList>`](https://reactnative.dev/docs/flatlist).\n *\n * Simple Examples:\n *\n *     <SectionList\n *       renderItem={({item}) => <ListItem title={item} />}\n *       renderSectionHeader={({section}) => <Header title={section.title} />}\n *       sections={[ // homogeneous rendering between sections\n *         {data: [...], title: ...},\n *         {data: [...], title: ...},\n *         {data: [...], title: ...},\n *       ]}\n *     />\n *\n *     <SectionList\n *       sections={[ // heterogeneous rendering between sections\n *         {data: [...], renderItem: ...},\n *         {data: [...], renderItem: ...},\n *         {data: [...], renderItem: ...},\n *       ]}\n *     />\n *\n * This is a convenience wrapper around [`<VirtualizedList>`](docs/virtualizedlist),\n * and thus inherits its props (as well as those of `ScrollView`) that aren't explicitly listed\n * here, along with the following caveats:\n *\n * - Internal state is not preserved when content scrolls out of the render window. Make sure all\n *   your data is captured in the item data or external stores like Flux, Redux, or Relay.\n * - This is a `PureComponent` which means that it will not re-render if `props` remain shallow-\n *   equal. Make sure that everything your `renderItem` function depends on is passed as a prop\n *   (e.g. `extraData`) that is not `===` after updates, otherwise your UI may not update on\n *   changes. This includes the `data` prop and parent component state.\n * - In order to constrain memory and enable smooth scrolling, content is rendered asynchronously\n *   offscreen. This means it's possible to scroll faster than the fill rate and momentarily see\n *   blank content. This is a tradeoff that can be adjusted to suit the needs of each application,\n *   and we are working on improving it behind the scenes.\n * - By default, the list looks for a `key` prop on each item and uses that for the React key.\n *   Alternatively, you can provide a custom `keyExtractor` prop.\n *\n */\nexport default class SectionList extends React.PureComponent {\n  constructor() {\n    super(...arguments);\n    this._captureRef = ref => {\n      this._wrapperListRef = ref;\n    };\n  }\n  /**\n   * Scrolls to the item at the specified `sectionIndex` and `itemIndex` (within the section)\n   * positioned in the viewable area such that `viewPosition` 0 places it at the top (and may be\n   * covered by a sticky header), 1 at the bottom, and 0.5 centered in the middle. `viewOffset` is a\n   * fixed number of pixels to offset the final target position, e.g. to compensate for sticky\n   * headers.\n   *\n   * Note: cannot scroll to locations outside the render window without specifying the\n   * `getItemLayout` prop.\n   */\n  scrollToLocation(params) {\n    if (this._wrapperListRef != null) {\n      this._wrapperListRef.scrollToLocation(params);\n    }\n  }\n\n  /**\n   * Tells the list an interaction has occurred, which should trigger viewability calculations, e.g.\n   * if `waitForInteractions` is true and the user has not scrolled. This is typically called by\n   * taps on items or by navigation actions.\n   */\n  recordInteraction() {\n    var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n    listRef && listRef.recordInteraction();\n  }\n\n  /**\n   * Displays the scroll indicators momentarily.\n   *\n   * @platform ios\n   */\n  flashScrollIndicators() {\n    var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n    listRef && listRef.flashScrollIndicators();\n  }\n\n  /**\n   * Provides a handle to the underlying scroll responder.\n   */\n  getScrollResponder() {\n    var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n    if (listRef) {\n      return listRef.getScrollResponder();\n    }\n  }\n  getScrollableNode() {\n    var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n    if (listRef) {\n      return listRef.getScrollableNode();\n    }\n  }\n  render() {\n    var _this$props = this.props,\n      _stickySectionHeadersEnabled = _this$props.stickySectionHeadersEnabled,\n      restProps = _objectWithoutPropertiesLoose(_this$props, _excluded);\n    var stickySectionHeadersEnabled = _stickySectionHeadersEnabled !== null && _stickySectionHeadersEnabled !== void 0 ? _stickySectionHeadersEnabled : Platform.OS === 'ios';\n    return /*#__PURE__*/React.createElement(VirtualizedSectionList, _extends({}, restProps, {\n      stickySectionHeadersEnabled: stickySectionHeadersEnabled,\n      ref: this._captureRef,\n      getItemCount: items => items.length,\n      getItem: (items, index) => items[index]\n    }));\n  }\n}"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAJ,eAAA,CAAAI,CAAA,GAAAL,0BAAA,CAAAI,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAL,eAAA,CAAAG,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAEb,OAAOY,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,6BAA6B,CAAC;AAC/C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,sBAAsB,MAAM,2BAA2B;AAAC,IAwD1CC,WAAW,aAAAC,oBAAA;EAC9B,SAAAD,YAAA,EAAc;IAAA,IAAAE,KAAA;IAAA1B,eAAA,OAAAwB,WAAA;IACZE,KAAA,GAAArB,UAAA,OAAAmB,WAAA,EAASG,SAAS;IAClBD,KAAA,CAAKE,WAAW,GAAG,UAAAC,GAAG,EAAI;MACxBH,KAAA,CAAKI,eAAe,GAAGD,GAAG;IAC5B,CAAC;IAAC,OAAAH,KAAA;EACJ;EAACtB,SAAA,CAAAoB,WAAA,EAAAC,oBAAA;EAAA,OAAAxB,YAAA,CAAAuB,WAAA;IAAAO,GAAA;IAAAC,KAAA,EAWD,SAAAC,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAI,IAAI,CAACJ,eAAe,IAAI,IAAI,EAAE;QAChC,IAAI,CAACA,eAAe,CAACG,gBAAgB,CAACC,MAAM,CAAC;MAC/C;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAOD,SAAAG,iBAAiBA,CAAA,EAAG;MAClB,IAAIC,OAAO,GAAG,IAAI,CAACN,eAAe,IAAI,IAAI,CAACA,eAAe,CAACO,UAAU,CAAC,CAAC;MACvED,OAAO,IAAIA,OAAO,CAACD,iBAAiB,CAAC,CAAC;IACxC;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAOD,SAAAM,qBAAqBA,CAAA,EAAG;MACtB,IAAIF,OAAO,GAAG,IAAI,CAACN,eAAe,IAAI,IAAI,CAACA,eAAe,CAACO,UAAU,CAAC,CAAC;MACvED,OAAO,IAAIA,OAAO,CAACE,qBAAqB,CAAC,CAAC;IAC5C;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAKD,SAAAO,kBAAkBA,CAAA,EAAG;MACnB,IAAIH,OAAO,GAAG,IAAI,CAACN,eAAe,IAAI,IAAI,CAACA,eAAe,CAACO,UAAU,CAAC,CAAC;MACvE,IAAID,OAAO,EAAE;QACX,OAAOA,OAAO,CAACG,kBAAkB,CAAC,CAAC;MACrC;IACF;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACD,SAAAQ,iBAAiBA,CAAA,EAAG;MAClB,IAAIJ,OAAO,GAAG,IAAI,CAACN,eAAe,IAAI,IAAI,CAACA,eAAe,CAACO,UAAU,CAAC,CAAC;MACvE,IAAID,OAAO,EAAE;QACX,OAAOA,OAAO,CAACI,iBAAiB,CAAC,CAAC;MACpC;IACF;EAAC;IAAAT,GAAA;IAAAC,KAAA,EACD,SAAAS,MAAMA,CAAA,EAAG;MACP,IAAIC,WAAW,GAAG,IAAI,CAACC,KAAK;QAC1BC,4BAA4B,GAAGF,WAAW,CAACG,2BAA2B;QACtEC,SAAS,GAAG3B,6BAA6B,CAACuB,WAAW,EAAEtB,SAAS,CAAC;MACnE,IAAIyB,2BAA2B,GAAGD,4BAA4B,KAAK,IAAI,IAAIA,4BAA4B,KAAK,KAAK,CAAC,GAAGA,4BAA4B,GAAGvB,QAAQ,CAAC0B,EAAE,KAAK,KAAK;MACzK,OAAoBzB,KAAK,CAAC0B,aAAa,CAACzB,sBAAsB,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAE4B,SAAS,EAAE;QACtFD,2BAA2B,EAAEA,2BAA2B;QACxDhB,GAAG,EAAE,IAAI,CAACD,WAAW;QACrBqB,YAAY,EAAE,SAAdA,YAAYA,CAAEC,KAAK;UAAA,OAAIA,KAAK,CAACC,MAAM;QAAA;QACnCC,OAAO,EAAE,SAATA,OAAOA,CAAGF,KAAK,EAAEG,KAAK;UAAA,OAAKH,KAAK,CAACG,KAAK,CAAC;QAAA;MACzC,CAAC,CAAC,CAAC;IACL;EAAC;AAAA,EArEsC/B,KAAK,CAACgC,aAAa;AAAA,SAAvC9B,WAAW,IAAA+B,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}