{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"colors\", \"enabled\", \"onRefresh\", \"progressBackgroundColor\", \"progressViewOffset\", \"refreshing\", \"size\", \"tintColor\", \"title\", \"titleColor\"];\nimport View from \"../View\";\nimport React from 'react';\nfunction RefreshControl(props) {\n  var colors = props.colors,\n    enabled = props.enabled,\n    onRefresh = props.onRefresh,\n    progressBackgroundColor = props.progressBackgroundColor,\n    progressViewOffset = props.progressViewOffset,\n    refreshing = props.refreshing,\n    size = props.size,\n    tintColor = props.tintColor,\n    title = props.title,\n    titleColor = props.titleColor,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  return React.createElement(View, rest);\n}\nexport default RefreshControl;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_excluded", "View", "React", "RefreshControl", "props", "colors", "enabled", "onRefresh", "progressBackgroundColor", "progressViewOffset", "refreshing", "size", "tintColor", "title", "titleColor", "rest", "createElement"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/exports/RefreshControl/index.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"colors\", \"enabled\", \"onRefresh\", \"progressBackgroundColor\", \"progressViewOffset\", \"refreshing\", \"size\", \"tintColor\", \"title\", \"titleColor\"];\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport View from '../View';\nimport React from 'react';\nfunction RefreshControl(props) {\n  var colors = props.colors,\n    enabled = props.enabled,\n    onRefresh = props.onRefresh,\n    progressBackgroundColor = props.progressBackgroundColor,\n    progressViewOffset = props.progressViewOffset,\n    refreshing = props.refreshing,\n    size = props.size,\n    tintColor = props.tintColor,\n    title = props.title,\n    titleColor = props.titleColor,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  return /*#__PURE__*/React.createElement(View, rest);\n}\nexport default RefreshControl;"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,yBAAyB,EAAE,oBAAoB,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;AAW7J,OAAOC,IAAI;AACX,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACvBC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,uBAAuB,GAAGJ,KAAK,CAACI,uBAAuB;IACvDC,kBAAkB,GAAGL,KAAK,CAACK,kBAAkB;IAC7CC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,IAAI,GAAGP,KAAK,CAACO,IAAI;IACjBC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,UAAU,GAAGV,KAAK,CAACU,UAAU;IAC7BC,IAAI,GAAGhB,6BAA6B,CAACK,KAAK,EAAEJ,SAAS,CAAC;EACxD,OAAoBE,KAAK,CAACc,aAAa,CAACf,IAAI,EAAEc,IAAI,CAAC;AACrD;AACA,eAAeZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}