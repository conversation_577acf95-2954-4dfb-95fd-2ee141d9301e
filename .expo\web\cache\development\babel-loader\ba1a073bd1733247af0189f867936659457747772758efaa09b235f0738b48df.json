{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nvar _jsxFileName = \"D:\\\\aplikasi\\\\TRAE\\\\psg-bmi\\\\src\\\\components\\\\ScrollTestContent.js\",\n  _this = this;\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { Colors, Typography, Spacing } from '../constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nvar ScrollTestContent = function ScrollTestContent() {\n  var testItems = Array.from({\n    length: 10\n  }, function (_, index) {\n    return {\n      id: index + 1,\n      title: `Test Section ${index + 1}`,\n      description: `This is test content section ${index + 1} to verify that the bottom navigation remains fixed while scrolling through the page content.`\n    };\n  });\n  return _jsxDEV(View, {\n    style: styles.container,\n    children: [_jsxDEV(Text, {\n      style: styles.header,\n      children: \"Fixed Navigation Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, _this), _jsxDEV(Text, {\n      style: styles.subheader,\n      children: \"Scroll down to verify that the bottom navigation stays fixed at the bottom of the screen.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, _this), testItems.map(function (item) {\n      return _jsxDEV(View, {\n        style: styles.testItem,\n        children: [_jsxDEV(Text, {\n          style: styles.itemTitle,\n          children: item.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, _this), _jsxDEV(Text, {\n          style: styles.itemDescription,\n          children: item.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, _this), _jsxDEV(View, {\n          style: styles.itemContent,\n          children: _jsxDEV(Text, {\n            style: styles.itemText,\n            children: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, _this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, _this)]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, _this);\n    }), _jsxDEV(View, {\n      style: styles.finalSection,\n      children: _jsxDEV(Text, {\n        style: styles.finalText,\n        children: \"\\uD83C\\uDF89 If you can see this text and the bottom navigation is still visible and fixed at the bottom, then the implementation is working correctly!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, _this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, _this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, _this);\n};\nvar styles = StyleSheet.create({\n  container: {\n    padding: Spacing.padding.md\n  },\n  header: _objectSpread(_objectSpread({}, Typography.h2), {}, {\n    color: Colors.primary,\n    textAlign: 'center',\n    marginBottom: Spacing.sm\n  }),\n  subheader: _objectSpread(_objectSpread({}, Typography.body1), {}, {\n    color: Colors.onSurfaceVariant,\n    textAlign: 'center',\n    marginBottom: Spacing.xl\n  }),\n  testItem: {\n    backgroundColor: Colors.surface,\n    borderRadius: Spacing.borderRadius.lg,\n    padding: Spacing.padding.md,\n    marginBottom: Spacing.lg,\n    shadowColor: Colors.cardShadow,\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3\n  },\n  itemTitle: _objectSpread(_objectSpread({}, Typography.h3), {}, {\n    color: Colors.onSurface,\n    marginBottom: Spacing.sm\n  }),\n  itemDescription: _objectSpread(_objectSpread({}, Typography.body2), {}, {\n    color: Colors.onSurfaceVariant,\n    marginBottom: Spacing.md\n  }),\n  itemContent: {\n    borderLeftWidth: 3,\n    borderLeftColor: Colors.primary,\n    paddingLeft: Spacing.md\n  },\n  itemText: _objectSpread(_objectSpread({}, Typography.body1), {}, {\n    color: Colors.onSurface,\n    lineHeight: 24\n  }),\n  finalSection: {\n    backgroundColor: Colors.successLight,\n    borderRadius: Spacing.borderRadius.lg,\n    padding: Spacing.padding.lg,\n    marginTop: Spacing.xl,\n    borderWidth: 2,\n    borderColor: Colors.success\n  },\n  finalText: _objectSpread(_objectSpread({}, Typography.body1), {}, {\n    color: Colors.onSurface,\n    textAlign: 'center',\n    fontWeight: '600'\n  })\n});\nexport default ScrollTestContent;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "Colors", "Typography", "Spacing", "jsxDEV", "_jsxDEV", "ScrollTestContent", "testItems", "Array", "from", "length", "_", "index", "id", "title", "description", "style", "styles", "container", "children", "header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_this", "subheader", "map", "item", "testItem", "itemTitle", "itemDescription", "itemContent", "itemText", "finalSection", "finalText", "create", "padding", "md", "_objectSpread", "h2", "color", "primary", "textAlign", "marginBottom", "sm", "body1", "onSurfaceVariant", "xl", "backgroundColor", "surface", "borderRadius", "lg", "shadowColor", "cardShadow", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation", "h3", "onSurface", "body2", "borderLeftWidth", "borderLeftColor", "paddingLeft", "lineHeight", "successLight", "marginTop", "borderWidth", "borderColor", "success", "fontWeight"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/components/ScrollTestContent.js"], "sourcesContent": ["import React from 'react';\nimport { View, Text, StyleSheet } from 'react-native';\nimport { Colors, Typography, Spacing } from '../constants';\n\n/**\n * Test component to verify fixed navigation behavior\n * Adds extra content to test scrolling and ensure navigation stays fixed\n */\nconst ScrollTestContent = () => {\n  const testItems = Array.from({ length: 10 }, (_, index) => ({\n    id: index + 1,\n    title: `Test Section ${index + 1}`,\n    description: `This is test content section ${index + 1} to verify that the bottom navigation remains fixed while scrolling through the page content.`,\n  }));\n\n  return (\n    <View style={styles.container}>\n      <Text style={styles.header}>Fixed Navigation Test</Text>\n      <Text style={styles.subheader}>\n        Scroll down to verify that the bottom navigation stays fixed at the bottom of the screen.\n      </Text>\n      \n      {testItems.map((item) => (\n        <View key={item.id} style={styles.testItem}>\n          <Text style={styles.itemTitle}>{item.title}</Text>\n          <Text style={styles.itemDescription}>{item.description}</Text>\n          <View style={styles.itemContent}>\n            <Text style={styles.itemText}>\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor \n              incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis \n              nostrud exercitation ullamco laboris.\n            </Text>\n          </View>\n        </View>\n      ))}\n      \n      <View style={styles.finalSection}>\n        <Text style={styles.finalText}>\n          🎉 If you can see this text and the bottom navigation is still visible and fixed \n          at the bottom, then the implementation is working correctly!\n        </Text>\n      </View>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    padding: Spacing.padding.md,\n  },\n  header: {\n    ...Typography.h2,\n    color: Colors.primary,\n    textAlign: 'center',\n    marginBottom: Spacing.sm,\n  },\n  subheader: {\n    ...Typography.body1,\n    color: Colors.onSurfaceVariant,\n    textAlign: 'center',\n    marginBottom: Spacing.xl,\n  },\n  testItem: {\n    backgroundColor: Colors.surface,\n    borderRadius: Spacing.borderRadius.lg,\n    padding: Spacing.padding.md,\n    marginBottom: Spacing.lg,\n    shadowColor: Colors.cardShadow,\n    shadowOffset: {\n      width: 0,\n      height: 2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3,\n  },\n  itemTitle: {\n    ...Typography.h3,\n    color: Colors.onSurface,\n    marginBottom: Spacing.sm,\n  },\n  itemDescription: {\n    ...Typography.body2,\n    color: Colors.onSurfaceVariant,\n    marginBottom: Spacing.md,\n  },\n  itemContent: {\n    borderLeftWidth: 3,\n    borderLeftColor: Colors.primary,\n    paddingLeft: Spacing.md,\n  },\n  itemText: {\n    ...Typography.body1,\n    color: Colors.onSurface,\n    lineHeight: 24,\n  },\n  finalSection: {\n    backgroundColor: Colors.successLight,\n    borderRadius: Spacing.borderRadius.lg,\n    padding: Spacing.padding.lg,\n    marginTop: Spacing.xl,\n    borderWidth: 2,\n    borderColor: Colors.success,\n  },\n  finalText: {\n    ...Typography.body1,\n    color: Colors.onSurface,\n    textAlign: 'center',\n    fontWeight: '600',\n  },\n});\n\nexport default ScrollTestContent;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAE1B,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3D,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;EAC9B,IAAMC,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC,EAAE,UAACC,CAAC,EAAEC,KAAK;IAAA,OAAM;MAC1DC,EAAE,EAAED,KAAK,GAAG,CAAC;MACbE,KAAK,EAAE,gBAAgBF,KAAK,GAAG,CAAC,EAAE;MAClCG,WAAW,EAAE,gCAAgCH,KAAK,GAAG,CAAC;IACxD,CAAC;EAAA,CAAC,CAAC;EAEH,OACEP,OAAA,CAACP,IAAI;IAACkB,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5Bd,OAAA,CAACN,IAAI;MAACiB,KAAK,EAAEC,MAAM,CAACG,MAAO;MAAAD,QAAA,EAAC;IAAqB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,GAAAC,KAAM,CAAC,EACxDpB,OAAA,CAACN,IAAI;MAACiB,KAAK,EAAEC,MAAM,CAACS,SAAU;MAAAP,QAAA,EAAC;IAE/B;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,GAAAC,KAAM,CAAC,EAENlB,SAAS,CAACoB,GAAG,CAAC,UAACC,IAAI;MAAA,OAClBvB,OAAA,CAACP,IAAI;QAAekB,KAAK,EAAEC,MAAM,CAACY,QAAS;QAAAV,QAAA,GACzCd,OAAA,CAACN,IAAI;UAACiB,KAAK,EAAEC,MAAM,CAACa,SAAU;UAAAX,QAAA,EAAES,IAAI,CAACd;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KAAO,CAAC,EAClDpB,OAAA,CAACN,IAAI;UAACiB,KAAK,EAAEC,MAAM,CAACc,eAAgB;UAAAZ,QAAA,EAAES,IAAI,CAACb;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KAAO,CAAC,EAC9DpB,OAAA,CAACP,IAAI;UAACkB,KAAK,EAAEC,MAAM,CAACe,WAAY;UAAAb,QAAA,EAC9Bd,OAAA,CAACN,IAAI;YAACiB,KAAK,EAAEC,MAAM,CAACgB,QAAS;YAAAd,QAAA,EAAC;UAI9B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,GAAAC,KAAM;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,GAAAC,KACH,CAAC;MAAA,GATEG,IAAI,CAACf,EAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KAUZ,CAAC;IAAA,CACR,CAAC,EAEFpB,OAAA,CAACP,IAAI;MAACkB,KAAK,EAAEC,MAAM,CAACiB,YAAa;MAAAf,QAAA,EAC/Bd,OAAA,CAACN,IAAI;QAACiB,KAAK,EAAEC,MAAM,CAACkB,SAAU;QAAAhB,QAAA,EAAC;MAG/B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KAAM;IAAC;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,GAAAC,KACH,CAAC;EAAA;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,GAAAC,KACH,CAAC;AAEX,CAAC;AAED,IAAMR,MAAM,GAAGjB,UAAU,CAACoC,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IACTmB,OAAO,EAAElC,OAAO,CAACkC,OAAO,CAACC;EAC3B,CAAC;EACDlB,MAAM,EAAAmB,aAAA,CAAAA,aAAA,KACDrC,UAAU,CAACsC,EAAE;IAChBC,KAAK,EAAExC,MAAM,CAACyC,OAAO;IACrBC,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAEzC,OAAO,CAAC0C;EAAE,EACzB;EACDnB,SAAS,EAAAa,aAAA,CAAAA,aAAA,KACJrC,UAAU,CAAC4C,KAAK;IACnBL,KAAK,EAAExC,MAAM,CAAC8C,gBAAgB;IAC9BJ,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAEzC,OAAO,CAAC6C;EAAE,EACzB;EACDnB,QAAQ,EAAE;IACRoB,eAAe,EAAEhD,MAAM,CAACiD,OAAO;IAC/BC,YAAY,EAAEhD,OAAO,CAACgD,YAAY,CAACC,EAAE;IACrCf,OAAO,EAAElC,OAAO,CAACkC,OAAO,CAACC,EAAE;IAC3BM,YAAY,EAAEzC,OAAO,CAACiD,EAAE;IACxBC,WAAW,EAAEpD,MAAM,CAACqD,UAAU;IAC9BC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACD9B,SAAS,EAAAS,aAAA,CAAAA,aAAA,KACJrC,UAAU,CAAC2D,EAAE;IAChBpB,KAAK,EAAExC,MAAM,CAAC6D,SAAS;IACvBlB,YAAY,EAAEzC,OAAO,CAAC0C;EAAE,EACzB;EACDd,eAAe,EAAAQ,aAAA,CAAAA,aAAA,KACVrC,UAAU,CAAC6D,KAAK;IACnBtB,KAAK,EAAExC,MAAM,CAAC8C,gBAAgB;IAC9BH,YAAY,EAAEzC,OAAO,CAACmC;EAAE,EACzB;EACDN,WAAW,EAAE;IACXgC,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAEhE,MAAM,CAACyC,OAAO;IAC/BwB,WAAW,EAAE/D,OAAO,CAACmC;EACvB,CAAC;EACDL,QAAQ,EAAAM,aAAA,CAAAA,aAAA,KACHrC,UAAU,CAAC4C,KAAK;IACnBL,KAAK,EAAExC,MAAM,CAAC6D,SAAS;IACvBK,UAAU,EAAE;EAAE,EACf;EACDjC,YAAY,EAAE;IACZe,eAAe,EAAEhD,MAAM,CAACmE,YAAY;IACpCjB,YAAY,EAAEhD,OAAO,CAACgD,YAAY,CAACC,EAAE;IACrCf,OAAO,EAAElC,OAAO,CAACkC,OAAO,CAACe,EAAE;IAC3BiB,SAAS,EAAElE,OAAO,CAAC6C,EAAE;IACrBsB,WAAW,EAAE,CAAC;IACdC,WAAW,EAAEtE,MAAM,CAACuE;EACtB,CAAC;EACDrC,SAAS,EAAAI,aAAA,CAAAA,aAAA,KACJrC,UAAU,CAAC4C,KAAK;IACnBL,KAAK,EAAExC,MAAM,CAAC6D,SAAS;IACvBnB,SAAS,EAAE,QAAQ;IACnB8B,UAAU,EAAE;EAAK;AAErB,CAAC,CAAC;AAEF,eAAenE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}