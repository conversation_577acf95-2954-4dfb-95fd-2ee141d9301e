{"version": 3, "sources": ["TapGestureHandler.ts"], "names": ["createHandler", "baseGestureHandlerProps", "tapGestureHandlerProps", "tapHandlerName", "TapGestureHandler", "name", "allowedProps", "config", "shouldCancelWhenOutside"], "mappings": "AAAA,OAAOA,aAAP,MAA0B,iBAA1B;AACA,SAEEC,uBAFF,QAGO,wBAHP;AAKA,OAAO,MAAMC,sBAAsB,GAAG,CACpC,eADoC,EAEpC,YAFoC,EAGpC,cAHoC,EAIpC,WAJoC,EAKpC,WALoC,EAMpC,SANoC,EAOpC,aAPoC,CAA/B;AAuEP,OAAO,MAAMC,cAAc,GAAG,mBAAvB;AAGP;AACA,OAAO,MAAMC,iBAAiB,GAAGJ,aAAa,CAG5C;AACAK,EAAAA,IAAI,EAAEF,cADN;AAEAG,EAAAA,YAAY,EAAE,CACZ,GAAGL,uBADS,EAEZ,GAAGC,sBAFS,CAFd;AAMAK,EAAAA,MAAM,EAAE;AACNC,IAAAA,uBAAuB,EAAE;AADnB;AANR,CAH4C,CAAvC", "sourcesContent": ["import createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\nexport const tapGestureHandlerProps = [\n  'maxDurationMs',\n  'maxDelayMs',\n  'numberOfTaps',\n  'maxDeltaX',\n  'maxDeltaY',\n  'maxDist',\n  'minPointers',\n] as const;\n\nexport type TapGestureHandlerEventPayload = {\n  x: number;\n  y: number;\n  absoluteX: number;\n  absoluteY: number;\n};\nexport interface TapGestureConfig {\n  /**\n   * Minimum number of pointers (fingers) required to be placed before the\n   * handler activates. Should be a positive integer.\n   * The default value is 1.\n   */\n  minPointers?: number;\n\n  /**\n   * Maximum time, expressed in milliseconds, that defines how fast a finger\n   * must be released after a touch. The default value is 500.\n   */\n  maxDurationMs?: number;\n\n  /**\n   * Maximum time, expressed in milliseconds, that can pass before the next tap\n   * if many taps are required. The default value is 500.\n   */\n  maxDelayMs?: number;\n\n  /**\n   * Number of tap gestures required to activate the handler. The default value\n   * is 1.\n   */\n  numberOfTaps?: number;\n\n  /**\n   * Maximum distance, expressed in points, that defines how far the finger is\n   * allowed to travel along the X axis during a tap gesture. If the finger\n   * travels further than the defined distance along the X axis and the handler\n   * hasn't yet activated, it will fail to recognize the gesture.\n   */\n  maxDeltaX?: number;\n\n  /**\n   * Maximum distance, expressed in points, that defines how far the finger is\n   * allowed to travel along the Y axis during a tap gesture. If the finger\n   * travels further than the defined distance along the Y axis and the handler\n   * hasn't yet activated, it will fail to recognize the gesture.\n   */\n  maxDeltaY?: number;\n\n  /**\n   * Maximum distance, expressed in points, that defines how far the finger is\n   * allowed to travel during a tap gesture. If the finger travels further than\n   * the defined distance and the handler hasn't yet\n   * activated, it will fail to recognize the gesture.\n   */\n  maxDist?: number;\n}\n\nexport interface TapGestureHandlerProps\n  extends BaseGestureHandlerProps<TapGestureHandlerEventPayload>,\n    TapGestureConfig {}\n\nexport const tapHandlerName = 'TapGestureHandler';\n\nexport type TapGestureHandler = typeof TapGestureHandler;\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const TapGestureHandler = createHandler<\n  TapGestureHandlerProps,\n  TapGestureHandlerEventPayload\n>({\n  name: tapHandlerName,\n  allowedProps: [\n    ...baseGestureHandlerProps,\n    ...tapGestureHandlerProps,\n  ] as const,\n  config: {\n    shouldCancelWhenOutside: true,\n  },\n});\n"]}