{"version": 3, "sources": ["PointerEventManager.ts"], "names": ["EventTypes", "PointerType", "EventManager", "isPointerInBounds", "POINTER_CAPTURE_EXCLUDE_LIST", "Set", "PointerEventManager", "setListeners", "view", "addEventListener", "event", "pointerType", "TOUCH", "x", "clientX", "y", "clientY", "adaptedEvent", "mapEvent", "DOWN", "target", "has", "tagName", "setPointerCapture", "pointerId", "markAsInBounds", "trackedPointers", "add", "activePointersCounter", "eventType", "ADDITIONAL_POINTER_DOWN", "onPointerAdd", "onPointerDown", "UP", "releasePointerCapture", "markAsOutOfBounds", "delete", "ADDITIONAL_POINTER_UP", "onPointerRemove", "onPointerUp", "MOVE", "hasPointerCapture", "inBounds", "pointerIndex", "pointersInBounds", "indexOf", "ENTER", "onPointerEnter", "onPointerMove", "LEAVE", "onPointerLeave", "onPointerOutOfBounds", "CANCEL", "onPointerCancel", "clear", "onPointerMoveOver", "onPointerMoveOut", "offsetX", "offsetY", "buttons", "time", "timeStamp", "resetManager"], "mappings": ";;AAAA,SAAuBA,UAAvB,EAAmCC,WAAnC,QAAsD,eAAtD;AACA,OAAOC,YAAP,MAAyB,gBAAzB;AACA,SAASC,iBAAT,QAAkC,UAAlC;AAEA,MAAMC,4BAA4B,GAAG,IAAIC,GAAJ,CAAgB,CAAC,QAAD,EAAW,OAAX,CAAhB,CAArC;AAEA,eAAe,MAAMC,mBAAN,SAAkCJ,YAAlC,CAA4D;AAAA;AAAA;;AAAA,6CAC/C,IAAIG,GAAJ,EAD+C;AAAA;;AAGlEE,EAAAA,YAAY,GAAS;AAC1B,SAAKC,IAAL,CAAUC,gBAAV,CAA2B,aAA3B,EAA2CC,KAAD,IAA+B;AACvE,UAAIA,KAAK,CAACC,WAAN,KAAsBV,WAAW,CAACW,KAAtC,EAA6C;AAC3C;AACD;;AACD,UACE,CAACT,iBAAiB,CAAC,KAAKK,IAAN,EAAY;AAAEK,QAAAA,CAAC,EAAEH,KAAK,CAACI,OAAX;AAAoBC,QAAAA,CAAC,EAAEL,KAAK,CAACM;AAA7B,OAAZ,CADpB,EAEE;AACA;AACD;;AAED,YAAMC,YAA0B,GAAG,KAAKC,QAAL,CAAcR,KAAd,EAAqBV,UAAU,CAACmB,IAAhC,CAAnC;AACA,YAAMC,MAAM,GAAGV,KAAK,CAACU,MAArB;;AAEA,UAAI,CAAChB,4BAA4B,CAACiB,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAAL,EAAuD;AACrDF,QAAAA,MAAM,CAACG,iBAAP,CAAyBN,YAAY,CAACO,SAAtC;AACD;;AAED,WAAKC,cAAL,CAAoBR,YAAY,CAACO,SAAjC;AACA,WAAKE,eAAL,CAAqBC,GAArB,CAAyBV,YAAY,CAACO,SAAtC;;AAEA,UAAI,EAAE,KAAKI,qBAAP,GAA+B,CAAnC,EAAsC;AACpCX,QAAAA,YAAY,CAACY,SAAb,GAAyB7B,UAAU,CAAC8B,uBAApC;AACA,aAAKC,YAAL,CAAkBd,YAAlB;AACD,OAHD,MAGO;AACL,aAAKe,aAAL,CAAmBf,YAAnB;AACD;AACF,KA1BD;AA4BA,SAAKT,IAAL,CAAUC,gBAAV,CAA2B,WAA3B,EAAyCC,KAAD,IAA+B;AACrE,UAAIA,KAAK,CAACC,WAAN,KAAsBV,WAAW,CAACW,KAAtC,EAA6C;AAC3C;AACD,OAHoE,CAKrE;AACA;AACA;AACA;;;AACA,UAAI,KAAKgB,qBAAL,KAA+B,CAAnC,EAAsC;AACpC;AACD;;AAED,YAAMX,YAA0B,GAAG,KAAKC,QAAL,CAAcR,KAAd,EAAqBV,UAAU,CAACiC,EAAhC,CAAnC;AACA,YAAMb,MAAM,GAAGV,KAAK,CAACU,MAArB;;AAEA,UAAI,CAAChB,4BAA4B,CAACiB,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAAL,EAAuD;AACrDF,QAAAA,MAAM,CAACc,qBAAP,CAA6BjB,YAAY,CAACO,SAA1C;AACD;;AAED,WAAKW,iBAAL,CAAuBlB,YAAY,CAACO,SAApC;AACA,WAAKE,eAAL,CAAqBU,MAArB,CAA4BnB,YAAY,CAACO,SAAzC;;AAEA,UAAI,EAAE,KAAKI,qBAAP,GAA+B,CAAnC,EAAsC;AACpCX,QAAAA,YAAY,CAACY,SAAb,GAAyB7B,UAAU,CAACqC,qBAApC;AACA,aAAKC,eAAL,CAAqBrB,YAArB;AACD,OAHD,MAGO;AACL,aAAKsB,WAAL,CAAiBtB,YAAjB;AACD;AACF,KA7BD;AA+BA,SAAKT,IAAL,CAAUC,gBAAV,CAA2B,aAA3B,EAA2CC,KAAD,IAA+B;AACvE,UAAIA,KAAK,CAACC,WAAN,KAAsBV,WAAW,CAACW,KAAtC,EAA6C;AAC3C;AACD;;AAED,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcR,KAAd,EAAqBV,UAAU,CAACwC,IAAhC,CAAnC;AACA,YAAMpB,MAAM,GAAGV,KAAK,CAACU,MAArB,CANuE,CAQvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,UACE,CAACA,MAAM,CAACqB,iBAAP,CAAyB/B,KAAK,CAACc,SAA/B,CAAD,IACA,CAACpB,4BAA4B,CAACiB,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAFH,EAGE;AACAF,QAAAA,MAAM,CAACG,iBAAP,CAAyBb,KAAK,CAACc,SAA/B;AACD;;AAED,YAAMkB,QAAiB,GAAGvC,iBAAiB,CAAC,KAAKK,IAAN,EAAY;AACrDK,QAAAA,CAAC,EAAEI,YAAY,CAACJ,CADqC;AAErDE,QAAAA,CAAC,EAAEE,YAAY,CAACF;AAFqC,OAAZ,CAA3C;AAKA,YAAM4B,YAAoB,GAAG,KAAKC,gBAAL,CAAsBC,OAAtB,CAC3B5B,YAAY,CAACO,SADc,CAA7B;;AAIA,UAAIkB,QAAJ,EAAc;AACZ,YAAIC,YAAY,GAAG,CAAnB,EAAsB;AACpB1B,UAAAA,YAAY,CAACY,SAAb,GAAyB7B,UAAU,CAAC8C,KAApC;AACA,eAAKC,cAAL,CAAoB9B,YAApB;AACA,eAAKQ,cAAL,CAAoBR,YAAY,CAACO,SAAjC;AACD,SAJD,MAIO;AACL,eAAKwB,aAAL,CAAmB/B,YAAnB;AACD;AACF,OARD,MAQO;AACL,YAAI0B,YAAY,IAAI,CAApB,EAAuB;AACrB1B,UAAAA,YAAY,CAACY,SAAb,GAAyB7B,UAAU,CAACiD,KAApC;AACA,eAAKC,cAAL,CAAoBjC,YAApB;AACA,eAAKkB,iBAAL,CAAuBlB,YAAY,CAACO,SAApC;AACD,SAJD,MAIO;AACL,eAAK2B,oBAAL,CAA0BlC,YAA1B;AACD;AACF;AACF,KArDD;AAuDA,SAAKT,IAAL,CAAUC,gBAAV,CAA2B,eAA3B,EAA6CC,KAAD,IAA+B;AACzE,UAAIA,KAAK,CAACC,WAAN,KAAsBV,WAAW,CAACW,KAAtC,EAA6C;AAC3C;AACD;;AAED,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CACjCR,KADiC,EAEjCV,UAAU,CAACoD,MAFsB,CAAnC;AAKA,WAAKC,eAAL,CAAqBpC,YAArB;AACA,WAAKkB,iBAAL,CAAuBlB,YAAY,CAACO,SAApC;AACA,WAAKI,qBAAL,GAA6B,CAA7B;AACA,WAAKF,eAAL,CAAqB4B,KAArB;AACD,KAdD,EAnH0B,CAmI1B;AACA;AACA;AACA;;AAEA,SAAK9C,IAAL,CAAUC,gBAAV,CAA2B,cAA3B,EAA4CC,KAAD,IAA+B;AACxE,UAAIA,KAAK,CAACC,WAAN,KAAsBV,WAAW,CAACW,KAAtC,EAA6C;AAC3C;AACD;;AAED,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcR,KAAd,EAAqBV,UAAU,CAAC8C,KAAhC,CAAnC;AAEA,WAAKS,iBAAL,CAAuBtC,YAAvB;AACD,KARD;AAUA,SAAKT,IAAL,CAAUC,gBAAV,CAA2B,cAA3B,EAA4CC,KAAD,IAA+B;AACxE,UAAIA,KAAK,CAACC,WAAN,KAAsBV,WAAW,CAACW,KAAtC,EAA6C;AAC3C;AACD;;AAED,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcR,KAAd,EAAqBV,UAAU,CAACiD,KAAhC,CAAnC;AAEA,WAAKO,gBAAL,CAAsBvC,YAAtB;AACD,KARD;AAUA,SAAKT,IAAL,CAAUC,gBAAV,CACE,oBADF,EAEGC,KAAD,IAA+B;AAC7B,YAAMO,YAA0B,GAAG,KAAKC,QAAL,CACjCR,KADiC,EAEjCV,UAAU,CAACoD,MAFsB,CAAnC;;AAKA,UAAI,KAAK1B,eAAL,CAAqBL,GAArB,CAAyBJ,YAAY,CAACO,SAAtC,CAAJ,EAAsD;AACpD;AACA;AACA,aAAK6B,eAAL,CAAqBpC,YAArB;AAEA,aAAKW,qBAAL,GAA6B,CAA7B;AACA,aAAKF,eAAL,CAAqB4B,KAArB;AACD;AACF,KAhBH;AAkBD;;AAESpC,EAAAA,QAAQ,CAACR,KAAD,EAAsBmB,SAAtB,EAA2D;AAC3E,WAAO;AACLhB,MAAAA,CAAC,EAAEH,KAAK,CAACI,OADJ;AAELC,MAAAA,CAAC,EAAEL,KAAK,CAACM,OAFJ;AAGLyC,MAAAA,OAAO,EAAE/C,KAAK,CAAC+C,OAHV;AAILC,MAAAA,OAAO,EAAEhD,KAAK,CAACgD,OAJV;AAKLlC,MAAAA,SAAS,EAAEd,KAAK,CAACc,SALZ;AAMLK,MAAAA,SAAS,EAAEA,SANN;AAOLlB,MAAAA,WAAW,EAAED,KAAK,CAACC,WAPd;AAQLgD,MAAAA,OAAO,EAAEjD,KAAK,CAACiD,OARV;AASLC,MAAAA,IAAI,EAAElD,KAAK,CAACmD;AATP,KAAP;AAWD;;AAEMC,EAAAA,YAAY,GAAS;AAC1B,UAAMA,YAAN;AACA,SAAKpC,eAAL,CAAqB4B,KAArB;AACD;;AApMwE", "sourcesContent": ["import { AdaptedEvent, EventTypes, PointerType } from '../interfaces';\nimport EventManager from './EventManager';\nimport { isPointerInBounds } from '../utils';\n\nconst POINTER_CAPTURE_EXCLUDE_LIST = new Set<string>(['SELECT', 'INPUT']);\n\nexport default class PointerEventManager extends EventManager<HTMLElement> {\n  private trackedPointers = new Set<number>();\n\n  public setListeners(): void {\n    this.view.addEventListener('pointerdown', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n      if (\n        !isPointerInBounds(this.view, { x: event.clientX, y: event.clientY })\n      ) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.DOWN);\n      const target = event.target as HTMLElement;\n\n      if (!POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n        target.setPointerCapture(adaptedEvent.pointerId);\n      }\n\n      this.markAsInBounds(adaptedEvent.pointerId);\n      this.trackedPointers.add(adaptedEvent.pointerId);\n\n      if (++this.activePointersCounter > 1) {\n        adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_DOWN;\n        this.onPointerAdd(adaptedEvent);\n      } else {\n        this.onPointerDown(adaptedEvent);\n      }\n    });\n\n    this.view.addEventListener('pointerup', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n\n      // When we call reset on gesture handlers, it also resets their event managers\n      // In some handlers (like RotationGestureHandler) reset is called before all pointers leave view\n      // This means, that activePointersCounter will be set to 0, while there are still remaining pointers on view\n      // Removing them will end in activePointersCounter going below 0, therefore handlers won't behave properly\n      if (this.activePointersCounter === 0) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.UP);\n      const target = event.target as HTMLElement;\n\n      if (!POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n        target.releasePointerCapture(adaptedEvent.pointerId);\n      }\n\n      this.markAsOutOfBounds(adaptedEvent.pointerId);\n      this.trackedPointers.delete(adaptedEvent.pointerId);\n\n      if (--this.activePointersCounter > 0) {\n        adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_UP;\n        this.onPointerRemove(adaptedEvent);\n      } else {\n        this.onPointerUp(adaptedEvent);\n      }\n    });\n\n    this.view.addEventListener('pointermove', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.MOVE);\n      const target = event.target as HTMLElement;\n\n      // You may be wondering why are we setting pointer capture here, when we\n      // already set it in `pointerdown` handler. Well, that's a great question,\n      // for which I don't have an answer. Specification (https://www.w3.org/TR/pointerevents2/#dom-element-setpointercapture)\n      // says that the requirement for `setPointerCapture` to work is that pointer\n      // must be in 'active buttons state`, otherwise it will fail silently, which\n      // is lovely. Obviously, when `pointerdown` is fired, one of the buttons\n      // (when using mouse) is pressed, but that doesn't mean that `setPointerCapture`\n      // will succeed, for some reason. Since it fails silently, we don't actually know\n      // if it worked or not (there's `gotpointercapture` event, but the complexity of\n      // incorporating it here seems stupid), so we just call it again here, every time\n      // pointer moves until it succeeds.\n      // God, I do love web development.\n      if (\n        !target.hasPointerCapture(event.pointerId) &&\n        !POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)\n      ) {\n        target.setPointerCapture(event.pointerId);\n      }\n\n      const inBounds: boolean = isPointerInBounds(this.view, {\n        x: adaptedEvent.x,\n        y: adaptedEvent.y,\n      });\n\n      const pointerIndex: number = this.pointersInBounds.indexOf(\n        adaptedEvent.pointerId\n      );\n\n      if (inBounds) {\n        if (pointerIndex < 0) {\n          adaptedEvent.eventType = EventTypes.ENTER;\n          this.onPointerEnter(adaptedEvent);\n          this.markAsInBounds(adaptedEvent.pointerId);\n        } else {\n          this.onPointerMove(adaptedEvent);\n        }\n      } else {\n        if (pointerIndex >= 0) {\n          adaptedEvent.eventType = EventTypes.LEAVE;\n          this.onPointerLeave(adaptedEvent);\n          this.markAsOutOfBounds(adaptedEvent.pointerId);\n        } else {\n          this.onPointerOutOfBounds(adaptedEvent);\n        }\n      }\n    });\n\n    this.view.addEventListener('pointercancel', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(\n        event,\n        EventTypes.CANCEL\n      );\n\n      this.onPointerCancel(adaptedEvent);\n      this.markAsOutOfBounds(adaptedEvent.pointerId);\n      this.activePointersCounter = 0;\n      this.trackedPointers.clear();\n    });\n\n    // onPointerEnter and onPointerLeave are triggered by a custom logic responsible for\n    // handling shouldCancelWhenOutside flag, and are unreliable unless the pointer is down.\n    // We therefore use pointerenter and pointerleave events to handle the hover gesture,\n    // mapping them to onPointerMoveOver and onPointerMoveOut respectively.\n\n    this.view.addEventListener('pointerenter', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.ENTER);\n\n      this.onPointerMoveOver(adaptedEvent);\n    });\n\n    this.view.addEventListener('pointerleave', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.LEAVE);\n\n      this.onPointerMoveOut(adaptedEvent);\n    });\n\n    this.view.addEventListener(\n      'lostpointercapture',\n      (event: PointerEvent): void => {\n        const adaptedEvent: AdaptedEvent = this.mapEvent(\n          event,\n          EventTypes.CANCEL\n        );\n\n        if (this.trackedPointers.has(adaptedEvent.pointerId)) {\n          // in some cases the `pointerup` event is not fired, but `lostpointercapture` is\n          // we simulate the `pointercancel` event here to make sure the gesture handler stops tracking it\n          this.onPointerCancel(adaptedEvent);\n\n          this.activePointersCounter = 0;\n          this.trackedPointers.clear();\n        }\n      }\n    );\n  }\n\n  protected mapEvent(event: PointerEvent, eventType: EventTypes): AdaptedEvent {\n    return {\n      x: event.clientX,\n      y: event.clientY,\n      offsetX: event.offsetX,\n      offsetY: event.offsetY,\n      pointerId: event.pointerId,\n      eventType: eventType,\n      pointerType: event.pointerType as PointerType,\n      buttons: event.buttons,\n      time: event.timeStamp,\n    };\n  }\n\n  public resetManager(): void {\n    super.resetManager();\n    this.trackedPointers.clear();\n  }\n}\n"]}