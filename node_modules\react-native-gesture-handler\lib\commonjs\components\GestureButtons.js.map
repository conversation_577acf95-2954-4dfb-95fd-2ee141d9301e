{"version": 3, "sources": ["GestureButtons.tsx"], "names": ["RawButton", "GestureHandlerButton", "shouldCancelWhenOutside", "shouldActivateOnStart", "BaseButton", "React", "Component", "constructor", "props", "nativeEvent", "state", "oldState", "pointerInside", "active", "State", "ACTIVE", "lastActive", "onActiveStateChange", "longPressDetected", "CANCELLED", "onPress", "Platform", "OS", "BEGAN", "onLongPress", "longPressTimeout", "setTimeout", "delayLongPress", "undefined", "clearTimeout", "END", "FAILED", "e", "onHandlerStateChange", "handleEvent", "onGestureEvent", "render", "rippleColor", "rest", "AnimatedBaseButton", "Animated", "createAnimatedComponent", "btnStyles", "StyleSheet", "create", "underlay", "position", "left", "right", "bottom", "top", "RectButton", "opacity", "setValue", "activeOpacity", "Value", "children", "style", "resolvedStyle", "flatten", "backgroundColor", "underlayColor", "borderRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "BorderlessButton", "borderless"], "mappings": ";;;;;;;;;;;;;AAAA;;AACA;;AASA;;AACA;;AACA;;;;;;;;;;;;AA2GO,MAAMA,SAAS,GAAG,kCAAoBC,6BAApB,EAA0C;AACjEC,EAAAA,uBAAuB,EAAE,KADwC;AAEjEC,EAAAA,qBAAqB,EAAE;AAF0C,CAA1C,CAAlB;;;AAKA,MAAMC,UAAN,SAAyBC,KAAK,CAACC,SAA/B,CAA0D;AAS/DC,EAAAA,WAAW,CAACC,KAAD,EAAyB;AAClC,UAAMA,KAAN;;AADkC;;AAAA;;AAAA;;AAAA,yCAMd,CAAC;AACrBC,MAAAA;AADqB,KAAD,KAE0C;AAC9D,YAAM;AAAEC,QAAAA,KAAF;AAASC,QAAAA,QAAT;AAAmBC,QAAAA;AAAnB,UAAqCH,WAA3C;AACA,YAAMI,MAAM,GAAGD,aAAa,IAAIF,KAAK,KAAKI,aAAMC,MAAhD;;AAEA,UAAIF,MAAM,KAAK,KAAKG,UAAhB,IAA8B,KAAKR,KAAL,CAAWS,mBAA7C,EAAkE;AAChE,aAAKT,KAAL,CAAWS,mBAAX,CAA+BJ,MAA/B;AACD;;AAED,UACE,CAAC,KAAKK,iBAAN,IACAP,QAAQ,KAAKG,aAAMC,MADnB,IAEAL,KAAK,KAAKI,aAAMK,SAFhB,IAGA,KAAKH,UAHL,IAIA,KAAKR,KAAL,CAAWY,OALb,EAME;AACA,aAAKZ,KAAL,CAAWY,OAAX,CAAmBP,MAAnB;AACD;;AAED,UACE,CAAC,KAAKG,UAAN,IACA;AACAN,MAAAA,KAAK,MAAMW,sBAASC,EAAT,KAAgB,SAAhB,GAA4BR,aAAMC,MAAlC,GAA2CD,aAAMS,KAAvD,CAFL,IAGAX,aAJF,EAKE;AACA,aAAKM,iBAAL,GAAyB,KAAzB;;AACA,YAAI,KAAKV,KAAL,CAAWgB,WAAf,EAA4B;AAC1B,eAAKC,gBAAL,GAAwBC,UAAU,CAChC,KAAKF,WAD2B,EAEhC,KAAKhB,KAAL,CAAWmB,cAFqB,CAAlC;AAID;AACF,OAbD,MAaO,KACL;AACAjB,MAAAA,KAAK,KAAKI,aAAMC,MAAhB,IACA,CAACH,aADD,IAEA,KAAKa,gBAAL,KAA0BG,SAJrB,EAKL;AACAC,QAAAA,YAAY,CAAC,KAAKJ,gBAAN,CAAZ;AACA,aAAKA,gBAAL,GAAwBG,SAAxB;AACD,OARM,MAQA,KACL;AACA,WAAKH,gBAAL,KAA0BG,SAA1B,KACClB,KAAK,KAAKI,aAAMgB,GAAhB,IACCpB,KAAK,KAAKI,aAAMK,SADjB,IAECT,KAAK,KAAKI,aAAMiB,MAHlB,CAFK,EAML;AACAF,QAAAA,YAAY,CAAC,KAAKJ,gBAAN,CAAZ;AACA,aAAKA,gBAAL,GAAwBG,SAAxB;AACD;;AAED,WAAKZ,UAAL,GAAkBH,MAAlB;AACD,KA3DmC;;AAAA,yCA6Dd,MAAM;AAAA;;AAC1B,WAAKK,iBAAL,GAAyB,IAAzB;AACA,mDAAKV,KAAL,EAAWgB,WAAX;AACD,KAhEmC;;AAAA,kDAuElCQ,CAD6B,IAE1B;AAAA;;AACH,oDAAKxB,KAAL,EAAWyB,oBAAX,mGAAkCD,CAAlC;AACA,WAAKE,WAAL,CAAiBF,CAAjB;AACD,KA3EmC;;AAAA,4CA8ElCA,CADuB,IAEpB;AAAA;;AACH,oDAAKxB,KAAL,EAAW2B,cAAX,mGAA4BH,CAA5B;AACA,WAAKE,WAAL,CACEF,CADF,EAFG,CAIA;AACJ,KApFmC;;AAElC,SAAKhB,UAAL,GAAkB,KAAlB;AACA,SAAKE,iBAAL,GAAyB,KAAzB;AACD;;AAkFDkB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEC,MAAAA,WAAF;AAAe,SAAGC;AAAlB,QAA2B,KAAK9B,KAAtC;AAEA,wBACE,oBAAC,SAAD;AACE,MAAA,WAAW,EAAE,+BAAa6B,WAAb;AADf,OAEMC,IAFN;AAGE,MAAA,cAAc,EAAE,KAAKH,cAHvB;AAIE,MAAA,oBAAoB,EAAE,KAAKF;AAJ7B,OADF;AAQD;;AA1G8D;;;;gBAApD7B,U,kBACW;AACpBuB,EAAAA,cAAc,EAAE;AADI,C;;AA4GxB,MAAMY,kBAAkB,GAAGC,sBAASC,uBAAT,CAAiCrC,UAAjC,CAA3B;;AAEA,MAAMsC,SAAS,GAAGC,wBAAWC,MAAX,CAAkB;AAClCC,EAAAA,QAAQ,EAAE;AACRC,IAAAA,QAAQ,EAAE,UADF;AAERC,IAAAA,IAAI,EAAE,CAFE;AAGRC,IAAAA,KAAK,EAAE,CAHC;AAIRC,IAAAA,MAAM,EAAE,CAJA;AAKRC,IAAAA,GAAG,EAAE;AALG;AADwB,CAAlB,CAAlB;;AAUO,MAAMC,UAAN,SAAyB9C,KAAK,CAACC,SAA/B,CAA0D;AAQ/DC,EAAAA,WAAW,CAACC,KAAD,EAAyB;AAClC,UAAMA,KAAN;;AADkC;;AAAA,iDAKLK,MAAD,IAAqB;AAAA;;AACjD,UAAIQ,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,aAAK8B,OAAL,CAAaC,QAAb,CAAsBxC,MAAM,GAAG,KAAKL,KAAL,CAAW8C,aAAd,GAA+B,CAA3D;AACD;;AAED,oDAAK9C,KAAL,EAAWS,mBAAX,mGAAiCJ,MAAjC;AACD,KAXmC;;AAElC,SAAKuC,OAAL,GAAe,IAAIZ,sBAASe,KAAb,CAAmB,CAAnB,CAAf;AACD;;AAUDnB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEoB,MAAAA,QAAF;AAAYC,MAAAA,KAAZ;AAAmB,SAAGnB;AAAtB,QAA+B,KAAK9B,KAA1C;;AAEA,UAAMkD,aAAa,GAAGf,wBAAWgB,OAAX,CAAmBF,KAAnB,aAAmBA,KAAnB,cAAmBA,KAAnB,GAA4B,EAA5B,CAAtB;;AAEA,wBACE,oBAAC,UAAD,eACMnB,IADN;AAEE,MAAA,KAAK,EAAEoB,aAFT;AAGE,MAAA,mBAAmB,EAAE,KAAKzC;AAH5B,qBAIE,oBAAC,qBAAD,CAAU,IAAV;AACE,MAAA,KAAK,EAAE,CACLyB,SAAS,CAACG,QADL,EAEL;AACEO,QAAAA,OAAO,EAAE,KAAKA,OADhB;AAEEQ,QAAAA,eAAe,EAAE,KAAKpD,KAAL,CAAWqD,aAF9B;AAGEC,QAAAA,YAAY,EAAEJ,aAAa,CAACI,YAH9B;AAIEC,QAAAA,mBAAmB,EAAEL,aAAa,CAACK,mBAJrC;AAKEC,QAAAA,oBAAoB,EAAEN,aAAa,CAACM,oBALtC;AAMEC,QAAAA,sBAAsB,EAAEP,aAAa,CAACO,sBANxC;AAOEC,QAAAA,uBAAuB,EAAER,aAAa,CAACQ;AAPzC,OAFK;AADT,MAJF,EAkBGV,QAlBH,CADF;AAsBD;;AAhD8D;;;;gBAApDL,U,kBACW;AACpBG,EAAAA,aAAa,EAAE,KADK;AAEpBO,EAAAA,aAAa,EAAE;AAFK,C;;AAkDjB,MAAMM,gBAAN,SAA+B9D,KAAK,CAACC,SAArC,CAAsE;AAQ3EC,EAAAA,WAAW,CAACC,KAAD,EAA+B;AACxC,UAAMA,KAAN;;AADwC;;AAAA,iDAKXK,MAAD,IAAqB;AAAA;;AACjD,UAAIQ,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,aAAK8B,OAAL,CAAaC,QAAb,CAAsBxC,MAAM,GAAG,KAAKL,KAAL,CAAW8C,aAAd,GAA+B,CAA3D;AACD;;AAED,qDAAK9C,KAAL,EAAWS,mBAAX,qGAAiCJ,MAAjC;AACD,KAXyC;;AAExC,SAAKuC,OAAL,GAAe,IAAIZ,sBAASe,KAAb,CAAmB,CAAnB,CAAf;AACD;;AAUDnB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEoB,MAAAA,QAAF;AAAYC,MAAAA,KAAZ;AAAmB,SAAGnB;AAAtB,QAA+B,KAAK9B,KAA1C;AAEA,wBACE,oBAAC,kBAAD,eACM8B,IADN;AAEE,MAAA,mBAAmB,EAAE,KAAKrB,mBAF5B;AAGE,MAAA,KAAK,EAAE,CAACwC,KAAD,EAAQpC,sBAASC,EAAT,KAAgB,KAAhB,IAAyB;AAAE8B,QAAAA,OAAO,EAAE,KAAKA;AAAhB,OAAjC;AAHT,QAIGI,QAJH,CADF;AAQD;;AAhC0E;;;;gBAAhEW,gB,kBACW;AACpBb,EAAAA,aAAa,EAAE,GADK;AAEpBc,EAAAA,UAAU,EAAE;AAFQ,C", "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  Platform,\n  processColor,\n  StyleSheet,\n  StyleProp,\n  ViewStyle,\n} from 'react-native';\n\nimport createNativeWrapper from '../handlers/createNativeWrapper';\nimport GestureHandlerButton from './GestureHandlerButton';\nimport { State } from '../State';\n\nimport {\n  GestureEvent,\n  HandlerStateChangeEvent,\n} from '../handlers/gestureHandlerCommon';\nimport {\n  NativeViewGestureHandlerPayload,\n  NativeViewGestureHandlerProps,\n} from '../handlers/NativeViewGestureHandler';\n\nexport interface RawButtonProps extends NativeViewGestureHandlerProps {\n  /**\n   * Defines if more than one button could be pressed simultaneously. By default\n   * set true.\n   */\n  exclusive?: boolean;\n  // TODO: we should transform props in `createNativeWrapper`\n\n  /**\n   * Android only.\n   *\n   * Defines color of native ripple animation used since API level 21.\n   */\n  rippleColor?: any; // it was present in BaseButtonProps before but is used here in code\n\n  /**\n   * Android only.\n   *\n   * Defines radius of native ripple animation used since API level 21.\n   */\n  rippleRadius?: number | null;\n\n  /**\n   * Android only.\n   *\n   * Set this to true if you want the ripple animation to render outside the view bounds.\n   */\n  borderless?: boolean;\n\n  /**\n   * Android only.\n   *\n   * Defines whether the ripple animation should be drawn on the foreground of the view.\n   */\n  foreground?: boolean;\n\n  /**\n   * Android only.\n   *\n   * Set this to true if you don't want the system to play sound when the button is pressed.\n   */\n  touchSoundDisabled?: boolean;\n}\n\nexport interface BaseButtonProps extends RawButtonProps {\n  /**\n   * Called when the button gets pressed (analogous to `onPress` in\n   * `TouchableHighlight` from RN core).\n   */\n  onPress?: (pointerInside: boolean) => void;\n\n  /**\n   * Called when the button gets pressed and is held for `delayLongPress`\n   * milliseconds.\n   */\n  onLongPress?: () => void;\n\n  /**\n   * Called when button changes from inactive to active and vice versa. It\n   * passes active state as a boolean variable as a first parameter for that\n   * method.\n   */\n  onActiveStateChange?: (active: boolean) => void;\n  style?: StyleProp<ViewStyle>;\n  testID?: string;\n\n  /**\n   * Delay, in milliseconds, after which the `onLongPress` callback gets called.\n   * Defaults to 600.\n   */\n  delayLongPress?: number;\n}\n\nexport interface RectButtonProps extends BaseButtonProps {\n  /**\n   * Background color that will be dimmed when button is in active state.\n   */\n  underlayColor?: string;\n\n  /**\n   * iOS only.\n   *\n   * Opacity applied to the underlay when button is in active state.\n   */\n  activeOpacity?: number;\n}\n\nexport interface BorderlessButtonProps extends BaseButtonProps {\n  /**\n   * iOS only.\n   *\n   * Opacity applied to the button when it is in an active state.\n   */\n  activeOpacity?: number;\n}\n\nexport const RawButton = createNativeWrapper(GestureHandlerButton, {\n  shouldCancelWhenOutside: false,\n  shouldActivateOnStart: false,\n});\n\nexport class BaseButton extends React.Component<BaseButtonProps> {\n  static defaultProps = {\n    delayLongPress: 600,\n  };\n\n  private lastActive: boolean;\n  private longPressTimeout: ReturnType<typeof setTimeout> | undefined;\n  private longPressDetected: boolean;\n\n  constructor(props: BaseButtonProps) {\n    super(props);\n    this.lastActive = false;\n    this.longPressDetected = false;\n  }\n\n  private handleEvent = ({\n    nativeEvent,\n  }: HandlerStateChangeEvent<NativeViewGestureHandlerPayload>) => {\n    const { state, oldState, pointerInside } = nativeEvent;\n    const active = pointerInside && state === State.ACTIVE;\n\n    if (active !== this.lastActive && this.props.onActiveStateChange) {\n      this.props.onActiveStateChange(active);\n    }\n\n    if (\n      !this.longPressDetected &&\n      oldState === State.ACTIVE &&\n      state !== State.CANCELLED &&\n      this.lastActive &&\n      this.props.onPress\n    ) {\n      this.props.onPress(active);\n    }\n\n    if (\n      !this.lastActive &&\n      // NativeViewGestureHandler sends different events based on platform\n      state === (Platform.OS !== 'android' ? State.ACTIVE : State.BEGAN) &&\n      pointerInside\n    ) {\n      this.longPressDetected = false;\n      if (this.props.onLongPress) {\n        this.longPressTimeout = setTimeout(\n          this.onLongPress,\n          this.props.delayLongPress\n        );\n      }\n    } else if (\n      // cancel longpress timeout if it's set and the finger moved out of the view\n      state === State.ACTIVE &&\n      !pointerInside &&\n      this.longPressTimeout !== undefined\n    ) {\n      clearTimeout(this.longPressTimeout);\n      this.longPressTimeout = undefined;\n    } else if (\n      // cancel longpress timeout if it's set and the gesture has finished\n      this.longPressTimeout !== undefined &&\n      (state === State.END ||\n        state === State.CANCELLED ||\n        state === State.FAILED)\n    ) {\n      clearTimeout(this.longPressTimeout);\n      this.longPressTimeout = undefined;\n    }\n\n    this.lastActive = active;\n  };\n\n  private onLongPress = () => {\n    this.longPressDetected = true;\n    this.props.onLongPress?.();\n  };\n\n  // Normally, the parent would execute it's handler first, then forward the\n  // event to listeners. However, here our handler is virtually only forwarding\n  // events to listeners, so we reverse the order to keep the proper order of\n  // the callbacks (from \"raw\" ones to \"processed\").\n  private onHandlerStateChange = (\n    e: HandlerStateChangeEvent<NativeViewGestureHandlerPayload>\n  ) => {\n    this.props.onHandlerStateChange?.(e);\n    this.handleEvent(e);\n  };\n\n  private onGestureEvent = (\n    e: GestureEvent<NativeViewGestureHandlerPayload>\n  ) => {\n    this.props.onGestureEvent?.(e);\n    this.handleEvent(\n      e as HandlerStateChangeEvent<NativeViewGestureHandlerPayload>\n    ); // TODO: maybe it is not correct\n  };\n\n  render() {\n    const { rippleColor, ...rest } = this.props;\n\n    return (\n      <RawButton\n        rippleColor={processColor(rippleColor)}\n        {...rest}\n        onGestureEvent={this.onGestureEvent}\n        onHandlerStateChange={this.onHandlerStateChange}\n      />\n    );\n  }\n}\n\nconst AnimatedBaseButton = Animated.createAnimatedComponent(BaseButton);\n\nconst btnStyles = StyleSheet.create({\n  underlay: {\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    bottom: 0,\n    top: 0,\n  },\n});\n\nexport class RectButton extends React.Component<RectButtonProps> {\n  static defaultProps = {\n    activeOpacity: 0.105,\n    underlayColor: 'black',\n  };\n\n  private opacity: Animated.Value;\n\n  constructor(props: RectButtonProps) {\n    super(props);\n    this.opacity = new Animated.Value(0);\n  }\n\n  private onActiveStateChange = (active: boolean) => {\n    if (Platform.OS !== 'android') {\n      this.opacity.setValue(active ? this.props.activeOpacity! : 0);\n    }\n\n    this.props.onActiveStateChange?.(active);\n  };\n\n  render() {\n    const { children, style, ...rest } = this.props;\n\n    const resolvedStyle = StyleSheet.flatten(style ?? {});\n\n    return (\n      <BaseButton\n        {...rest}\n        style={resolvedStyle}\n        onActiveStateChange={this.onActiveStateChange}>\n        <Animated.View\n          style={[\n            btnStyles.underlay,\n            {\n              opacity: this.opacity,\n              backgroundColor: this.props.underlayColor,\n              borderRadius: resolvedStyle.borderRadius,\n              borderTopLeftRadius: resolvedStyle.borderTopLeftRadius,\n              borderTopRightRadius: resolvedStyle.borderTopRightRadius,\n              borderBottomLeftRadius: resolvedStyle.borderBottomLeftRadius,\n              borderBottomRightRadius: resolvedStyle.borderBottomRightRadius,\n            },\n          ]}\n        />\n        {children}\n      </BaseButton>\n    );\n  }\n}\n\nexport class BorderlessButton extends React.Component<BorderlessButtonProps> {\n  static defaultProps = {\n    activeOpacity: 0.3,\n    borderless: true,\n  };\n\n  private opacity: Animated.Value;\n\n  constructor(props: BorderlessButtonProps) {\n    super(props);\n    this.opacity = new Animated.Value(1);\n  }\n\n  private onActiveStateChange = (active: boolean) => {\n    if (Platform.OS !== 'android') {\n      this.opacity.setValue(active ? this.props.activeOpacity! : 1);\n    }\n\n    this.props.onActiveStateChange?.(active);\n  };\n\n  render() {\n    const { children, style, ...rest } = this.props;\n\n    return (\n      <AnimatedBaseButton\n        {...rest}\n        onActiveStateChange={this.onActiveStateChange}\n        style={[style, Platform.OS === 'ios' && { opacity: this.opacity }]}>\n        {children}\n      </AnimatedBaseButton>\n    );\n  }\n}\n\nexport { default as PureNativeButton } from './GestureHandlerButton';\n"]}