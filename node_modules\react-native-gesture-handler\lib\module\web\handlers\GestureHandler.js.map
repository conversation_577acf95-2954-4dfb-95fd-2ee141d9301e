{"version": 3, "sources": ["GestureHandler.ts"], "names": ["State", "PointerType", "TouchEventType", "EventTypes", "GestureHandlerOrchestrator", "InteractionManager", "PointerTracker", "Gesture<PERSON>andler", "constructor", "delegate", "UNDETERMINED", "enabled", "NONE", "newState", "oldState", "onGestureHandlerEvent", "onGestureHandlerStateChange", "propsRef", "current", "resultEvent", "transformEventData", "lastSentState", "invokeNullableMethod", "currentState", "ACTIVE", "nativeEvent", "undefined", "init", "viewRef", "attachEventManager", "manager", "setOnPointerDown", "onPointerDown", "bind", "setOnPointerAdd", "onPointerAdd", "setOnPointerUp", "onPointerUp", "setOnPointerRemove", "onPointerRemove", "setOnPointerMove", "onPointerMove", "setOnPointerEnter", "onPointerEnter", "setOnPointerLeave", "onPointerLeave", "setOnPointerCancel", "onPointerCancel", "setOnPointerOutOfBounds", "onPointerOutOfBounds", "setOnPointerMoveOver", "onPointerMoveOver", "setOnPointerMoveOut", "onPointerMoveOut", "setListeners", "onCancel", "onReset", "resetProgress", "reset", "tracker", "resetTracker", "moveToState", "sendIfDisabled", "getTrackedPointersCount", "config", "needsPointerData", "isFinished", "cancelTouches", "getInstance", "onHandlerStateChange", "onStateChange", "_newState", "_oldState", "begin", "checkHitSlop", "BEGAN", "fail", "onFail", "FAILED", "cancel", "CANCELLED", "activate", "_force", "onActivate", "end", "onEnd", "END", "isAwaiting", "awaiting", "setAwaiting", "value", "isActive", "active", "setActive", "getShouldResetProgress", "shouldResetProgress", "setShouldResetProgress", "getActivationIndex", "activationIndex", "setActivationIndex", "shouldWaitForHandlerFailure", "handler", "shouldRequireToWaitForFailure", "shouldRequireHandlerToWaitForFailure", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "shouldHandlerBeCancelledBy", "event", "recordHandlerIfNotPresent", "pointerType", "TOUCH", "cancelMouseAndPenGestures", "sendTouchEvent", "tryToSendMoveEvent", "shouldCancellWhenOutside", "_event", "out", "sendEvent", "touchEvent", "transformTouchEvent", "numberOfPointers", "state", "pointerInside", "isPointerInBounds", "x", "getLastAvgX", "y", "getLastAvgY", "transformNativeEvent", "handlerTag", "target", "timeStamp", "Date", "now", "rect", "measure<PERSON>iew", "all", "changed", "trackerData", "getData", "size", "has", "pointerId", "for<PERSON>ach", "element", "key", "id", "getMappedTouchEventId", "push", "lastX", "pageX", "lastY", "pageY", "absoluteX", "absoluteY", "eventType", "CANCEL", "DOWN", "ADDITIONAL_POINTER_DOWN", "UP", "ADDITIONAL_POINTER_UP", "MOVE", "numberOfTouches", "length", "touchEventType", "changedTouches", "allTouches", "cancelEvent", "updateGestureConfig", "props", "shouldCancelWhenOutside", "setShouldCancelWhenOutside", "validateHitSlops", "removeHandlerFromOrchestrator", "checkCustomActivationCriteria", "criterias", "indexOf", "hasCustomActivationCriteria", "hitSlop", "left", "right", "width", "Error", "height", "top", "bottom", "horizontal", "vertical", "offsetX", "getLastX", "offsetY", "getLastY", "resetConfig", "getTag", "setTag", "tag", "getConfig", "getDelegate", "getTracker", "getTrackedPointersID", "getState", "isEnabled", "shouldCancel", "getShouldCancelWhenOutside", "getPointerType", "method", "__<PERSON><PERSON><PERSON><PERSON>", "arg<PERSON><PERSON><PERSON>", "__nodeConfig", "Array", "isArray", "index", "entries", "nativeValue", "setValue"], "mappings": ";;AAAA;AACA,SAASA,KAAT,QAAsB,aAAtB;AACA,SAOEC,WAPF,EAQEC,cARF,EASEC,UATF,QAUO,eAVP;AAYA,OAAOC,0BAAP,MAAuC,qCAAvC;AACA,OAAOC,kBAAP,MAA+B,6BAA/B;AACA,OAAOC,cAAP,MAA+C,yBAA/C;AAGA,eAAe,MAAeC,cAAf,CAA8B;AAe3C;AASOC,EAAAA,WAAW,CAACC,QAAD,EAA4C;AAAA,2CAvBxB,IAuBwB;;AAAA,0CAtB9BT,KAAK,CAACU,YAsBwB;;AAAA,sDApBzB,KAoByB;;AAAA,yDAnBtB,KAmBsB;;AAAA,qCAlB1C,KAkB0C;;AAAA;;AAAA;;AAAA;;AAAA,oCAbnC;AAAEC,MAAAA,OAAO,EAAE;AAAX,KAamC;;AAAA,qCAX1B,IAAIL,cAAJ,EAW0B;;AAAA,6CARlC,CAQkC;;AAAA,sCAPzC,KAOyC;;AAAA,oCAN3C,KAM2C;;AAAA,iDAL9B,KAK8B;;AAAA,yCAJzBL,WAAW,CAACW,IAIa;;AAAA;;AAAA,uCAkV3C,CAACC,QAAD,EAAkBC,QAAlB,KAA4C;AAC7D,YAAM;AAAEC,QAAAA,qBAAF;AAAyBC,QAAAA;AAAzB,UACJ,KAAKC,QAAL,CAAcC,OADhB;AAGA,YAAMC,WAAwB,GAAG,KAAKC,kBAAL,CAC/BP,QAD+B,EAE/BC,QAF+B,CAAjC,CAJ6D,CAS7D;AACA;AACA;AACA;;AAEA,UAAI,KAAKO,aAAL,KAAuBR,QAA3B,EAAqC;AACnC,aAAKQ,aAAL,GAAqBR,QAArB;AACAS,QAAAA,oBAAoB,CAACN,2BAAD,EAA8BG,WAA9B,CAApB;AACD;;AACD,UAAI,KAAKI,YAAL,KAAsBvB,KAAK,CAACwB,MAAhC,EAAwC;AACtCL,QAAAA,WAAW,CAACM,WAAZ,CAAwBX,QAAxB,GAAmCY,SAAnC;AACAJ,QAAAA,oBAAoB,CAACP,qBAAD,EAAwBI,WAAxB,CAApB;AACD;AACF,KAxW6D;;AAC5D,SAAKV,QAAL,GAAgBA,QAAhB;AACD,GA1B0C,CA4B3C;AACA;AACA;;;AAEUkB,EAAAA,IAAI,CAACC,OAAD,EAAkBX,QAAlB,EAAsD;AAClE,SAAKA,QAAL,GAAgBA,QAAhB;AACA,SAAKW,OAAL,GAAeA,OAAf;AAEA,SAAKL,YAAL,GAAoBvB,KAAK,CAACU,YAA1B;AAEA,SAAKD,QAAL,CAAckB,IAAd,CAAmBC,OAAnB,EAA4B,IAA5B;AACD;;AAEMC,EAAAA,kBAAkB,CAACC,OAAD,EAAuC;AAC9DA,IAAAA,OAAO,CAACC,gBAAR,CAAyB,KAAKC,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAAzB;AACAH,IAAAA,OAAO,CAACI,eAAR,CAAwB,KAAKC,YAAL,CAAkBF,IAAlB,CAAuB,IAAvB,CAAxB;AACAH,IAAAA,OAAO,CAACM,cAAR,CAAuB,KAAKC,WAAL,CAAiBJ,IAAjB,CAAsB,IAAtB,CAAvB;AACAH,IAAAA,OAAO,CAACQ,kBAAR,CAA2B,KAAKC,eAAL,CAAqBN,IAArB,CAA0B,IAA1B,CAA3B;AACAH,IAAAA,OAAO,CAACU,gBAAR,CAAyB,KAAKC,aAAL,CAAmBR,IAAnB,CAAwB,IAAxB,CAAzB;AACAH,IAAAA,OAAO,CAACY,iBAAR,CAA0B,KAAKC,cAAL,CAAoBV,IAApB,CAAyB,IAAzB,CAA1B;AACAH,IAAAA,OAAO,CAACc,iBAAR,CAA0B,KAAKC,cAAL,CAAoBZ,IAApB,CAAyB,IAAzB,CAA1B;AACAH,IAAAA,OAAO,CAACgB,kBAAR,CAA2B,KAAKC,eAAL,CAAqBd,IAArB,CAA0B,IAA1B,CAA3B;AACAH,IAAAA,OAAO,CAACkB,uBAAR,CAAgC,KAAKC,oBAAL,CAA0BhB,IAA1B,CAA+B,IAA/B,CAAhC;AACAH,IAAAA,OAAO,CAACoB,oBAAR,CAA6B,KAAKC,iBAAL,CAAuBlB,IAAvB,CAA4B,IAA5B,CAA7B;AACAH,IAAAA,OAAO,CAACsB,mBAAR,CAA4B,KAAKC,gBAAL,CAAsBpB,IAAtB,CAA2B,IAA3B,CAA5B;AACAH,IAAAA,OAAO,CAACwB,YAAR;AACD,GAtD0C,CAwD3C;AACA;AACA;;;AAEUC,EAAAA,QAAQ,GAAS,CAAE;;AACnBC,EAAAA,OAAO,GAAS,CAAE;;AAClBC,EAAAA,aAAa,GAAS,CAAE;;AAE3BC,EAAAA,KAAK,GAAS;AACnB,SAAKC,OAAL,CAAaC,YAAb;AACA,SAAKJ,OAAL;AACA,SAAKC,aAAL;AACA,SAAKhD,QAAL,CAAciD,KAAd;AACA,SAAKnC,YAAL,GAAoBvB,KAAK,CAACU,YAA1B;AACD,GAtE0C,CAwE3C;AACA;AACA;;;AAEOmD,EAAAA,WAAW,CAAChD,QAAD,EAAkBiD,cAAlB,EAA4C;AAC5D,QAAI,KAAKvC,YAAL,KAAsBV,QAA1B,EAAoC;AAClC;AACD;;AAED,UAAMC,QAAQ,GAAG,KAAKS,YAAtB;AACA,SAAKA,YAAL,GAAoBV,QAApB;;AAEA,QACE,KAAK8C,OAAL,CAAaI,uBAAb,KAAyC,CAAzC,IACA,KAAKC,MAAL,CAAYC,gBADZ,IAEA,KAAKC,UAAL,EAHF,EAIE;AACA,WAAKC,aAAL;AACD;;AAED/D,IAAAA,0BAA0B,CAACgE,WAA3B,GAAyCC,oBAAzC,CACE,IADF,EAEExD,QAFF,EAGEC,QAHF,EAIEgD,cAJF;AAOA,SAAKQ,aAAL,CAAmBzD,QAAnB,EAA6BC,QAA7B;AACD;;AAESwD,EAAAA,aAAa,CAACC,SAAD,EAAmBC,SAAnB,EAA2C,CAAE;;AAE7DC,EAAAA,KAAK,GAAS;AACnB,QAAI,CAAC,KAAKC,YAAL,EAAL,EAA0B;AACxB;AACD;;AAED,QAAI,KAAKnD,YAAL,KAAsBvB,KAAK,CAACU,YAAhC,EAA8C;AAC5C,WAAKmD,WAAL,CAAiB7D,KAAK,CAAC2E,KAAvB;AACD;AACF;AAED;AACF;AACA;;;AACSC,EAAAA,IAAI,CAACd,cAAD,EAAiC;AAC1C,QACE,KAAKvC,YAAL,KAAsBvB,KAAK,CAACwB,MAA5B,IACA,KAAKD,YAAL,KAAsBvB,KAAK,CAAC2E,KAF9B,EAGE;AACA;AACA;AACA,WAAKlE,QAAL,CAAcoE,MAAd;AAEA,WAAKhB,WAAL,CAAiB7D,KAAK,CAAC8E,MAAvB,EAA+BhB,cAA/B;AACD;;AAED,SAAKL,aAAL;AACD;AAED;AACF;AACA;;;AACSsB,EAAAA,MAAM,CAACjB,cAAD,EAAiC;AAC5C,QACE,KAAKvC,YAAL,KAAsBvB,KAAK,CAACwB,MAA5B,IACA,KAAKD,YAAL,KAAsBvB,KAAK,CAACU,YAD5B,IAEA,KAAKa,YAAL,KAAsBvB,KAAK,CAAC2E,KAH9B,EAIE;AACA,WAAKpB,QAAL,GADA,CAGA;;AACA,WAAK9C,QAAL,CAAc8C,QAAd;AAEA,WAAKM,WAAL,CAAiB7D,KAAK,CAACgF,SAAvB,EAAkClB,cAAlC;AACD;AACF;;AAEMmB,EAAAA,QAAQ,CAACC,MAAM,GAAG,KAAV,EAAiB;AAC9B,QACE,KAAK3D,YAAL,KAAsBvB,KAAK,CAACU,YAA5B,IACA,KAAKa,YAAL,KAAsBvB,KAAK,CAAC2E,KAF9B,EAGE;AACA,WAAKlE,QAAL,CAAc0E,UAAd;AAEA,WAAKtB,WAAL,CAAiB7D,KAAK,CAACwB,MAAvB;AACD;AACF;;AAEM4D,EAAAA,GAAG,GAAG;AACX,QACE,KAAK7D,YAAL,KAAsBvB,KAAK,CAAC2E,KAA5B,IACA,KAAKpD,YAAL,KAAsBvB,KAAK,CAACwB,MAF9B,EAGE;AACA;AACA,WAAKf,QAAL,CAAc4E,KAAd;AAEA,WAAKxB,WAAL,CAAiB7D,KAAK,CAACsF,GAAvB;AACD;;AAED,SAAK7B,aAAL;AACD,GA7K0C,CA+K3C;AACA;AACA;;;AAEO8B,EAAAA,UAAU,GAAY;AAC3B,WAAO,KAAKC,QAAZ;AACD;;AACMC,EAAAA,WAAW,CAACC,KAAD,EAAuB;AACvC,SAAKF,QAAL,GAAgBE,KAAhB;AACD;;AAEMC,EAAAA,QAAQ,GAAY;AACzB,WAAO,KAAKC,MAAZ;AACD;;AACMC,EAAAA,SAAS,CAACH,KAAD,EAAuB;AACrC,SAAKE,MAAL,GAAcF,KAAd;AACD;;AAEMI,EAAAA,sBAAsB,GAAY;AACvC,WAAO,KAAKC,mBAAZ;AACD;;AACMC,EAAAA,sBAAsB,CAACN,KAAD,EAAuB;AAClD,SAAKK,mBAAL,GAA2BL,KAA3B;AACD;;AAEMO,EAAAA,kBAAkB,GAAW;AAClC,WAAO,KAAKC,eAAZ;AACD;;AACMC,EAAAA,kBAAkB,CAACT,KAAD,EAAsB;AAC7C,SAAKQ,eAAL,GAAuBR,KAAvB;AACD;;AAEMU,EAAAA,2BAA2B,CAACC,OAAD,EAAmC;AACnE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,KAAP;AACD;;AAED,WAAOhG,kBAAkB,CAAC+D,WAAnB,GAAiCgC,2BAAjC,CACL,IADK,EAELC,OAFK,CAAP;AAID;;AAEMC,EAAAA,6BAA6B,CAACD,OAAD,EAAmC;AACrE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,KAAP;AACD;;AAED,WAAOhG,kBAAkB,CAAC+D,WAAnB,GAAiCmC,oCAAjC,CACL,IADK,EAELF,OAFK,CAAP;AAID;;AAEMG,EAAAA,6BAA6B,CAACH,OAAD,EAAmC;AACrE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,IAAP;AACD;;AAED,WAAOhG,kBAAkB,CAAC+D,WAAnB,GAAiCoC,6BAAjC,CACL,IADK,EAELH,OAFK,CAAP;AAID;;AAEMI,EAAAA,wBAAwB,CAACJ,OAAD,EAAmC;AAChE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,KAAP;AACD;;AAED,WAAOhG,kBAAkB,CAAC+D,WAAnB,GAAiCsC,0BAAjC,CACL,IADK,EAELL,OAFK,CAAP;AAID,GAzP0C,CA2P3C;AACA;AACA;;;AAEUrE,EAAAA,aAAa,CAAC2E,KAAD,EAA4B;AACjDvG,IAAAA,0BAA0B,CAACgE,WAA3B,GAAyCwC,yBAAzC,CAAmE,IAAnE;AACA,SAAKC,WAAL,GAAmBF,KAAK,CAACE,WAAzB;;AAEA,QAAI,KAAKA,WAAL,KAAqB5G,WAAW,CAAC6G,KAArC,EAA4C;AAC1C1G,MAAAA,0BAA0B,CAACgE,WAA3B,GAAyC2C,yBAAzC,CAAmE,IAAnE;AACD;;AAED,QAAI,KAAK/C,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAK+C,cAAL,CAAoBL,KAApB;AACD;AACF,GA1Q0C,CA2Q3C;;;AACUxE,EAAAA,YAAY,CAACwE,KAAD,EAA4B;AAChD,QAAI,KAAK3C,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAK+C,cAAL,CAAoBL,KAApB;AACD;AACF;;AACStE,EAAAA,WAAW,CAACsE,KAAD,EAA4B;AAC/C,QAAI,KAAK3C,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAK+C,cAAL,CAAoBL,KAApB;AACD;AACF,GArR0C,CAsR3C;;;AACUpE,EAAAA,eAAe,CAACoE,KAAD,EAA4B;AACnD,QAAI,KAAK3C,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAK+C,cAAL,CAAoBL,KAApB;AACD;AACF;;AACSlE,EAAAA,aAAa,CAACkE,KAAD,EAA4B;AACjD,SAAKM,kBAAL,CAAwB,KAAxB;;AACA,QAAI,KAAKjD,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAK+C,cAAL,CAAoBL,KAApB;AACD;AACF;;AACS9D,EAAAA,cAAc,CAAC8D,KAAD,EAA4B;AAClD,QAAI,KAAKO,wBAAT,EAAmC;AACjC,cAAQ,KAAK3F,YAAb;AACE,aAAKvB,KAAK,CAACwB,MAAX;AACE,eAAKuD,MAAL;AACA;;AACF,aAAK/E,KAAK,CAAC2E,KAAX;AACE,eAAKC,IAAL;AACA;AANJ;;AAQA;AACD;;AAED,QAAI,KAAKZ,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAK+C,cAAL,CAAoBL,KAApB;AACD;AACF;;AACShE,EAAAA,cAAc,CAACgE,KAAD,EAA4B;AAClD,QAAI,KAAK3C,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAK+C,cAAL,CAAoBL,KAApB;AACD;AACF;;AACS5D,EAAAA,eAAe,CAAC4D,KAAD,EAA4B;AACnD,QAAI,KAAK3C,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAK+C,cAAL,CAAoBL,KAApB;AACD;;AAED,SAAK5B,MAAL;AACA,SAAKrB,KAAL;AACD;;AACST,EAAAA,oBAAoB,CAAC0D,KAAD,EAA4B;AACxD,SAAKM,kBAAL,CAAwB,IAAxB;;AACA,QAAI,KAAKjD,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAK+C,cAAL,CAAoBL,KAApB;AACD;AACF;;AACSxD,EAAAA,iBAAiB,CAACgE,MAAD,EAA6B,CACtD;AACD;;AACS9D,EAAAA,gBAAgB,CAAC8D,MAAD,EAA6B,CACrD;AACD;;AACOF,EAAAA,kBAAkB,CAACG,GAAD,EAAqB;AAC7C,QACE,KAAKzG,OAAL,IACA,KAAKiF,MADL,KAEC,CAACwB,GAAD,IAASA,GAAG,IAAI,CAAC,KAAKF,wBAFvB,CADF,EAIE;AACA,WAAKG,SAAL,CAAe,KAAK9F,YAApB,EAAkC,KAAKA,YAAvC;AACD;AACF;;AAEMyF,EAAAA,cAAc,CAACL,KAAD,EAA4B;AAC/C,QAAI,CAAC,KAAKhG,OAAV,EAAmB;AACjB;AACD;;AAED,UAAM;AAAEI,MAAAA;AAAF,QAAsC,KAAKE,QAAL,CACzCC,OADH;AAGA,UAAMoG,UAAwC,GAC5C,KAAKC,mBAAL,CAAyBZ,KAAzB,CADF;;AAGA,QAAIW,UAAJ,EAAgB;AACdhG,MAAAA,oBAAoB,CAACP,qBAAD,EAAwBuG,UAAxB,CAApB;AACD;AACF,GApW0C,CAsW3C;AACA;AACA;;;AA0BQlG,EAAAA,kBAAkB,CAACP,QAAD,EAAkBC,QAAlB,EAAgD;AACxE,WAAO;AACLW,MAAAA,WAAW,EAAE;AACX+F,QAAAA,gBAAgB,EAAE,KAAK7D,OAAL,CAAaI,uBAAb,EADP;AAEX0D,QAAAA,KAAK,EAAE5G,QAFI;AAGX6G,QAAAA,aAAa,EAAE,KAAKjH,QAAL,CAAckH,iBAAd,CAAgC;AAC7CC,UAAAA,CAAC,EAAE,KAAKjE,OAAL,CAAakE,WAAb,EAD0C;AAE7CC,UAAAA,CAAC,EAAE,KAAKnE,OAAL,CAAaoE,WAAb;AAF0C,SAAhC,CAHJ;AAOX,WAAG,KAAKC,oBAAL,EAPQ;AAQXC,QAAAA,UAAU,EAAE,KAAKA,UARN;AASXC,QAAAA,MAAM,EAAE,KAAKtG,OATF;AAUXd,QAAAA,QAAQ,EAAED,QAAQ,KAAKC,QAAb,GAAwBA,QAAxB,GAAmCY;AAVlC,OADR;AAaLyG,MAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AAbN,KAAP;AAeD;;AAEOd,EAAAA,mBAAmB,CACzBZ,KADyB,EAEK;AAAA;;AAC9B,UAAM2B,IAAI,GAAG,KAAK7H,QAAL,CAAc8H,WAAd,EAAb;AAEA,UAAMC,GAAkB,GAAG,EAA3B;AACA,UAAMC,OAAsB,GAAG,EAA/B;AAEA,UAAMC,WAAW,GAAG,KAAK/E,OAAL,CAAagF,OAAb,EAApB,CAN8B,CAQ9B;AACA;AACA;AACA;;AACA,QAAID,WAAW,CAACE,IAAZ,KAAqB,CAArB,IAA0B,CAACF,WAAW,CAACG,GAAZ,CAAgBlC,KAAK,CAACmC,SAAtB,CAA/B,EAAiE;AAC/D;AACD;;AAEDJ,IAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,OAAD,EAA0BC,GAA1B,KAAgD;AAClE,YAAMC,EAAU,GAAG,KAAKvF,OAAL,CAAawF,qBAAb,CAAmCF,GAAnC,CAAnB;AAEAT,MAAAA,GAAG,CAACY,IAAJ,CAAS;AACPF,QAAAA,EAAE,EAAEA,EADG;AAEPtB,QAAAA,CAAC,EAAEoB,OAAO,CAACK,KAAR,GAAgBf,IAAI,CAACgB,KAFjB;AAGPxB,QAAAA,CAAC,EAAEkB,OAAO,CAACO,KAAR,GAAgBjB,IAAI,CAACkB,KAHjB;AAIPC,QAAAA,SAAS,EAAET,OAAO,CAACK,KAJZ;AAKPK,QAAAA,SAAS,EAAEV,OAAO,CAACO;AALZ,OAAT;AAOD,KAVD,EAhB8B,CA4B9B;AACA;;AACA,QAAI5C,KAAK,CAACgD,SAAN,KAAoBxJ,UAAU,CAACyJ,MAAnC,EAA2C;AACzCnB,MAAAA,OAAO,CAACW,IAAR,CAAa;AACXF,QAAAA,EAAE,EAAE,KAAKvF,OAAL,CAAawF,qBAAb,CAAmCxC,KAAK,CAACmC,SAAzC,CADO;AAEXlB,QAAAA,CAAC,EAAEjB,KAAK,CAACiB,CAAN,GAAUU,IAAI,CAACgB,KAFP;AAGXxB,QAAAA,CAAC,EAAEnB,KAAK,CAACmB,CAAN,GAAUQ,IAAI,CAACkB,KAHP;AAIXC,QAAAA,SAAS,EAAE9C,KAAK,CAACiB,CAJN;AAKX8B,QAAAA,SAAS,EAAE/C,KAAK,CAACmB;AALN,OAAb;AAOD,KARD,MAQO;AACLY,MAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,OAAD,EAA0BC,GAA1B,KAAgD;AAClE,cAAMC,EAAU,GAAG,KAAKvF,OAAL,CAAawF,qBAAb,CAAmCF,GAAnC,CAAnB;AAEAR,QAAAA,OAAO,CAACW,IAAR,CAAa;AACXF,UAAAA,EAAE,EAAEA,EADO;AAEXtB,UAAAA,CAAC,EAAEoB,OAAO,CAACK,KAAR,GAAgBf,IAAI,CAACgB,KAFb;AAGXxB,UAAAA,CAAC,EAAEkB,OAAO,CAACO,KAAR,GAAgBjB,IAAI,CAACkB,KAHb;AAIXC,UAAAA,SAAS,EAAET,OAAO,CAACK,KAJR;AAKXK,UAAAA,SAAS,EAAEV,OAAO,CAACO;AALR,SAAb;AAOD,OAVD;AAWD;;AAED,QAAII,SAAyB,GAAGzJ,cAAc,CAACQ,YAA/C;;AAEA,YAAQiG,KAAK,CAACgD,SAAd;AACE,WAAKxJ,UAAU,CAAC0J,IAAhB;AACA,WAAK1J,UAAU,CAAC2J,uBAAhB;AACEH,QAAAA,SAAS,GAAGzJ,cAAc,CAAC2J,IAA3B;AACA;;AACF,WAAK1J,UAAU,CAAC4J,EAAhB;AACA,WAAK5J,UAAU,CAAC6J,qBAAhB;AACEL,QAAAA,SAAS,GAAGzJ,cAAc,CAAC6J,EAA3B;AACA;;AACF,WAAK5J,UAAU,CAAC8J,IAAhB;AACEN,QAAAA,SAAS,GAAGzJ,cAAc,CAAC+J,IAA3B;AACA;;AACF,WAAK9J,UAAU,CAACyJ,MAAhB;AACED,QAAAA,SAAS,GAAGzJ,cAAc,CAAC8E,SAA3B;AACA;AAdJ,KAtD8B,CAuE9B;AACA;AACA;;;AACA,QAAIkF,eAAuB,GAAG1B,GAAG,CAAC2B,MAAlC;;AAEA,QACExD,KAAK,CAACgD,SAAN,KAAoBxJ,UAAU,CAAC4J,EAA/B,IACApD,KAAK,CAACgD,SAAN,KAAoBxJ,UAAU,CAAC6J,qBAFjC,EAGE;AACA,QAAEE,eAAF;AACD;;AAED,WAAO;AACLzI,MAAAA,WAAW,EAAE;AACXwG,QAAAA,UAAU,EAAE,KAAKA,UADN;AAEXR,QAAAA,KAAK,EAAE,KAAKlG,YAFD;AAGXoI,QAAAA,SAAS,2BAAEhD,KAAK,CAACyD,cAAR,yEAA0BT,SAHxB;AAIXU,QAAAA,cAAc,EAAE5B,OAJL;AAKX6B,QAAAA,UAAU,EAAE9B,GALD;AAMX0B,QAAAA,eAAe,EAAEA;AANN,OADR;AASL/B,MAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AATN,KAAP;AAWD;;AAEOlE,EAAAA,aAAa,GAAS;AAC5B,UAAMmE,IAAI,GAAG,KAAK7H,QAAL,CAAc8H,WAAd,EAAb;AAEA,UAAMC,GAAkB,GAAG,EAA3B;AACA,UAAMC,OAAsB,GAAG,EAA/B;AAEA,UAAMC,WAAW,GAAG,KAAK/E,OAAL,CAAagF,OAAb,EAApB;;AAEA,QAAID,WAAW,CAACE,IAAZ,KAAqB,CAAzB,EAA4B;AAC1B;AACD;;AAEDF,IAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,OAAD,EAA0BC,GAA1B,KAAgD;AAClE,YAAMC,EAAU,GAAG,KAAKvF,OAAL,CAAawF,qBAAb,CAAmCF,GAAnC,CAAnB;AAEAT,MAAAA,GAAG,CAACY,IAAJ,CAAS;AACPF,QAAAA,EAAE,EAAEA,EADG;AAEPtB,QAAAA,CAAC,EAAEoB,OAAO,CAACK,KAAR,GAAgBf,IAAI,CAACgB,KAFjB;AAGPxB,QAAAA,CAAC,EAAEkB,OAAO,CAACO,KAAR,GAAgBjB,IAAI,CAACkB,KAHjB;AAIPC,QAAAA,SAAS,EAAET,OAAO,CAACK,KAJZ;AAKPK,QAAAA,SAAS,EAAEV,OAAO,CAACO;AALZ,OAAT;AAQAd,MAAAA,OAAO,CAACW,IAAR,CAAa;AACXF,QAAAA,EAAE,EAAEA,EADO;AAEXtB,QAAAA,CAAC,EAAEoB,OAAO,CAACK,KAAR,GAAgBf,IAAI,CAACgB,KAFb;AAGXxB,QAAAA,CAAC,EAAEkB,OAAO,CAACO,KAAR,GAAgBjB,IAAI,CAACkB,KAHb;AAIXC,QAAAA,SAAS,EAAET,OAAO,CAACK,KAJR;AAKXK,QAAAA,SAAS,EAAEV,OAAO,CAACO;AALR,OAAb;AAOD,KAlBD;AAoBA,UAAMgB,WAA6B,GAAG;AACpC9I,MAAAA,WAAW,EAAE;AACXwG,QAAAA,UAAU,EAAE,KAAKA,UADN;AAEXR,QAAAA,KAAK,EAAE,KAAKlG,YAFD;AAGXoI,QAAAA,SAAS,EAAEzJ,cAAc,CAAC8E,SAHf;AAIXqF,QAAAA,cAAc,EAAE5B,OAJL;AAKX6B,QAAAA,UAAU,EAAE9B,GALD;AAMX0B,QAAAA,eAAe,EAAE1B,GAAG,CAAC2B;AANV,OADuB;AASpChC,MAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AATyB,KAAtC;AAYA,UAAM;AAAEtH,MAAAA;AAAF,QAAsC,KAAKE,QAAL,CACzCC,OADH;AAGAI,IAAAA,oBAAoB,CAACP,qBAAD,EAAwBwJ,WAAxB,CAApB;AACD;;AAESvC,EAAAA,oBAAoB,GAA4B;AACxD;AACA,UAAMM,IAAI,GAAG,KAAK7H,QAAL,CAAc8H,WAAd,EAAb;AAEA,WAAO;AACLX,MAAAA,CAAC,EAAE,KAAKjE,OAAL,CAAakE,WAAb,KAA6BS,IAAI,CAACgB,KADhC;AAELxB,MAAAA,CAAC,EAAE,KAAKnE,OAAL,CAAaoE,WAAb,KAA6BO,IAAI,CAACkB,KAFhC;AAGLC,MAAAA,SAAS,EAAE,KAAK9F,OAAL,CAAakE,WAAb,EAHN;AAIL6B,MAAAA,SAAS,EAAE,KAAK/F,OAAL,CAAaoE,WAAb;AAJN,KAAP;AAMD,GAljB0C,CAojB3C;AACA;AACA;;;AAEOyC,EAAAA,mBAAmB,CAAC;AAAE7J,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAG8J;AAArB,GAAD,EAA6C;AACrE,SAAKzG,MAAL,GAAc;AAAErD,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAG8J;AAAvB,KAAd;AACA,SAAK9J,OAAL,GAAeA,OAAf;;AAEA,QAAI,KAAKqD,MAAL,CAAY0G,uBAAZ,KAAwChJ,SAA5C,EAAuD;AACrD,WAAKiJ,0BAAL,CAAgC,KAAK3G,MAAL,CAAY0G,uBAA5C;AACD;;AAED,SAAKE,gBAAL;;AAEA,QAAI,KAAKjK,OAAT,EAAkB;AAChB;AACD;;AAED,YAAQ,KAAKY,YAAb;AACE,WAAKvB,KAAK,CAACwB,MAAX;AACE,aAAKoD,IAAL,CAAU,IAAV;AACA;;AACF,WAAK5E,KAAK,CAACU,YAAX;AACEN,QAAAA,0BAA0B,CAACgE,WAA3B,GAAyCyG,6BAAzC,CACE,IADF;AAGA;;AACF;AACE,aAAK9F,MAAL,CAAY,IAAZ;AACA;AAXJ;AAaD;;AAES+F,EAAAA,6BAA6B,CAACC,SAAD,EAA4B;AACjE,SAAK,MAAM9B,GAAX,IAAkB,KAAKjF,MAAvB,EAA+B;AAC7B,UAAI+G,SAAS,CAACC,OAAV,CAAkB/B,GAAlB,KAA0B,CAA9B,EAAiC;AAC/B,aAAKgC,2BAAL,GAAmC,IAAnC;AACD;AACF;AACF;;AAEOL,EAAAA,gBAAgB,GAAS;AAC/B,QAAI,CAAC,KAAK5G,MAAL,CAAYkH,OAAjB,EAA0B;AACxB;AACD;;AAED,QACE,KAAKlH,MAAL,CAAYkH,OAAZ,CAAoBC,IAApB,KAA6BzJ,SAA7B,IACA,KAAKsC,MAAL,CAAYkH,OAAZ,CAAoBE,KAApB,KAA8B1J,SAD9B,IAEA,KAAKsC,MAAL,CAAYkH,OAAZ,CAAoBG,KAApB,KAA8B3J,SAHhC,EAIE;AACA,YAAM,IAAI4J,KAAJ,CACJ,qEADI,CAAN;AAGD;;AAED,QACE,KAAKtH,MAAL,CAAYkH,OAAZ,CAAoBG,KAApB,KAA8B3J,SAA9B,IACA,KAAKsC,MAAL,CAAYkH,OAAZ,CAAoBC,IAApB,KAA6BzJ,SAD7B,IAEA,KAAKsC,MAAL,CAAYkH,OAAZ,CAAoBE,KAApB,KAA8B1J,SAHhC,EAIE;AACA,YAAM,IAAI4J,KAAJ,CACJ,8EADI,CAAN;AAGD;;AAED,QACE,KAAKtH,MAAL,CAAYkH,OAAZ,CAAoBK,MAApB,KAA+B7J,SAA/B,IACA,KAAKsC,MAAL,CAAYkH,OAAZ,CAAoBM,GAApB,KAA4B9J,SAD5B,IAEA,KAAKsC,MAAL,CAAYkH,OAAZ,CAAoBO,MAApB,KAA+B/J,SAHjC,EAIE;AACA,YAAM,IAAI4J,KAAJ,CACJ,sEADI,CAAN;AAGD;;AAED,QACE,KAAKtH,MAAL,CAAYkH,OAAZ,CAAoBK,MAApB,KAA+B7J,SAA/B,IACA,KAAKsC,MAAL,CAAYkH,OAAZ,CAAoBM,GAApB,KAA4B9J,SAD5B,IAEA,KAAKsC,MAAL,CAAYkH,OAAZ,CAAoBO,MAApB,KAA+B/J,SAHjC,EAIE;AACA,YAAM,IAAI4J,KAAJ,CACJ,+EADI,CAAN;AAGD;AACF;;AAEO5G,EAAAA,YAAY,GAAY;AAC9B,QAAI,CAAC,KAAKV,MAAL,CAAYkH,OAAjB,EAA0B;AACxB,aAAO,IAAP;AACD;;AAED,UAAM;AAAEG,MAAAA,KAAF;AAASE,MAAAA;AAAT,QAAoB,KAAK9K,QAAL,CAAc8H,WAAd,EAA1B;AAEA,QAAI4C,IAAI,GAAG,CAAX;AACA,QAAIK,GAAG,GAAG,CAAV;AACA,QAAIJ,KAAa,GAAGC,KAApB;AACA,QAAII,MAAc,GAAGF,MAArB;;AAEA,QAAI,KAAKvH,MAAL,CAAYkH,OAAZ,CAAoBQ,UAApB,KAAmChK,SAAvC,EAAkD;AAChDyJ,MAAAA,IAAI,IAAI,KAAKnH,MAAL,CAAYkH,OAAZ,CAAoBQ,UAA5B;AACAN,MAAAA,KAAK,IAAI,KAAKpH,MAAL,CAAYkH,OAAZ,CAAoBQ,UAA7B;AACD;;AAED,QAAI,KAAK1H,MAAL,CAAYkH,OAAZ,CAAoBS,QAApB,KAAiCjK,SAArC,EAAgD;AAC9C8J,MAAAA,GAAG,IAAI,KAAKxH,MAAL,CAAYkH,OAAZ,CAAoBS,QAA3B;AACAF,MAAAA,MAAM,IAAI,KAAKzH,MAAL,CAAYkH,OAAZ,CAAoBS,QAA9B;AACD;;AAED,QAAI,KAAK3H,MAAL,CAAYkH,OAAZ,CAAoBC,IAApB,KAA6BzJ,SAAjC,EAA4C;AAC1CyJ,MAAAA,IAAI,GAAG,CAAC,KAAKnH,MAAL,CAAYkH,OAAZ,CAAoBC,IAA5B;AACD;;AAED,QAAI,KAAKnH,MAAL,CAAYkH,OAAZ,CAAoBE,KAApB,KAA8B1J,SAAlC,EAA6C;AAC3C0J,MAAAA,KAAK,GAAGC,KAAK,GAAG,KAAKrH,MAAL,CAAYkH,OAAZ,CAAoBE,KAApC;AACD;;AAED,QAAI,KAAKpH,MAAL,CAAYkH,OAAZ,CAAoBM,GAApB,KAA4B9J,SAAhC,EAA2C;AACzC8J,MAAAA,GAAG,GAAG,CAAC,KAAKxH,MAAL,CAAYkH,OAAZ,CAAoBM,GAA3B;AACD;;AAED,QAAI,KAAKxH,MAAL,CAAYkH,OAAZ,CAAoBO,MAApB,KAA+B/J,SAAnC,EAA8C;AAC5C+J,MAAAA,MAAM,GAAGJ,KAAK,GAAG,KAAKrH,MAAL,CAAYkH,OAAZ,CAAoBO,MAArC;AACD;;AACD,QAAI,KAAKzH,MAAL,CAAYkH,OAAZ,CAAoBG,KAApB,KAA8B3J,SAAlC,EAA6C;AAC3C,UAAI,KAAKsC,MAAL,CAAYkH,OAAZ,CAAoBC,IAApB,KAA6BzJ,SAAjC,EAA4C;AAC1C0J,QAAAA,KAAK,GAAGD,IAAI,GAAG,KAAKnH,MAAL,CAAYkH,OAAZ,CAAoBG,KAAnC;AACD,OAFD,MAEO,IAAI,KAAKrH,MAAL,CAAYkH,OAAZ,CAAoBE,KAApB,KAA8B1J,SAAlC,EAA6C;AAClDyJ,QAAAA,IAAI,GAAGC,KAAK,GAAG,KAAKpH,MAAL,CAAYkH,OAAZ,CAAoBG,KAAnC;AACD;AACF;;AAED,QAAI,KAAKrH,MAAL,CAAYkH,OAAZ,CAAoBK,MAApB,KAA+B7J,SAAnC,EAA8C;AAC5C,UAAI,KAAKsC,MAAL,CAAYkH,OAAZ,CAAoBM,GAApB,KAA4B9J,SAAhC,EAA2C;AACzC+J,QAAAA,MAAM,GAAGD,GAAG,GAAG,KAAKxH,MAAL,CAAYkH,OAAZ,CAAoBK,MAAnC;AACD,OAFD,MAEO,IAAI,KAAKvH,MAAL,CAAYkH,OAAZ,CAAoBO,MAApB,KAA+B/J,SAAnC,EAA8C;AACnD8J,QAAAA,GAAG,GAAGC,MAAM,GAAG,KAAKzH,MAAL,CAAYkH,OAAZ,CAAoBK,MAAnC;AACD;AACF;;AAED,UAAMjD,IAAI,GAAG,KAAK7H,QAAL,CAAc8H,WAAd,EAAb;AACA,UAAMqD,OAAe,GAAG,KAAKjI,OAAL,CAAakI,QAAb,KAA0BvD,IAAI,CAACgB,KAAvD;AACA,UAAMwC,OAAe,GAAG,KAAKnI,OAAL,CAAaoI,QAAb,KAA0BzD,IAAI,CAACkB,KAAvD;;AAEA,QACEoC,OAAO,IAAIT,IAAX,IACAS,OAAO,IAAIR,KADX,IAEAU,OAAO,IAAIN,GAFX,IAGAM,OAAO,IAAIL,MAJb,EAKE;AACA,aAAO,IAAP;AACD;;AACD,WAAO,KAAP;AACD;;AAESO,EAAAA,WAAW,GAAS,CAAE,CA/sBW,CAitB3C;AACA;AACA;;;AAEOC,EAAAA,MAAM,GAAW;AACtB,WAAO,KAAKhE,UAAZ;AACD;;AAEMiE,EAAAA,MAAM,CAACC,GAAD,EAAoB;AAC/B,SAAKlE,UAAL,GAAkBkE,GAAlB;AACD;;AAEMC,EAAAA,SAAS,GAAG;AACjB,WAAO,KAAKpI,MAAZ;AACD;;AAEMqI,EAAAA,WAAW,GAAoC;AACpD,WAAO,KAAK5L,QAAZ;AACD;;AAEM6L,EAAAA,UAAU,GAAmB;AAClC,WAAO,KAAK3I,OAAZ;AACD;;AAEM4I,EAAAA,oBAAoB,GAAa;AACtC,WAAO,KAAK5I,OAAL,CAAa4I,oBAAb,EAAP;AACD;;AAEMC,EAAAA,QAAQ,GAAU;AACvB,WAAO,KAAKjL,YAAZ;AACD;;AAEMkL,EAAAA,SAAS,GAAY;AAC1B,WAAO,KAAK9L,OAAZ;AACD;;AAEOuD,EAAAA,UAAU,GAAY;AAC5B,WACE,KAAK3C,YAAL,KAAsBvB,KAAK,CAACsF,GAA5B,IACA,KAAK/D,YAAL,KAAsBvB,KAAK,CAAC8E,MAD5B,IAEA,KAAKvD,YAAL,KAAsBvB,KAAK,CAACgF,SAH9B;AAKD;;AAES2F,EAAAA,0BAA0B,CAAC+B,YAAD,EAAwB;AAC1D,SAAKxF,wBAAL,GAAgCwF,YAAhC;AACD;;AAESC,EAAAA,0BAA0B,GAAY;AAC9C,WAAO,KAAKzF,wBAAZ;AACD;;AAEM0F,EAAAA,cAAc,GAAgB;AACnC,WAAO,KAAK/F,WAAZ;AACD;;AAvwB0C;;AA0wB7C,SAASvF,oBAAT,CACEuL,MADF,EAKElG,KALF,EAMQ;AACN,MAAI,CAACkG,MAAL,EAAa;AACX;AACD;;AAED,MAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;AAChCA,IAAAA,MAAM,CAAClG,KAAD,CAAN;AACA;AACD;;AAED,MAAI,kBAAkBkG,MAAlB,IAA4B,OAAOA,MAAM,CAACC,YAAd,KAA+B,UAA/D,EAA2E;AACzE,UAAMzG,OAAO,GAAGwG,MAAM,CAACC,YAAP,EAAhB;;AACAxL,IAAAA,oBAAoB,CAAC+E,OAAD,EAAUM,KAAV,CAApB;AACA;AACD;;AAED,MAAI,EAAE,kBAAkBkG,MAApB,CAAJ,EAAiC;AAC/B;AACD;;AAED,QAAM;AAAEE,IAAAA;AAAF,MAA0CF,MAAM,CAACG,YAAvD;;AACA,MAAI,CAACC,KAAK,CAACC,OAAN,CAAcH,UAAd,CAAL,EAAgC;AAC9B;AACD;;AAED,OAAK,MAAM,CAACI,KAAD,EAAQ,CAAClE,GAAD,EAAMvD,KAAN,CAAR,CAAX,IAAoCqH,UAAU,CAACK,OAAX,EAApC,EAA0D;AACxD,QAAI,EAAEnE,GAAG,IAAItC,KAAK,CAAClF,WAAf,CAAJ,EAAiC;AAC/B;AACD,KAHuD,CAKxD;;;AACA,UAAM4L,WAAW,GAAG1G,KAAK,CAAClF,WAAN,CAAkBwH,GAAlB,CAApB,CANwD,CAQxD;;AACA,QAAIvD,KAAJ,aAAIA,KAAJ,eAAIA,KAAK,CAAE4H,QAAX,EAAqB;AACnB;AACA;AACA5H,MAAAA,KAAK,CAAC4H,QAAN,CAAeD,WAAf;AACD,KAJD,MAIO;AACL;AACAR,MAAAA,MAAM,CAACG,YAAP,CAAoBD,UAApB,CAA+BI,KAA/B,IAAwC,CAAClE,GAAD,EAAMoE,WAAN,CAAxC;AACD;AACF;;AAED;AACD", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-empty-function */\nimport { State } from '../../State';\nimport {\n  Config,\n  AdaptedEvent,\n  PropsRef,\n  ResultEvent,\n  PointerData,\n  ResultTouchEvent,\n  PointerType,\n  TouchEventType,\n  EventTypes,\n} from '../interfaces';\nimport EventManager from '../tools/EventManager';\nimport GestureHandlerOrchestrator from '../tools/GestureHandlerOrchestrator';\nimport InteractionManager from '../tools/InteractionManager';\nimport PointerTracker, { TrackerElement } from '../tools/PointerTracker';\nimport { GestureHandlerDelegate } from '../tools/GestureHandlerDelegate';\n\nexport default abstract class GestureHandler {\n  private lastSentState: State | null = null;\n  protected currentState: State = State.UNDETERMINED;\n\n  protected shouldCancellWhenOutside = false;\n  protected hasCustomActivationCriteria = false;\n  protected enabled = false;\n\n  private viewRef!: number;\n  private propsRef!: React.RefObject<unknown>;\n  private handlerTag!: number;\n  protected config: Config = { enabled: false };\n\n  protected tracker: PointerTracker = new PointerTracker();\n\n  // Orchestrator properties\n  protected activationIndex = 0;\n  protected awaiting = false;\n  protected active = false;\n  protected shouldResetProgress = false;\n  protected pointerType: PointerType = PointerType.NONE;\n\n  protected delegate: GestureHandlerDelegate<unknown>;\n\n  public constructor(delegate: GestureHandlerDelegate<unknown>) {\n    this.delegate = delegate;\n  }\n\n  //\n  // Initializing handler\n  //\n\n  protected init(viewRef: number, propsRef: React.RefObject<unknown>) {\n    this.propsRef = propsRef;\n    this.viewRef = viewRef;\n\n    this.currentState = State.UNDETERMINED;\n\n    this.delegate.init(viewRef, this);\n  }\n\n  public attachEventManager(manager: EventManager<unknown>): void {\n    manager.setOnPointerDown(this.onPointerDown.bind(this));\n    manager.setOnPointerAdd(this.onPointerAdd.bind(this));\n    manager.setOnPointerUp(this.onPointerUp.bind(this));\n    manager.setOnPointerRemove(this.onPointerRemove.bind(this));\n    manager.setOnPointerMove(this.onPointerMove.bind(this));\n    manager.setOnPointerEnter(this.onPointerEnter.bind(this));\n    manager.setOnPointerLeave(this.onPointerLeave.bind(this));\n    manager.setOnPointerCancel(this.onPointerCancel.bind(this));\n    manager.setOnPointerOutOfBounds(this.onPointerOutOfBounds.bind(this));\n    manager.setOnPointerMoveOver(this.onPointerMoveOver.bind(this));\n    manager.setOnPointerMoveOut(this.onPointerMoveOut.bind(this));\n    manager.setListeners();\n  }\n\n  //\n  // Resetting handler\n  //\n\n  protected onCancel(): void {}\n  protected onReset(): void {}\n  protected resetProgress(): void {}\n\n  public reset(): void {\n    this.tracker.resetTracker();\n    this.onReset();\n    this.resetProgress();\n    this.delegate.reset();\n    this.currentState = State.UNDETERMINED;\n  }\n\n  //\n  // State logic\n  //\n\n  public moveToState(newState: State, sendIfDisabled?: boolean) {\n    if (this.currentState === newState) {\n      return;\n    }\n\n    const oldState = this.currentState;\n    this.currentState = newState;\n\n    if (\n      this.tracker.getTrackedPointersCount() > 0 &&\n      this.config.needsPointerData &&\n      this.isFinished()\n    ) {\n      this.cancelTouches();\n    }\n\n    GestureHandlerOrchestrator.getInstance().onHandlerStateChange(\n      this,\n      newState,\n      oldState,\n      sendIfDisabled\n    );\n\n    this.onStateChange(newState, oldState);\n  }\n\n  protected onStateChange(_newState: State, _oldState: State): void {}\n\n  public begin(): void {\n    if (!this.checkHitSlop()) {\n      return;\n    }\n\n    if (this.currentState === State.UNDETERMINED) {\n      this.moveToState(State.BEGAN);\n    }\n  }\n\n  /**\n   * @param {boolean} sendIfDisabled - Used when handler becomes disabled. With this flag orchestrator will be forced to send fail event\n   */\n  public fail(sendIfDisabled?: boolean): void {\n    if (\n      this.currentState === State.ACTIVE ||\n      this.currentState === State.BEGAN\n    ) {\n      // Here the order of calling the delegate and moveToState is important.\n      // At this point we can use currentState as previuos state, because immediately after changing cursor we call moveToState method.\n      this.delegate.onFail();\n\n      this.moveToState(State.FAILED, sendIfDisabled);\n    }\n\n    this.resetProgress();\n  }\n\n  /**\n   * @param {boolean} sendIfDisabled - Used when handler becomes disabled. With this flag orchestrator will be forced to send cancel event\n   */\n  public cancel(sendIfDisabled?: boolean): void {\n    if (\n      this.currentState === State.ACTIVE ||\n      this.currentState === State.UNDETERMINED ||\n      this.currentState === State.BEGAN\n    ) {\n      this.onCancel();\n\n      // Same as above - order matters\n      this.delegate.onCancel();\n\n      this.moveToState(State.CANCELLED, sendIfDisabled);\n    }\n  }\n\n  public activate(_force = false) {\n    if (\n      this.currentState === State.UNDETERMINED ||\n      this.currentState === State.BEGAN\n    ) {\n      this.delegate.onActivate();\n\n      this.moveToState(State.ACTIVE);\n    }\n  }\n\n  public end() {\n    if (\n      this.currentState === State.BEGAN ||\n      this.currentState === State.ACTIVE\n    ) {\n      // Same as above - order matters\n      this.delegate.onEnd();\n\n      this.moveToState(State.END);\n    }\n\n    this.resetProgress();\n  }\n\n  //\n  // Methods for orchestrator\n  //\n\n  public isAwaiting(): boolean {\n    return this.awaiting;\n  }\n  public setAwaiting(value: boolean): void {\n    this.awaiting = value;\n  }\n\n  public isActive(): boolean {\n    return this.active;\n  }\n  public setActive(value: boolean): void {\n    this.active = value;\n  }\n\n  public getShouldResetProgress(): boolean {\n    return this.shouldResetProgress;\n  }\n  public setShouldResetProgress(value: boolean): void {\n    this.shouldResetProgress = value;\n  }\n\n  public getActivationIndex(): number {\n    return this.activationIndex;\n  }\n  public setActivationIndex(value: number): void {\n    this.activationIndex = value;\n  }\n\n  public shouldWaitForHandlerFailure(handler: GestureHandler): boolean {\n    if (handler === this) {\n      return false;\n    }\n\n    return InteractionManager.getInstance().shouldWaitForHandlerFailure(\n      this,\n      handler\n    );\n  }\n\n  public shouldRequireToWaitForFailure(handler: GestureHandler): boolean {\n    if (handler === this) {\n      return false;\n    }\n\n    return InteractionManager.getInstance().shouldRequireHandlerToWaitForFailure(\n      this,\n      handler\n    );\n  }\n\n  public shouldRecognizeSimultaneously(handler: GestureHandler): boolean {\n    if (handler === this) {\n      return true;\n    }\n\n    return InteractionManager.getInstance().shouldRecognizeSimultaneously(\n      this,\n      handler\n    );\n  }\n\n  public shouldBeCancelledByOther(handler: GestureHandler): boolean {\n    if (handler === this) {\n      return false;\n    }\n\n    return InteractionManager.getInstance().shouldHandlerBeCancelledBy(\n      this,\n      handler\n    );\n  }\n\n  //\n  // Event actions\n  //\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    GestureHandlerOrchestrator.getInstance().recordHandlerIfNotPresent(this);\n    this.pointerType = event.pointerType;\n\n    if (this.pointerType === PointerType.TOUCH) {\n      GestureHandlerOrchestrator.getInstance().cancelMouseAndPenGestures(this);\n    }\n\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n  }\n  // Adding another pointer to existing ones\n  protected onPointerAdd(event: AdaptedEvent): void {\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n  }\n  protected onPointerUp(event: AdaptedEvent): void {\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n  }\n  // Removing pointer, when there is more than one pointers\n  protected onPointerRemove(event: AdaptedEvent): void {\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n  }\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tryToSendMoveEvent(false);\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n  }\n  protected onPointerLeave(event: AdaptedEvent): void {\n    if (this.shouldCancellWhenOutside) {\n      switch (this.currentState) {\n        case State.ACTIVE:\n          this.cancel();\n          break;\n        case State.BEGAN:\n          this.fail();\n          break;\n      }\n      return;\n    }\n\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n  }\n  protected onPointerEnter(event: AdaptedEvent): void {\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n  }\n  protected onPointerCancel(event: AdaptedEvent): void {\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n\n    this.cancel();\n    this.reset();\n  }\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    this.tryToSendMoveEvent(true);\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n  }\n  protected onPointerMoveOver(_event: AdaptedEvent): void {\n    // used only by hover gesture handler atm\n  }\n  protected onPointerMoveOut(_event: AdaptedEvent): void {\n    // used only by hover gesture handler atm\n  }\n  private tryToSendMoveEvent(out: boolean): void {\n    if (\n      this.enabled &&\n      this.active &&\n      (!out || (out && !this.shouldCancellWhenOutside))\n    ) {\n      this.sendEvent(this.currentState, this.currentState);\n    }\n  }\n\n  public sendTouchEvent(event: AdaptedEvent): void {\n    if (!this.enabled) {\n      return;\n    }\n\n    const { onGestureHandlerEvent }: PropsRef = this.propsRef\n      .current as PropsRef;\n\n    const touchEvent: ResultTouchEvent | undefined =\n      this.transformTouchEvent(event);\n\n    if (touchEvent) {\n      invokeNullableMethod(onGestureHandlerEvent, touchEvent);\n    }\n  }\n\n  //\n  // Events Sending\n  //\n\n  public sendEvent = (newState: State, oldState: State): void => {\n    const { onGestureHandlerEvent, onGestureHandlerStateChange }: PropsRef =\n      this.propsRef.current as PropsRef;\n\n    const resultEvent: ResultEvent = this.transformEventData(\n      newState,\n      oldState\n    );\n\n    // In the new API oldState field has to be undefined, unless we send event state changed\n    // Here the order is flipped to avoid workarounds such as making backup of the state and setting it to undefined first, then changing it back\n    // Flipping order with setting oldState to undefined solves issue, when events were being sent twice instead of once\n    // However, this may cause trouble in the future (but for now we don't know that)\n\n    if (this.lastSentState !== newState) {\n      this.lastSentState = newState;\n      invokeNullableMethod(onGestureHandlerStateChange, resultEvent);\n    }\n    if (this.currentState === State.ACTIVE) {\n      resultEvent.nativeEvent.oldState = undefined;\n      invokeNullableMethod(onGestureHandlerEvent, resultEvent);\n    }\n  };\n\n  private transformEventData(newState: State, oldState: State): ResultEvent {\n    return {\n      nativeEvent: {\n        numberOfPointers: this.tracker.getTrackedPointersCount(),\n        state: newState,\n        pointerInside: this.delegate.isPointerInBounds({\n          x: this.tracker.getLastAvgX(),\n          y: this.tracker.getLastAvgY(),\n        }),\n        ...this.transformNativeEvent(),\n        handlerTag: this.handlerTag,\n        target: this.viewRef,\n        oldState: newState !== oldState ? oldState : undefined,\n      },\n      timeStamp: Date.now(),\n    };\n  }\n\n  private transformTouchEvent(\n    event: AdaptedEvent\n  ): ResultTouchEvent | undefined {\n    const rect = this.delegate.measureView();\n\n    const all: PointerData[] = [];\n    const changed: PointerData[] = [];\n\n    const trackerData = this.tracker.getData();\n\n    // This if handles edge case where all pointers have been cancelled\n    // When pointercancel is triggered, reset method is called. This means that tracker will be reset after first pointer being cancelled\n    // The problem is, that handler will receive another pointercancel event from the rest of the pointers\n    // To avoid crashing, we don't send event if tracker tracks no pointers, i.e. has been reset\n    if (trackerData.size === 0 || !trackerData.has(event.pointerId)) {\n      return;\n    }\n\n    trackerData.forEach((element: TrackerElement, key: number): void => {\n      const id: number = this.tracker.getMappedTouchEventId(key);\n\n      all.push({\n        id: id,\n        x: element.lastX - rect.pageX,\n        y: element.lastY - rect.pageY,\n        absoluteX: element.lastX,\n        absoluteY: element.lastY,\n      });\n    });\n\n    // Each pointer sends its own event, so we want changed touches to contain only the pointer that has changed.\n    // However, if the event is cancel, we want to cancel all pointers to avoid crashes\n    if (event.eventType !== EventTypes.CANCEL) {\n      changed.push({\n        id: this.tracker.getMappedTouchEventId(event.pointerId),\n        x: event.x - rect.pageX,\n        y: event.y - rect.pageY,\n        absoluteX: event.x,\n        absoluteY: event.y,\n      });\n    } else {\n      trackerData.forEach((element: TrackerElement, key: number): void => {\n        const id: number = this.tracker.getMappedTouchEventId(key);\n\n        changed.push({\n          id: id,\n          x: element.lastX - rect.pageX,\n          y: element.lastY - rect.pageY,\n          absoluteX: element.lastX,\n          absoluteY: element.lastY,\n        });\n      });\n    }\n\n    let eventType: TouchEventType = TouchEventType.UNDETERMINED;\n\n    switch (event.eventType) {\n      case EventTypes.DOWN:\n      case EventTypes.ADDITIONAL_POINTER_DOWN:\n        eventType = TouchEventType.DOWN;\n        break;\n      case EventTypes.UP:\n      case EventTypes.ADDITIONAL_POINTER_UP:\n        eventType = TouchEventType.UP;\n        break;\n      case EventTypes.MOVE:\n        eventType = TouchEventType.MOVE;\n        break;\n      case EventTypes.CANCEL:\n        eventType = TouchEventType.CANCELLED;\n        break;\n    }\n\n    // Here, when we receive up event, we want to decrease number of touches\n    // That's because we want handler to send information that there's one pointer less\n    // However, we still want this pointer to be present in allTouches array, so that its data can be accessed\n    let numberOfTouches: number = all.length;\n\n    if (\n      event.eventType === EventTypes.UP ||\n      event.eventType === EventTypes.ADDITIONAL_POINTER_UP\n    ) {\n      --numberOfTouches;\n    }\n\n    return {\n      nativeEvent: {\n        handlerTag: this.handlerTag,\n        state: this.currentState,\n        eventType: event.touchEventType ?? eventType,\n        changedTouches: changed,\n        allTouches: all,\n        numberOfTouches: numberOfTouches,\n      },\n      timeStamp: Date.now(),\n    };\n  }\n\n  private cancelTouches(): void {\n    const rect = this.delegate.measureView();\n\n    const all: PointerData[] = [];\n    const changed: PointerData[] = [];\n\n    const trackerData = this.tracker.getData();\n\n    if (trackerData.size === 0) {\n      return;\n    }\n\n    trackerData.forEach((element: TrackerElement, key: number): void => {\n      const id: number = this.tracker.getMappedTouchEventId(key);\n\n      all.push({\n        id: id,\n        x: element.lastX - rect.pageX,\n        y: element.lastY - rect.pageY,\n        absoluteX: element.lastX,\n        absoluteY: element.lastY,\n      });\n\n      changed.push({\n        id: id,\n        x: element.lastX - rect.pageX,\n        y: element.lastY - rect.pageY,\n        absoluteX: element.lastX,\n        absoluteY: element.lastY,\n      });\n    });\n\n    const cancelEvent: ResultTouchEvent = {\n      nativeEvent: {\n        handlerTag: this.handlerTag,\n        state: this.currentState,\n        eventType: TouchEventType.CANCELLED,\n        changedTouches: changed,\n        allTouches: all,\n        numberOfTouches: all.length,\n      },\n      timeStamp: Date.now(),\n    };\n\n    const { onGestureHandlerEvent }: PropsRef = this.propsRef\n      .current as PropsRef;\n\n    invokeNullableMethod(onGestureHandlerEvent, cancelEvent);\n  }\n\n  protected transformNativeEvent(): Record<string, unknown> {\n    // those properties are shared by most handlers and if not this method will be overriden\n    const rect = this.delegate.measureView();\n\n    return {\n      x: this.tracker.getLastAvgX() - rect.pageX,\n      y: this.tracker.getLastAvgY() - rect.pageY,\n      absoluteX: this.tracker.getLastAvgX(),\n      absoluteY: this.tracker.getLastAvgY(),\n    };\n  }\n\n  //\n  // Handling config\n  //\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    this.config = { enabled: enabled, ...props };\n    this.enabled = enabled;\n\n    if (this.config.shouldCancelWhenOutside !== undefined) {\n      this.setShouldCancelWhenOutside(this.config.shouldCancelWhenOutside);\n    }\n\n    this.validateHitSlops();\n\n    if (this.enabled) {\n      return;\n    }\n\n    switch (this.currentState) {\n      case State.ACTIVE:\n        this.fail(true);\n        break;\n      case State.UNDETERMINED:\n        GestureHandlerOrchestrator.getInstance().removeHandlerFromOrchestrator(\n          this\n        );\n        break;\n      default:\n        this.cancel(true);\n        break;\n    }\n  }\n\n  protected checkCustomActivationCriteria(criterias: string[]): void {\n    for (const key in this.config) {\n      if (criterias.indexOf(key) >= 0) {\n        this.hasCustomActivationCriteria = true;\n      }\n    }\n  }\n\n  private validateHitSlops(): void {\n    if (!this.config.hitSlop) {\n      return;\n    }\n\n    if (\n      this.config.hitSlop.left !== undefined &&\n      this.config.hitSlop.right !== undefined &&\n      this.config.hitSlop.width !== undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: Cannot define left, right and width at the same time'\n      );\n    }\n\n    if (\n      this.config.hitSlop.width !== undefined &&\n      this.config.hitSlop.left === undefined &&\n      this.config.hitSlop.right === undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: When width is defined, either left or right has to be defined'\n      );\n    }\n\n    if (\n      this.config.hitSlop.height !== undefined &&\n      this.config.hitSlop.top !== undefined &&\n      this.config.hitSlop.bottom !== undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: Cannot define top, bottom and height at the same time'\n      );\n    }\n\n    if (\n      this.config.hitSlop.height !== undefined &&\n      this.config.hitSlop.top === undefined &&\n      this.config.hitSlop.bottom === undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: When height is defined, either top or bottom has to be defined'\n      );\n    }\n  }\n\n  private checkHitSlop(): boolean {\n    if (!this.config.hitSlop) {\n      return true;\n    }\n\n    const { width, height } = this.delegate.measureView();\n\n    let left = 0;\n    let top = 0;\n    let right: number = width;\n    let bottom: number = height;\n\n    if (this.config.hitSlop.horizontal !== undefined) {\n      left -= this.config.hitSlop.horizontal;\n      right += this.config.hitSlop.horizontal;\n    }\n\n    if (this.config.hitSlop.vertical !== undefined) {\n      top -= this.config.hitSlop.vertical;\n      bottom += this.config.hitSlop.vertical;\n    }\n\n    if (this.config.hitSlop.left !== undefined) {\n      left = -this.config.hitSlop.left;\n    }\n\n    if (this.config.hitSlop.right !== undefined) {\n      right = width + this.config.hitSlop.right;\n    }\n\n    if (this.config.hitSlop.top !== undefined) {\n      top = -this.config.hitSlop.top;\n    }\n\n    if (this.config.hitSlop.bottom !== undefined) {\n      bottom = width + this.config.hitSlop.bottom;\n    }\n    if (this.config.hitSlop.width !== undefined) {\n      if (this.config.hitSlop.left !== undefined) {\n        right = left + this.config.hitSlop.width;\n      } else if (this.config.hitSlop.right !== undefined) {\n        left = right - this.config.hitSlop.width;\n      }\n    }\n\n    if (this.config.hitSlop.height !== undefined) {\n      if (this.config.hitSlop.top !== undefined) {\n        bottom = top + this.config.hitSlop.height;\n      } else if (this.config.hitSlop.bottom !== undefined) {\n        top = bottom - this.config.hitSlop.height;\n      }\n    }\n\n    const rect = this.delegate.measureView();\n    const offsetX: number = this.tracker.getLastX() - rect.pageX;\n    const offsetY: number = this.tracker.getLastY() - rect.pageY;\n\n    if (\n      offsetX >= left &&\n      offsetX <= right &&\n      offsetY >= top &&\n      offsetY <= bottom\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  protected resetConfig(): void {}\n\n  //\n  // Getters and setters\n  //\n\n  public getTag(): number {\n    return this.handlerTag;\n  }\n\n  public setTag(tag: number): void {\n    this.handlerTag = tag;\n  }\n\n  public getConfig() {\n    return this.config;\n  }\n\n  public getDelegate(): GestureHandlerDelegate<unknown> {\n    return this.delegate;\n  }\n\n  public getTracker(): PointerTracker {\n    return this.tracker;\n  }\n\n  public getTrackedPointersID(): number[] {\n    return this.tracker.getTrackedPointersID();\n  }\n\n  public getState(): State {\n    return this.currentState;\n  }\n\n  public isEnabled(): boolean {\n    return this.enabled;\n  }\n\n  private isFinished(): boolean {\n    return (\n      this.currentState === State.END ||\n      this.currentState === State.FAILED ||\n      this.currentState === State.CANCELLED\n    );\n  }\n\n  protected setShouldCancelWhenOutside(shouldCancel: boolean) {\n    this.shouldCancellWhenOutside = shouldCancel;\n  }\n\n  protected getShouldCancelWhenOutside(): boolean {\n    return this.shouldCancellWhenOutside;\n  }\n\n  public getPointerType(): PointerType {\n    return this.pointerType;\n  }\n}\n\nfunction invokeNullableMethod(\n  method:\n    | ((event: ResultEvent | ResultTouchEvent) => void)\n    | { __getHandler: () => (event: ResultEvent | ResultTouchEvent) => void }\n    | { __nodeConfig: { argMapping: unknown[] } },\n  event: ResultEvent | ResultTouchEvent\n): void {\n  if (!method) {\n    return;\n  }\n\n  if (typeof method === 'function') {\n    method(event);\n    return;\n  }\n\n  if ('__getHandler' in method && typeof method.__getHandler === 'function') {\n    const handler = method.__getHandler();\n    invokeNullableMethod(handler, event);\n    return;\n  }\n\n  if (!('__nodeConfig' in method)) {\n    return;\n  }\n\n  const { argMapping }: { argMapping: unknown } = method.__nodeConfig;\n  if (!Array.isArray(argMapping)) {\n    return;\n  }\n\n  for (const [index, [key, value]] of argMapping.entries()) {\n    if (!(key in event.nativeEvent)) {\n      continue;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    const nativeValue = event.nativeEvent[key];\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    if (value?.setValue) {\n      //Reanimated API\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call\n      value.setValue(nativeValue);\n    } else {\n      //RN Animated API\n      method.__nodeConfig.argMapping[index] = [key, nativeValue];\n    }\n  }\n\n  return;\n}\n"]}