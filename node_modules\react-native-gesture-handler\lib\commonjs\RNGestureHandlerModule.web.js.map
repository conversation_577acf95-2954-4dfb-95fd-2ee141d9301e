{"version": 3, "sources": ["RNGestureHandlerModule.web.ts"], "names": ["Gestures", "NativeViewGestureHandler", "PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "ManualGestureHandler", "HoverGestureHandler", "HammerGestures", "HammerNativeViewGestureHandler", "HammerPanGestureHandler", "HammerTapGestureHandler", "HammerLongPressGestureHandler", "HammerPinchGestureHandler", "HammerRotationGestureHandler", "HammerFlingGestureHandler", "handleSetJSResponder", "tag", "blockNativeResponder", "console", "warn", "handleClearJSResponder", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "Error", "GestureClass", "NodeManager", "GestureHandlerWebDelegate", "InteractionManager", "getInstance", "configureInteractions", "<PERSON><PERSON><PERSON><PERSON>", "HammerNodeManager", "updateGestureHandler", "attachGestureHandler", "newView", "_actionType", "propsRef", "HTMLElement", "React", "Component", "init", "<PERSON><PERSON><PERSON><PERSON>", "newConfig", "updateGestureConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "mappings": ";;;;;;;AAAA;;AAGA;;AAGA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAGA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA;;;;;;;;AAvBA;AAaA;AAYO,MAAMA,QAAQ,GAAG;AACtBC,EAAAA,wBAAwB,EAAxBA,iCADsB;AAEtBC,EAAAA,iBAAiB,EAAjBA,0BAFsB;AAGtBC,EAAAA,iBAAiB,EAAjBA,0BAHsB;AAItBC,EAAAA,uBAAuB,EAAvBA,gCAJsB;AAKtBC,EAAAA,mBAAmB,EAAnBA,4BALsB;AAMtBC,EAAAA,sBAAsB,EAAtBA,+BANsB;AAOtBC,EAAAA,mBAAmB,EAAnBA,4BAPsB;AAQtBC,EAAAA,oBAAoB,EAApBA,6BARsB;AAStBC,EAAAA,mBAAmB,EAAnBA;AATsB,CAAjB;;AAYA,MAAMC,cAAc,GAAG;AAC5BT,EAAAA,wBAAwB,EAAEU,kCADE;AAE5BT,EAAAA,iBAAiB,EAAEU,2BAFS;AAG5BT,EAAAA,iBAAiB,EAAEU,2BAHS;AAI5BT,EAAAA,uBAAuB,EAAEU,iCAJG;AAK5BT,EAAAA,mBAAmB,EAAEU,6BALO;AAM5BT,EAAAA,sBAAsB,EAAEU,gCANI;AAO5BT,EAAAA,mBAAmB,EAAEU;AAPO,CAAvB;;eAUQ;AACbC,EAAAA,oBAAoB,CAACC,GAAD,EAAcC,oBAAd,EAA6C;AAC/DC,IAAAA,OAAO,CAACC,IAAR,CAAa,wBAAb,EAAuCH,GAAvC,EAA4CC,oBAA5C;AACD,GAHY;;AAIbG,EAAAA,sBAAsB,GAAG;AACvBF,IAAAA,OAAO,CAACC,IAAR,CAAa,0BAAb;AACD,GANY;;AAObE,EAAAA,oBAAoB,CAClBC,WADkB,EAElBC,UAFkB,EAGlBC,MAHkB,EAIlB;AACA,QAAI,gEAAJ,EAAqC;AACnC,UAAI,EAAEF,WAAW,IAAIzB,QAAjB,CAAJ,EAAgC;AAC9B,cAAM,IAAI4B,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD;;AAED,YAAMI,YAAY,GAAG7B,QAAQ,CAACyB,WAAD,CAA7B;;AACAK,2BAAYN,oBAAZ,CACEE,UADF,EAEE,IAAIG,YAAJ,CAAiB,IAAIE,oDAAJ,EAAjB,CAFF;;AAIAC,kCAAmBC,WAAnB,GAAiCC,qBAAjC,CACEJ,qBAAYK,UAAZ,CAAuBT,UAAvB,CADF,EAEEC,MAFF;AAID,KAhBD,MAgBO;AACL,UAAI,EAAEF,WAAW,IAAIf,cAAjB,CAAJ,EAAsC;AACpC,cAAM,IAAIkB,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD,OALI,CAOL;AACA;;;AACA,YAAMI,YAAY,GAAGnB,cAAc,CAACe,WAAD,CAAnC,CATK,CAUL;;AACAW,MAAAA,iBAAiB,CAACZ,oBAAlB,CAAuCE,UAAvC,EAAmD,IAAIG,YAAJ,EAAnD;AACD;;AAED,SAAKQ,oBAAL,CAA0BX,UAA1B,EAAsCC,MAAtC;AACD,GA3CY;;AA4CbW,EAAAA,oBAAoB,CAClBZ,UADkB,EAElB;AACAa,EAAAA,OAHkB,EAIlBC,WAJkB,EAKlBC,QALkB,EAMlB;AACA,QACE,EAAEF,OAAO,YAAYG,WAAnB,IAAkCH,OAAO,YAAYI,eAAMC,SAA7D,CADF,EAEE;AACA;AACD;;AAED,QAAI,gEAAJ,EAAqC;AACnC;AACAd,2BAAYK,UAAZ,CAAuBT,UAAvB,EAAmCmB,IAAnC,CAAwCN,OAAxC,EAAiDE,QAAjD;AACD,KAHD,MAGO;AACL;AACAL,MAAAA,iBAAiB,CAACD,UAAlB,CAA6BT,UAA7B,EAAyCoB,OAAzC,CAAiDP,OAAjD,EAA0DE,QAA1D;AACD;AACF,GAhEY;;AAiEbJ,EAAAA,oBAAoB,CAACX,UAAD,EAAqBqB,SAArB,EAAwC;AAC1D,QAAI,gEAAJ,EAAqC;AACnCjB,2BAAYK,UAAZ,CAAuBT,UAAvB,EAAmCsB,mBAAnC,CAAuDD,SAAvD;;AAEAf,kCAAmBC,WAAnB,GAAiCC,qBAAjC,CACEJ,qBAAYK,UAAZ,CAAuBT,UAAvB,CADF,EAEEqB,SAFF;AAID,KAPD,MAOO;AACLX,MAAAA,iBAAiB,CAACD,UAAlB,CAA6BT,UAA7B,EAAyCsB,mBAAzC,CAA6DD,SAA7D;AACD;AACF,GA5EY;;AA6EbE,EAAAA,qBAAqB,CAACvB,UAAD,EAAqB;AACxC,QAAI,gEAAJ,EAAqC;AACnC,aAAOI,qBAAYK,UAAZ,CAAuBT,UAAvB,CAAP;AACD,KAFD,MAEO;AACL,aAAOU,iBAAiB,CAACD,UAAlB,CAA6BT,UAA7B,CAAP;AACD;AACF,GAnFY;;AAoFbwB,EAAAA,kBAAkB,CAACxB,UAAD,EAAqB;AACrC,QAAI,gEAAJ,EAAqC;AACnCI,2BAAYoB,kBAAZ,CAA+BxB,UAA/B;AACD,KAFD,MAEO;AACLU,MAAAA,iBAAiB,CAACc,kBAAlB,CAAqCxB,UAArC;AACD;AACF,GA1FY;;AA2Fb;AACAyB,EAAAA,eAAe,GAAG,CAAE;;AA5FP,C", "sourcesContent": ["import React from 'react';\n\nimport { ActionType } from './ActionType';\nimport { isNewWebImplementationEnabled } from './EnableNewWebImplementation';\n\n//GestureHandlers\nimport InteractionManager from './web/tools/InteractionManager';\nimport NodeManager from './web/tools/NodeManager';\nimport PanGestureHandler from './web/handlers/PanGestureHandler';\nimport TapGestureHandler from './web/handlers/TapGestureHandler';\nimport LongPressGestureHandler from './web/handlers/LongPressGestureHandler';\nimport PinchGestureHandler from './web/handlers/PinchGestureHandler';\nimport RotationGestureHandler from './web/handlers/RotationGestureHandler';\nimport FlingGestureHandler from './web/handlers/FlingGestureHandler';\nimport NativeViewGestureHandler from './web/handlers/NativeViewGestureHandler';\nimport ManualGestureHandler from './web/handlers/ManualGestureHandler';\nimport HoverGestureHandler from './web/handlers/HoverGestureHandler';\n\n//Hammer Handlers\nimport * as HammerNodeManager from './web_hammer/NodeManager';\nimport HammerNativeViewGestureHandler from './web_hammer/NativeViewGestureHandler';\nimport HammerPanGestureHandler from './web_hammer/PanGestureHandler';\nimport HammerTapGestureHandler from './web_hammer/TapGestureHandler';\nimport HammerLongPressGestureHandler from './web_hammer/LongPressGestureHandler';\nimport HammerPinchGestureHandler from './web_hammer/PinchGestureHandler';\nimport HammerRotationGestureHandler from './web_hammer/RotationGestureHandler';\nimport HammerFlingGestureHandler from './web_hammer/FlingGestureHandler';\nimport { Config } from './web/interfaces';\nimport { GestureHandlerWebDelegate } from './web/tools/GestureHandlerWebDelegate';\n\nexport const Gestures = {\n  NativeViewGestureHandler,\n  PanGestureHandler,\n  TapGestureHandler,\n  LongPressGestureHandler,\n  PinchGestureHandler,\n  RotationGestureHandler,\n  FlingGestureHandler,\n  ManualGestureHandler,\n  HoverGestureHandler,\n};\n\nexport const HammerGestures = {\n  NativeViewGestureHandler: HammerNativeViewGestureHandler,\n  PanGestureHandler: HammerPanGestureHandler,\n  TapGestureHandler: HammerTapGestureHandler,\n  LongPressGestureHandler: HammerLongPressGestureHandler,\n  PinchGestureHandler: HammerPinchGestureHandler,\n  RotationGestureHandler: HammerRotationGestureHandler,\n  FlingGestureHandler: HammerFlingGestureHandler,\n};\n\nexport default {\n  handleSetJSResponder(tag: number, blockNativeResponder: boolean) {\n    console.warn('handleSetJSResponder: ', tag, blockNativeResponder);\n  },\n  handleClearJSResponder() {\n    console.warn('handleClearJSResponder: ');\n  },\n  createGestureHandler<T>(\n    handlerName: keyof typeof Gestures,\n    handlerTag: number,\n    config: T\n  ) {\n    if (isNewWebImplementationEnabled()) {\n      if (!(handlerName in Gestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      const GestureClass = Gestures[handlerName];\n      NodeManager.createGestureHandler(\n        handlerTag,\n        new GestureClass(new GestureHandlerWebDelegate())\n      );\n      InteractionManager.getInstance().configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        config as unknown as Config\n      );\n    } else {\n      if (!(handlerName in HammerGestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      // @ts-ignore If it doesn't exist, the error is thrown\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      const GestureClass = HammerGestures[handlerName];\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n      HammerNodeManager.createGestureHandler(handlerTag, new GestureClass());\n    }\n\n    this.updateGestureHandler(handlerTag, config as unknown as Config);\n  },\n  attachGestureHandler(\n    handlerTag: number,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    newView: any,\n    _actionType: ActionType,\n    propsRef: React.RefObject<unknown>\n  ) {\n    if (\n      !(newView instanceof HTMLElement || newView instanceof React.Component)\n    ) {\n      return;\n    }\n\n    if (isNewWebImplementationEnabled()) {\n      //@ts-ignore Types should be HTMLElement or React.Component\n      NodeManager.getHandler(handlerTag).init(newView, propsRef);\n    } else {\n      //@ts-ignore Types should be HTMLElement or React.Component\n      HammerNodeManager.getHandler(handlerTag).setView(newView, propsRef);\n    }\n  },\n  updateGestureHandler(handlerTag: number, newConfig: Config) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n\n      InteractionManager.getInstance().configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        newConfig\n      );\n    } else {\n      HammerNodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n    }\n  },\n  getGestureHandlerNode(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      return NodeManager.getHandler(handlerTag);\n    } else {\n      return HammerNodeManager.getHandler(handlerTag);\n    }\n  },\n  dropGestureHandler(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.dropGestureHandler(handlerTag);\n    } else {\n      HammerNodeManager.dropGestureHandler(handlerTag);\n    }\n  },\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  flushOperations() {},\n};\n"]}