{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport { AnimatedEvent } from \"../AnimatedEvent\";\nimport AnimatedNode from \"./AnimatedNode\";\nimport AnimatedStyle from \"./AnimatedStyle\";\nimport NativeAnimatedHelper from \"../NativeAnimatedHelper\";\nimport invariant from 'fbjs/lib/invariant';\nvar AnimatedProps = function (_AnimatedNode) {\n  function AnimatedProps(props, callback) {\n    var _this;\n    _classCallCheck(this, AnimatedProps);\n    _this = _callSuper(this, AnimatedProps);\n    if (props.style) {\n      props = _objectSpread(_objectSpread({}, props), {}, {\n        style: new AnimatedStyle(props.style)\n      });\n    }\n    _this._props = props;\n    _this._callback = callback;\n    _this.__attach();\n    return _this;\n  }\n  _inherits(AnimatedProps, _AnimatedNode);\n  return _createClass(AnimatedProps, [{\n    key: \"__getValue\",\n    value: function __getValue() {\n      var props = {};\n      for (var key in this._props) {\n        var value = this._props[key];\n        if (value instanceof AnimatedNode) {\n          if (!value.__isNative || value instanceof AnimatedStyle) {\n            props[key] = value.__getValue();\n          }\n        } else if (value instanceof AnimatedEvent) {\n          props[key] = value.__getHandler();\n        } else {\n          props[key] = value;\n        }\n      }\n      return props;\n    }\n  }, {\n    key: \"__getAnimatedValue\",\n    value: function __getAnimatedValue() {\n      var props = {};\n      for (var key in this._props) {\n        var value = this._props[key];\n        if (value instanceof AnimatedNode) {\n          props[key] = value.__getAnimatedValue();\n        }\n      }\n      return props;\n    }\n  }, {\n    key: \"__attach\",\n    value: function __attach() {\n      for (var key in this._props) {\n        var value = this._props[key];\n        if (value instanceof AnimatedNode) {\n          value.__addChild(this);\n        }\n      }\n    }\n  }, {\n    key: \"__detach\",\n    value: function __detach() {\n      if (this.__isNative && this._animatedView) {\n        this.__disconnectAnimatedView();\n      }\n      for (var key in this._props) {\n        var value = this._props[key];\n        if (value instanceof AnimatedNode) {\n          value.__removeChild(this);\n        }\n      }\n      _superPropGet(AnimatedProps, \"__detach\", this, 3)([]);\n    }\n  }, {\n    key: \"update\",\n    value: function update() {\n      this._callback();\n    }\n  }, {\n    key: \"__makeNative\",\n    value: function __makeNative() {\n      if (!this.__isNative) {\n        this.__isNative = true;\n        for (var key in this._props) {\n          var value = this._props[key];\n          if (value instanceof AnimatedNode) {\n            value.__makeNative();\n          }\n        }\n        if (this._animatedView) {\n          this.__connectAnimatedView();\n        }\n      }\n    }\n  }, {\n    key: \"setNativeView\",\n    value: function setNativeView(animatedView) {\n      if (this._animatedView === animatedView) {\n        return;\n      }\n      this._animatedView = animatedView;\n      if (this.__isNative) {\n        this.__connectAnimatedView();\n      }\n    }\n  }, {\n    key: \"__connectAnimatedView\",\n    value: function __connectAnimatedView() {\n      invariant(this.__isNative, 'Expected node to be marked as \"native\"');\n      var nativeViewTag = this._animatedView;\n      invariant(nativeViewTag != null, 'Unable to locate attached view in the native tree');\n      NativeAnimatedHelper.API.connectAnimatedNodeToView(this.__getNativeTag(), nativeViewTag);\n    }\n  }, {\n    key: \"__disconnectAnimatedView\",\n    value: function __disconnectAnimatedView() {\n      invariant(this.__isNative, 'Expected node to be marked as \"native\"');\n      var nativeViewTag = this._animatedView;\n      invariant(nativeViewTag != null, 'Unable to locate attached view in the native tree');\n      NativeAnimatedHelper.API.disconnectAnimatedNodeFromView(this.__getNativeTag(), nativeViewTag);\n    }\n  }, {\n    key: \"__restoreDefaultValues\",\n    value: function __restoreDefaultValues() {\n      if (this.__isNative) {\n        NativeAnimatedHelper.API.restoreDefaultValues(this.__getNativeTag());\n      }\n    }\n  }, {\n    key: \"__getNativeConfig\",\n    value: function __getNativeConfig() {\n      var propsConfig = {};\n      for (var propKey in this._props) {\n        var value = this._props[propKey];\n        if (value instanceof AnimatedNode) {\n          value.__makeNative();\n          propsConfig[propKey] = value.__getNativeTag();\n        }\n      }\n      return {\n        type: 'props',\n        props: propsConfig\n      };\n    }\n  }]);\n}(AnimatedNode);\nexport default AnimatedProps;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_objectSpread", "AnimatedEvent", "AnimatedNode", "AnimatedStyle", "NativeAnimatedHelper", "invariant", "AnimatedProps", "_AnimatedNode", "props", "callback", "_this", "style", "_props", "_callback", "__attach", "key", "value", "__getValue", "__isNative", "__<PERSON><PERSON><PERSON><PERSON>", "__getAnimatedValue", "__add<PERSON><PERSON>d", "__detach", "_animated<PERSON>iew", "__disconnectAnimatedView", "__remove<PERSON><PERSON>d", "update", "__makeNative", "__connectAnimatedView", "setNativeView", "animatedView", "nativeViewTag", "API", "connectAnimatedNodeToView", "__getNativeTag", "disconnectAnimatedNodeFromView", "__restore<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "restoreDefaultValues", "__getNativeConfig", "propsConfig", "<PERSON><PERSON><PERSON>", "type"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedProps.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport { AnimatedEvent } from '../AnimatedEvent';\nimport AnimatedNode from './AnimatedNode';\nimport AnimatedStyle from './AnimatedStyle';\nimport NativeAnimatedHelper from '../NativeAnimatedHelper';\nimport invariant from 'fbjs/lib/invariant';\nclass AnimatedProps extends AnimatedNode {\n  constructor(props, callback) {\n    super();\n    if (props.style) {\n      props = _objectSpread(_objectSpread({}, props), {}, {\n        style: new AnimatedStyle(props.style)\n      });\n    }\n    this._props = props;\n    this._callback = callback;\n    this.__attach();\n  }\n  __getValue() {\n    var props = {};\n    for (var key in this._props) {\n      var value = this._props[key];\n      if (value instanceof AnimatedNode) {\n        if (!value.__isNative || value instanceof AnimatedStyle) {\n          // We cannot use value of natively driven nodes this way as the value we have access from\n          // JS may not be up to date.\n          props[key] = value.__getValue();\n        }\n      } else if (value instanceof AnimatedEvent) {\n        props[key] = value.__getHandler();\n      } else {\n        props[key] = value;\n      }\n    }\n    return props;\n  }\n  __getAnimatedValue() {\n    var props = {};\n    for (var key in this._props) {\n      var value = this._props[key];\n      if (value instanceof AnimatedNode) {\n        props[key] = value.__getAnimatedValue();\n      }\n    }\n    return props;\n  }\n  __attach() {\n    for (var key in this._props) {\n      var value = this._props[key];\n      if (value instanceof AnimatedNode) {\n        value.__addChild(this);\n      }\n    }\n  }\n  __detach() {\n    if (this.__isNative && this._animatedView) {\n      this.__disconnectAnimatedView();\n    }\n    for (var key in this._props) {\n      var value = this._props[key];\n      if (value instanceof AnimatedNode) {\n        value.__removeChild(this);\n      }\n    }\n    super.__detach();\n  }\n  update() {\n    this._callback();\n  }\n  __makeNative() {\n    if (!this.__isNative) {\n      this.__isNative = true;\n      for (var key in this._props) {\n        var value = this._props[key];\n        if (value instanceof AnimatedNode) {\n          value.__makeNative();\n        }\n      }\n      if (this._animatedView) {\n        this.__connectAnimatedView();\n      }\n    }\n  }\n  setNativeView(animatedView) {\n    if (this._animatedView === animatedView) {\n      return;\n    }\n    this._animatedView = animatedView;\n    if (this.__isNative) {\n      this.__connectAnimatedView();\n    }\n  }\n  __connectAnimatedView() {\n    invariant(this.__isNative, 'Expected node to be marked as \"native\"');\n    var nativeViewTag = this._animatedView;\n    invariant(nativeViewTag != null, 'Unable to locate attached view in the native tree');\n    NativeAnimatedHelper.API.connectAnimatedNodeToView(this.__getNativeTag(), nativeViewTag);\n  }\n  __disconnectAnimatedView() {\n    invariant(this.__isNative, 'Expected node to be marked as \"native\"');\n    var nativeViewTag = this._animatedView;\n    invariant(nativeViewTag != null, 'Unable to locate attached view in the native tree');\n    NativeAnimatedHelper.API.disconnectAnimatedNodeFromView(this.__getNativeTag(), nativeViewTag);\n  }\n  __restoreDefaultValues() {\n    // When using the native driver, view properties need to be restored to\n    // their default values manually since react no longer tracks them. This\n    // is needed to handle cases where a prop driven by native animated is removed\n    // after having been changed natively by an animation.\n    if (this.__isNative) {\n      NativeAnimatedHelper.API.restoreDefaultValues(this.__getNativeTag());\n    }\n  }\n  __getNativeConfig() {\n    var propsConfig = {};\n    for (var propKey in this._props) {\n      var value = this._props[propKey];\n      if (value instanceof AnimatedNode) {\n        value.__makeNative();\n        propsConfig[propKey] = value.__getNativeTag();\n      }\n    }\n    return {\n      type: 'props',\n      props: propsConfig\n    };\n  }\n}\nexport default AnimatedProps;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,aAAa,MAAM,sCAAsC;AAChE,SAASC,aAAa;AACtB,OAAOC,YAAY;AACnB,OAAOC,aAAa;AACpB,OAAOC,oBAAoB;AAC3B,OAAOC,SAAS,MAAM,oBAAoB;AAAC,IACrCC,aAAa,aAAAC,aAAA;EACjB,SAAAD,cAAYE,KAAK,EAAEC,QAAQ,EAAE;IAAA,IAAAC,KAAA;IAAAhC,eAAA,OAAA4B,aAAA;IAC3BI,KAAA,GAAA1B,UAAA,OAAAsB,aAAA;IACA,IAAIE,KAAK,CAACG,KAAK,EAAE;MACfH,KAAK,GAAGR,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEQ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAClDG,KAAK,EAAE,IAAIR,aAAa,CAACK,KAAK,CAACG,KAAK;MACtC,CAAC,CAAC;IACJ;IACAD,KAAA,CAAKE,MAAM,GAAGJ,KAAK;IACnBE,KAAA,CAAKG,SAAS,GAAGJ,QAAQ;IACzBC,KAAA,CAAKI,QAAQ,CAAC,CAAC;IAAC,OAAAJ,KAAA;EAClB;EAAC3B,SAAA,CAAAuB,aAAA,EAAAC,aAAA;EAAA,OAAA5B,YAAA,CAAA2B,aAAA;IAAAS,GAAA;IAAAC,KAAA,EACD,SAAAC,UAAUA,CAAA,EAAG;MACX,IAAIT,KAAK,GAAG,CAAC,CAAC;MACd,KAAK,IAAIO,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;QAC3B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYd,YAAY,EAAE;UACjC,IAAI,CAACc,KAAK,CAACE,UAAU,IAAIF,KAAK,YAAYb,aAAa,EAAE;YAGvDK,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK,CAACC,UAAU,CAAC,CAAC;UACjC;QACF,CAAC,MAAM,IAAID,KAAK,YAAYf,aAAa,EAAE;UACzCO,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK,CAACG,YAAY,CAAC,CAAC;QACnC,CAAC,MAAM;UACLX,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK;QACpB;MACF;MACA,OAAOR,KAAK;IACd;EAAC;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAI,kBAAkBA,CAAA,EAAG;MACnB,IAAIZ,KAAK,GAAG,CAAC,CAAC;MACd,KAAK,IAAIO,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;QAC3B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYd,YAAY,EAAE;UACjCM,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK,CAACI,kBAAkB,CAAC,CAAC;QACzC;MACF;MACA,OAAOZ,KAAK;IACd;EAAC;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAF,QAAQA,CAAA,EAAG;MACT,KAAK,IAAIC,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;QAC3B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYd,YAAY,EAAE;UACjCc,KAAK,CAACK,UAAU,CAAC,IAAI,CAAC;QACxB;MACF;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAAM,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAACJ,UAAU,IAAI,IAAI,CAACK,aAAa,EAAE;QACzC,IAAI,CAACC,wBAAwB,CAAC,CAAC;MACjC;MACA,KAAK,IAAIT,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;QAC3B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYd,YAAY,EAAE;UACjCc,KAAK,CAACS,aAAa,CAAC,IAAI,CAAC;QAC3B;MACF;MACA5B,aAAA,CAAAS,aAAA;IACF;EAAC;IAAAS,GAAA;IAAAC,KAAA,EACD,SAAAU,MAAMA,CAAA,EAAG;MACP,IAAI,CAACb,SAAS,CAAC,CAAC;IAClB;EAAC;IAAAE,GAAA;IAAAC,KAAA,EACD,SAAAW,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC,IAAI,CAACT,UAAU,EAAE;QACpB,IAAI,CAACA,UAAU,GAAG,IAAI;QACtB,KAAK,IAAIH,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;UAC3B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC;UAC5B,IAAIC,KAAK,YAAYd,YAAY,EAAE;YACjCc,KAAK,CAACW,YAAY,CAAC,CAAC;UACtB;QACF;QACA,IAAI,IAAI,CAACJ,aAAa,EAAE;UACtB,IAAI,CAACK,qBAAqB,CAAC,CAAC;QAC9B;MACF;IACF;EAAC;IAAAb,GAAA;IAAAC,KAAA,EACD,SAAAa,aAAaA,CAACC,YAAY,EAAE;MAC1B,IAAI,IAAI,CAACP,aAAa,KAAKO,YAAY,EAAE;QACvC;MACF;MACA,IAAI,CAACP,aAAa,GAAGO,YAAY;MACjC,IAAI,IAAI,CAACZ,UAAU,EAAE;QACnB,IAAI,CAACU,qBAAqB,CAAC,CAAC;MAC9B;IACF;EAAC;IAAAb,GAAA;IAAAC,KAAA,EACD,SAAAY,qBAAqBA,CAAA,EAAG;MACtBvB,SAAS,CAAC,IAAI,CAACa,UAAU,EAAE,wCAAwC,CAAC;MACpE,IAAIa,aAAa,GAAG,IAAI,CAACR,aAAa;MACtClB,SAAS,CAAC0B,aAAa,IAAI,IAAI,EAAE,mDAAmD,CAAC;MACrF3B,oBAAoB,CAAC4B,GAAG,CAACC,yBAAyB,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEH,aAAa,CAAC;IAC1F;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EACD,SAAAQ,wBAAwBA,CAAA,EAAG;MACzBnB,SAAS,CAAC,IAAI,CAACa,UAAU,EAAE,wCAAwC,CAAC;MACpE,IAAIa,aAAa,GAAG,IAAI,CAACR,aAAa;MACtClB,SAAS,CAAC0B,aAAa,IAAI,IAAI,EAAE,mDAAmD,CAAC;MACrF3B,oBAAoB,CAAC4B,GAAG,CAACG,8BAA8B,CAAC,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEH,aAAa,CAAC;IAC/F;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EACD,SAAAoB,sBAAsBA,CAAA,EAAG;MAKvB,IAAI,IAAI,CAAClB,UAAU,EAAE;QACnBd,oBAAoB,CAAC4B,GAAG,CAACK,oBAAoB,CAAC,IAAI,CAACH,cAAc,CAAC,CAAC,CAAC;MACtE;IACF;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EACD,SAAAsB,iBAAiBA,CAAA,EAAG;MAClB,IAAIC,WAAW,GAAG,CAAC,CAAC;MACpB,KAAK,IAAIC,OAAO,IAAI,IAAI,CAAC5B,MAAM,EAAE;QAC/B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAAC4B,OAAO,CAAC;QAChC,IAAIxB,KAAK,YAAYd,YAAY,EAAE;UACjCc,KAAK,CAACW,YAAY,CAAC,CAAC;UACpBY,WAAW,CAACC,OAAO,CAAC,GAAGxB,KAAK,CAACkB,cAAc,CAAC,CAAC;QAC/C;MACF;MACA,OAAO;QACLO,IAAI,EAAE,OAAO;QACbjC,KAAK,EAAE+B;MACT,CAAC;IACH;EAAC;AAAA,EAxHyBrC,YAAY;AA0HxC,eAAeI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}