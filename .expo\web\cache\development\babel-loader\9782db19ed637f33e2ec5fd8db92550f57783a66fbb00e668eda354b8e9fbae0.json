{"ast": null, "code": "import { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport { getBottomNavHeight, BOTTOM_NAV_HEIGHT, FAB_ELEVATION } from '../components/BottomNavigation';\nexport var useBottomNavigation = function useBottomNavigation() {\n  var insets = useSafeAreaInsets();\n  return {\n    baseHeight: BOTTOM_NAV_HEIGHT,\n    safeAreaBottom: insets.bottom,\n    totalHeight: getBottomNavHeight(insets.bottom),\n    fabElevation: FAB_ELEVATION,\n    getContentPadding: function getContentPadding() {\n      var extraPadding = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      return getBottomNavHeight(insets.bottom) + extraPadding;\n    },\n    getContentStyle: function getContentStyle() {\n      var extraPadding = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      return {\n        paddingBottom: getBottomNavHeight(insets.bottom) + extraPadding\n      };\n    },\n    getScrollContentStyle: function getScrollContentStyle() {\n      var extraPadding = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      return {\n        paddingBottom: getBottomNavHeight(insets.bottom) + extraPadding\n      };\n    }\n  };\n};\nexport default useBottomNavigation;", "map": {"version": 3, "names": ["useSafeAreaInsets", "getBottomNavHeight", "BOTTOM_NAV_HEIGHT", "FAB_ELEVATION", "useBottomNavigation", "insets", "baseHeight", "safeAreaBottom", "bottom", "totalHeight", "fabElevation", "getContentPadding", "extraPadding", "arguments", "length", "undefined", "getContentStyle", "paddingBottom", "getScrollContentStyle"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/hooks/useBottomNavigation.js"], "sourcesContent": ["import { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport { getBottomNavHeight, BOTTOM_NAV_HEIGHT, FAB_ELEVATION } from '../components/BottomNavigation';\n\n/**\n * Custom hook to get bottom navigation dimensions and utilities\n * @returns {Object} Object containing navigation height info and utilities\n */\nexport const useBottomNavigation = () => {\n  const insets = useSafeAreaInsets();\n  \n  return {\n    // Heights\n    baseHeight: BOTTOM_NAV_HEIGHT,\n    safeAreaBottom: insets.bottom,\n    totalHeight: getBottomNavHeight(insets.bottom),\n    fabElevation: FAB_ELEVATION,\n    \n    // Utility functions\n    getContentPadding: (extraPadding = 0) => {\n      return getBottomNavHeight(insets.bottom) + extraPadding;\n    },\n    \n    // Style helpers\n    getContentStyle: (extraPadding = 0) => ({\n      paddingBottom: getBottomNavHeight(insets.bottom) + extraPadding,\n    }),\n    \n    getScrollContentStyle: (extraPadding = 0) => ({\n      paddingBottom: getBottomNavHeight(insets.bottom) + extraPadding,\n    }),\n  };\n};\n\nexport default useBottomNavigation;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,gCAAgC;AAMrG,OAAO,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;EACvC,IAAMC,MAAM,GAAGL,iBAAiB,CAAC,CAAC;EAElC,OAAO;IAELM,UAAU,EAAEJ,iBAAiB;IAC7BK,cAAc,EAAEF,MAAM,CAACG,MAAM;IAC7BC,WAAW,EAAER,kBAAkB,CAACI,MAAM,CAACG,MAAM,CAAC;IAC9CE,YAAY,EAAEP,aAAa;IAG3BQ,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAA,EAAwB;MAAA,IAArBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAClC,OAAOZ,kBAAkB,CAACI,MAAM,CAACG,MAAM,CAAC,GAAGI,YAAY;IACzD,CAAC;IAGDI,eAAe,EAAE,SAAjBA,eAAeA,CAAA;MAAA,IAAGJ,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,OAAM;QACtCI,aAAa,EAAEhB,kBAAkB,CAACI,MAAM,CAACG,MAAM,CAAC,GAAGI;MACrD,CAAC;IAAA,CAAC;IAEFM,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAA;MAAA,IAAGN,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,OAAM;QAC5CI,aAAa,EAAEhB,kBAAkB,CAACI,MAAM,CAACG,MAAM,CAAC,GAAGI;MACrD,CAAC;IAAA;EACH,CAAC;AACH,CAAC;AAED,eAAeR,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}