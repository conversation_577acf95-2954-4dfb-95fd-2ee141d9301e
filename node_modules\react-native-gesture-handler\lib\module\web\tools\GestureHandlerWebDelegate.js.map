{"version": 3, "sources": ["GestureHandlerWebDelegate.ts"], "names": ["findNodeHandle", "PointerEventManager", "TouchEventManager", "State", "isPointerInBounds", "GestureHandlerWebDelegate", "get<PERSON>iew", "view", "init", "viewRef", "handler", "Error", "getTag", "<PERSON><PERSON><PERSON><PERSON>", "style", "config", "getConfig", "userSelect", "eventManagers", "push", "for<PERSON>ach", "manager", "attachEventManager", "x", "y", "measure<PERSON>iew", "rect", "getBoundingClientRect", "pageX", "left", "pageY", "top", "width", "height", "reset", "resetManager", "tryResetCursor", "activeCursor", "getState", "ACTIVE", "cursor", "onBegin", "onActivate", "onEnd", "onCancel", "onFail"], "mappings": ";;AAAA,SAASA,cAAT,QAA+B,cAA/B;AAMA,OAAOC,mBAAP,MAAgC,uBAAhC;AACA,OAAOC,iBAAP,MAA8B,qBAA9B;AACA,SAASC,KAAT,QAAsB,aAAtB;AACA,SAASC,iBAAT,QAAkC,UAAlC;AAGA,OAAO,MAAMC,yBAAN,CAEP;AAAA;AAAA;;AAAA;;AAAA,2CAGmD,EAHnD;AAAA;;AAKEC,EAAAA,OAAO,GAAgB;AACrB,WAAO,KAAKC,IAAZ;AACD;;AAEDC,EAAAA,IAAI,CAACC,OAAD,EAAkBC,OAAlB,EAAiD;AACnD,QAAI,CAACD,OAAL,EAAc;AACZ,YAAM,IAAIE,KAAJ,CACH,wCAAuCD,OAAO,CAACE,MAAR,EAAiB,EADrD,CAAN;AAGD;;AAED,SAAKC,cAAL,GAAsBH,OAAtB;AACA,SAAKH,IAAL,GAAYP,cAAc,CAACS,OAAD,CAA1B;AAEA,SAAKF,IAAL,CAAUO,KAAV,CAAgB,aAAhB,IAAiC,MAAjC,CAVmD,CAWnD;;AACA,SAAKP,IAAL,CAAUO,KAAV,CAAgB,oBAAhB,IAAwC,MAAxC;AAEA,UAAMC,MAAM,GAAGL,OAAO,CAACM,SAAR,EAAf;;AAEA,QAAI,CAACD,MAAM,CAACE,UAAZ,EAAwB;AACtB,WAAKV,IAAL,CAAUO,KAAV,CAAgB,kBAAhB,IAAsC,MAAtC;AACA,WAAKP,IAAL,CAAUO,KAAV,CAAgB,YAAhB,IAAgC,MAAhC;AACD,KAHD,MAGO;AACL,WAAKP,IAAL,CAAUO,KAAV,CAAgB,kBAAhB,IAAsCC,MAAM,CAACE,UAA7C;AACA,WAAKV,IAAL,CAAUO,KAAV,CAAgB,YAAhB,IAAgCC,MAAM,CAACE,UAAvC;AACD;;AAED,SAAKC,aAAL,CAAmBC,IAAnB,CAAwB,IAAIlB,mBAAJ,CAAwB,KAAKM,IAA7B,CAAxB;AACA,SAAKW,aAAL,CAAmBC,IAAnB,CAAwB,IAAIjB,iBAAJ,CAAsB,KAAKK,IAA3B,CAAxB;AAEA,SAAKW,aAAL,CAAmBE,OAAnB,CAA4BC,OAAD,IACzB,KAAKR,cAAL,CAAoBS,kBAApB,CAAuCD,OAAvC,CADF;AAGD;;AAEDjB,EAAAA,iBAAiB,CAAC;AAAEmB,IAAAA,CAAF;AAAKC,IAAAA;AAAL,GAAD,EAA8C;AAC7D,WAAOpB,iBAAiB,CAAC,KAAKG,IAAN,EAAY;AAAEgB,MAAAA,CAAF;AAAKC,MAAAA;AAAL,KAAZ,CAAxB;AACD;;AAEDC,EAAAA,WAAW,GAAkB;AAC3B,UAAMC,IAAI,GAAG,KAAKnB,IAAL,CAAUoB,qBAAV,EAAb;AAEA,WAAO;AACLC,MAAAA,KAAK,EAAEF,IAAI,CAACG,IADP;AAELC,MAAAA,KAAK,EAAEJ,IAAI,CAACK,GAFP;AAGLC,MAAAA,KAAK,EAAEN,IAAI,CAACM,KAHP;AAILC,MAAAA,MAAM,EAAEP,IAAI,CAACO;AAJR,KAAP;AAMD;;AAEDC,EAAAA,KAAK,GAAS;AACZ,SAAKhB,aAAL,CAAmBE,OAAnB,CAA4BC,OAAD,IACzBA,OAAO,CAACc,YAAR,EADF;AAGD;;AAEDC,EAAAA,cAAc,GAAG;AACf,UAAMrB,MAAM,GAAG,KAAKF,cAAL,CAAoBG,SAApB,EAAf;;AAEA,QACED,MAAM,CAACsB,YAAP,IACAtB,MAAM,CAACsB,YAAP,KAAwB,MADxB,IAEA,KAAKxB,cAAL,CAAoByB,QAApB,OAAmCnC,KAAK,CAACoC,MAH3C,EAIE;AACA,WAAKhC,IAAL,CAAUO,KAAV,CAAgB0B,MAAhB,GAAyB,MAAzB;AACD;AACF;;AAEDC,EAAAA,OAAO,GAAS,CACd;AACD;;AAEDC,EAAAA,UAAU,GAAS;AACjB,UAAM3B,MAAM,GAAG,KAAKF,cAAL,CAAoBG,SAApB,EAAf;;AAEA,QACE,CAAC,CAAC,KAAKT,IAAL,CAAUO,KAAV,CAAgB0B,MAAjB,IAA2B,KAAKjC,IAAL,CAAUO,KAAV,CAAgB0B,MAAhB,KAA2B,MAAvD,KACAzB,MAAM,CAACsB,YAFT,EAGE;AACA,WAAK9B,IAAL,CAAUO,KAAV,CAAgB0B,MAAhB,GAAyBzB,MAAM,CAACsB,YAAhC;AACD;AACF;;AAEDM,EAAAA,KAAK,GAAS;AACZ,SAAKP,cAAL;AACD;;AAEDQ,EAAAA,QAAQ,GAAS;AACf,SAAKR,cAAL;AACD;;AAEDS,EAAAA,MAAM,GAAS;AACb,SAAKT,cAAL;AACD;;AAnGH", "sourcesContent": ["import { findNodeHandle } from 'react-native';\nimport type G<PERSON>ureHand<PERSON> from '../handlers/GestureHandler';\nimport {\n  GestureHandlerDelegate,\n  MeasureResult,\n} from './GestureHandlerDelegate';\nimport PointerEventManager from './PointerEventManager';\nimport TouchEventManager from './TouchEventManager';\nimport { State } from '../../State';\nimport { isPointerInBounds } from '../utils';\nimport EventManager from './EventManager';\n\nexport class GestureHandlerWebDelegate\n  implements GestureHandlerDelegate<HTMLElement>\n{\n  private view!: HTMLElement;\n  private gestureHandler!: GestureHandler;\n  private eventManagers: EventManager<unknown>[] = [];\n\n  getView(): HTMLElement {\n    return this.view;\n  }\n\n  init(viewRef: number, handler: GestureHandler): void {\n    if (!viewRef) {\n      throw new Error(\n        `Cannot find HTML Element for handler ${handler.getTag()}`\n      );\n    }\n\n    this.gestureHandler = handler;\n    this.view = findNodeHandle(viewRef) as unknown as HTMLElement;\n\n    this.view.style['touchAction'] = 'none';\n    //@ts-ignore This one disables default events on Safari\n    this.view.style['WebkitTouchCallout'] = 'none';\n\n    const config = handler.getConfig();\n\n    if (!config.userSelect) {\n      this.view.style['webkitUserSelect'] = 'none';\n      this.view.style['userSelect'] = 'none';\n    } else {\n      this.view.style['webkitUserSelect'] = config.userSelect;\n      this.view.style['userSelect'] = config.userSelect;\n    }\n\n    this.eventManagers.push(new PointerEventManager(this.view));\n    this.eventManagers.push(new TouchEventManager(this.view));\n\n    this.eventManagers.forEach((manager) =>\n      this.gestureHandler.attachEventManager(manager)\n    );\n  }\n\n  isPointerInBounds({ x, y }: { x: number; y: number }): boolean {\n    return isPointerInBounds(this.view, { x, y });\n  }\n\n  measureView(): MeasureResult {\n    const rect = this.view.getBoundingClientRect();\n\n    return {\n      pageX: rect.left,\n      pageY: rect.top,\n      width: rect.width,\n      height: rect.height,\n    };\n  }\n\n  reset(): void {\n    this.eventManagers.forEach((manager: EventManager<unknown>) =>\n      manager.resetManager()\n    );\n  }\n\n  tryResetCursor() {\n    const config = this.gestureHandler.getConfig();\n\n    if (\n      config.activeCursor &&\n      config.activeCursor !== 'auto' &&\n      this.gestureHandler.getState() === State.ACTIVE\n    ) {\n      this.view.style.cursor = 'auto';\n    }\n  }\n\n  onBegin(): void {\n    // no-op for now\n  }\n\n  onActivate(): void {\n    const config = this.gestureHandler.getConfig();\n\n    if (\n      (!this.view.style.cursor || this.view.style.cursor === 'auto') &&\n      config.activeCursor\n    ) {\n      this.view.style.cursor = config.activeCursor;\n    }\n  }\n\n  onEnd(): void {\n    this.tryResetCursor();\n  }\n\n  onCancel(): void {\n    this.tryResetCursor();\n  }\n\n  onFail(): void {\n    this.tryResetCursor();\n  }\n}\n"]}