{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/SimpleLineIcons.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/SimpleLineIcons.json\";\nexport default createIconSet(glyphMap, 'simple-line-icons', font);", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\@expo\\vector-icons\\src\\SimpleLineIcons.ts"], "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/SimpleLineIcons.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/SimpleLineIcons.json';\n\nexport default createIconSet(glyphMap, 'simple-line-icons', font);\n"], "mappings": "AAAA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,eAAeF,aAAa,CAACE,QAAQ,EAAE,mBAAmB,EAAED,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}