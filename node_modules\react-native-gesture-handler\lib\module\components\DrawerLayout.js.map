{"version": 3, "sources": ["DrawerLayout.tsx"], "names": ["React", "Component", "invariant", "Animated", "StyleSheet", "View", "Keyboard", "StatusBar", "I18nManager", "PanGestureHandler", "TapGestureHandler", "State", "DRAG_TOSS", "IDLE", "DRAGGING", "SETTLING", "DrawerLayout", "constructor", "props", "createRef", "state", "drawerPosition", "drawerWidth", "drawerType", "dragX", "dragXValue", "touchX", "touchXValue", "drawerTranslation", "containerWidth", "multiply", "Value", "add", "setValue", "translationX", "startPositionX", "dragOffsetFromOnStartPosition", "interpolate", "inputRange", "outputRange", "openValue", "extrapolate", "gestureOptions", "useNativeDriver", "useNativeAnimations", "onDrawerSlide", "listener", "ev", "Math", "floor", "abs", "nativeEvent", "position", "onGestureEvent", "event", "x", "setState", "layout", "width", "newState", "drawerWillShow", "onDrawerStateChanged", "oldState", "ACTIVE", "handleRelease", "emitStateChanged", "drawerState", "keyboardDismissMode", "dismiss", "hideStatusBar", "setHidden", "statusBarAnimation", "drawerShown", "drawerLockMode", "closeDrawer", "velocityX", "gestureStartX", "dragOffsetBasedOnStart", "startOffsetX", "projOffsetX", "shouldOpen", "animateDrawer", "showing", "accessibilityIsModalView", "current", "setNativeProps", "accessibilityViewIsModal", "pointerEventsView", "pointerEvents", "minSwipeDistance", "edgeWidth", "fromLeft", "gestureOrientation", "hitSlop", "left", "undefined", "right", "panGestureHandler", "activeOffsetX", "fromValue", "toValue", "velocity", "speed", "nextFramePosition", "min", "max", "willShow", "updateShowing", "spring", "bounciness", "start", "finished", "drawerOpened", "onDrawerOpen", "onDrawerClose", "options", "forceUpdate", "overlayOpacity", "dynamicOverlayStyles", "opacity", "backgroundColor", "overlayColor", "onTapHandlerStateChange", "styles", "overlay", "drawerBackgroundColor", "drawerContainerStyle", "contentContainerStyle", "drawerSlide", "containerSlide", "reverseContentDirection", "isRTL", "dynamicDrawerStyles", "containerStyles", "containerTranslateX", "transform", "translateX", "drawerTranslateX", "closedDrawerOffset", "drawerStyles", "flexDirection", "main", "handleContainerLayout", "containerOnBack", "containerInFront", "children", "renderOverlay", "drawerContainer", "renderNavigationView", "ref", "onGestureRef", "updateAnimatedEvent", "shouldComponentUpdate", "render", "userSelect", "activeCursor", "setPanGestureRef", "openingHandlerStateChange", "enableTrackpadTwoFingerGesture", "renderDrawer", "Left", "Right", "create", "absoluteFillObject", "zIndex", "flex", "overflow"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,SAASC,SAAT,QAA0B,OAA1B;AACA,OAAOC,SAAP,MAAsB,WAAtB;AACA,SACEC,QADF,EAEEC,UAFF,EAGEC,IAHF,EAIEC,QAJF,EAKEC,SALF,EAMEC,WANF,QAYO,cAZP;AAoBA,SACEC,iBADF,QAGO,+BAHP;AAIA,SACEC,iBADF,QAGO,+BAHP;AAIA,SAASC,KAAT,QAAsB,UAAtB;AAEA,MAAMC,SAAS,GAAG,IAAlB;AAEA,MAAMC,IAAiB,GAAG,MAA1B;AACA,MAAMC,QAAqB,GAAG,UAA9B;AACA,MAAMC,QAAqB,GAAG,UAA9B;AAiJA,eAAe,MAAMC,YAAN,SAA2Bf,SAA3B,CAGb;AAaAgB,EAAAA,WAAW,CAACC,MAAD,EAA2B;AACpC,UAAMA,MAAN;;AADoC;;AAAA;;AAAA,mEAoCHlB,KAAK,CAACmB,SAAN,EApCG;;AAAA,4DAqCVnB,KAAK,CAACmB,SAAN,EArCU;;AAAA,4DAsCVnB,KAAK,CAACmB,SAAN,EAtCU;;AAAA,yCAuChB,KAvCgB;;AAAA,iDA8CR,CAC5BD,KAD4B,EAE5BE,KAF4B,KAGzB;AACH;AACA,YAAM;AAAEC,QAAAA,cAAF;AAAkBC,QAAAA,WAAlB;AAA+BC,QAAAA;AAA/B,UAA8CL,KAApD;AACA,YAAM;AACJM,QAAAA,KAAK,EAAEC,UADH;AAEJC,QAAAA,MAAM,EAAEC,WAFJ;AAGJC,QAAAA,iBAHI;AAIJC,QAAAA;AAJI,UAKFT,KALJ;AAOA,UAAII,KAAK,GAAGC,UAAZ;AACA,UAAIC,MAAM,GAAGC,WAAb;;AAEA,UAAIN,cAAc,KAAK,MAAvB,EAA+B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACAG,QAAAA,KAAK,GAAGrB,QAAQ,CAAC2B,QAAT,CACN,IAAI3B,QAAQ,CAAC4B,KAAb,CAAmB,CAAC,CAApB,CADM,EAENN,UAFM,CAAR,CAP6B,CAUR;;AACrBC,QAAAA,MAAM,GAAGvB,QAAQ,CAAC6B,GAAT,CACP,IAAI7B,QAAQ,CAAC4B,KAAb,CAAmBF,cAAnB,CADO,EAEP1B,QAAQ,CAAC2B,QAAT,CAAkB,IAAI3B,QAAQ,CAAC4B,KAAb,CAAmB,CAAC,CAApB,CAAlB,EAA0CJ,WAA1C,CAFO,CAAT,CAX6B,CAcR;;AACrBA,QAAAA,WAAW,CAACM,QAAZ,CAAqBJ,cAArB;AACD,OAhBD,MAgBO;AACLF,QAAAA,WAAW,CAACM,QAAZ,CAAqB,CAArB;AACD,OA/BE,CAiCH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,UAAIC,YAAY,GAAGV,KAAnB;;AACA,UAAID,UAAU,KAAK,OAAnB,EAA4B;AAC1B,cAAMY,cAAc,GAAGhC,QAAQ,CAAC6B,GAAT,CACrBN,MADqB,EAErBvB,QAAQ,CAAC2B,QAAT,CAAkB,IAAI3B,QAAQ,CAAC4B,KAAb,CAAmB,CAAC,CAApB,CAAlB,EAA0CP,KAA1C,CAFqB,CAAvB;AAKA,cAAMY,6BAA6B,GAAGD,cAAc,CAACE,WAAf,CAA2B;AAC/DC,UAAAA,UAAU,EAAE,CAAChB,WAAW,GAAI,CAAhB,EAAmBA,WAAnB,EAAiCA,WAAW,GAAI,CAAhD,CADmD;AAE/DiB,UAAAA,WAAW,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP;AAFkD,SAA3B,CAAtC;AAIAL,QAAAA,YAAY,GAAG/B,QAAQ,CAAC6B,GAAT,CACbR,KADa,EAEbY,6BAFa,CAAf,CAV0B,CAaL;AACtB;;AAED,WAAKI,SAAL,GAAiBrC,QAAQ,CAAC6B,GAAT,CAAaE,YAAb,EAA2BN,iBAA3B,EAA8CS,WAA9C,CAA0D;AACzEC,QAAAA,UAAU,EAAE,CAAC,CAAD,EAAIhB,WAAJ,CAD6D;AAEzEiB,QAAAA,WAAW,EAAE,CAAC,CAAD,EAAI,CAAJ,CAF4D;AAGzEE,QAAAA,WAAW,EAAE;AAH4D,OAA1D,CAAjB;AAMA,YAAMC,cAML,GAAG;AACFC,QAAAA,eAAe,EAAEzB,KAAK,CAAC0B;AADrB,OANJ;;AAUA,UAAI,KAAK1B,KAAL,CAAW2B,aAAf,EAA8B;AAC5BH,QAAAA,cAAc,CAACI,QAAf,GAA2BC,EAAD,IAAQ;AAAA;;AAChC,gBAAMb,YAAY,GAAGc,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,GAAL,CAASH,EAAE,CAACI,WAAH,CAAejB,YAAxB,CAAX,CAArB;AACA,gBAAMkB,QAAQ,GAAGlB,YAAY,GAAG,KAAKd,KAAL,CAAWS,cAA3C;AAEA,uDAAKX,KAAL,EAAW2B,aAAX,kGAA2BO,QAA3B;AACD,SALD;AAMD;;AAED,WAAKC,cAAL,GAAsBlD,QAAQ,CAACmD,KAAT,CACpB,CAAC;AAAEH,QAAAA,WAAW,EAAE;AAAEjB,UAAAA,YAAY,EAAET,UAAhB;AAA4B8B,UAAAA,CAAC,EAAE5B;AAA/B;AAAf,OAAD,CADoB,EAEpBe,cAFoB,CAAtB;AAID,KAzJqC;;AAAA,mDA2JN,CAAC;AAAES,MAAAA;AAAF,KAAD,KAAwC;AACtE,WAAKK,QAAL,CAAc;AAAE3B,QAAAA,cAAc,EAAEsB,WAAW,CAACM,MAAZ,CAAmBC;AAArC,OAAd;AACD,KA7JqC;;AAAA,8CA+JX,CACzBC,QADyB,EAEzBC,cAFyB,KAGtB;AAAA;;AACH,qDAAK1C,KAAL,EAAW2C,oBAAX,qGAAkCF,QAAlC,EAA4CC,cAA5C;AACD,KApKqC;;AAAA,uDAsKF,CAAC;AACnCT,MAAAA;AADmC,KAAD,KAE0B;AAC5D,UAAIA,WAAW,CAACW,QAAZ,KAAyBnD,KAAK,CAACoD,MAAnC,EAA2C;AACzC,aAAKC,aAAL,CAAmB;AAAEb,UAAAA;AAAF,SAAnB;AACD,OAFD,MAEO,IAAIA,WAAW,CAAC/B,KAAZ,KAAsBT,KAAK,CAACoD,MAAhC,EAAwC;AAC7C,aAAKE,gBAAL,CAAsBnD,QAAtB,EAAgC,KAAhC;AACA,aAAK0C,QAAL,CAAc;AAAEU,UAAAA,WAAW,EAAEpD;AAAf,SAAd;;AACA,YAAI,KAAKI,KAAL,CAAWiD,mBAAX,KAAmC,SAAvC,EAAkD;AAChD7D,UAAAA,QAAQ,CAAC8D,OAAT;AACD;;AACD,YAAI,KAAKlD,KAAL,CAAWmD,aAAf,EAA8B;AAC5B9D,UAAAA,SAAS,CAAC+D,SAAV,CAAoB,IAApB,EAA0B,KAAKpD,KAAL,CAAWqD,kBAAX,IAAiC,OAA3D;AACD;AACF;AACF,KArLqC;;AAAA,qDAuLJ,CAAC;AACjCpB,MAAAA;AADiC,KAAD,KAE4B;AAC5D,UACE,KAAKqB,WAAL,IACArB,WAAW,CAACW,QAAZ,KAAyBnD,KAAK,CAACoD,MAD/B,IAEA,KAAK7C,KAAL,CAAWuD,cAAX,KAA8B,aAHhC,EAIE;AACA,aAAKC,WAAL;AACD;AACF,KAjMqC;;AAAA,2CAmMd,CAAC;AACvBvB,MAAAA;AADuB,KAAD,KAEsC;AAC5D,YAAM;AAAE7B,QAAAA,WAAF;AAAeD,QAAAA,cAAf;AAA+BE,QAAAA;AAA/B,UAA8C,KAAKL,KAAzD;AACA,YAAM;AAAEW,QAAAA;AAAF,UAAqB,KAAKT,KAAhC;AACA,UAAI;AAAEc,QAAAA,YAAY,EAAEV,KAAhB;AAAuBmD,QAAAA,SAAvB;AAAkCpB,QAAAA,CAAC,EAAE7B;AAArC,UAAgDyB,WAApD;;AAEA,UAAI9B,cAAc,KAAK,MAAvB,EAA+B;AAC7B;AACA;AACAG,QAAAA,KAAK,GAAG,CAACA,KAAT;AACAE,QAAAA,MAAM,GAAGG,cAAc,GAAGH,MAA1B;AACAiD,QAAAA,SAAS,GAAG,CAACA,SAAb;AACD;;AAED,YAAMC,aAAa,GAAGlD,MAAM,GAAGF,KAA/B;AACA,UAAIqD,sBAAsB,GAAG,CAA7B;;AAEA,UAAItD,UAAU,KAAK,OAAnB,EAA4B;AAC1BsD,QAAAA,sBAAsB,GACpBD,aAAa,GAAGtD,WAAhB,GAA+BsD,aAAa,GAAGtD,WAA/C,GAA8D,CADhE;AAED;;AAED,YAAMwD,YAAY,GAChBtD,KAAK,GAAGqD,sBAAR,IAAkC,KAAKL,WAAL,GAAmBlD,WAAnB,GAAkC,CAApE,CADF;AAEA,YAAMyD,WAAW,GAAGD,YAAY,GAAGlE,SAAS,GAAG+D,SAA/C;AAEA,YAAMK,UAAU,GAAGD,WAAW,GAAGzD,WAAW,GAAI,CAAhD;;AAEA,UAAI0D,UAAJ,EAAgB;AACd,aAAKC,aAAL,CAAmBH,YAAnB,EAAiCxD,WAAjC,EAA+CqD,SAA/C;AACD,OAFD,MAEO;AACL,aAAKM,aAAL,CAAmBH,YAAnB,EAAiC,CAAjC,EAAoCH,SAApC;AACD;AACF,KArOqC;;AAAA,2CAuObO,OAAD,IAAsB;AAAA;;AAC5C,WAAKV,WAAL,GAAmBU,OAAnB;AACA,oCAAKC,wBAAL,CAA8BC,OAA9B,gFAAuCC,cAAvC,CAAsD;AACpDC,QAAAA,wBAAwB,EAAEJ;AAD0B,OAAtD;AAGA,oCAAKK,iBAAL,CAAuBH,OAAvB,gFAAgCC,cAAhC,CAA+C;AAC7CG,QAAAA,aAAa,EAAEN,OAAO,GAAG,MAAH,GAAY;AADW,OAA/C;AAGA,YAAM;AAAE7D,QAAAA,cAAF;AAAkBoE,QAAAA,gBAAlB;AAAoCC,QAAAA;AAApC,UAAkD,KAAKxE,KAA7D;AACA,YAAMyE,QAAQ,GAAGtE,cAAc,KAAK,MAApC,CAT4C,CAU5C;AACA;AACA;;AACA,YAAMuE,kBAAkB,GACtB,CAACD,QAAQ,GAAG,CAAH,GAAO,CAAC,CAAjB,KAAuB,KAAKnB,WAAL,GAAmB,CAAC,CAApB,GAAwB,CAA/C,CADF,CAb4C,CAe5C;AACA;AACA;;AACA,YAAMqB,OAAO,GAAGF,QAAQ,GACpB;AAAEG,QAAAA,IAAI,EAAE,CAAR;AAAWpC,QAAAA,KAAK,EAAEwB,OAAO,GAAGa,SAAH,GAAeL;AAAxC,OADoB,GAEpB;AAAEM,QAAAA,KAAK,EAAE,CAAT;AAAYtC,QAAAA,KAAK,EAAEwB,OAAO,GAAGa,SAAH,GAAeL;AAAzC,OAFJ,CAlB4C,CAqB5C;;AACA,oCAAKO,iBAAL,CAAuBb,OAAvB,gFAAgCC,cAAhC,CAA+C;AAC7CQ,QAAAA,OAD6C;AAE7CK,QAAAA,aAAa,EAAEN,kBAAkB,GAAGH;AAFS,OAA/C;AAID,KAjQqC;;AAAA,2CAmQd,CACtBU,SADsB,EAEtBC,OAFsB,EAGtBC,QAHsB,EAItBC,KAJsB,KAKnB;AACH,WAAKlF,KAAL,CAAWI,KAAX,CAAiBS,QAAjB,CAA0B,CAA1B;AACA,WAAKb,KAAL,CAAWM,MAAX,CAAkBO,QAAlB,CACE,KAAKf,KAAL,CAAWG,cAAX,KAA8B,MAA9B,GAAuC,CAAvC,GAA2C,KAAKD,KAAL,CAAWS,cADxD;;AAIA,UAAIsE,SAAS,IAAI,IAAjB,EAAuB;AACrB,YAAII,iBAAiB,GAAGJ,SAAxB;;AACA,YAAI,KAAKjF,KAAL,CAAW0B,mBAAf,EAAoC;AAClC;AACA;AACA;AACA;AACA,cAAIuD,SAAS,GAAGC,OAAZ,IAAuBC,QAAQ,GAAG,CAAtC,EAAyC;AACvCE,YAAAA,iBAAiB,GAAGvD,IAAI,CAACwD,GAAL,CAASL,SAAS,GAAGE,QAAQ,GAAG,IAAhC,EAAsCD,OAAtC,CAApB;AACD,WAFD,MAEO,IAAID,SAAS,GAAGC,OAAZ,IAAuBC,QAAQ,GAAG,CAAtC,EAAyC;AAC9CE,YAAAA,iBAAiB,GAAGvD,IAAI,CAACyD,GAAL,CAASN,SAAS,GAAGE,QAAQ,GAAG,IAAhC,EAAsCD,OAAtC,CAApB;AACD;AACF;;AACD,aAAKhF,KAAL,CAAWQ,iBAAX,CAA6BK,QAA7B,CAAsCsE,iBAAtC;AACD;;AAED,YAAMG,QAAQ,GAAGN,OAAO,KAAK,CAA7B;AACA,WAAKO,aAAL,CAAmBD,QAAnB;AACA,WAAKzC,gBAAL,CAAsBlD,QAAtB,EAAgC2F,QAAhC;AACA,WAAKlD,QAAL,CAAc;AAAEU,QAAAA,WAAW,EAAEnD;AAAf,OAAd;;AACA,UAAI,KAAKG,KAAL,CAAWmD,aAAf,EAA8B;AAC5B9D,QAAAA,SAAS,CAAC+D,SAAV,CAAoBoC,QAApB,EAA8B,KAAKxF,KAAL,CAAWqD,kBAAX,IAAiC,OAA/D;AACD;;AACDpE,MAAAA,QAAQ,CAACyG,MAAT,CAAgB,KAAKxF,KAAL,CAAWQ,iBAA3B,EAA8C;AAC5CyE,QAAAA,QAD4C;AAE5CQ,QAAAA,UAAU,EAAE,CAFgC;AAG5CT,QAAAA,OAH4C;AAI5CzD,QAAAA,eAAe,EAAE,KAAKzB,KAAL,CAAW0B,mBAJgB;AAK5C0D,QAAAA,KAAK,EAAEA,KAAF,aAAEA,KAAF,cAAEA,KAAF,GAAWP;AAL4B,OAA9C,EAMGe,KANH,CAMS,CAAC;AAAEC,QAAAA;AAAF,OAAD,KAAkB;AACzB,YAAIA,QAAJ,EAAc;AACZ,eAAK9C,gBAAL,CAAsBpD,IAAtB,EAA4B6F,QAA5B;AACA,eAAKlD,QAAL,CAAc;AAAEwD,YAAAA,YAAY,EAAEN;AAAhB,WAAd;;AACA,cAAI,KAAKtF,KAAL,CAAW8C,WAAX,KAA2BpD,QAA/B,EAAyC;AACvC;AACA;AACA,iBAAK0C,QAAL,CAAc;AAAEU,cAAAA,WAAW,EAAErD;AAAf,aAAd;AACD;;AACD,cAAI6F,QAAJ,EAAc;AAAA;;AACZ,0DAAKxF,KAAL,EAAW+F,YAAX;AACD,WAFD,MAEO;AAAA;;AACL,0DAAK/F,KAAL,EAAWgG,aAAX;AACD;AACF;AACF,OArBD;AAsBD,KA3TqC;;AAAA,wCA6TzB,CAACC,OAA6B,GAAG,EAAjC,KAAwC;AACnD,WAAKlC,aAAL,EACE;AACAc,MAAAA,SAFF,EAGE,KAAK7E,KAAL,CAAWI,WAHb,EAIE6F,OAAO,CAACd,QAAR,GAAmBc,OAAO,CAACd,QAA3B,GAAsC,CAJxC,EAKEc,OAAO,CAACb,KALV,EADmD,CASnD;AACA;;AACA,WAAKc,WAAL;AACD,KAzUqC;;AAAA,yCA2UxB,CAACD,OAA6B,GAAG,EAAjC,KAAwC;AACpD;AACA,WAAKlC,aAAL,CACEc,SADF,EAEE,CAFF,EAGEoB,OAAO,CAACd,QAAR,GAAmBc,OAAO,CAACd,QAA3B,GAAsC,CAHxC,EAIEc,OAAO,CAACb,KAJV,EAFoD,CASpD;AACA;;AACA,WAAKc,WAAL;AACD,KAvVqC;;AAAA,2CAyVd,MAAM;AAC5B;AACAlH,MAAAA,SAAS,CAAC,KAAKsC,SAAN,EAAiB,eAAjB,CAAT;AACA,UAAI6E,cAAJ;;AAEA,UAAI,KAAKjG,KAAL,CAAW8C,WAAX,KAA2BrD,IAA/B,EAAqC;AACnCwG,QAAAA,cAAc,GAAG,KAAK7E,SAAtB;AACD,OAFD,MAEO;AACL6E,QAAAA,cAAc,GAAG,KAAKjG,KAAL,CAAW4F,YAAX,GAA0B,CAA1B,GAA8B,CAA/C;AACD;;AAED,YAAMM,oBAAoB,GAAG;AAC3BC,QAAAA,OAAO,EAAEF,cADkB;AAE3BG,QAAAA,eAAe,EAAE,KAAKtG,KAAL,CAAWuG;AAFD,OAA7B;AAKA,0BACE,oBAAC,iBAAD;AAAmB,QAAA,oBAAoB,EAAE,KAAKC;AAA9C,sBACE,oBAAC,QAAD,CAAU,IAAV;AACE,QAAA,aAAa,EAAE,KAAKlD,WAAL,GAAmB,MAAnB,GAA4B,MAD7C;AAEE,QAAA,GAAG,EAAE,KAAKe,iBAFZ;AAGE,QAAA,KAAK,EAAE,CAACoC,MAAM,CAACC,OAAR,EAAiBN,oBAAjB;AAHT,QADF,CADF;AASD,KAlXqC;;AAAA,0CAoXf,MAAM;AAC3B,YAAM;AACJO,QAAAA,qBADI;AAEJvG,QAAAA,WAFI;AAGJD,QAAAA,cAHI;AAIJE,QAAAA,UAJI;AAKJuG,QAAAA,oBALI;AAMJC,QAAAA;AANI,UAOF,KAAK7G,KAPT;AASA,YAAMyE,QAAQ,GAAGtE,cAAc,KAAK,MAApC;AACA,YAAM2G,WAAW,GAAGzG,UAAU,KAAK,MAAnC;AACA,YAAM0G,cAAc,GAAG1G,UAAU,KAAK,OAAtC,CAZ2B,CAc3B;AACA;AACA;AACA;;AACA,YAAM2G,uBAAuB,GAAG1H,WAAW,CAAC2H,KAAZ,GAAoBxC,QAApB,GAA+B,CAACA,QAAhE;AAEA,YAAMyC,mBAAmB,GAAG;AAC1BZ,QAAAA,eAAe,EAAEK,qBADS;AAE1BnE,QAAAA,KAAK,EAAEpC;AAFmB,OAA5B;AAIA,YAAMkB,SAAS,GAAG,KAAKA,SAAvB;AACAtC,MAAAA,SAAS,CAACsC,SAAD,EAAY,eAAZ,CAAT;AAEA,UAAI6F,eAAJ;;AACA,UAAIJ,cAAJ,EAAoB;AAClB,cAAMK,mBAAmB,GAAG9F,SAAS,CAACH,WAAV,CAAsB;AAChDC,UAAAA,UAAU,EAAE,CAAC,CAAD,EAAI,CAAJ,CADoC;AAEhDC,UAAAA,WAAW,EAAEoD,QAAQ,GAAG,CAAC,CAAD,EAAIrE,WAAJ,CAAH,GAAuB,CAAC,CAAD,EAAI,CAACA,WAAL,CAFI;AAGhDmB,UAAAA,WAAW,EAAE;AAHmC,SAAtB,CAA5B;AAKA4F,QAAAA,eAAe,GAAG;AAChBE,UAAAA,SAAS,EAAE,CAAC;AAAEC,YAAAA,UAAU,EAAEF;AAAd,WAAD;AADK,SAAlB;AAGD;;AAED,UAAIG,gBAAgD,GAAG,CAAvD;;AACA,UAAIT,WAAJ,EAAiB;AACf,cAAMU,kBAAkB,GAAG/C,QAAQ,GAAG,CAACrE,WAAJ,GAAmBA,WAAtD;;AACA,YAAI,KAAKF,KAAL,CAAW8C,WAAX,KAA2BrD,IAA/B,EAAqC;AACnC4H,UAAAA,gBAAgB,GAAGjG,SAAS,CAACH,WAAV,CAAsB;AACvCC,YAAAA,UAAU,EAAE,CAAC,CAAD,EAAI,CAAJ,CAD2B;AAEvCC,YAAAA,WAAW,EAAE,CAACmG,kBAAD,EAAqB,CAArB,CAF0B;AAGvCjG,YAAAA,WAAW,EAAE;AAH0B,WAAtB,CAAnB;AAKD,SAND,MAMO;AACLgG,UAAAA,gBAAgB,GAAG,KAAKrH,KAAL,CAAW4F,YAAX,GAA0B,CAA1B,GAA8B0B,kBAAjD;AACD;AACF;;AACD,YAAMC,YAGL,GAAG;AACFJ,QAAAA,SAAS,EAAE,CAAC;AAAEC,UAAAA,UAAU,EAAEC;AAAd,SAAD,CADT;AAEFG,QAAAA,aAAa,EAAEV,uBAAuB,GAAG,aAAH,GAAmB;AAFvD,OAHJ;AAQA,0BACE,oBAAC,QAAD,CAAU,IAAV;AAAe,QAAA,KAAK,EAAEP,MAAM,CAACkB,IAA7B;AAAmC,QAAA,QAAQ,EAAE,KAAKC;AAAlD,sBACE,oBAAC,QAAD,CAAU,IAAV;AACE,QAAA,KAAK,EAAE,CACLvH,UAAU,KAAK,OAAf,GACIoG,MAAM,CAACoB,eADX,GAEIpB,MAAM,CAACqB,gBAHN,EAILX,eAJK,EAKLN,qBALK,CADT;AAQE,QAAA,yBAAyB,EACvB,KAAKvD,WAAL,GAAmB,qBAAnB,GAA2C;AAT/C,SAWG,OAAO,KAAKtD,KAAL,CAAW+H,QAAlB,KAA+B,UAA/B,GACG,KAAK/H,KAAL,CAAW+H,QAAX,CAAoB,KAAKzG,SAAzB,CADH,GAEG,KAAKtB,KAAL,CAAW+H,QAbjB,EAcG,KAAKC,aAAL,EAdH,CADF,eAiBE,oBAAC,QAAD,CAAU,IAAV;AACE,QAAA,aAAa,EAAC,UADhB;AAEE,QAAA,GAAG,EAAE,KAAK/D,wBAFZ;AAGE,QAAA,wBAAwB,EAAE,KAAKX,WAHjC;AAIE,QAAA,KAAK,EAAE,CAACmD,MAAM,CAACwB,eAAR,EAAyBR,YAAzB,EAAuCb,oBAAvC;AAJT,sBAKE,oBAAC,IAAD;AAAM,QAAA,KAAK,EAAEM;AAAb,SACG,KAAKlH,KAAL,CAAWkI,oBAAX,CAAgC,KAAK5G,SAArC,CADH,CALF,CAjBF,CADF;AA6BD,KA7cqC;;AAAA,8CA+cV6G,GAAD,IAA4B;AAAA;;AACrD;AACA;AAEE,WAAKpD,iBADP,CAEEb,OAFF,GAEYiE,GAFZ;AAGA,oDAAKnI,KAAL,EAAWoI,YAAX,mGAA0BD,GAA1B;AACD,KAtdqC;;AAGpC,UAAM7H,MAAK,GAAG,IAAIrB,QAAQ,CAAC4B,KAAb,CAAmB,CAAnB,CAAd;;AACA,UAAML,OAAM,GAAG,IAAIvB,QAAQ,CAAC4B,KAAb,CAAmB,CAAnB,CAAf;;AACA,UAAMH,kBAAiB,GAAG,IAAIzB,QAAQ,CAAC4B,KAAb,CAAmB,CAAnB,CAA1B;;AAEA,SAAKX,KAAL,GAAa;AACXI,MAAAA,KAAK,EAALA,MADW;AAEXE,MAAAA,MAAM,EAANA,OAFW;AAGXE,MAAAA,iBAAiB,EAAjBA,kBAHW;AAIXC,MAAAA,cAAc,EAAE,CAJL;AAKXqC,MAAAA,WAAW,EAAErD,IALF;AAMXmG,MAAAA,YAAY,EAAE;AANH,KAAb;AASA,SAAKuC,mBAAL,CAAyBrI,MAAzB,EAAgC,KAAKE,KAArC;AACD;;AAEDoI,EAAAA,qBAAqB,CAACtI,KAAD,EAA2BE,KAA3B,EAAqD;AACxE,QACE,KAAKF,KAAL,CAAWG,cAAX,KAA8BH,KAAK,CAACG,cAApC,IACA,KAAKH,KAAL,CAAWI,WAAX,KAA2BJ,KAAK,CAACI,WADjC,IAEA,KAAKJ,KAAL,CAAWK,UAAX,KAA0BL,KAAK,CAACK,UAFhC,IAGA,KAAKH,KAAL,CAAWS,cAAX,KAA8BT,KAAK,CAACS,cAJtC,EAKE;AACA,WAAK0H,mBAAL,CAAyBrI,KAAzB,EAAgCE,KAAhC;AACD;;AAED,WAAO,IAAP;AACD;;AA0bDqI,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEpI,MAAAA,cAAF;AAAkBoD,MAAAA,cAAlB;AAAkCiB,MAAAA,SAAlC;AAA6CD,MAAAA;AAA7C,QACJ,KAAKvE,KADP;AAGA,UAAMyE,QAAQ,GAAGtE,cAAc,KAAK,MAApC,CAJO,CAMP;AACA;AACA;;AACA,UAAMuE,kBAAkB,GACtB,CAACD,QAAQ,GAAG,CAAH,GAAO,CAAC,CAAjB,KAAuB,KAAKnB,WAAL,GAAmB,CAAC,CAApB,GAAwB,CAA/C,CADF,CATO,CAYP;AACA;AACA;;AACA,UAAMqB,OAAO,GAAGF,QAAQ,GACpB;AAAEG,MAAAA,IAAI,EAAE,CAAR;AAAWpC,MAAAA,KAAK,EAAE,KAAKc,WAAL,GAAmBuB,SAAnB,GAA+BL;AAAjD,KADoB,GAEpB;AAAEM,MAAAA,KAAK,EAAE,CAAT;AAAYtC,MAAAA,KAAK,EAAE,KAAKc,WAAL,GAAmBuB,SAAnB,GAA+BL;AAAlD,KAFJ;AAIA,wBACE,oBAAC,iBAAD,CACE;AADF;AAEE,MAAA,UAAU,EAAE,KAAKxE,KAAL,CAAWwI,UAFzB;AAGE,MAAA,YAAY,EAAE,KAAKxI,KAAL,CAAWyI,YAH3B;AAIE,MAAA,GAAG,EAAE,KAAKC,gBAJZ;AAKE,MAAA,OAAO,EAAE/D,OALX;AAME,MAAA,aAAa,EAAED,kBAAkB,GAAGH,gBANtC;AAOE,MAAA,WAAW,EAAE,CAAC,CAAC,EAAF,EAAM,EAAN,CAPf;AAQE,MAAA,cAAc,EAAE,KAAKpC,cARvB;AASE,MAAA,oBAAoB,EAAE,KAAKwG,yBAT7B;AAUE,MAAA,8BAA8B,EAC5B,KAAK3I,KAAL,CAAW4I,8BAXf;AAaE,MAAA,OAAO,EACLrF,cAAc,KAAK,eAAnB,IAAsCA,cAAc,KAAK;AAd7D,OAgBG,KAAKsF,YAAL,EAhBH,CADF;AAoBD;;AA5gBD;;gBAHmB/I,Y,kBAIG;AACpBM,EAAAA,WAAW,EAAE,GADO;AAEpBD,EAAAA,cAAc,EAAE,MAFI;AAGpBuB,EAAAA,mBAAmB,EAAE,IAHD;AAIpBrB,EAAAA,UAAU,EAAE,OAJQ;AAKpBmE,EAAAA,SAAS,EAAE,EALS;AAMpBD,EAAAA,gBAAgB,EAAE,CANE;AAOpBgC,EAAAA,YAAY,EAAE,oBAPM;AAQpBhD,EAAAA,cAAc,EAAE,UARI;AASpBqF,EAAAA,8BAA8B,EAAE;AATZ,C;;gBAJH9I,Y,eAyDA;AACjBgJ,EAAAA,IAAI,EAAE,MADW;AAEjBC,EAAAA,KAAK,EAAE;AAFU,C;;AAydrB,MAAMtC,MAAM,GAAGvH,UAAU,CAAC8J,MAAX,CAAkB;AAC/Bf,EAAAA,eAAe,EAAE,EACf,GAAG/I,UAAU,CAAC+J,kBADC;AAEfC,IAAAA,MAAM,EAAE,IAFO;AAGfxB,IAAAA,aAAa,EAAE;AAHA,GADc;AAM/BI,EAAAA,gBAAgB,EAAE,EAChB,GAAG5I,UAAU,CAAC+J,kBADE;AAEhBC,IAAAA,MAAM,EAAE;AAFQ,GANa;AAU/BrB,EAAAA,eAAe,EAAE,EACf,GAAG3I,UAAU,CAAC+J;AADC,GAVc;AAa/BtB,EAAAA,IAAI,EAAE;AACJwB,IAAAA,IAAI,EAAE,CADF;AAEJD,IAAAA,MAAM,EAAE,CAFJ;AAGJE,IAAAA,QAAQ,EAAE;AAHN,GAbyB;AAkB/B1C,EAAAA,OAAO,EAAE,EACP,GAAGxH,UAAU,CAAC+J,kBADP;AAEPC,IAAAA,MAAM,EAAE;AAFD;AAlBsB,CAAlB,CAAf", "sourcesContent": ["// This component is based on RN's DrawerLayoutAndroid API\n//\n// It perhaps deserves to be put in a separate repo, but since it relies on\n// react-native-gesture-handler library which isn't very popular at the moment I\n// decided to keep it here for the time being. It will allow us to move faster\n// and fix issues that may arise in gesture handler library that could be found\n// when using the drawer component\n\nimport * as React from 'react';\nimport { Component } from 'react';\nimport invariant from 'invariant';\nimport {\n  Animated,\n  StyleSheet,\n  View,\n  Keyboard,\n  StatusBar,\n  I18nManager,\n  StatusBarAnimation,\n  StyleProp,\n  ViewStyle,\n  LayoutChangeEvent,\n  NativeSyntheticEvent,\n} from 'react-native';\n\nimport {\n  GestureEvent,\n  HandlerStateChangeEvent,\n  UserSelect,\n  ActiveCursor,\n} from '../handlers/gestureHandlerCommon';\nimport {\n  PanGestureHandler,\n  PanGestureHandlerEventPayload,\n} from '../handlers/PanGestureHandler';\nimport {\n  TapGestureHandler,\n  TapGestureHandlerEventPayload,\n} from '../handlers/TapGestureHandler';\nimport { State } from '../State';\n\nconst DRAG_TOSS = 0.05;\n\nconst IDLE: DrawerState = 'Idle';\nconst DRAGGING: DrawerState = 'Dragging';\nconst SETTLING: DrawerState = 'Settling';\n\nexport type DrawerPosition = 'left' | 'right';\n\nexport type DrawerState = 'Idle' | 'Dragging' | 'Settling';\n\nexport type DrawerType = 'front' | 'back' | 'slide';\n\nexport type DrawerLockMode = 'unlocked' | 'locked-closed' | 'locked-open';\n\nexport type DrawerKeyboardDismissMode = 'none' | 'on-drag';\n\n// Animated.AnimatedInterpolation has been converted to a generic type\n// in @types/react-native 0.70. This way we can maintain compatibility\n// with all versions of @types/react-native`\ntype AnimatedInterpolation = ReturnType<Animated.Value['interpolate']>;\nexport interface DrawerLayoutProps {\n  /**\n   * This attribute is present in the standard implementation already and is one\n   * of the required params. Gesture handler version of DrawerLayout make it\n   * possible for the function passed as `renderNavigationView` to take an\n   * Animated value as a parameter that indicates the progress of drawer\n   * opening/closing animation (progress value is 0 when closed and 1 when\n   * opened). This can be used by the drawer component to animated its children\n   * while the drawer is opening or closing.\n   */\n  renderNavigationView: (\n    progressAnimatedValue: Animated.Value\n  ) => React.ReactNode;\n\n  drawerPosition?: DrawerPosition;\n\n  drawerWidth?: number;\n\n  drawerBackgroundColor?: string;\n\n  drawerLockMode?: DrawerLockMode;\n\n  keyboardDismissMode?: DrawerKeyboardDismissMode;\n\n  /**\n   * Called when the drawer is closed.\n   */\n  onDrawerClose?: () => void;\n\n  /**\n   * Called when the drawer is opened.\n   */\n  onDrawerOpen?: () => void;\n\n  /**\n   * Called when the status of the drawer changes.\n   */\n  onDrawerStateChanged?: (\n    newState: DrawerState,\n    drawerWillShow: boolean\n  ) => void;\n  useNativeAnimations?: boolean;\n\n  drawerType?: DrawerType;\n\n  /**\n   * Defines how far from the edge of the content view the gesture should\n   * activate.\n   */\n  edgeWidth?: number;\n\n  minSwipeDistance?: number;\n\n  /**\n   * When set to true Drawer component will use\n   * {@link https://reactnative.dev/docs/statusbar StatusBar} API to hide the OS\n   * status bar whenever the drawer is pulled or when its in an \"open\" state.\n   */\n  hideStatusBar?: boolean;\n\n  /**\n   * @default 'slide'\n   *\n   * Can be used when hideStatusBar is set to true and will select the animation\n   * used for hiding/showing the status bar. See\n   * {@link https://reactnative.dev/docs/statusbar StatusBar} documentation for\n   * more details\n   */\n  statusBarAnimation?: StatusBarAnimation;\n\n  /**\n   * @default black\n   *\n   * Color of a semi-transparent overlay to be displayed on top of the content\n   * view when drawer gets open. A solid color should be used as the opacity is\n   * added by the Drawer itself and the opacity of the overlay is animated (from\n   * 0% to 70%).\n   */\n  overlayColor?: string;\n\n  contentContainerStyle?: StyleProp<ViewStyle>;\n\n  drawerContainerStyle?: StyleProp<ViewStyle>;\n\n  /**\n   * Enables two-finger gestures on supported devices, for example iPads with\n   * trackpads. If not enabled the gesture will require click + drag, with\n   * `enableTrackpadTwoFingerGesture` swiping with two fingers will also trigger\n   * the gesture.\n   */\n  enableTrackpadTwoFingerGesture?: boolean;\n\n  onDrawerSlide?: (position: number) => void;\n\n  onGestureRef?: (ref: PanGestureHandler) => void;\n\n  // implicit `children` prop has been removed in @types/react^18.0.0\n  children?:\n    | React.ReactNode\n    | ((openValue?: AnimatedInterpolation) => React.ReactNode);\n\n  /**\n   * @default 'none'\n   * Defines which userSelect property should be used.\n   * Values: 'none'|'text'|'auto'\n   */\n  userSelect?: UserSelect;\n\n  /**\n   * @default 'auto'\n   * Defines which cursor property should be used when gesture activates.\n   * Values: see CSS cursor values\n   */\n  activeCursor?: ActiveCursor;\n}\n\nexport type DrawerLayoutState = {\n  dragX: Animated.Value;\n  touchX: Animated.Value;\n  drawerTranslation: Animated.Value;\n  containerWidth: number;\n  drawerState: DrawerState;\n  drawerOpened: boolean;\n};\n\nexport type DrawerMovementOption = {\n  velocity?: number;\n  speed?: number;\n};\nexport default class DrawerLayout extends Component<\n  DrawerLayoutProps,\n  DrawerLayoutState\n> {\n  static defaultProps = {\n    drawerWidth: 200,\n    drawerPosition: 'left',\n    useNativeAnimations: true,\n    drawerType: 'front',\n    edgeWidth: 20,\n    minSwipeDistance: 3,\n    overlayColor: 'rgba(0, 0, 0, 0.7)',\n    drawerLockMode: 'unlocked',\n    enableTrackpadTwoFingerGesture: false,\n  };\n\n  constructor(props: DrawerLayoutProps) {\n    super(props);\n\n    const dragX = new Animated.Value(0);\n    const touchX = new Animated.Value(0);\n    const drawerTranslation = new Animated.Value(0);\n\n    this.state = {\n      dragX,\n      touchX,\n      drawerTranslation,\n      containerWidth: 0,\n      drawerState: IDLE,\n      drawerOpened: false,\n    };\n\n    this.updateAnimatedEvent(props, this.state);\n  }\n\n  shouldComponentUpdate(props: DrawerLayoutProps, state: DrawerLayoutState) {\n    if (\n      this.props.drawerPosition !== props.drawerPosition ||\n      this.props.drawerWidth !== props.drawerWidth ||\n      this.props.drawerType !== props.drawerType ||\n      this.state.containerWidth !== state.containerWidth\n    ) {\n      this.updateAnimatedEvent(props, state);\n    }\n\n    return true;\n  }\n\n  private openValue?: AnimatedInterpolation;\n  private onGestureEvent?: (\n    event: GestureEvent<PanGestureHandlerEventPayload>\n  ) => void;\n  private accessibilityIsModalView = React.createRef<View>();\n  private pointerEventsView = React.createRef<View>();\n  private panGestureHandler = React.createRef<PanGestureHandler | null>();\n  private drawerShown = false;\n\n  static positions = {\n    Left: 'left',\n    Right: 'right',\n  };\n\n  private updateAnimatedEvent = (\n    props: DrawerLayoutProps,\n    state: DrawerLayoutState\n  ) => {\n    // Event definition is based on\n    const { drawerPosition, drawerWidth, drawerType } = props;\n    const {\n      dragX: dragXValue,\n      touchX: touchXValue,\n      drawerTranslation,\n      containerWidth,\n    } = state;\n\n    let dragX = dragXValue;\n    let touchX = touchXValue;\n\n    if (drawerPosition !== 'left') {\n      // Most of the code is written in a way to handle left-side drawer. In\n      // order to handle right-side drawer the only thing we need to do is to\n      // reverse events coming from gesture handler in a way they emulate\n      // left-side drawer gestures. E.g. dragX is simply -dragX, and touchX is\n      // calulcated by subtracing real touchX from the width of the container\n      // (such that when touch happens at the right edge the value is simply 0)\n      dragX = Animated.multiply(\n        new Animated.Value(-1),\n        dragXValue\n      ) as Animated.Value; // TODO(TS): (for all \"as\" in this file) make sure we can map this\n      touchX = Animated.add(\n        new Animated.Value(containerWidth),\n        Animated.multiply(new Animated.Value(-1), touchXValue)\n      ) as Animated.Value; // TODO(TS): make sure we can map this;\n      touchXValue.setValue(containerWidth);\n    } else {\n      touchXValue.setValue(0);\n    }\n\n    // While closing the drawer when user starts gesture outside of its area (in greyed\n    // out part of the window), we want the drawer to follow only once finger reaches the\n    // edge of the drawer.\n    // E.g. on the diagram below drawer is illustrate by X signs and the greyed out area by\n    // dots. The touch gesture starts at '*' and moves left, touch path is indicated by\n    // an arrow pointing left\n    // 1) +---------------+ 2) +---------------+ 3) +---------------+ 4) +---------------+\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|......|    |XXXXXXXX|.<-*..|    |XXXXXXXX|<--*..|    |XXXXX|<-----*..|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXXXXX|......|    |XXXXX|.........|\n    //    +---------------+    +---------------+    +---------------+    +---------------+\n    //\n    // For the above to work properly we define animated value that will keep\n    // start position of the gesture. Then we use that value to calculate how\n    // much we need to subtract from the dragX. If the gesture started on the\n    // greyed out area we take the distance from the edge of the drawer to the\n    // start position. Otherwise we don't subtract at all and the drawer be\n    // pulled back as soon as you start the pan.\n    //\n    // This is used only when drawerType is \"front\"\n    //\n    let translationX = dragX;\n    if (drawerType === 'front') {\n      const startPositionX = Animated.add(\n        touchX,\n        Animated.multiply(new Animated.Value(-1), dragX)\n      );\n\n      const dragOffsetFromOnStartPosition = startPositionX.interpolate({\n        inputRange: [drawerWidth! - 1, drawerWidth!, drawerWidth! + 1],\n        outputRange: [0, 0, 1],\n      });\n      translationX = Animated.add(\n        dragX,\n        dragOffsetFromOnStartPosition\n      ) as Animated.Value; // TODO: as above\n    }\n\n    this.openValue = Animated.add(translationX, drawerTranslation).interpolate({\n      inputRange: [0, drawerWidth!],\n      outputRange: [0, 1],\n      extrapolate: 'clamp',\n    });\n\n    const gestureOptions: {\n      useNativeDriver: boolean;\n      // TODO: make sure it is correct\n      listener?: (\n        ev: NativeSyntheticEvent<PanGestureHandlerEventPayload>\n      ) => void;\n    } = {\n      useNativeDriver: props.useNativeAnimations!,\n    };\n\n    if (this.props.onDrawerSlide) {\n      gestureOptions.listener = (ev) => {\n        const translationX = Math.floor(Math.abs(ev.nativeEvent.translationX));\n        const position = translationX / this.state.containerWidth;\n\n        this.props.onDrawerSlide?.(position);\n      };\n    }\n\n    this.onGestureEvent = Animated.event(\n      [{ nativeEvent: { translationX: dragXValue, x: touchXValue } }],\n      gestureOptions\n    );\n  };\n\n  private handleContainerLayout = ({ nativeEvent }: LayoutChangeEvent) => {\n    this.setState({ containerWidth: nativeEvent.layout.width });\n  };\n\n  private emitStateChanged = (\n    newState: DrawerState,\n    drawerWillShow: boolean\n  ) => {\n    this.props.onDrawerStateChanged?.(newState, drawerWillShow);\n  };\n\n  private openingHandlerStateChange = ({\n    nativeEvent,\n  }: HandlerStateChangeEvent<PanGestureHandlerEventPayload>) => {\n    if (nativeEvent.oldState === State.ACTIVE) {\n      this.handleRelease({ nativeEvent });\n    } else if (nativeEvent.state === State.ACTIVE) {\n      this.emitStateChanged(DRAGGING, false);\n      this.setState({ drawerState: DRAGGING });\n      if (this.props.keyboardDismissMode === 'on-drag') {\n        Keyboard.dismiss();\n      }\n      if (this.props.hideStatusBar) {\n        StatusBar.setHidden(true, this.props.statusBarAnimation || 'slide');\n      }\n    }\n  };\n\n  private onTapHandlerStateChange = ({\n    nativeEvent,\n  }: HandlerStateChangeEvent<TapGestureHandlerEventPayload>) => {\n    if (\n      this.drawerShown &&\n      nativeEvent.oldState === State.ACTIVE &&\n      this.props.drawerLockMode !== 'locked-open'\n    ) {\n      this.closeDrawer();\n    }\n  };\n\n  private handleRelease = ({\n    nativeEvent,\n  }: HandlerStateChangeEvent<PanGestureHandlerEventPayload>) => {\n    const { drawerWidth, drawerPosition, drawerType } = this.props;\n    const { containerWidth } = this.state;\n    let { translationX: dragX, velocityX, x: touchX } = nativeEvent;\n\n    if (drawerPosition !== 'left') {\n      // See description in _updateAnimatedEvent about why events are flipped\n      // for right-side drawer\n      dragX = -dragX;\n      touchX = containerWidth - touchX;\n      velocityX = -velocityX;\n    }\n\n    const gestureStartX = touchX - dragX;\n    let dragOffsetBasedOnStart = 0;\n\n    if (drawerType === 'front') {\n      dragOffsetBasedOnStart =\n        gestureStartX > drawerWidth! ? gestureStartX - drawerWidth! : 0;\n    }\n\n    const startOffsetX =\n      dragX + dragOffsetBasedOnStart + (this.drawerShown ? drawerWidth! : 0);\n    const projOffsetX = startOffsetX + DRAG_TOSS * velocityX;\n\n    const shouldOpen = projOffsetX > drawerWidth! / 2;\n\n    if (shouldOpen) {\n      this.animateDrawer(startOffsetX, drawerWidth!, velocityX);\n    } else {\n      this.animateDrawer(startOffsetX, 0, velocityX);\n    }\n  };\n\n  private updateShowing = (showing: boolean) => {\n    this.drawerShown = showing;\n    this.accessibilityIsModalView.current?.setNativeProps({\n      accessibilityViewIsModal: showing,\n    });\n    this.pointerEventsView.current?.setNativeProps({\n      pointerEvents: showing ? 'auto' : 'none',\n    });\n    const { drawerPosition, minSwipeDistance, edgeWidth } = this.props;\n    const fromLeft = drawerPosition === 'left';\n    // gestureOrientation is 1 if the expected gesture is from left to right and\n    // -1 otherwise e.g. when drawer is on the left and is closed we expect left\n    // to right gesture, thus orientation will be 1.\n    const gestureOrientation =\n      (fromLeft ? 1 : -1) * (this.drawerShown ? -1 : 1);\n    // When drawer is closed we want the hitSlop to be horizontally shorter than\n    // the container size by the value of SLOP. This will make it only activate\n    // when gesture happens not further than SLOP away from the edge\n    const hitSlop = fromLeft\n      ? { left: 0, width: showing ? undefined : edgeWidth }\n      : { right: 0, width: showing ? undefined : edgeWidth };\n    // @ts-ignore internal API, maybe could be fixed in handler types\n    this.panGestureHandler.current?.setNativeProps({\n      hitSlop,\n      activeOffsetX: gestureOrientation * minSwipeDistance!,\n    });\n  };\n\n  private animateDrawer = (\n    fromValue: number | null | undefined,\n    toValue: number,\n    velocity: number,\n    speed?: number\n  ) => {\n    this.state.dragX.setValue(0);\n    this.state.touchX.setValue(\n      this.props.drawerPosition === 'left' ? 0 : this.state.containerWidth\n    );\n\n    if (fromValue != null) {\n      let nextFramePosition = fromValue;\n      if (this.props.useNativeAnimations) {\n        // When using native driver, we predict the next position of the\n        // animation because it takes one frame of a roundtrip to pass RELEASE\n        // event from native driver to JS before we can start animating. Without\n        // it, it is more noticable that the frame is dropped.\n        if (fromValue < toValue && velocity > 0) {\n          nextFramePosition = Math.min(fromValue + velocity / 60.0, toValue);\n        } else if (fromValue > toValue && velocity < 0) {\n          nextFramePosition = Math.max(fromValue + velocity / 60.0, toValue);\n        }\n      }\n      this.state.drawerTranslation.setValue(nextFramePosition);\n    }\n\n    const willShow = toValue !== 0;\n    this.updateShowing(willShow);\n    this.emitStateChanged(SETTLING, willShow);\n    this.setState({ drawerState: SETTLING });\n    if (this.props.hideStatusBar) {\n      StatusBar.setHidden(willShow, this.props.statusBarAnimation || 'slide');\n    }\n    Animated.spring(this.state.drawerTranslation, {\n      velocity,\n      bounciness: 0,\n      toValue,\n      useNativeDriver: this.props.useNativeAnimations!,\n      speed: speed ?? undefined,\n    }).start(({ finished }) => {\n      if (finished) {\n        this.emitStateChanged(IDLE, willShow);\n        this.setState({ drawerOpened: willShow });\n        if (this.state.drawerState !== DRAGGING) {\n          // it's possilbe that user started drag while the drawer\n          // was settling, don't override state in this case\n          this.setState({ drawerState: IDLE });\n        }\n        if (willShow) {\n          this.props.onDrawerOpen?.();\n        } else {\n          this.props.onDrawerClose?.();\n        }\n      }\n    });\n  };\n\n  openDrawer = (options: DrawerMovementOption = {}) => {\n    this.animateDrawer(\n      // TODO: decide if it should be null or undefined is the proper value\n      undefined,\n      this.props.drawerWidth!,\n      options.velocity ? options.velocity : 0,\n      options.speed\n    );\n\n    // We need to force the update, otherwise the overlay is not rerendered and\n    // it would not be clickable\n    this.forceUpdate();\n  };\n\n  closeDrawer = (options: DrawerMovementOption = {}) => {\n    // TODO: decide if it should be null or undefined is the proper value\n    this.animateDrawer(\n      undefined,\n      0,\n      options.velocity ? options.velocity : 0,\n      options.speed\n    );\n\n    // We need to force the update, otherwise the overlay is not rerendered and\n    // it would be still clickable\n    this.forceUpdate();\n  };\n\n  private renderOverlay = () => {\n    /* Overlay styles */\n    invariant(this.openValue, 'should be set');\n    let overlayOpacity;\n\n    if (this.state.drawerState !== IDLE) {\n      overlayOpacity = this.openValue;\n    } else {\n      overlayOpacity = this.state.drawerOpened ? 1 : 0;\n    }\n\n    const dynamicOverlayStyles = {\n      opacity: overlayOpacity,\n      backgroundColor: this.props.overlayColor,\n    };\n\n    return (\n      <TapGestureHandler onHandlerStateChange={this.onTapHandlerStateChange}>\n        <Animated.View\n          pointerEvents={this.drawerShown ? 'auto' : 'none'}\n          ref={this.pointerEventsView}\n          style={[styles.overlay, dynamicOverlayStyles]}\n        />\n      </TapGestureHandler>\n    );\n  };\n\n  private renderDrawer = () => {\n    const {\n      drawerBackgroundColor,\n      drawerWidth,\n      drawerPosition,\n      drawerType,\n      drawerContainerStyle,\n      contentContainerStyle,\n    } = this.props;\n\n    const fromLeft = drawerPosition === 'left';\n    const drawerSlide = drawerType !== 'back';\n    const containerSlide = drawerType !== 'front';\n\n    // we rely on row and row-reverse flex directions to position the drawer\n    // properly. Apparently for RTL these are flipped which requires us to use\n    // the opposite setting for the drawer to appear from left or right\n    // according to the drawerPosition prop\n    const reverseContentDirection = I18nManager.isRTL ? fromLeft : !fromLeft;\n\n    const dynamicDrawerStyles = {\n      backgroundColor: drawerBackgroundColor,\n      width: drawerWidth,\n    };\n    const openValue = this.openValue;\n    invariant(openValue, 'should be set');\n\n    let containerStyles;\n    if (containerSlide) {\n      const containerTranslateX = openValue.interpolate({\n        inputRange: [0, 1],\n        outputRange: fromLeft ? [0, drawerWidth!] : [0, -drawerWidth!],\n        extrapolate: 'clamp',\n      });\n      containerStyles = {\n        transform: [{ translateX: containerTranslateX }],\n      };\n    }\n\n    let drawerTranslateX: number | AnimatedInterpolation = 0;\n    if (drawerSlide) {\n      const closedDrawerOffset = fromLeft ? -drawerWidth! : drawerWidth!;\n      if (this.state.drawerState !== IDLE) {\n        drawerTranslateX = openValue.interpolate({\n          inputRange: [0, 1],\n          outputRange: [closedDrawerOffset, 0],\n          extrapolate: 'clamp',\n        });\n      } else {\n        drawerTranslateX = this.state.drawerOpened ? 0 : closedDrawerOffset;\n      }\n    }\n    const drawerStyles: {\n      transform: { translateX: number | AnimatedInterpolation }[];\n      flexDirection: 'row-reverse' | 'row';\n    } = {\n      transform: [{ translateX: drawerTranslateX }],\n      flexDirection: reverseContentDirection ? 'row-reverse' : 'row',\n    };\n\n    return (\n      <Animated.View style={styles.main} onLayout={this.handleContainerLayout}>\n        <Animated.View\n          style={[\n            drawerType === 'front'\n              ? styles.containerOnBack\n              : styles.containerInFront,\n            containerStyles,\n            contentContainerStyle,\n          ]}\n          importantForAccessibility={\n            this.drawerShown ? 'no-hide-descendants' : 'yes'\n          }>\n          {typeof this.props.children === 'function'\n            ? this.props.children(this.openValue)\n            : this.props.children}\n          {this.renderOverlay()}\n        </Animated.View>\n        <Animated.View\n          pointerEvents=\"box-none\"\n          ref={this.accessibilityIsModalView}\n          accessibilityViewIsModal={this.drawerShown}\n          style={[styles.drawerContainer, drawerStyles, drawerContainerStyle]}>\n          <View style={dynamicDrawerStyles}>\n            {this.props.renderNavigationView(this.openValue as Animated.Value)}\n          </View>\n        </Animated.View>\n      </Animated.View>\n    );\n  };\n\n  private setPanGestureRef = (ref: PanGestureHandler) => {\n    // TODO(TS): make sure it is OK taken from\n    // https://github.com/DefinitelyTyped/DefinitelyTyped/issues/31065#issuecomment-596081842\n    (\n      this.panGestureHandler as React.MutableRefObject<PanGestureHandler>\n    ).current = ref;\n    this.props.onGestureRef?.(ref);\n  };\n\n  render() {\n    const { drawerPosition, drawerLockMode, edgeWidth, minSwipeDistance } =\n      this.props;\n\n    const fromLeft = drawerPosition === 'left';\n\n    // gestureOrientation is 1 if the expected gesture is from left to right and\n    // -1 otherwise e.g. when drawer is on the left and is closed we expect left\n    // to right gesture, thus orientation will be 1.\n    const gestureOrientation =\n      (fromLeft ? 1 : -1) * (this.drawerShown ? -1 : 1);\n\n    // When drawer is closed we want the hitSlop to be horizontally shorter than\n    // the container size by the value of SLOP. This will make it only activate\n    // when gesture happens not further than SLOP away from the edge\n    const hitSlop = fromLeft\n      ? { left: 0, width: this.drawerShown ? undefined : edgeWidth }\n      : { right: 0, width: this.drawerShown ? undefined : edgeWidth };\n\n    return (\n      <PanGestureHandler\n        // @ts-ignore could be fixed in handler types\n        userSelect={this.props.userSelect}\n        activeCursor={this.props.activeCursor}\n        ref={this.setPanGestureRef}\n        hitSlop={hitSlop}\n        activeOffsetX={gestureOrientation * minSwipeDistance!}\n        failOffsetY={[-15, 15]}\n        onGestureEvent={this.onGestureEvent}\n        onHandlerStateChange={this.openingHandlerStateChange}\n        enableTrackpadTwoFingerGesture={\n          this.props.enableTrackpadTwoFingerGesture\n        }\n        enabled={\n          drawerLockMode !== 'locked-closed' && drawerLockMode !== 'locked-open'\n        }>\n        {this.renderDrawer()}\n      </PanGestureHandler>\n    );\n  }\n}\n\nconst styles = StyleSheet.create({\n  drawerContainer: {\n    ...StyleSheet.absoluteFillObject,\n    zIndex: 1001,\n    flexDirection: 'row',\n  },\n  containerInFront: {\n    ...StyleSheet.absoluteFillObject,\n    zIndex: 1002,\n  },\n  containerOnBack: {\n    ...StyleSheet.absoluteFillObject,\n  },\n  main: {\n    flex: 1,\n    zIndex: 0,\n    overflow: 'hidden',\n  },\n  overlay: {\n    ...StyleSheet.absoluteFillObject,\n    zIndex: 1000,\n  },\n});\n"]}