{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport RefreshControl from \"react-native-web/dist/exports/RefreshControl\";\nimport StatusBar from \"react-native-web/dist/exports/StatusBar\";\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { Colors, Spacing } from \"../constants\";\nimport Header from \"../components/Header\";\nimport MenuGrid from \"../components/MenuGrid\";\nimport Carousel from \"../components/Carousel\";\nimport DashboardOverview from \"../components/DashboardOverview\";\nimport VideoGallery from \"../components/VideoGallery\";\nimport RecentActivities from \"../components/RecentActivities\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar HomeScreen = function HomeScreen() {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    refreshing = _React$useState2[0],\n    setRefreshing = _React$useState2[1];\n  var onRefresh = React.useCallback(function () {\n    setRefreshing(true);\n    setTimeout(function () {\n      setRefreshing(false);\n    }, 1000);\n  }, []);\n  return _jsxs(SafeAreaView, {\n    style: styles.container,\n    edges: ['top'],\n    children: [_jsx(StatusBar, {\n      barStyle: \"light-content\",\n      backgroundColor: Colors.primary\n    }), _jsx(Header, {}), _jsxs(ScrollView, {\n      style: styles.scrollView,\n      contentContainerStyle: styles.scrollContent,\n      showsVerticalScrollIndicator: false,\n      refreshControl: _jsx(RefreshControl, {\n        refreshing: refreshing,\n        onRefresh: onRefresh,\n        colors: [Colors.primary],\n        tintColor: Colors.primary\n      }),\n      children: [_jsx(View, {\n        style: styles.section,\n        children: _jsx(Carousel, {})\n      }), _jsx(View, {\n        style: styles.section,\n        children: _jsx(DashboardOverview, {})\n      }), _jsx(View, {\n        style: styles.section,\n        children: _jsx(MenuGrid, {})\n      }), _jsx(View, {\n        style: styles.section,\n        children: _jsx(VideoGallery, {})\n      }), _jsx(View, {\n        style: styles.section,\n        children: _jsx(RecentActivities, {})\n      }), _jsx(View, {\n        style: styles.bottomSpacing\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: Colors.background\n  },\n  scrollView: {\n    flex: 1\n  },\n  scrollContent: {\n    paddingBottom: Spacing.xl\n  },\n  section: {\n    marginBottom: Spacing.lg\n  },\n  bottomSpacing: {\n    height: 80\n  }\n});\nexport default HomeScreen;", "map": {"version": 3, "names": ["React", "View", "ScrollView", "StyleSheet", "RefreshControl", "StatusBar", "SafeAreaView", "Colors", "Spacing", "Header", "MenuGrid", "Carousel", "DashboardOverview", "VideoGallery", "RecentActivities", "jsx", "_jsx", "jsxs", "_jsxs", "HomeScreen", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "refreshing", "setRefreshing", "onRefresh", "useCallback", "setTimeout", "style", "styles", "container", "edges", "children", "barStyle", "backgroundColor", "primary", "scrollView", "contentContainerStyle", "scrollContent", "showsVerticalScrollIndicator", "refreshControl", "colors", "tintColor", "section", "bottomSpacing", "create", "flex", "background", "paddingBottom", "xl", "marginBottom", "lg", "height"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/screens/HomeScreen.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  ScrollView,\n  StyleSheet,\n  RefreshControl,\n  StatusBar,\n} from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { Colors, Spacing } from '../constants';\n\n// Import components that we'll create in subsequent tasks\nimport Header from '../components/Header';\nimport MenuGrid from '../components/MenuGrid';\nimport Carousel from '../components/Carousel';\nimport DashboardOverview from '../components/DashboardOverview';\nimport VideoGallery from '../components/VideoGallery';\nimport RecentActivities from '../components/RecentActivities';\n\nconst HomeScreen = () => {\n  const [refreshing, setRefreshing] = React.useState(false);\n\n  const onRefresh = React.useCallback(() => {\n    setRefreshing(true);\n    // Simulate refresh delay\n    setTimeout(() => {\n      setRefreshing(false);\n    }, 1000);\n  }, []);\n\n  return (\n    <SafeAreaView style={styles.container} edges={['top']}>\n      <StatusBar barStyle=\"light-content\" backgroundColor={Colors.primary} />\n      \n      <Header />\n      \n      <ScrollView\n        style={styles.scrollView}\n        contentContainerStyle={styles.scrollContent}\n        showsVerticalScrollIndicator={false}\n        refreshControl={\n          <RefreshControl\n            refreshing={refreshing}\n            onRefresh={onRefresh}\n            colors={[Colors.primary]}\n            tintColor={Colors.primary}\n          />\n        }\n      >\n        {/* Auto-scroll Carousel */}\n        <View style={styles.section}>\n          <Carousel />\n        </View>\n\n        {/* Dashboard Overview */}\n        <View style={styles.section}>\n          <DashboardOverview />\n        </View>\n\n        {/* Menu Grid */}\n        <View style={styles.section}>\n          <MenuGrid />\n        </View>\n\n        {/* Video Gallery */}\n        <View style={styles.section}>\n          <VideoGallery />\n        </View>\n\n        {/* Recent Activities */}\n        <View style={styles.section}>\n          <RecentActivities />\n        </View>\n\n        {/* Bottom spacing for navigation */}\n        <View style={styles.bottomSpacing} />\n      </ScrollView>\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: Colors.background,\n  },\n  scrollView: {\n    flex: 1,\n  },\n  scrollContent: {\n    paddingBottom: Spacing.xl,\n  },\n  section: {\n    marginBottom: Spacing.lg,\n  },\n  bottomSpacing: {\n    height: 80, // Space for bottom navigation\n  },\n});\n\nexport default HomeScreen;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,cAAA;AAAA,OAAAC,SAAA;AAQ1B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,MAAM,EAAEC,OAAO;AAGxB,OAAOC,MAAM;AACb,OAAOC,QAAQ;AACf,OAAOC,QAAQ;AACf,OAAOC,iBAAiB;AACxB,OAAOC,YAAY;AACnB,OAAOC,gBAAgB;AAAuC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9D,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;EACvB,IAAAC,eAAA,GAAoCpB,KAAK,CAACqB,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAlDI,UAAU,GAAAF,gBAAA;IAAEG,aAAa,GAAAH,gBAAA;EAEhC,IAAMI,SAAS,GAAG1B,KAAK,CAAC2B,WAAW,CAAC,YAAM;IACxCF,aAAa,CAAC,IAAI,CAAC;IAEnBG,UAAU,CAAC,YAAM;MACfH,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,OACEP,KAAA,CAACZ,YAAY;IAACuB,KAAK,EAAEC,MAAM,CAACC,SAAU;IAACC,KAAK,EAAE,CAAC,KAAK,CAAE;IAAAC,QAAA,GACpDjB,IAAA,CAACX,SAAS;MAAC6B,QAAQ,EAAC,eAAe;MAACC,eAAe,EAAE5B,MAAM,CAAC6B;IAAQ,CAAE,CAAC,EAEvEpB,IAAA,CAACP,MAAM,IAAE,CAAC,EAEVS,KAAA,CAAChB,UAAU;MACT2B,KAAK,EAAEC,MAAM,CAACO,UAAW;MACzBC,qBAAqB,EAAER,MAAM,CAACS,aAAc;MAC5CC,4BAA4B,EAAE,KAAM;MACpCC,cAAc,EACZzB,IAAA,CAACZ,cAAc;QACboB,UAAU,EAAEA,UAAW;QACvBE,SAAS,EAAEA,SAAU;QACrBgB,MAAM,EAAE,CAACnC,MAAM,CAAC6B,OAAO,CAAE;QACzBO,SAAS,EAAEpC,MAAM,CAAC6B;MAAQ,CAC3B,CACF;MAAAH,QAAA,GAGDjB,IAAA,CAACf,IAAI;QAAC4B,KAAK,EAAEC,MAAM,CAACc,OAAQ;QAAAX,QAAA,EAC1BjB,IAAA,CAACL,QAAQ,IAAE;MAAC,CACR,CAAC,EAGPK,IAAA,CAACf,IAAI;QAAC4B,KAAK,EAAEC,MAAM,CAACc,OAAQ;QAAAX,QAAA,EAC1BjB,IAAA,CAACJ,iBAAiB,IAAE;MAAC,CACjB,CAAC,EAGPI,IAAA,CAACf,IAAI;QAAC4B,KAAK,EAAEC,MAAM,CAACc,OAAQ;QAAAX,QAAA,EAC1BjB,IAAA,CAACN,QAAQ,IAAE;MAAC,CACR,CAAC,EAGPM,IAAA,CAACf,IAAI;QAAC4B,KAAK,EAAEC,MAAM,CAACc,OAAQ;QAAAX,QAAA,EAC1BjB,IAAA,CAACH,YAAY,IAAE;MAAC,CACZ,CAAC,EAGPG,IAAA,CAACf,IAAI;QAAC4B,KAAK,EAAEC,MAAM,CAACc,OAAQ;QAAAX,QAAA,EAC1BjB,IAAA,CAACF,gBAAgB,IAAE;MAAC,CAChB,CAAC,EAGPE,IAAA,CAACf,IAAI;QAAC4B,KAAK,EAAEC,MAAM,CAACe;MAAc,CAAE,CAAC;IAAA,CAC3B,CAAC;EAAA,CACD,CAAC;AAEnB,CAAC;AAED,IAAMf,MAAM,GAAG3B,UAAU,CAAC2C,MAAM,CAAC;EAC/Bf,SAAS,EAAE;IACTgB,IAAI,EAAE,CAAC;IACPZ,eAAe,EAAE5B,MAAM,CAACyC;EAC1B,CAAC;EACDX,UAAU,EAAE;IACVU,IAAI,EAAE;EACR,CAAC;EACDR,aAAa,EAAE;IACbU,aAAa,EAAEzC,OAAO,CAAC0C;EACzB,CAAC;EACDN,OAAO,EAAE;IACPO,YAAY,EAAE3C,OAAO,CAAC4C;EACxB,CAAC;EACDP,aAAa,EAAE;IACbQ,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,eAAelC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}