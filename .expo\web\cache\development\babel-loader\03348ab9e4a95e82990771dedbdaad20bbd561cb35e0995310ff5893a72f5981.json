{"ast": null, "code": "export var Animations = {\n  duration: {\n    fast: 150,\n    normal: 300,\n    slow: 500\n  },\n  easing: {\n    easeInOut: 'ease-in-out',\n    easeIn: 'ease-in',\n    easeOut: 'ease-out',\n    linear: 'linear'\n  },\n  scale: {\n    pressed: 0.95,\n    normal: 1.0,\n    hover: 1.02\n  },\n  opacity: {\n    hidden: 0,\n    visible: 1,\n    disabled: 0.6\n  },\n  spring: {\n    default: {\n      damping: 15,\n      stiffness: 150,\n      mass: 1\n    },\n    gentle: {\n      damping: 20,\n      stiffness: 100,\n      mass: 1\n    },\n    bouncy: {\n      damping: 10,\n      stiffness: 200,\n      mass: 1\n    }\n  },\n  timing: {\n    fast: {\n      duration: 150,\n      useNativeDriver: true\n    },\n    normal: {\n      duration: 300,\n      useNativeDriver: true\n    },\n    slow: {\n      duration: 500,\n      useNativeDriver: true\n    }\n  }\n};", "map": {"version": 3, "names": ["Animations", "duration", "fast", "normal", "slow", "easing", "easeInOut", "easeIn", "easeOut", "linear", "scale", "pressed", "hover", "opacity", "hidden", "visible", "disabled", "spring", "default", "damping", "stiffness", "mass", "gentle", "bouncy", "timing", "useNativeDriver"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/constants/Animations.js"], "sourcesContent": ["// Animation constants for consistent micro-interactions\nexport const Animations = {\n  // Duration constants\n  duration: {\n    fast: 150,\n    normal: 300,\n    slow: 500,\n  },\n  \n  // Easing curves\n  easing: {\n    easeInOut: 'ease-in-out',\n    easeIn: 'ease-in',\n    easeOut: 'ease-out',\n    linear: 'linear',\n  },\n  \n  // Scale animations\n  scale: {\n    pressed: 0.95,\n    normal: 1.0,\n    hover: 1.02,\n  },\n  \n  // Opacity animations\n  opacity: {\n    hidden: 0,\n    visible: 1,\n    disabled: 0.6,\n  },\n  \n  // Spring configurations\n  spring: {\n    default: {\n      damping: 15,\n      stiffness: 150,\n      mass: 1,\n    },\n    gentle: {\n      damping: 20,\n      stiffness: 100,\n      mass: 1,\n    },\n    bouncy: {\n      damping: 10,\n      stiffness: 200,\n      mass: 1,\n    },\n  },\n  \n  // Timing configurations\n  timing: {\n    fast: {\n      duration: 150,\n      useNativeDriver: true,\n    },\n    normal: {\n      duration: 300,\n      useNativeDriver: true,\n    },\n    slow: {\n      duration: 500,\n      useNativeDriver: true,\n    },\n  },\n};\n"], "mappings": "AACA,OAAO,IAAMA,UAAU,GAAG;EAExBC,QAAQ,EAAE;IACRC,IAAI,EAAE,GAAG;IACTC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE;EACR,CAAC;EAGDC,MAAM,EAAE;IACNC,SAAS,EAAE,aAAa;IACxBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,UAAU;IACnBC,MAAM,EAAE;EACV,CAAC;EAGDC,KAAK,EAAE;IACLC,OAAO,EAAE,IAAI;IACbR,MAAM,EAAE,GAAG;IACXS,KAAK,EAAE;EACT,CAAC;EAGDC,OAAO,EAAE;IACPC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE;EACZ,CAAC;EAGDC,MAAM,EAAE;IACNC,OAAO,EAAE;MACPC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE;IACR,CAAC;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE;IACR,CAAC;IACDE,MAAM,EAAE;MACNJ,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE;IACR;EACF,CAAC;EAGDG,MAAM,EAAE;IACNtB,IAAI,EAAE;MACJD,QAAQ,EAAE,GAAG;MACbwB,eAAe,EAAE;IACnB,CAAC;IACDtB,MAAM,EAAE;MACNF,QAAQ,EAAE,GAAG;MACbwB,eAAe,EAAE;IACnB,CAAC;IACDrB,IAAI,EAAE;MACJH,QAAQ,EAAE,GAAG;MACbwB,eAAe,EAAE;IACnB;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}