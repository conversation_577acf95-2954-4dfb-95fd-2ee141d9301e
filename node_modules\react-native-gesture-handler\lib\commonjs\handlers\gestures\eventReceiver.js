"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.onGestureHandlerEvent = onGestureHandlerEvent;
exports.startListening = startListening;
exports.stopListening = stopListening;

var _reactNative = require("react-native");

var _State = require("../../State");

var _TouchEventType = require("../../TouchEventType");

var _handlersRegistry = require("../handlersRegistry");

var _gestureStateManager = require("./gestureStateManager");

let gestureHandlerEventSubscription = null;
let gestureHandlerStateChangeEventSubscription = null;
const gestureStateManagers = new Map();
const lastUpdateEvent = [];

function isStateChangeEvent(event) {
  // @ts-ignore oldState doesn't exist on GestureTouchEvent and that's the point
  return event.oldState != null;
}

function isTouchEvent(event) {
  return event.eventType != null;
}

function onGestureHandlerEvent(event) {
  var _handler$handlers7, _handler$handlers7$on, _handler$handlers8, _handler$handlers8$on, _handler$handlers9, _handler$handlers9$on, _handler$handlers10, _handler$handlers10$o;

  const handler = (0, _handlersRegistry.findHandler)(event.handlerTag);

  if (handler) {
    if (isStateChangeEvent(event)) {
      if (event.oldState === _State.State.UNDETERMINED && event.state === _State.State.BEGAN) {
        var _handler$handlers$onB, _handler$handlers;

        (_handler$handlers$onB = (_handler$handlers = handler.handlers).onBegin) === null || _handler$handlers$onB === void 0 ? void 0 : _handler$handlers$onB.call(_handler$handlers, event);
      } else if ((event.oldState === _State.State.BEGAN || event.oldState === _State.State.UNDETERMINED) && event.state === _State.State.ACTIVE) {
        var _handler$handlers$onS, _handler$handlers2;

        (_handler$handlers$onS = (_handler$handlers2 = handler.handlers).onStart) === null || _handler$handlers$onS === void 0 ? void 0 : _handler$handlers$onS.call(_handler$handlers2, event);
        lastUpdateEvent[handler.handlers.handlerTag] = event;
      } else if (event.oldState !== event.state && event.state === _State.State.END) {
        var _handler$handlers$onF, _handler$handlers4;

        if (event.oldState === _State.State.ACTIVE) {
          var _handler$handlers$onE, _handler$handlers3;

          (_handler$handlers$onE = (_handler$handlers3 = handler.handlers).onEnd) === null || _handler$handlers$onE === void 0 ? void 0 : _handler$handlers$onE.call(_handler$handlers3, event, true);
        }

        (_handler$handlers$onF = (_handler$handlers4 = handler.handlers).onFinalize) === null || _handler$handlers$onF === void 0 ? void 0 : _handler$handlers$onF.call(_handler$handlers4, event, true);
        lastUpdateEvent[handler.handlers.handlerTag] = undefined;
      } else if ((event.state === _State.State.FAILED || event.state === _State.State.CANCELLED) && event.oldState !== event.state) {
        var _handler$handlers$onF2, _handler$handlers6;

        if (event.oldState === _State.State.ACTIVE) {
          var _handler$handlers$onE2, _handler$handlers5;

          (_handler$handlers$onE2 = (_handler$handlers5 = handler.handlers).onEnd) === null || _handler$handlers$onE2 === void 0 ? void 0 : _handler$handlers$onE2.call(_handler$handlers5, event, false);
        }

        (_handler$handlers$onF2 = (_handler$handlers6 = handler.handlers).onFinalize) === null || _handler$handlers$onF2 === void 0 ? void 0 : _handler$handlers$onF2.call(_handler$handlers6, event, false);
        gestureStateManagers.delete(event.handlerTag);
        lastUpdateEvent[handler.handlers.handlerTag] = undefined;
      }
    } else if (isTouchEvent(event)) {
      if (!gestureStateManagers.has(event.handlerTag)) {
        gestureStateManagers.set(event.handlerTag, _gestureStateManager.GestureStateManager.create(event.handlerTag));
      } // eslint-disable-next-line @typescript-eslint/no-non-null-assertion


      const manager = gestureStateManagers.get(event.handlerTag);

      switch (event.eventType) {
        case _TouchEventType.TouchEventType.TOUCHES_DOWN:
          (_handler$handlers7 = handler.handlers) === null || _handler$handlers7 === void 0 ? void 0 : (_handler$handlers7$on = _handler$handlers7.onTouchesDown) === null || _handler$handlers7$on === void 0 ? void 0 : _handler$handlers7$on.call(_handler$handlers7, event, manager);
          break;

        case _TouchEventType.TouchEventType.TOUCHES_MOVE:
          (_handler$handlers8 = handler.handlers) === null || _handler$handlers8 === void 0 ? void 0 : (_handler$handlers8$on = _handler$handlers8.onTouchesMove) === null || _handler$handlers8$on === void 0 ? void 0 : _handler$handlers8$on.call(_handler$handlers8, event, manager);
          break;

        case _TouchEventType.TouchEventType.TOUCHES_UP:
          (_handler$handlers9 = handler.handlers) === null || _handler$handlers9 === void 0 ? void 0 : (_handler$handlers9$on = _handler$handlers9.onTouchesUp) === null || _handler$handlers9$on === void 0 ? void 0 : _handler$handlers9$on.call(_handler$handlers9, event, manager);
          break;

        case _TouchEventType.TouchEventType.TOUCHES_CANCELLED:
          (_handler$handlers10 = handler.handlers) === null || _handler$handlers10 === void 0 ? void 0 : (_handler$handlers10$o = _handler$handlers10.onTouchesCancelled) === null || _handler$handlers10$o === void 0 ? void 0 : _handler$handlers10$o.call(_handler$handlers10, event, manager);
          break;
      }
    } else {
      var _handler$handlers$onU, _handler$handlers11;

      (_handler$handlers$onU = (_handler$handlers11 = handler.handlers).onUpdate) === null || _handler$handlers$onU === void 0 ? void 0 : _handler$handlers$onU.call(_handler$handlers11, event);

      if (handler.handlers.onChange && handler.handlers.changeEventCalculator) {
        var _handler$handlers$onC, _handler$handlers12, _handler$handlers$cha, _handler$handlers13;

        (_handler$handlers$onC = (_handler$handlers12 = handler.handlers).onChange) === null || _handler$handlers$onC === void 0 ? void 0 : _handler$handlers$onC.call(_handler$handlers12, (_handler$handlers$cha = (_handler$handlers13 = handler.handlers).changeEventCalculator) === null || _handler$handlers$cha === void 0 ? void 0 : _handler$handlers$cha.call(_handler$handlers13, event, lastUpdateEvent[handler.handlers.handlerTag]));
        lastUpdateEvent[handler.handlers.handlerTag] = event;
      }
    }
  } else {
    const oldHandler = (0, _handlersRegistry.findOldGestureHandler)(event.handlerTag);

    if (oldHandler) {
      const nativeEvent = {
        nativeEvent: event
      };

      if (isStateChangeEvent(event)) {
        oldHandler.onGestureStateChange(nativeEvent);
      } else {
        oldHandler.onGestureEvent(nativeEvent);
      }

      return;
    }
  }
}

function startListening() {
  stopListening();
  gestureHandlerEventSubscription = _reactNative.DeviceEventEmitter.addListener('onGestureHandlerEvent', onGestureHandlerEvent);
  gestureHandlerStateChangeEventSubscription = _reactNative.DeviceEventEmitter.addListener('onGestureHandlerStateChange', onGestureHandlerEvent);
}

function stopListening() {
  if (gestureHandlerEventSubscription) {
    gestureHandlerEventSubscription.remove();
    gestureHandlerEventSubscription = null;
  }

  if (gestureHandlerStateChangeEventSubscription) {
    gestureHandlerStateChangeEventSubscription.remove();
    gestureHandlerStateChangeEventSubscription = null;
  }
}
//# sourceMappingURL=eventReceiver.js.map