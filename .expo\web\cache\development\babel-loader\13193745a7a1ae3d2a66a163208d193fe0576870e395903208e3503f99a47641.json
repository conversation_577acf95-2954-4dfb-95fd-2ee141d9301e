{"ast": null, "code": "function murmurhash2_32_gc(str, seed) {\n  var l = str.length,\n    h = seed ^ l,\n    i = 0,\n    k;\n  while (l >= 4) {\n    k = str.charCodeAt(i) & 0xff | (str.charCodeAt(++i) & 0xff) << 8 | (str.charCodeAt(++i) & 0xff) << 16 | (str.charCodeAt(++i) & 0xff) << 24;\n    k = (k & 0xffff) * 0x5bd1e995 + (((k >>> 16) * 0x5bd1e995 & 0xffff) << 16);\n    k ^= k >>> 24;\n    k = (k & 0xffff) * 0x5bd1e995 + (((k >>> 16) * 0x5bd1e995 & 0xffff) << 16);\n    h = (h & 0xffff) * 0x5bd1e995 + (((h >>> 16) * 0x5bd1e995 & 0xffff) << 16) ^ k;\n    l -= 4;\n    ++i;\n  }\n  switch (l) {\n    case 3:\n      h ^= (str.charCodeAt(i + 2) & 0xff) << 16;\n    case 2:\n      h ^= (str.charCodeAt(i + 1) & 0xff) << 8;\n    case 1:\n      h ^= str.charCodeAt(i) & 0xff;\n      h = (h & 0xffff) * 0x5bd1e995 + (((h >>> 16) * 0x5bd1e995 & 0xffff) << 16);\n  }\n  h ^= h >>> 13;\n  h = (h & 0xffff) * 0x5bd1e995 + (((h >>> 16) * 0x5bd1e995 & 0xffff) << 16);\n  h ^= h >>> 15;\n  return h >>> 0;\n}\nvar hash = function hash(str) {\n  return murmurhash2_32_gc(str, 1).toString(36);\n};\nexport default hash;", "map": {"version": 3, "names": ["murmurhash2_32_gc", "str", "seed", "l", "length", "h", "i", "k", "charCodeAt", "hash", "toString"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/exports/StyleSheet/compiler/hash.js"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * JS Implementation of MurmurHash2\n *\n * <AUTHOR> href=\"mailto:<EMAIL>\"><PERSON></a>\n * @see http://github.com/garycourt/murmurhash-js\n * <AUTHOR> href=\"mailto:<EMAIL>\"><PERSON> Appleby</a>\n * @see http://sites.google.com/site/murmurhash/\n *\n * @param {string} str ASCII only\n * @param {number} seed Positive integer only\n * @return {number} 32-bit positive integer hash\n *\n * \n */\n\nfunction murmurhash2_32_gc(str, seed) {\n  var l = str.length,\n    h = seed ^ l,\n    i = 0,\n    k;\n  while (l >= 4) {\n    k = str.charCodeAt(i) & 0xff | (str.charCodeAt(++i) & 0xff) << 8 | (str.charCodeAt(++i) & 0xff) << 16 | (str.charCodeAt(++i) & 0xff) << 24;\n    k = (k & 0xffff) * 0x5bd1e995 + (((k >>> 16) * 0x5bd1e995 & 0xffff) << 16);\n    k ^= k >>> 24;\n    k = (k & 0xffff) * 0x5bd1e995 + (((k >>> 16) * 0x5bd1e995 & 0xffff) << 16);\n    h = (h & 0xffff) * 0x5bd1e995 + (((h >>> 16) * 0x5bd1e995 & 0xffff) << 16) ^ k;\n    l -= 4;\n    ++i;\n  }\n  switch (l) {\n    case 3:\n      h ^= (str.charCodeAt(i + 2) & 0xff) << 16;\n    case 2:\n      h ^= (str.charCodeAt(i + 1) & 0xff) << 8;\n    case 1:\n      h ^= str.charCodeAt(i) & 0xff;\n      h = (h & 0xffff) * 0x5bd1e995 + (((h >>> 16) * 0x5bd1e995 & 0xffff) << 16);\n  }\n  h ^= h >>> 13;\n  h = (h & 0xffff) * 0x5bd1e995 + (((h >>> 16) * 0x5bd1e995 & 0xffff) << 16);\n  h ^= h >>> 15;\n  return h >>> 0;\n}\nvar hash = str => murmurhash2_32_gc(str, 1).toString(36);\nexport default hash;"], "mappings": "AAiBA,SAASA,iBAAiBA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACpC,IAAIC,CAAC,GAAGF,GAAG,CAACG,MAAM;IAChBC,CAAC,GAAGH,IAAI,GAAGC,CAAC;IACZG,CAAC,GAAG,CAAC;IACLC,CAAC;EACH,OAAOJ,CAAC,IAAI,CAAC,EAAE;IACbI,CAAC,GAAGN,GAAG,CAACO,UAAU,CAACF,CAAC,CAAC,GAAG,IAAI,GAAG,CAACL,GAAG,CAACO,UAAU,CAAC,EAAEF,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAACL,GAAG,CAACO,UAAU,CAAC,EAAEF,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,GAAG,CAACL,GAAG,CAACO,UAAU,CAAC,EAAEF,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE;IAC1IC,CAAC,GAAG,CAACA,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,CAAC,CAACA,CAAC,KAAK,EAAE,IAAI,UAAU,GAAG,MAAM,KAAK,EAAE,CAAC;IAC1EA,CAAC,IAAIA,CAAC,KAAK,EAAE;IACbA,CAAC,GAAG,CAACA,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,CAAC,CAACA,CAAC,KAAK,EAAE,IAAI,UAAU,GAAG,MAAM,KAAK,EAAE,CAAC;IAC1EF,CAAC,GAAG,CAACA,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,CAAC,CAACA,CAAC,KAAK,EAAE,IAAI,UAAU,GAAG,MAAM,KAAK,EAAE,CAAC,GAAGE,CAAC;IAC9EJ,CAAC,IAAI,CAAC;IACN,EAAEG,CAAC;EACL;EACA,QAAQH,CAAC;IACP,KAAK,CAAC;MACJE,CAAC,IAAI,CAACJ,GAAG,CAACO,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE;IAC3C,KAAK,CAAC;MACJD,CAAC,IAAI,CAACJ,GAAG,CAACO,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;IAC1C,KAAK,CAAC;MACJD,CAAC,IAAIJ,GAAG,CAACO,UAAU,CAACF,CAAC,CAAC,GAAG,IAAI;MAC7BD,CAAC,GAAG,CAACA,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,CAAC,CAACA,CAAC,KAAK,EAAE,IAAI,UAAU,GAAG,MAAM,KAAK,EAAE,CAAC;EAC9E;EACAA,CAAC,IAAIA,CAAC,KAAK,EAAE;EACbA,CAAC,GAAG,CAACA,CAAC,GAAG,MAAM,IAAI,UAAU,IAAI,CAAC,CAACA,CAAC,KAAK,EAAE,IAAI,UAAU,GAAG,MAAM,KAAK,EAAE,CAAC;EAC1EA,CAAC,IAAIA,CAAC,KAAK,EAAE;EACb,OAAOA,CAAC,KAAK,CAAC;AAChB;AACA,IAAII,IAAI,GAAG,SAAPA,IAAIA,CAAGR,GAAG;EAAA,OAAID,iBAAiB,CAACC,GAAG,EAAE,CAAC,CAAC,CAACS,QAAQ,CAAC,EAAE,CAAC;AAAA;AACxD,eAAeD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}