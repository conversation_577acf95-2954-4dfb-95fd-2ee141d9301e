{"version": 3, "sources": ["gestureObjects.ts"], "names": ["GestureObjects", "Tap", "TapGesture", "Pan", "PanGesture", "Pinch", "PinchGesture", "Rotation", "RotationGesture", "Fling", "FlingGesture", "Long<PERSON>ress", "LongPressGesture", "ForceTouch", "ForceTouchGesture", "Native", "NativeGesture", "Manual", "ManualGesture", "Hover", "HoverGesture", "Race", "gestures", "ComposedGesture", "Simultaneous", "SimultaneousGesture", "Exclusive", "ExclusiveGesture"], "mappings": ";;;;;;;AAAA;;AACA;;AAEA;;AAKA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEO,MAAMA,cAAc,GAAG;AAC5BC,EAAAA,GAAG,EAAE,MAAM;AACT,WAAO,IAAIC,sBAAJ,EAAP;AACD,GAH2B;AAK5BC,EAAAA,GAAG,EAAE,MAAM;AACT,WAAO,IAAIC,sBAAJ,EAAP;AACD,GAP2B;AAS5BC,EAAAA,KAAK,EAAE,MAAM;AACX,WAAO,IAAIC,0BAAJ,EAAP;AACD,GAX2B;AAa5BC,EAAAA,QAAQ,EAAE,MAAM;AACd,WAAO,IAAIC,gCAAJ,EAAP;AACD,GAf2B;AAiB5BC,EAAAA,KAAK,EAAE,MAAM;AACX,WAAO,IAAIC,0BAAJ,EAAP;AACD,GAnB2B;AAqB5BC,EAAAA,SAAS,EAAE,MAAM;AACf,WAAO,IAAIC,kCAAJ,EAAP;AACD,GAvB2B;AAyB5BC,EAAAA,UAAU,EAAE,MAAM;AAChB,WAAO,IAAIC,oCAAJ,EAAP;AACD,GA3B2B;AA6B5BC,EAAAA,MAAM,EAAE,MAAM;AACZ,WAAO,IAAIC,4BAAJ,EAAP;AACD,GA/B2B;AAiC5BC,EAAAA,MAAM,EAAE,MAAM;AACZ,WAAO,IAAIC,4BAAJ,EAAP;AACD,GAnC2B;AAqC5BC,EAAAA,KAAK,EAAE,MAAM;AACX,WAAO,IAAIC,0BAAJ,EAAP;AACD,GAvC2B;;AAyC5B;AACF;AACA;AACA;AACEC,EAAAA,IAAI,EAAE,CAAC,GAAGC,QAAJ,KAA4B;AAChC,WAAO,IAAIC,mCAAJ,CAAoB,GAAGD,QAAvB,CAAP;AACD,GA/C2B;;AAiD5B;AACF;AACA;AACEE,EAAAA,YAAY,CAAC,GAAGF,QAAJ,EAAyB;AACnC,WAAO,IAAIG,uCAAJ,CAAwB,GAAGH,QAA3B,CAAP;AACD,GAtD2B;;AAwD5B;AACF;AACA;AACA;AACA;AACA;AACA;AACEI,EAAAA,SAAS,CAAC,GAAGJ,QAAJ,EAAyB;AAChC,WAAO,IAAIK,oCAAJ,CAAqB,GAAGL,QAAxB,CAAP;AACD;;AAjE2B,CAAvB", "sourcesContent": ["import { FlingGesture } from './flingGesture';\nimport { ForceTouchGesture } from './forceTouchGesture';\nimport { Gesture } from './gesture';\nimport {\n  ComposedGesture,\n  ExclusiveGesture,\n  SimultaneousGesture,\n} from './gestureComposition';\nimport { LongPressGesture } from './longPressGesture';\nimport { PanGesture } from './panGesture';\nimport { PinchGesture } from './pinchGesture';\nimport { RotationGesture } from './rotationGesture';\nimport { TapGesture } from './tapGesture';\nimport { NativeGesture } from './nativeGesture';\nimport { ManualGesture } from './manualGesture';\nimport { HoverGesture } from './hoverGesture';\n\nexport const GestureObjects = {\n  Tap: () => {\n    return new TapGesture();\n  },\n\n  Pan: () => {\n    return new PanGesture();\n  },\n\n  Pinch: () => {\n    return new PinchGesture();\n  },\n\n  Rotation: () => {\n    return new RotationGesture();\n  },\n\n  Fling: () => {\n    return new FlingGesture();\n  },\n\n  LongPress: () => {\n    return new LongPressGesture();\n  },\n\n  ForceTouch: () => {\n    return new ForceTouchGesture();\n  },\n\n  Native: () => {\n    return new NativeGesture();\n  },\n\n  Manual: () => {\n    return new ManualGesture();\n  },\n\n  Hover: () => {\n    return new HoverGesture();\n  },\n\n  /**\n   * Builds a composed gesture consisting of gestures provided as parameters.\n   * The first one that becomes active cancels the rest of gestures.\n   */\n  Race: (...gestures: Gesture[]) => {\n    return new ComposedGesture(...gestures);\n  },\n\n  /**\n   * Builds a composed gesture that allows all base gestures to run simultaneously.\n   */\n  Simultaneous(...gestures: Gesture[]) {\n    return new SimultaneousGesture(...gestures);\n  },\n\n  /**\n   * Builds a composed gesture where only one of the provided gestures can become active.\n   * Priority is decided through the order of gestures: the first one has higher priority\n   * than the second one, second one has higher priority than the third one, and so on.\n   * For example, to make a gesture that recognizes both single and double tap you need\n   * to call Exclusive(doubleTap, singleTap).\n   */\n  Exclusive(...gestures: Gesture[]) {\n    return new ExclusiveGesture(...gestures);\n  },\n};\n"]}