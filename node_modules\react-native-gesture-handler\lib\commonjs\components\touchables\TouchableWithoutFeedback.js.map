{"version": 3, "sources": ["TouchableWithoutFeedback.tsx"], "names": ["TouchableWithoutFeedback", "React", "forwardRef", "props", "ref", "defaultProps", "GenericTouchable"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;;;;;AAIA,MAAMA,wBAAwB,gBAAGC,KAAK,CAACC,UAAN,CAG/B,CAACC,KAAD,EAAQC,GAAR,kBAAgB,oBAAC,yBAAD;AAAkB,EAAA,GAAG,EAAEA;AAAvB,GAAgCD,KAAhC,EAHe,CAAjC;AAKAH,wBAAwB,CAACK,YAAzB,GAAwCC,0BAAiBD,YAAzD;eAEeL,wB", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport GenericTouchable, { GenericTouchableProps } from './GenericTouchable';\n\nexport type TouchableWithoutFeedbackProps = GenericTouchableProps;\n\nconst TouchableWithoutFeedback = React.forwardRef<\n  GenericTouchable,\n  PropsWithChildren<TouchableWithoutFeedbackProps>\n>((props, ref) => <GenericTouchable ref={ref} {...props} />);\n\nTouchableWithoutFeedback.defaultProps = GenericTouchable.defaultProps;\n\nexport default TouchableWithoutFeedback;\n"]}