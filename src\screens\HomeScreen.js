import React from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors, Spacing } from '../constants';
import { useBottomNavigation } from '../hooks/useBottomNavigation';

// Import components that we'll create in subsequent tasks
import Header from '../components/Header';
import MenuGrid from '../components/MenuGrid';
import Carousel from '../components/Carousel';
import DashboardOverview from '../components/DashboardOverview';
import VideoGallery from '../components/VideoGallery';
import RecentActivities from '../components/RecentActivities';
import ScrollTestContent from '../components/ScrollTestContent';

const HomeScreen = () => {
  const [refreshing, setRefreshing] = React.useState(false);
  const { getContentPadding } = useBottomNavigation();

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  // Calculate bottom padding to account for fixed navigation
  const contentBottomPadding = getContentPadding(Spacing.lg);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary} />
      
      <Header />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: contentBottomPadding }
        ]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.primary]}
            tintColor={Colors.primary}
          />
        }
      >
        {/* Auto-scroll Carousel */}
        <View style={styles.section}>
          <Carousel />
        </View>

        {/* Dashboard Overview */}
        <View style={styles.section}>
          <DashboardOverview />
        </View>

        {/* Menu Grid */}
        <View style={styles.section}>
          <MenuGrid />
        </View>

        {/* Video Gallery */}
        <View style={styles.section}>
          <VideoGallery />
        </View>

        {/* Recent Activities */}
        <View style={styles.section}>
          <RecentActivities />
        </View>

        {/* Test Content for Fixed Navigation */}
        <View style={styles.section}>
          <ScrollTestContent />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    // paddingBottom is now dynamically calculated and applied inline
  },
  section: {
    marginBottom: Spacing.lg,
  },
});

export default HomeScreen;
