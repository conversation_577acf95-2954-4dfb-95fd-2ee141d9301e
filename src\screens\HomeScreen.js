import React from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors, Spacing } from '../constants';

// Import components that we'll create in subsequent tasks
import Header from '../components/Header';
import MenuGrid from '../components/MenuGrid';
import Carousel from '../components/Carousel';
import DashboardOverview from '../components/DashboardOverview';
import VideoGallery from '../components/VideoGallery';
import RecentActivities from '../components/RecentActivities';

const HomeScreen = () => {
  const [refreshing, setRefreshing] = React.useState(false);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary} />
      
      <Header />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.primary]}
            tintColor={Colors.primary}
          />
        }
      >
        {/* Auto-scroll Carousel */}
        <View style={styles.section}>
          <Carousel />
        </View>

        {/* Dashboard Overview */}
        <View style={styles.section}>
          <DashboardOverview />
        </View>

        {/* Menu Grid */}
        <View style={styles.section}>
          <MenuGrid />
        </View>

        {/* Video Gallery */}
        <View style={styles.section}>
          <VideoGallery />
        </View>

        {/* Recent Activities */}
        <View style={styles.section}>
          <RecentActivities />
        </View>

        {/* Bottom spacing for navigation */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: Spacing.xl,
  },
  section: {
    marginBottom: Spacing.lg,
  },
  bottomSpacing: {
    height: 80, // Space for bottom navigation
  },
});

export default HomeScreen;
