{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _createForOfIteratorHelperLoose from \"@babel/runtime/helpers/createForOfIteratorHelperLoose\";\nimport invariant from 'fbjs/lib/invariant';\nvar ChildListCollection = function () {\n  function ChildListCollection() {\n    _classCallCheck(this, ChildListCollection);\n    this._cellKeyToChildren = new Map();\n    this._childrenToCellKey = new Map();\n  }\n  return _createClass(ChildListCollection, [{\n    key: \"add\",\n    value: function add(list, cellKey) {\n      var _this$_cellKeyToChild;\n      invariant(!this._childrenToCellKey.has(list), 'Trying to add already present child list');\n      var cellLists = (_this$_cellKeyToChild = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild !== void 0 ? _this$_cellKeyToChild : new Set();\n      cellLists.add(list);\n      this._cellKeyToChildren.set(cellKey, cellLists);\n      this._childrenToCellKey.set(list, cellKey);\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(list) {\n      var cellKey = this._childrenToCellKey.get(list);\n      invariant(cellKey != null, 'Trying to remove non-present child list');\n      this._childrenToCellKey.delete(list);\n      var cellLists = this._cellKeyToChildren.get(cellKey);\n      invariant(cellLists, '_cellKeyToChildren should contain cellKey');\n      cellLists.delete(list);\n      if (cellLists.size === 0) {\n        this._cellKeyToChildren.delete(cellKey);\n      }\n    }\n  }, {\n    key: \"forEach\",\n    value: function forEach(fn) {\n      for (var _iterator = _createForOfIteratorHelperLoose(this._cellKeyToChildren.values()), _step; !(_step = _iterator()).done;) {\n        var listSet = _step.value;\n        for (var _iterator2 = _createForOfIteratorHelperLoose(listSet), _step2; !(_step2 = _iterator2()).done;) {\n          var list = _step2.value;\n          fn(list);\n        }\n      }\n    }\n  }, {\n    key: \"forEachInCell\",\n    value: function forEachInCell(cellKey, fn) {\n      var _this$_cellKeyToChild2;\n      var listSet = (_this$_cellKeyToChild2 = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild2 !== void 0 ? _this$_cellKeyToChild2 : [];\n      for (var _iterator3 = _createForOfIteratorHelperLoose(listSet), _step3; !(_step3 = _iterator3()).done;) {\n        var list = _step3.value;\n        fn(list);\n      }\n    }\n  }, {\n    key: \"anyInCell\",\n    value: function anyInCell(cellKey, fn) {\n      var _this$_cellKeyToChild3;\n      var listSet = (_this$_cellKeyToChild3 = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild3 !== void 0 ? _this$_cellKeyToChild3 : [];\n      for (var _iterator4 = _createForOfIteratorHelperLoose(listSet), _step4; !(_step4 = _iterator4()).done;) {\n        var list = _step4.value;\n        if (fn(list)) {\n          return true;\n        }\n      }\n      return false;\n    }\n  }, {\n    key: \"size\",\n    value: function size() {\n      return this._childrenToCellKey.size;\n    }\n  }]);\n}();\nexport { ChildListCollection as default };", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "invariant", "ChildListCollection", "_classCallCheck", "_cellKeyToChildren", "Map", "_children<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "key", "value", "add", "list", "cellKey", "_this$_cellKeyToChild", "has", "cellLists", "get", "Set", "set", "remove", "delete", "size", "for<PERSON>ach", "fn", "_iterator", "values", "_step", "done", "listSet", "_iterator2", "_step2", "forEachInCell", "_this$_cellKeyToChild2", "_iterator3", "_step3", "anyInCell", "_this$_cellKeyToChild3", "_iterator4", "_step4", "default"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/vendor/react-native/VirtualizedList/ChildListCollection.js"], "sourcesContent": ["import _createForOfIteratorHelperLoose from \"@babel/runtime/helpers/createForOfIteratorHelperLoose\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport invariant from 'fbjs/lib/invariant';\nexport default class ChildListCollection {\n  constructor() {\n    this._cellKeyToChildren = new Map();\n    this._childrenToCellKey = new Map();\n  }\n  add(list, cellKey) {\n    var _this$_cellKeyToChild;\n    invariant(!this._childrenToCellKey.has(list), 'Trying to add already present child list');\n    var cellLists = (_this$_cellKeyToChild = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild !== void 0 ? _this$_cellKeyToChild : new Set();\n    cellLists.add(list);\n    this._cellKeyToChildren.set(cellKey, cellLists);\n    this._childrenToCellKey.set(list, cellKey);\n  }\n  remove(list) {\n    var cellKey = this._childrenToCellKey.get(list);\n    invariant(cellKey != null, 'Trying to remove non-present child list');\n    this._childrenToCellKey.delete(list);\n    var cellLists = this._cellKeyToChildren.get(cellKey);\n    invariant(cellLists, '_cellKeyToChildren should contain cellKey');\n    cellLists.delete(list);\n    if (cellLists.size === 0) {\n      this._cellKeyToChildren.delete(cellKey);\n    }\n  }\n  forEach(fn) {\n    for (var _iterator = _createForOfIteratorHelperLoose(this._cellKeyToChildren.values()), _step; !(_step = _iterator()).done;) {\n      var listSet = _step.value;\n      for (var _iterator2 = _createForOfIteratorHelperLoose(listSet), _step2; !(_step2 = _iterator2()).done;) {\n        var list = _step2.value;\n        fn(list);\n      }\n    }\n  }\n  forEachInCell(cellKey, fn) {\n    var _this$_cellKeyToChild2;\n    var listSet = (_this$_cellKeyToChild2 = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild2 !== void 0 ? _this$_cellKeyToChild2 : [];\n    for (var _iterator3 = _createForOfIteratorHelperLoose(listSet), _step3; !(_step3 = _iterator3()).done;) {\n      var list = _step3.value;\n      fn(list);\n    }\n  }\n  anyInCell(cellKey, fn) {\n    var _this$_cellKeyToChild3;\n    var listSet = (_this$_cellKeyToChild3 = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild3 !== void 0 ? _this$_cellKeyToChild3 : [];\n    for (var _iterator4 = _createForOfIteratorHelperLoose(listSet), _step4; !(_step4 = _iterator4()).done;) {\n      var list = _step4.value;\n      if (fn(list)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  size() {\n    return this._childrenToCellKey.size;\n  }\n}"], "mappings": ";;AAAA,OAAOA,+BAA+B,MAAM,uDAAuD;AAWnG,OAAOC,SAAS,MAAM,oBAAoB;AAAC,IACtBC,mBAAmB;EACtC,SAAAA,oBAAA,EAAc;IAAAC,eAAA,OAAAD,mBAAA;IACZ,IAAI,CAACE,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnC,IAAI,CAACC,kBAAkB,GAAG,IAAID,GAAG,CAAC,CAAC;EACrC;EAAC,OAAAE,YAAA,CAAAL,mBAAA;IAAAM,GAAA;IAAAC,KAAA,EACD,SAAAC,GAAGA,CAACC,IAAI,EAAEC,OAAO,EAAE;MACjB,IAAIC,qBAAqB;MACzBZ,SAAS,CAAC,CAAC,IAAI,CAACK,kBAAkB,CAACQ,GAAG,CAACH,IAAI,CAAC,EAAE,0CAA0C,CAAC;MACzF,IAAII,SAAS,GAAG,CAACF,qBAAqB,GAAG,IAAI,CAACT,kBAAkB,CAACY,GAAG,CAACJ,OAAO,CAAC,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,IAAII,GAAG,CAAC,CAAC;MAC/JF,SAAS,CAACL,GAAG,CAACC,IAAI,CAAC;MACnB,IAAI,CAACP,kBAAkB,CAACc,GAAG,CAACN,OAAO,EAAEG,SAAS,CAAC;MAC/C,IAAI,CAACT,kBAAkB,CAACY,GAAG,CAACP,IAAI,EAAEC,OAAO,CAAC;IAC5C;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAU,MAAMA,CAACR,IAAI,EAAE;MACX,IAAIC,OAAO,GAAG,IAAI,CAACN,kBAAkB,CAACU,GAAG,CAACL,IAAI,CAAC;MAC/CV,SAAS,CAACW,OAAO,IAAI,IAAI,EAAE,yCAAyC,CAAC;MACrE,IAAI,CAACN,kBAAkB,CAACc,MAAM,CAACT,IAAI,CAAC;MACpC,IAAII,SAAS,GAAG,IAAI,CAACX,kBAAkB,CAACY,GAAG,CAACJ,OAAO,CAAC;MACpDX,SAAS,CAACc,SAAS,EAAE,2CAA2C,CAAC;MACjEA,SAAS,CAACK,MAAM,CAACT,IAAI,CAAC;MACtB,IAAII,SAAS,CAACM,IAAI,KAAK,CAAC,EAAE;QACxB,IAAI,CAACjB,kBAAkB,CAACgB,MAAM,CAACR,OAAO,CAAC;MACzC;IACF;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAa,OAAOA,CAACC,EAAE,EAAE;MACV,KAAK,IAAIC,SAAS,GAAGxB,+BAA+B,CAAC,IAAI,CAACI,kBAAkB,CAACqB,MAAM,CAAC,CAAC,CAAC,EAAEC,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGF,SAAS,CAAC,CAAC,EAAEG,IAAI,GAAG;QAC3H,IAAIC,OAAO,GAAGF,KAAK,CAACjB,KAAK;QACzB,KAAK,IAAIoB,UAAU,GAAG7B,+BAA+B,CAAC4B,OAAO,CAAC,EAAEE,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGD,UAAU,CAAC,CAAC,EAAEF,IAAI,GAAG;UACtG,IAAIhB,IAAI,GAAGmB,MAAM,CAACrB,KAAK;UACvBc,EAAE,CAACZ,IAAI,CAAC;QACV;MACF;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAsB,aAAaA,CAACnB,OAAO,EAAEW,EAAE,EAAE;MACzB,IAAIS,sBAAsB;MAC1B,IAAIJ,OAAO,GAAG,CAACI,sBAAsB,GAAG,IAAI,CAAC5B,kBAAkB,CAACY,GAAG,CAACJ,OAAO,CAAC,MAAM,IAAI,IAAIoB,sBAAsB,KAAK,KAAK,CAAC,GAAGA,sBAAsB,GAAG,EAAE;MACzJ,KAAK,IAAIC,UAAU,GAAGjC,+BAA+B,CAAC4B,OAAO,CAAC,EAAEM,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGD,UAAU,CAAC,CAAC,EAAEN,IAAI,GAAG;QACtG,IAAIhB,IAAI,GAAGuB,MAAM,CAACzB,KAAK;QACvBc,EAAE,CAACZ,IAAI,CAAC;MACV;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAA0B,SAASA,CAACvB,OAAO,EAAEW,EAAE,EAAE;MACrB,IAAIa,sBAAsB;MAC1B,IAAIR,OAAO,GAAG,CAACQ,sBAAsB,GAAG,IAAI,CAAChC,kBAAkB,CAACY,GAAG,CAACJ,OAAO,CAAC,MAAM,IAAI,IAAIwB,sBAAsB,KAAK,KAAK,CAAC,GAAGA,sBAAsB,GAAG,EAAE;MACzJ,KAAK,IAAIC,UAAU,GAAGrC,+BAA+B,CAAC4B,OAAO,CAAC,EAAEU,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGD,UAAU,CAAC,CAAC,EAAEV,IAAI,GAAG;QACtG,IAAIhB,IAAI,GAAG2B,MAAM,CAAC7B,KAAK;QACvB,IAAIc,EAAE,CAACZ,IAAI,CAAC,EAAE;UACZ,OAAO,IAAI;QACb;MACF;MACA,OAAO,KAAK;IACd;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAY,IAAIA,CAAA,EAAG;MACL,OAAO,IAAI,CAACf,kBAAkB,CAACe,IAAI;IACrC;EAAC;AAAA;AAAA,SAtDkBnB,mBAAmB,IAAAqC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}