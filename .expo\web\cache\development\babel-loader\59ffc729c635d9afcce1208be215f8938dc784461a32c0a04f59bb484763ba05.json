{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Colors, Typography, Spacing, Animations } from \"../constants\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar navigationItems = [{\n  id: 1,\n  name: 'Home',\n  icon: 'home',\n  label: 'Beranda',\n  badge: 0\n}, {\n  id: 2,\n  name: 'Activities',\n  icon: 'assignment',\n  label: 'Aktivitas',\n  badge: 3\n}, {\n  id: 3,\n  name: 'FAB',\n  icon: 'add',\n  label: '',\n  badge: 0\n}, {\n  id: 4,\n  name: 'Notifications',\n  icon: 'notifications',\n  label: 'Notifikasi',\n  badge: 5\n}, {\n  id: 5,\n  name: 'Profile',\n  icon: 'person',\n  label: 'Profil',\n  badge: 0\n}];\nvar BottomNavigation = function BottomNavigation() {\n  var _React$useState = React.useState('Home'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeTab = _React$useState2[0],\n    setActiveTab = _React$useState2[1];\n  var fabScale = React.useRef(new Animated.Value(1)).current;\n  var tabAnimations = React.useRef(navigationItems.reduce(function (acc, item) {\n    acc[item.name] = new Animated.Value(item.name === 'Home' ? 1 : 0.8);\n    return acc;\n  }, {})).current;\n  var handleTabPress = function handleTabPress(item) {\n    if (item.name === 'FAB') {\n      handleFABPress();\n      return;\n    }\n    setActiveTab(item.name);\n    Object.keys(tabAnimations).forEach(function (key) {\n      Animated.spring(tabAnimations[key], _objectSpread(_objectSpread({\n        toValue: key === item.name ? 1 : 0.8\n      }, Animations.spring.default), {}, {\n        useNativeDriver: true\n      })).start();\n    });\n    console.log(`Navigate to ${item.name}`);\n  };\n  var handleFABPress = function handleFABPress() {\n    Animated.sequence([Animated.spring(fabScale, _objectSpread(_objectSpread({\n      toValue: 0.9\n    }, Animations.spring.default), {}, {\n      useNativeDriver: true\n    })), Animated.spring(fabScale, _objectSpread(_objectSpread({\n      toValue: 1\n    }, Animations.spring.bouncy), {}, {\n      useNativeDriver: true\n    }))]).start();\n    Alert.alert('Quick Actions', 'Pilih aksi cepat yang ingin dilakukan', [{\n      text: 'Check-in/out',\n      onPress: function onPress() {\n        return console.log('Quick check-in');\n      }\n    }, {\n      text: 'Laporan Cepat',\n      onPress: function onPress() {\n        return console.log('Quick report');\n      }\n    }, {\n      text: 'Emergency',\n      onPress: function onPress() {\n        return console.log('Emergency action');\n      },\n      style: 'destructive'\n    }, {\n      text: 'Batal',\n      style: 'cancel'\n    }]);\n  };\n  var renderTabItem = function renderTabItem(item) {\n    if (item.name === 'FAB') {\n      return _jsx(View, {\n        style: styles.fabContainer,\n        children: _jsx(Animated.View, {\n          style: {\n            transform: [{\n              scale: fabScale\n            }]\n          },\n          children: _jsx(TouchableOpacity, {\n            style: styles.fab,\n            onPress: function onPress() {\n              return handleTabPress(item);\n            },\n            activeOpacity: 0.8,\n            children: _jsx(MaterialIcons, {\n              name: item.icon,\n              size: Spacing.iconSize.lg,\n              color: Colors.onPrimary\n            })\n          })\n        })\n      }, item.id);\n    }\n    var isActive = activeTab === item.name;\n    return _jsx(Animated.View, {\n      style: [styles.tabItem, {\n        transform: [{\n          scale: tabAnimations[item.name]\n        }]\n      }],\n      children: _jsxs(TouchableOpacity, {\n        style: styles.tabButton,\n        onPress: function onPress() {\n          return handleTabPress(item);\n        },\n        activeOpacity: 0.7,\n        children: [_jsxs(View, {\n          style: styles.iconContainer,\n          children: [_jsx(MaterialIcons, {\n            name: item.icon,\n            size: Spacing.iconSize.md,\n            color: isActive ? Colors.bottomNavActive : Colors.bottomNavInactive\n          }), item.badge > 0 && _jsx(View, {\n            style: styles.badge,\n            children: _jsx(Text, {\n              style: styles.badgeText,\n              children: item.badge > 9 ? '9+' : item.badge\n            })\n          })]\n        }), _jsx(Text, {\n          style: [styles.tabLabel, {\n            color: isActive ? Colors.bottomNavActive : Colors.bottomNavInactive\n          }],\n          children: item.label\n        })]\n      })\n    }, item.id);\n  };\n  return _jsx(View, {\n    style: styles.container,\n    children: _jsx(View, {\n      style: styles.navigationBar,\n      children: navigationItems.map(renderTabItem)\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0\n  },\n  navigationBar: {\n    flexDirection: 'row',\n    backgroundColor: Colors.bottomNavBackground,\n    paddingTop: Spacing.padding.sm,\n    paddingBottom: Spacing.padding.md,\n    paddingHorizontal: Spacing.padding.sm,\n    borderTopWidth: 1,\n    borderTopColor: Colors.outline,\n    shadowColor: Colors.shadow,\n    shadowOffset: {\n      width: 0,\n      height: -2\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 8\n  },\n  tabItem: {\n    flex: 1,\n    alignItems: 'center'\n  },\n  tabButton: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingVertical: Spacing.padding.xs\n  },\n  iconContainer: {\n    position: 'relative',\n    marginBottom: Spacing.xs / 2\n  },\n  tabLabel: _objectSpread(_objectSpread({}, Typography.caption), {}, {\n    fontSize: 10,\n    textAlign: 'center'\n  }),\n  fabContainer: {\n    flex: 1,\n    alignItems: 'center',\n    marginTop: -20\n  },\n  fab: {\n    width: 56,\n    height: 56,\n    borderRadius: 28,\n    backgroundColor: Colors.primary,\n    alignItems: 'center',\n    justifyContent: 'center',\n    shadowColor: Colors.shadow,\n    shadowOffset: {\n      width: 0,\n      height: 4\n    },\n    shadowOpacity: 0.3,\n    shadowRadius: 8,\n    elevation: 8\n  },\n  badge: {\n    position: 'absolute',\n    top: -4,\n    right: -4,\n    backgroundColor: Colors.error,\n    borderRadius: 8,\n    minWidth: 16,\n    height: 16,\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderWidth: 2,\n    borderColor: Colors.bottomNavBackground\n  },\n  badgeText: _objectSpread(_objectSpread({}, Typography.caption), {}, {\n    color: Colors.onPrimary,\n    fontSize: 9,\n    fontWeight: '600'\n  })\n});\nexport default BottomNavigation;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "TouchableOpacity", "Animated", "<PERSON><PERSON>", "MaterialIcons", "Colors", "Typography", "Spacing", "Animations", "jsx", "_jsx", "jsxs", "_jsxs", "navigationItems", "id", "name", "icon", "label", "badge", "BottomNavigation", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "activeTab", "setActiveTab", "fabScale", "useRef", "Value", "current", "tabAnimations", "reduce", "acc", "item", "handleTabPress", "handleFABPress", "Object", "keys", "for<PERSON>ach", "key", "spring", "_objectSpread", "toValue", "default", "useNativeDriver", "start", "console", "log", "sequence", "bouncy", "alert", "text", "onPress", "style", "renderTabItem", "styles", "fabContainer", "children", "transform", "scale", "fab", "activeOpacity", "size", "iconSize", "lg", "color", "onPrimary", "isActive", "tabItem", "tabButton", "iconContainer", "md", "bottomNavActive", "bottomNavInactive", "badgeText", "tabLabel", "container", "navigationBar", "map", "create", "position", "bottom", "left", "right", "flexDirection", "backgroundColor", "bottomNavBackground", "paddingTop", "padding", "sm", "paddingBottom", "paddingHorizontal", "borderTopWidth", "borderTopColor", "outline", "shadowColor", "shadow", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation", "flex", "alignItems", "justifyContent", "paddingVertical", "xs", "marginBottom", "caption", "fontSize", "textAlign", "marginTop", "borderRadius", "primary", "top", "error", "min<PERSON><PERSON><PERSON>", "borderWidth", "borderColor", "fontWeight"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/components/BottomNavigation.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  TouchableOpacity,\n  Animated,\n  Alert,\n} from 'react-native';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Colors, Typography, Spacing, Animations } from '../constants';\n\nconst navigationItems = [\n  {\n    id: 1,\n    name: 'Home',\n    icon: 'home',\n    label: 'Beranda',\n    badge: 0,\n  },\n  {\n    id: 2,\n    name: 'Activities',\n    icon: 'assignment',\n    label: 'Aktivitas',\n    badge: 3,\n  },\n  {\n    id: 3,\n    name: 'FAB', // This will be the FAB space\n    icon: 'add',\n    label: '',\n    badge: 0,\n  },\n  {\n    id: 4,\n    name: 'Notifications',\n    icon: 'notifications',\n    label: 'Notifikasi',\n    badge: 5,\n  },\n  {\n    id: 5,\n    name: 'Profile',\n    icon: 'person',\n    label: 'Profil',\n    badge: 0,\n  },\n];\n\nconst BottomNavigation = () => {\n  const [activeTab, setActiveTab] = React.useState('Home');\n  const fabScale = React.useRef(new Animated.Value(1)).current;\n  const tabAnimations = React.useRef(\n    navigationItems.reduce((acc, item) => {\n      acc[item.name] = new Animated.Value(item.name === 'Home' ? 1 : 0.8);\n      return acc;\n    }, {})\n  ).current;\n\n  const handleTabPress = (item) => {\n    if (item.name === 'FAB') {\n      handleFABPress();\n      return;\n    }\n\n    setActiveTab(item.name);\n    \n    // Animate tab selection\n    Object.keys(tabAnimations).forEach((key) => {\n      Animated.spring(tabAnimations[key], {\n        toValue: key === item.name ? 1 : 0.8,\n        ...Animations.spring.default,\n        useNativeDriver: true,\n      }).start();\n    });\n\n    console.log(`Navigate to ${item.name}`);\n  };\n\n  const handleFABPress = () => {\n    // Animate FAB press\n    Animated.sequence([\n      Animated.spring(fabScale, {\n        toValue: 0.9,\n        ...Animations.spring.default,\n        useNativeDriver: true,\n      }),\n      Animated.spring(fabScale, {\n        toValue: 1,\n        ...Animations.spring.bouncy,\n        useNativeDriver: true,\n      }),\n    ]).start();\n\n    // Show quick actions menu\n    Alert.alert(\n      'Quick Actions',\n      'Pilih aksi cepat yang ingin dilakukan',\n      [\n        {\n          text: 'Check-in/out',\n          onPress: () => console.log('Quick check-in'),\n        },\n        {\n          text: 'Laporan Cepat',\n          onPress: () => console.log('Quick report'),\n        },\n        {\n          text: 'Emergency',\n          onPress: () => console.log('Emergency action'),\n          style: 'destructive',\n        },\n        {\n          text: 'Batal',\n          style: 'cancel',\n        },\n      ]\n    );\n  };\n\n  const renderTabItem = (item) => {\n    if (item.name === 'FAB') {\n      return (\n        <View key={item.id} style={styles.fabContainer}>\n          <Animated.View style={{ transform: [{ scale: fabScale }] }}>\n            <TouchableOpacity\n              style={styles.fab}\n              onPress={() => handleTabPress(item)}\n              activeOpacity={0.8}\n            >\n              <MaterialIcons\n                name={item.icon}\n                size={Spacing.iconSize.lg}\n                color={Colors.onPrimary}\n              />\n            </TouchableOpacity>\n          </Animated.View>\n        </View>\n      );\n    }\n\n    const isActive = activeTab === item.name;\n    \n    return (\n      <Animated.View\n        key={item.id}\n        style={[\n          styles.tabItem,\n          { transform: [{ scale: tabAnimations[item.name] }] },\n        ]}\n      >\n        <TouchableOpacity\n          style={styles.tabButton}\n          onPress={() => handleTabPress(item)}\n          activeOpacity={0.7}\n        >\n          <View style={styles.iconContainer}>\n            <MaterialIcons\n              name={item.icon}\n              size={Spacing.iconSize.md}\n              color={isActive ? Colors.bottomNavActive : Colors.bottomNavInactive}\n            />\n            {item.badge > 0 && (\n              <View style={styles.badge}>\n                <Text style={styles.badgeText}>\n                  {item.badge > 9 ? '9+' : item.badge}\n                </Text>\n              </View>\n            )}\n          </View>\n          <Text\n            style={[\n              styles.tabLabel,\n              { color: isActive ? Colors.bottomNavActive : Colors.bottomNavInactive },\n            ]}\n          >\n            {item.label}\n          </Text>\n        </TouchableOpacity>\n      </Animated.View>\n    );\n  };\n\n  return (\n    <View style={styles.container}>\n      <View style={styles.navigationBar}>\n        {navigationItems.map(renderTabItem)}\n      </View>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n  },\n  navigationBar: {\n    flexDirection: 'row',\n    backgroundColor: Colors.bottomNavBackground,\n    paddingTop: Spacing.padding.sm,\n    paddingBottom: Spacing.padding.md,\n    paddingHorizontal: Spacing.padding.sm,\n    borderTopWidth: 1,\n    borderTopColor: Colors.outline,\n    shadowColor: Colors.shadow,\n    shadowOffset: {\n      width: 0,\n      height: -2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 8,\n  },\n  tabItem: {\n    flex: 1,\n    alignItems: 'center',\n  },\n  tabButton: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingVertical: Spacing.padding.xs,\n  },\n  iconContainer: {\n    position: 'relative',\n    marginBottom: Spacing.xs / 2,\n  },\n  tabLabel: {\n    ...Typography.caption,\n    fontSize: 10,\n    textAlign: 'center',\n  },\n  fabContainer: {\n    flex: 1,\n    alignItems: 'center',\n    marginTop: -20, // Elevate FAB above the navigation bar\n  },\n  fab: {\n    width: 56,\n    height: 56,\n    borderRadius: 28,\n    backgroundColor: Colors.primary,\n    alignItems: 'center',\n    justifyContent: 'center',\n    shadowColor: Colors.shadow,\n    shadowOffset: {\n      width: 0,\n      height: 4,\n    },\n    shadowOpacity: 0.3,\n    shadowRadius: 8,\n    elevation: 8,\n  },\n  badge: {\n    position: 'absolute',\n    top: -4,\n    right: -4,\n    backgroundColor: Colors.error,\n    borderRadius: 8,\n    minWidth: 16,\n    height: 16,\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderWidth: 2,\n    borderColor: Colors.bottomNavBackground,\n  },\n  badgeText: {\n    ...Typography.caption,\n    color: Colors.onPrimary,\n    fontSize: 9,\n    fontWeight: '600',\n  },\n});\n\nexport default BottomNavigation;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,KAAA;AAS1B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU;AAAuB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEvE,IAAMC,eAAe,GAAG,CACtB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,eAAe;EACrBC,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE;AACT,CAAC,CACF;AAED,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;EAC7B,IAAAC,eAAA,GAAkCvB,KAAK,CAACwB,QAAQ,CAAC,MAAM,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAjDI,SAAS,GAAAF,gBAAA;IAAEG,YAAY,GAAAH,gBAAA;EAC9B,IAAMI,QAAQ,GAAG7B,KAAK,CAAC8B,MAAM,CAAC,IAAIzB,QAAQ,CAAC0B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAC5D,IAAMC,aAAa,GAAGjC,KAAK,CAAC8B,MAAM,CAChCd,eAAe,CAACkB,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAK;IACpCD,GAAG,CAACC,IAAI,CAAClB,IAAI,CAAC,GAAG,IAAIb,QAAQ,CAAC0B,KAAK,CAACK,IAAI,CAAClB,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;IACnE,OAAOiB,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CACP,CAAC,CAACH,OAAO;EAET,IAAMK,cAAc,GAAG,SAAjBA,cAAcA,CAAID,IAAI,EAAK;IAC/B,IAAIA,IAAI,CAAClB,IAAI,KAAK,KAAK,EAAE;MACvBoB,cAAc,CAAC,CAAC;MAChB;IACF;IAEAV,YAAY,CAACQ,IAAI,CAAClB,IAAI,CAAC;IAGvBqB,MAAM,CAACC,IAAI,CAACP,aAAa,CAAC,CAACQ,OAAO,CAAC,UAACC,GAAG,EAAK;MAC1CrC,QAAQ,CAACsC,MAAM,CAACV,aAAa,CAACS,GAAG,CAAC,EAAAE,aAAA,CAAAA,aAAA;QAChCC,OAAO,EAAEH,GAAG,KAAKN,IAAI,CAAClB,IAAI,GAAG,CAAC,GAAG;MAAG,GACjCP,UAAU,CAACgC,MAAM,CAACG,OAAO;QAC5BC,eAAe,EAAE;MAAI,EACtB,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC;IAEFC,OAAO,CAACC,GAAG,CAAC,eAAed,IAAI,CAAClB,IAAI,EAAE,CAAC;EACzC,CAAC;EAED,IAAMoB,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAE3BjC,QAAQ,CAAC8C,QAAQ,CAAC,CAChB9C,QAAQ,CAACsC,MAAM,CAACd,QAAQ,EAAAe,aAAA,CAAAA,aAAA;MACtBC,OAAO,EAAE;IAAG,GACTlC,UAAU,CAACgC,MAAM,CAACG,OAAO;MAC5BC,eAAe,EAAE;IAAI,EACtB,CAAC,EACF1C,QAAQ,CAACsC,MAAM,CAACd,QAAQ,EAAAe,aAAA,CAAAA,aAAA;MACtBC,OAAO,EAAE;IAAC,GACPlC,UAAU,CAACgC,MAAM,CAACS,MAAM;MAC3BL,eAAe,EAAE;IAAI,EACtB,CAAC,CACH,CAAC,CAACC,KAAK,CAAC,CAAC;IAGV1C,KAAK,CAAC+C,KAAK,CACT,eAAe,EACf,uCAAuC,EACvC,CACE;MACEC,IAAI,EAAE,cAAc;MACpBC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQN,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAAA;IAC9C,CAAC,EACD;MACEI,IAAI,EAAE,eAAe;MACrBC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQN,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAAA;IAC5C,CAAC,EACD;MACEI,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQN,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAAA;MAC9CM,KAAK,EAAE;IACT,CAAC,EACD;MACEF,IAAI,EAAE,OAAO;MACbE,KAAK,EAAE;IACT,CAAC,CAEL,CAAC;EACH,CAAC;EAED,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIrB,IAAI,EAAK;IAC9B,IAAIA,IAAI,CAAClB,IAAI,KAAK,KAAK,EAAE;MACvB,OACEL,IAAA,CAACZ,IAAI;QAAeuD,KAAK,EAAEE,MAAM,CAACC,YAAa;QAAAC,QAAA,EAC7C/C,IAAA,CAACR,QAAQ,CAACJ,IAAI;UAACuD,KAAK,EAAE;YAAEK,SAAS,EAAE,CAAC;cAAEC,KAAK,EAAEjC;YAAS,CAAC;UAAE,CAAE;UAAA+B,QAAA,EACzD/C,IAAA,CAACT,gBAAgB;YACfoD,KAAK,EAAEE,MAAM,CAACK,GAAI;YAClBR,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQlB,cAAc,CAACD,IAAI,CAAC;YAAA,CAAC;YACpC4B,aAAa,EAAE,GAAI;YAAAJ,QAAA,EAEnB/C,IAAA,CAACN,aAAa;cACZW,IAAI,EAAEkB,IAAI,CAACjB,IAAK;cAChB8C,IAAI,EAAEvD,OAAO,CAACwD,QAAQ,CAACC,EAAG;cAC1BC,KAAK,EAAE5D,MAAM,CAAC6D;YAAU,CACzB;UAAC,CACc;QAAC,CACN;MAAC,GAbPjC,IAAI,CAACnB,EAcV,CAAC;IAEX;IAEA,IAAMqD,QAAQ,GAAG3C,SAAS,KAAKS,IAAI,CAAClB,IAAI;IAExC,OACEL,IAAA,CAACR,QAAQ,CAACJ,IAAI;MAEZuD,KAAK,EAAE,CACLE,MAAM,CAACa,OAAO,EACd;QAAEV,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE7B,aAAa,CAACG,IAAI,CAAClB,IAAI;QAAE,CAAC;MAAE,CAAC,CACpD;MAAA0C,QAAA,EAEF7C,KAAA,CAACX,gBAAgB;QACfoD,KAAK,EAAEE,MAAM,CAACc,SAAU;QACxBjB,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQlB,cAAc,CAACD,IAAI,CAAC;QAAA,CAAC;QACpC4B,aAAa,EAAE,GAAI;QAAAJ,QAAA,GAEnB7C,KAAA,CAACd,IAAI;UAACuD,KAAK,EAAEE,MAAM,CAACe,aAAc;UAAAb,QAAA,GAChC/C,IAAA,CAACN,aAAa;YACZW,IAAI,EAAEkB,IAAI,CAACjB,IAAK;YAChB8C,IAAI,EAAEvD,OAAO,CAACwD,QAAQ,CAACQ,EAAG;YAC1BN,KAAK,EAAEE,QAAQ,GAAG9D,MAAM,CAACmE,eAAe,GAAGnE,MAAM,CAACoE;UAAkB,CACrE,CAAC,EACDxC,IAAI,CAACf,KAAK,GAAG,CAAC,IACbR,IAAA,CAACZ,IAAI;YAACuD,KAAK,EAAEE,MAAM,CAACrC,KAAM;YAAAuC,QAAA,EACxB/C,IAAA,CAACX,IAAI;cAACsD,KAAK,EAAEE,MAAM,CAACmB,SAAU;cAAAjB,QAAA,EAC3BxB,IAAI,CAACf,KAAK,GAAG,CAAC,GAAG,IAAI,GAAGe,IAAI,CAACf;YAAK,CAC/B;UAAC,CACH,CACP;QAAA,CACG,CAAC,EACPR,IAAA,CAACX,IAAI;UACHsD,KAAK,EAAE,CACLE,MAAM,CAACoB,QAAQ,EACf;YAAEV,KAAK,EAAEE,QAAQ,GAAG9D,MAAM,CAACmE,eAAe,GAAGnE,MAAM,CAACoE;UAAkB,CAAC,CACvE;UAAAhB,QAAA,EAEDxB,IAAI,CAAChB;QAAK,CACP,CAAC;MAAA,CACS;IAAC,GAjCdgB,IAAI,CAACnB,EAkCG,CAAC;EAEpB,CAAC;EAED,OACEJ,IAAA,CAACZ,IAAI;IAACuD,KAAK,EAAEE,MAAM,CAACqB,SAAU;IAAAnB,QAAA,EAC5B/C,IAAA,CAACZ,IAAI;MAACuD,KAAK,EAAEE,MAAM,CAACsB,aAAc;MAAApB,QAAA,EAC/B5C,eAAe,CAACiE,GAAG,CAACxB,aAAa;IAAC,CAC/B;EAAC,CACH,CAAC;AAEX,CAAC;AAED,IAAMC,MAAM,GAAGvD,UAAU,CAAC+E,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT,CAAC;EACDN,aAAa,EAAE;IACbO,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAEhF,MAAM,CAACiF,mBAAmB;IAC3CC,UAAU,EAAEhF,OAAO,CAACiF,OAAO,CAACC,EAAE;IAC9BC,aAAa,EAAEnF,OAAO,CAACiF,OAAO,CAACjB,EAAE;IACjCoB,iBAAiB,EAAEpF,OAAO,CAACiF,OAAO,CAACC,EAAE;IACrCG,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAExF,MAAM,CAACyF,OAAO;IAC9BC,WAAW,EAAE1F,MAAM,CAAC2F,MAAM;IAC1BC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;IACX,CAAC;IACDC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDlC,OAAO,EAAE;IACPmC,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE;EACd,CAAC;EACDnC,SAAS,EAAE;IACTmC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,eAAe,EAAEnG,OAAO,CAACiF,OAAO,CAACmB;EACnC,CAAC;EACDrC,aAAa,EAAE;IACbU,QAAQ,EAAE,UAAU;IACpB4B,YAAY,EAAErG,OAAO,CAACoG,EAAE,GAAG;EAC7B,CAAC;EACDhC,QAAQ,EAAAlC,aAAA,CAAAA,aAAA,KACHnC,UAAU,CAACuG,OAAO;IACrBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE;EAAQ,EACpB;EACDvD,YAAY,EAAE;IACZ+C,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,QAAQ;IACpBQ,SAAS,EAAE,CAAC;EACd,CAAC;EACDpD,GAAG,EAAE;IACHsC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVc,YAAY,EAAE,EAAE;IAChB5B,eAAe,EAAEhF,MAAM,CAAC6G,OAAO;IAC/BV,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBV,WAAW,EAAE1F,MAAM,CAAC2F,MAAM;IAC1BC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDpF,KAAK,EAAE;IACL8D,QAAQ,EAAE,UAAU;IACpBmC,GAAG,EAAE,CAAC,CAAC;IACPhC,KAAK,EAAE,CAAC,CAAC;IACTE,eAAe,EAAEhF,MAAM,CAAC+G,KAAK;IAC7BH,YAAY,EAAE,CAAC;IACfI,QAAQ,EAAE,EAAE;IACZlB,MAAM,EAAE,EAAE;IACVK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBa,WAAW,EAAE,CAAC;IACdC,WAAW,EAAElH,MAAM,CAACiF;EACtB,CAAC;EACDZ,SAAS,EAAAjC,aAAA,CAAAA,aAAA,KACJnC,UAAU,CAACuG,OAAO;IACrB5C,KAAK,EAAE5D,MAAM,CAAC6D,SAAS;IACvB4C,QAAQ,EAAE,CAAC;IACXU,UAAU,EAAE;EAAK;AAErB,CAAC,CAAC;AAEF,eAAerG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}