{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Colors, Typography, Spacing, Animations } from \"../constants\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar StatCard = function StatCard(_ref) {\n  var title = _ref.title,\n    value = _ref.value,\n    subtitle = _ref.subtitle,\n    icon = _ref.icon,\n    _ref$color = _ref.color,\n    color = _ref$color === void 0 ? Colors.primary : _ref$color,\n    _ref$trend = _ref.trend,\n    trend = _ref$trend === void 0 ? 'neutral' : _ref$trend,\n    onPress = _ref.onPress,\n    style = _ref.style;\n  var scaleValue = React.useRef(new Animated.Value(1)).current;\n  var handlePressIn = function handlePressIn() {\n    if (onPress) {\n      Animated.spring(scaleValue, _objectSpread(_objectSpread({\n        toValue: Animations.scale.pressed\n      }, Animations.spring.default), {}, {\n        useNativeDriver: true\n      })).start();\n    }\n  };\n  var handlePressOut = function handlePressOut() {\n    if (onPress) {\n      Animated.spring(scaleValue, _objectSpread(_objectSpread({\n        toValue: Animations.scale.normal\n      }, Animations.spring.default), {}, {\n        useNativeDriver: true\n      })).start();\n    }\n  };\n  var getTrendIcon = function getTrendIcon() {\n    switch (trend) {\n      case 'positive':\n        return 'trending-up';\n      case 'negative':\n        return 'trending-down';\n      default:\n        return 'trending-flat';\n    }\n  };\n  var getTrendColor = function getTrendColor() {\n    switch (trend) {\n      case 'positive':\n        return Colors.success;\n      case 'negative':\n        return Colors.error;\n      default:\n        return Colors.onSurfaceVariant;\n    }\n  };\n  var CardContent = function CardContent() {\n    return _jsxs(View, {\n      style: [styles.container, style],\n      children: [_jsxs(View, {\n        style: styles.header,\n        children: [_jsx(View, {\n          style: [styles.iconContainer, {\n            backgroundColor: `${color}15`\n          }],\n          children: _jsx(MaterialIcons, {\n            name: icon,\n            size: Spacing.iconSize.md,\n            color: color\n          })\n        }), _jsx(MaterialIcons, {\n          name: getTrendIcon(),\n          size: Spacing.iconSize.sm,\n          color: getTrendColor()\n        })]\n      }), _jsxs(View, {\n        style: styles.content,\n        children: [_jsx(Text, {\n          style: styles.value,\n          children: value\n        }), _jsx(Text, {\n          style: styles.title,\n          numberOfLines: 1,\n          children: title\n        }), _jsx(Text, {\n          style: styles.subtitle,\n          numberOfLines: 1,\n          children: subtitle\n        })]\n      })]\n    });\n  };\n  if (onPress) {\n    return _jsx(Animated.View, {\n      style: {\n        transform: [{\n          scale: scaleValue\n        }]\n      },\n      children: _jsx(TouchableOpacity, {\n        onPress: onPress,\n        onPressIn: handlePressIn,\n        onPressOut: handlePressOut,\n        activeOpacity: 0.9,\n        children: _jsx(CardContent, {})\n      })\n    });\n  }\n  return _jsx(CardContent, {});\n};\nvar styles = StyleSheet.create({\n  container: {\n    backgroundColor: Colors.surface,\n    borderRadius: Spacing.borderRadius.lg,\n    padding: Spacing.padding.md,\n    shadowColor: Colors.cardShadow,\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3,\n    minHeight: 120\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: Spacing.sm\n  },\n  iconContainer: {\n    width: 40,\n    height: 40,\n    borderRadius: Spacing.borderRadius.md,\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  content: {\n    flex: 1,\n    justifyContent: 'center'\n  },\n  value: _objectSpread(_objectSpread({}, Typography.h2), {}, {\n    color: Colors.onSurface,\n    marginBottom: Spacing.xs\n  }),\n  title: _objectSpread(_objectSpread({}, Typography.body2), {}, {\n    color: Colors.onSurface,\n    marginBottom: Spacing.xs / 2\n  }),\n  subtitle: _objectSpread(_objectSpread({}, Typography.caption), {}, {\n    color: Colors.onSurfaceVariant\n  })\n});\nexport default StatCard;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "TouchableOpacity", "Animated", "MaterialIcons", "Colors", "Typography", "Spacing", "Animations", "jsx", "_jsx", "jsxs", "_jsxs", "StatCard", "_ref", "title", "value", "subtitle", "icon", "_ref$color", "color", "primary", "_ref$trend", "trend", "onPress", "style", "scaleValue", "useRef", "Value", "current", "handlePressIn", "spring", "_objectSpread", "toValue", "scale", "pressed", "default", "useNativeDriver", "start", "handlePressOut", "normal", "getTrendIcon", "getTrendColor", "success", "error", "onSurfaceVariant", "<PERSON><PERSON><PERSON><PERSON>", "styles", "container", "children", "header", "iconContainer", "backgroundColor", "name", "size", "iconSize", "md", "sm", "content", "numberOfLines", "transform", "onPressIn", "onPressOut", "activeOpacity", "create", "surface", "borderRadius", "lg", "padding", "shadowColor", "cardShadow", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation", "minHeight", "flexDirection", "justifyContent", "alignItems", "marginBottom", "flex", "h2", "onSurface", "xs", "body2", "caption"], "sources": ["D:/aplikasi/TRAE/psg-bmi/src/components/StatCard.js"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  TouchableOpacity,\n  Animated,\n} from 'react-native';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { Colors, Typography, Spacing, Animations } from '../constants';\n\nconst StatCard = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  color = Colors.primary,\n  trend = 'neutral',\n  onPress,\n  style,\n}) => {\n  const scaleValue = React.useRef(new Animated.Value(1)).current;\n\n  const handlePressIn = () => {\n    if (onPress) {\n      Animated.spring(scaleValue, {\n        toValue: Animations.scale.pressed,\n        ...Animations.spring.default,\n        useNativeDriver: true,\n      }).start();\n    }\n  };\n\n  const handlePressOut = () => {\n    if (onPress) {\n      Animated.spring(scaleValue, {\n        toValue: Animations.scale.normal,\n        ...Animations.spring.default,\n        useNativeDriver: true,\n      }).start();\n    }\n  };\n\n  const getTrendIcon = () => {\n    switch (trend) {\n      case 'positive':\n        return 'trending-up';\n      case 'negative':\n        return 'trending-down';\n      default:\n        return 'trending-flat';\n    }\n  };\n\n  const getTrendColor = () => {\n    switch (trend) {\n      case 'positive':\n        return Colors.success;\n      case 'negative':\n        return Colors.error;\n      default:\n        return Colors.onSurfaceVariant;\n    }\n  };\n\n  const CardContent = () => (\n    <View style={[styles.container, style]}>\n      <View style={styles.header}>\n        <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>\n          <MaterialIcons\n            name={icon}\n            size={Spacing.iconSize.md}\n            color={color}\n          />\n        </View>\n        <MaterialIcons\n          name={getTrendIcon()}\n          size={Spacing.iconSize.sm}\n          color={getTrendColor()}\n        />\n      </View>\n      \n      <View style={styles.content}>\n        <Text style={styles.value}>{value}</Text>\n        <Text style={styles.title} numberOfLines={1}>\n          {title}\n        </Text>\n        <Text style={styles.subtitle} numberOfLines={1}>\n          {subtitle}\n        </Text>\n      </View>\n    </View>\n  );\n\n  if (onPress) {\n    return (\n      <Animated.View style={{ transform: [{ scale: scaleValue }] }}>\n        <TouchableOpacity\n          onPress={onPress}\n          onPressIn={handlePressIn}\n          onPressOut={handlePressOut}\n          activeOpacity={0.9}\n        >\n          <CardContent />\n        </TouchableOpacity>\n      </Animated.View>\n    );\n  }\n\n  return <CardContent />;\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    backgroundColor: Colors.surface,\n    borderRadius: Spacing.borderRadius.lg,\n    padding: Spacing.padding.md,\n    shadowColor: Colors.cardShadow,\n    shadowOffset: {\n      width: 0,\n      height: 2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 3,\n    minHeight: 120,\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: Spacing.sm,\n  },\n  iconContainer: {\n    width: 40,\n    height: 40,\n    borderRadius: Spacing.borderRadius.md,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  content: {\n    flex: 1,\n    justifyContent: 'center',\n  },\n  value: {\n    ...Typography.h2,\n    color: Colors.onSurface,\n    marginBottom: Spacing.xs,\n  },\n  title: {\n    ...Typography.body2,\n    color: Colors.onSurface,\n    marginBottom: Spacing.xs / 2,\n  },\n  subtitle: {\n    ...Typography.caption,\n    color: Colors.onSurfaceVariant,\n  },\n});\n\nexport default StatCard;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,QAAA;AAQ1B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU;AAAuB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEvE,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EASR;EAAA,IARJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;IACRC,IAAI,GAAAJ,IAAA,CAAJI,IAAI;IAAAC,UAAA,GAAAL,IAAA,CACJM,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAGd,MAAM,CAACgB,OAAO,GAAAF,UAAA;IAAAG,UAAA,GAAAR,IAAA,CACtBS,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,SAAS,GAAAA,UAAA;IACjBE,OAAO,GAAAV,IAAA,CAAPU,OAAO;IACPC,KAAK,GAAAX,IAAA,CAALW,KAAK;EAEL,IAAMC,UAAU,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAIxB,QAAQ,CAACyB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAE9D,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1B,IAAIN,OAAO,EAAE;MACXrB,QAAQ,CAAC4B,MAAM,CAACL,UAAU,EAAAM,aAAA,CAAAA,aAAA;QACxBC,OAAO,EAAEzB,UAAU,CAAC0B,KAAK,CAACC;MAAO,GAC9B3B,UAAU,CAACuB,MAAM,CAACK,OAAO;QAC5BC,eAAe,EAAE;MAAI,EACtB,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC;EAED,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAIf,OAAO,EAAE;MACXrB,QAAQ,CAAC4B,MAAM,CAACL,UAAU,EAAAM,aAAA,CAAAA,aAAA;QACxBC,OAAO,EAAEzB,UAAU,CAAC0B,KAAK,CAACM;MAAM,GAC7BhC,UAAU,CAACuB,MAAM,CAACK,OAAO;QAC5BC,eAAe,EAAE;MAAI,EACtB,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC;EAED,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB,QAAQlB,KAAK;MACX,KAAK,UAAU;QACb,OAAO,aAAa;MACtB,KAAK,UAAU;QACb,OAAO,eAAe;MACxB;QACE,OAAO,eAAe;IAC1B;EACF,CAAC;EAED,IAAMmB,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1B,QAAQnB,KAAK;MACX,KAAK,UAAU;QACb,OAAOlB,MAAM,CAACsC,OAAO;MACvB,KAAK,UAAU;QACb,OAAOtC,MAAM,CAACuC,KAAK;MACrB;QACE,OAAOvC,MAAM,CAACwC,gBAAgB;IAClC;EACF,CAAC;EAED,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA;IAAA,OACflC,KAAA,CAACb,IAAI;MAAC0B,KAAK,EAAE,CAACsB,MAAM,CAACC,SAAS,EAAEvB,KAAK,CAAE;MAAAwB,QAAA,GACrCrC,KAAA,CAACb,IAAI;QAAC0B,KAAK,EAAEsB,MAAM,CAACG,MAAO;QAAAD,QAAA,GACzBvC,IAAA,CAACX,IAAI;UAAC0B,KAAK,EAAE,CAACsB,MAAM,CAACI,aAAa,EAAE;YAAEC,eAAe,EAAE,GAAGhC,KAAK;UAAK,CAAC,CAAE;UAAA6B,QAAA,EACrEvC,IAAA,CAACN,aAAa;YACZiD,IAAI,EAAEnC,IAAK;YACXoC,IAAI,EAAE/C,OAAO,CAACgD,QAAQ,CAACC,EAAG;YAC1BpC,KAAK,EAAEA;UAAM,CACd;QAAC,CACE,CAAC,EACPV,IAAA,CAACN,aAAa;UACZiD,IAAI,EAAEZ,YAAY,CAAC,CAAE;UACrBa,IAAI,EAAE/C,OAAO,CAACgD,QAAQ,CAACE,EAAG;UAC1BrC,KAAK,EAAEsB,aAAa,CAAC;QAAE,CACxB,CAAC;MAAA,CACE,CAAC,EAEP9B,KAAA,CAACb,IAAI;QAAC0B,KAAK,EAAEsB,MAAM,CAACW,OAAQ;QAAAT,QAAA,GAC1BvC,IAAA,CAACV,IAAI;UAACyB,KAAK,EAAEsB,MAAM,CAAC/B,KAAM;UAAAiC,QAAA,EAAEjC;QAAK,CAAO,CAAC,EACzCN,IAAA,CAACV,IAAI;UAACyB,KAAK,EAAEsB,MAAM,CAAChC,KAAM;UAAC4C,aAAa,EAAE,CAAE;UAAAV,QAAA,EACzClC;QAAK,CACF,CAAC,EACPL,IAAA,CAACV,IAAI;UAACyB,KAAK,EAAEsB,MAAM,CAAC9B,QAAS;UAAC0C,aAAa,EAAE,CAAE;UAAAV,QAAA,EAC5ChC;QAAQ,CACL,CAAC;MAAA,CACH,CAAC;IAAA,CACH,CAAC;EAAA,CACR;EAED,IAAIO,OAAO,EAAE;IACX,OACEd,IAAA,CAACP,QAAQ,CAACJ,IAAI;MAAC0B,KAAK,EAAE;QAAEmC,SAAS,EAAE,CAAC;UAAE1B,KAAK,EAAER;QAAW,CAAC;MAAE,CAAE;MAAAuB,QAAA,EAC3DvC,IAAA,CAACR,gBAAgB;QACfsB,OAAO,EAAEA,OAAQ;QACjBqC,SAAS,EAAE/B,aAAc;QACzBgC,UAAU,EAAEvB,cAAe;QAC3BwB,aAAa,EAAE,GAAI;QAAAd,QAAA,EAEnBvC,IAAA,CAACoC,WAAW,IAAE;MAAC,CACC;IAAC,CACN,CAAC;EAEpB;EAEA,OAAOpC,IAAA,CAACoC,WAAW,IAAE,CAAC;AACxB,CAAC;AAED,IAAMC,MAAM,GAAG9C,UAAU,CAAC+D,MAAM,CAAC;EAC/BhB,SAAS,EAAE;IACTI,eAAe,EAAE/C,MAAM,CAAC4D,OAAO;IAC/BC,YAAY,EAAE3D,OAAO,CAAC2D,YAAY,CAACC,EAAE;IACrCC,OAAO,EAAE7D,OAAO,CAAC6D,OAAO,CAACZ,EAAE;IAC3Ba,WAAW,EAAEhE,MAAM,CAACiE,UAAU;IAC9BC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE;EACb,CAAC;EACD3B,MAAM,EAAE;IACN4B,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE1E,OAAO,CAACkD;EACxB,CAAC;EACDN,aAAa,EAAE;IACbqB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVP,YAAY,EAAE3D,OAAO,CAAC2D,YAAY,CAACV,EAAE;IACrCwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE;EAClB,CAAC;EACDrB,OAAO,EAAE;IACPwB,IAAI,EAAE,CAAC;IACPH,cAAc,EAAE;EAClB,CAAC;EACD/D,KAAK,EAAAgB,aAAA,CAAAA,aAAA,KACA1B,UAAU,CAAC6E,EAAE;IAChB/D,KAAK,EAAEf,MAAM,CAAC+E,SAAS;IACvBH,YAAY,EAAE1E,OAAO,CAAC8E;EAAE,EACzB;EACDtE,KAAK,EAAAiB,aAAA,CAAAA,aAAA,KACA1B,UAAU,CAACgF,KAAK;IACnBlE,KAAK,EAAEf,MAAM,CAAC+E,SAAS;IACvBH,YAAY,EAAE1E,OAAO,CAAC8E,EAAE,GAAG;EAAC,EAC7B;EACDpE,QAAQ,EAAAe,aAAA,CAAAA,aAAA,KACH1B,UAAU,CAACiF,OAAO;IACrBnE,KAAK,EAAEf,MAAM,CAACwC;EAAgB;AAElC,CAAC,CAAC;AAEF,eAAehC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}