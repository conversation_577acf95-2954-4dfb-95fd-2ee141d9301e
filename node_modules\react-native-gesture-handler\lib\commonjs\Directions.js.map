{"version": 3, "sources": ["Directions.ts"], "names": ["Directions", "RIGHT", "LEFT", "UP", "DOWN"], "mappings": ";;;;;;AAAO,MAAMA,UAAU,GAAG;AACxBC,EAAAA,KAAK,EAAE,CADiB;AAExBC,EAAAA,IAAI,EAAE,CAFkB;AAGxBC,EAAAA,EAAE,EAAE,CAHoB;AAIxBC,EAAAA,IAAI,EAAE;AAJkB,CAAnB,C,CAOP", "sourcesContent": ["export const Directions = {\n  RIGHT: 1,\n  LEFT: 2,\n  UP: 4,\n  DOWN: 8,\n} as const;\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value\nexport type Directions = typeof Directions[keyof typeof Directions];\n"]}