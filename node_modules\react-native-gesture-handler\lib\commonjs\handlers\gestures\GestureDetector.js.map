{"version": 3, "sources": ["GestureDetector.tsx"], "names": ["ALLOWED_PROPS", "baseGestureHandlerWithMonitorProps", "tapGestureHandlerProps", "panGestureHandlerProps", "panGestureHandlerCustomNativeProps", "longPressGestureHandlerProps", "forceTouchGestureHandlerProps", "flingGestureHandlerProps", "hoverGestureHandlerProps", "nativeViewGestureHandlerProps", "convertToHandlerTag", "ref", "BaseGesture", "handlerTag", "current", "extractValidHandlerTags", "interactionGroup", "map", "filter", "tag", "dropHandlers", "preparedGesture", "handler", "config", "RNGestureHandlerModule", "dropGestureHandler", "testId", "checkGestureCallbacksForWorklets", "gesture", "runOnJS", "areSomeNotWorklets", "handlers", "isWorklet", "includes", "areSomeWorklets", "console", "error", "attachHandlers", "gestureConfig", "viewTag", "webEventHandlersRef", "mountedRef", "firstExecution", "initialize", "prepare", "createGestureHandler", "handler<PERSON>ame", "requireToFail", "simultaneousWith", "blocksHandlers", "updateGestureHandler", "simultaneousHandlers", "waitFor", "actionType", "shouldUseReanimated", "ActionType", "REANIMATED_WORKLET", "JS_FUNCTION_NEW_API", "Platform", "OS", "attachGestureHandler", "JS_FUNCTION_OLD_API", "animatedHandlers", "isAnimatedGesture", "g", "value", "updateHandlers", "i", "length", "previousHandlersValue", "newHandlersValue", "shouldUpdateSharedValue", "gestureId", "needsToReattach", "isStateChangeEvent", "event", "oldState", "isTouchEvent", "eventType", "<PERSON><PERSON><PERSON><PERSON>", "type", "CALLBACK_TYPE", "BEGAN", "onBegin", "START", "onStart", "UPDATE", "onUpdate", "CHANGE", "onChange", "END", "onEnd", "FINALIZE", "onFinalize", "TOUCHES_DOWN", "onTouchesDown", "TOUCHES_MOVE", "onTouchesMove", "TOUCHES_UP", "onTouchesUp", "TOUCHES_CANCELLED", "onTouchesCancelled", "touchEventTypeToCallbackType", "TouchEventType", "UNDEFINED", "runWorklet", "args", "warn", "useAnimatedGesture", "needsRebuild", "Reanimated", "sharedHandlersCallbacks", "useSharedValue", "lastUpdateEvent", "stateControllers", "callback", "currentCallback", "State", "UNDETERMINED", "state", "ACTIVE", "undefined", "FAILED", "CANCELLED", "GestureStateManager", "create", "changeEventCalculator", "useEvent", "animatedEventHandler", "validateDetectorChildren", "__DEV__", "REACT_NATIVE_VERSION", "wrapType", "minor", "major", "_reactInternals", "elementType", "_reactInternalFiber", "instance", "<PERSON><PERSON><PERSON><PERSON>", "findHostInstance_DEPRECATED", "_internalFiberInstanceHandleDEV", "sibling", "Error", "return", "applyUserSelectProp", "userSelect", "toGestureArray", "GestureDetector", "props", "rootViewContext", "GestureHandlerRootViewContext", "useReanimatedHook", "some", "firstRender", "viewRef", "previousViewTag", "forceReattach", "onGestureHandlerEvent", "e", "nativeEvent", "onGestureHandlerStateChange", "renderState", "setRenderState", "forceRender", "React", "useRef", "onHandlersUpdate", "skipConfigUpdate", "needsToRebuildReanimatedEvent", "refFunction", "node", "global", "isFormsStackingContext", "children", "Wrap", "Component", "render", "child", "Children", "only", "cloneElement", "collapsable", "AnimatedWrap", "default", "createAnimatedComponent"], "mappings": ";;;;;;;AAAA;;AACA;;AAOA;;AACA;;AACA;;AACA;;AAWA;;AAIA;;AACA;;AACA;;AACA;;AAIA;;AACA;;AACA;;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;AAMA,MAAMA,aAAa,GAAG,CACpB,GAAGC,wDADiB,EAEpB,GAAGC,yCAFiB,EAGpB,GAAGC,yCAHiB,EAIpB,GAAGC,qDAJiB,EAKpB,GAAGC,qDALiB,EAMpB,GAAGC,uDANiB,EAOpB,GAAGC,6CAPiB,EAQpB,GAAGC,sCARiB,EASpB,GAAGC,uDATiB,CAAtB;;AAsBA,SAASC,mBAAT,CAA6BC,GAA7B,EAAsD;AACpD,MAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;AAC3B,WAAOA,GAAP;AACD,GAFD,MAEO,IAAIA,GAAG,YAAYC,oBAAnB,EAAgC;AACrC,WAAOD,GAAG,CAACE,UAAX;AACD,GAFM,MAEA;AAAA;;AACL;AACA;AACA,oDAAOF,GAAG,CAACG,OAAX,iDAAO,aAAaD,UAApB,yEAAkC,CAAC,CAAnC;AACD;AACF;;AAED,SAASE,uBAAT,CAAiCC,gBAAjC,EAA6E;AAAA;;AAC3E,kCACEA,gBADF,aACEA,gBADF,iDACEA,gBAAgB,CAAEC,GAAlB,CAAsBP,mBAAtB,CADF,2DACE,uBAA4CQ,MAA5C,CAAoDC,GAAD,IAASA,GAAG,GAAG,CAAlE,CADF,yEAC0E,EAD1E;AAGD;;AAED,SAASC,YAAT,CAAsBC,eAAtB,EAA+D;AAC7D,OAAK,MAAMC,OAAX,IAAsBD,eAAe,CAACE,MAAtC,EAA8C;AAC5CC,oCAAuBC,kBAAvB,CAA0CH,OAAO,CAACT,UAAlD;;AAEA,6CAAkBS,OAAO,CAACT,UAA1B,EAAsCS,OAAO,CAACC,MAAR,CAAeG,MAArD;AACD;;AAED;AACD;;AAED,SAASC,gCAAT,CAA0CC,OAA1C,EAAgE;AAC9D;AACA;AACA,MAAIA,OAAO,CAACL,MAAR,CAAeM,OAAnB,EAA4B;AAC1B;AACD;;AAED,QAAMC,kBAAkB,GAAGF,OAAO,CAACG,QAAR,CAAiBC,SAAjB,CAA2BC,QAA3B,CAAoC,KAApC,CAA3B;AACA,QAAMC,eAAe,GAAGN,OAAO,CAACG,QAAR,CAAiBC,SAAjB,CAA2BC,QAA3B,CAAoC,IAApC,CAAxB,CAR8D,CAU9D;AACA;;AACA,MAAIH,kBAAkB,IAAII,eAA1B,EAA2C;AACzCC,IAAAA,OAAO,CAACC,KAAR,CACE,uBACG,2QADH,CADF;AAKD;AACF;;AAkBD,SAASC,cAAT,CAAwB;AACtBhB,EAAAA,eADsB;AAEtBiB,EAAAA,aAFsB;AAGtBV,EAAAA,OAHsB;AAItBW,EAAAA,OAJsB;AAKtBC,EAAAA,mBALsB;AAMtBC,EAAAA;AANsB,CAAxB,EAOyB;AACvB,MAAI,CAACpB,eAAe,CAACqB,cAArB,EAAqC;AACnCJ,IAAAA,aAAa,CAACK,UAAd;AACD,GAFD,MAEO;AACLtB,IAAAA,eAAe,CAACqB,cAAhB,GAAiC,KAAjC;AACD,GALsB,CAOvB;AACA;;;AACA,0CAAiB,MAAM;AACrB,QAAI,CAACD,UAAU,CAAC3B,OAAhB,EAAyB;AACvB;AACD;;AACDwB,IAAAA,aAAa,CAACM,OAAd;AACD,GALD;;AAOA,OAAK,MAAMtB,OAAX,IAAsBM,OAAtB,EAA+B;AAC7BD,IAAAA,gCAAgC,CAACL,OAAD,CAAhC;;AACAE,oCAAuBqB,oBAAvB,CACEvB,OAAO,CAACwB,WADV,EAEExB,OAAO,CAACT,UAFV,EAGE,wCAAaS,OAAO,CAACC,MAArB,EAA6BvB,aAA7B,CAHF;;AAMA,2CAAgBsB,OAAO,CAACT,UAAxB,EAAoCS,OAApC,EAA6CA,OAAO,CAACC,MAAR,CAAeG,MAA5D;AACD,GAzBsB,CA2BvB;AACA;;;AACA,0CAAiB,MAAM;AACrB,QAAI,CAACe,UAAU,CAAC3B,OAAhB,EAAyB;AACvB;AACD;;AACD,SAAK,MAAMQ,OAAX,IAAsBM,OAAtB,EAA+B;AAC7B,UAAImB,aAAuB,GAAG,EAA9B;;AACA,UAAIzB,OAAO,CAACC,MAAR,CAAewB,aAAnB,EAAkC;AAChCA,QAAAA,aAAa,GAAGhC,uBAAuB,CAACO,OAAO,CAACC,MAAR,CAAewB,aAAhB,CAAvC;AACD;;AAED,UAAIC,gBAA0B,GAAG,EAAjC;;AACA,UAAI1B,OAAO,CAACC,MAAR,CAAeyB,gBAAnB,EAAqC;AACnCA,QAAAA,gBAAgB,GAAGjC,uBAAuB,CACxCO,OAAO,CAACC,MAAR,CAAeyB,gBADyB,CAA1C;AAGD;;AAED,UAAIC,cAAwB,GAAG,EAA/B;;AACA,UAAI3B,OAAO,CAACC,MAAR,CAAe0B,cAAnB,EAAmC;AACjCA,QAAAA,cAAc,GAAGlC,uBAAuB,CAACO,OAAO,CAACC,MAAR,CAAe0B,cAAhB,CAAxC;AACD;;AAEDzB,sCAAuB0B,oBAAvB,CACE5B,OAAO,CAACT,UADV,EAEE,wCAAaS,OAAO,CAACC,MAArB,EAA6BvB,aAA7B,EAA4C;AAC1CmD,QAAAA,oBAAoB,EAAEH,gBADoB;AAE1CI,QAAAA,OAAO,EAAEL,aAFiC;AAG1CE,QAAAA,cAAc,EAAEA;AAH0B,OAA5C,CAFF;AAQD;;AAED;AACD,GAjCD;AAmCA5B,EAAAA,eAAe,CAACE,MAAhB,GAAyBK,OAAzB;;AAEA,OAAK,MAAMA,OAAX,IAAsBP,eAAe,CAACE,MAAtC,EAA8C;AAC5C,UAAM8B,UAAU,GAAGzB,OAAO,CAAC0B,mBAAR,GACfC,uBAAWC,kBADI,GAEfD,uBAAWE,mBAFf;;AAIA,QAAIC,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AAEvBnC,sCAAuBoC,oBADzB,CAGEhC,OAAO,CAACf,UAHV,EAIE0B,OAJF,EAKEgB,uBAAWM,mBALb,EAKkC;AAChCrB,MAAAA,mBANF;AAQD,KATD,MASO;AACLhB,sCAAuBoC,oBAAvB,CACEhC,OAAO,CAACf,UADV,EAEE0B,OAFF,EAGEc,UAHF;AAKD;AACF;;AAED,MAAIhC,eAAe,CAACyC,gBAApB,EAAsC;AACpC,UAAMC,iBAAiB,GAAIC,CAAD,IAAoBA,CAAC,CAACV,mBAAhD;;AAEAjC,IAAAA,eAAe,CAACyC,gBAAhB,CAAiCG,KAAjC,GAAyCrC,OAAO,CAC7CV,MADsC,CAC/B6C,iBAD+B,EAEtC9C,GAFsC,CAEjC+C,CAAD,IAAOA,CAAC,CAACjC,QAFyB,CAAzC;AAKD;AACF;;AAED,SAASmC,cAAT,CACE7C,eADF,EAEEiB,aAFF,EAGEV,OAHF,EAIEa,UAJF,EAKE;AACAH,EAAAA,aAAa,CAACM,OAAd;;AAEA,OAAK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGvC,OAAO,CAACwC,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACvC,UAAM7C,OAAO,GAAGD,eAAe,CAACE,MAAhB,CAAuB4C,CAAvB,CAAhB;AACAxC,IAAAA,gCAAgC,CAACL,OAAD,CAAhC,CAFuC,CAIvC;AACA;;AACA,QAAIM,OAAO,CAACuC,CAAD,CAAP,CAAWtD,UAAX,KAA0BS,OAAO,CAACT,UAAtC,EAAkD;AAChDe,MAAAA,OAAO,CAACuC,CAAD,CAAP,CAAWtD,UAAX,GAAwBS,OAAO,CAACT,UAAhC;AACAe,MAAAA,OAAO,CAACuC,CAAD,CAAP,CAAWpC,QAAX,CAAoBlB,UAApB,GAAiCS,OAAO,CAACT,UAAzC;AACD;AACF,GAbD,CAeA;AACA;AACA;;;AACA,0CAAiB,MAAM;AACrB,QAAI,CAAC4B,UAAU,CAAC3B,OAAhB,EAAyB;AACvB;AACD;;AACD,SAAK,IAAIqD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGvC,OAAO,CAACwC,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACvC,YAAM7C,OAAO,GAAGD,eAAe,CAACE,MAAhB,CAAuB4C,CAAvB,CAAhB;AAEA7C,MAAAA,OAAO,CAACC,MAAR,GAAiBK,OAAO,CAACuC,CAAD,CAAP,CAAW5C,MAA5B;AACAD,MAAAA,OAAO,CAACS,QAAR,GAAmBH,OAAO,CAACuC,CAAD,CAAP,CAAWpC,QAA9B;AAEA,YAAMgB,aAAa,GAAGhC,uBAAuB,CAC3CO,OAAO,CAACC,MAAR,CAAewB,aAD4B,CAA7C;AAIA,YAAMC,gBAAgB,GAAGjC,uBAAuB,CAC9CO,OAAO,CAACC,MAAR,CAAeyB,gBAD+B,CAAhD;;AAIAxB,sCAAuB0B,oBAAvB,CACE5B,OAAO,CAACT,UADV,EAEE,wCAAaS,OAAO,CAACC,MAArB,EAA6BvB,aAA7B,EAA4C;AAC1CmD,QAAAA,oBAAoB,EAAEH,gBADoB;AAE1CI,QAAAA,OAAO,EAAEL;AAFiC,OAA5C,CAFF;;AAQA,6CAAgBzB,OAAO,CAACT,UAAxB,EAAoCS,OAApC,EAA6CA,OAAO,CAACC,MAAR,CAAeG,MAA5D;AACD;;AAED,QAAIL,eAAe,CAACyC,gBAApB,EAAsC;AAAA;;AACpC,YAAMO,qBAAqB,4BACzBhD,eAAe,CAACyC,gBAAhB,CAAiCG,KADR,yEACiB,EAD5C;AAEA,YAAMK,gBAAgB,GAAGjD,eAAe,CAACE,MAAhB,CACtBL,MADsB,CACd8C,CAAD,IAAOA,CAAC,CAACV,mBADM,EACe;AADf,OAEtBrC,GAFsB,CAEjB+C,CAAD,IAAOA,CAAC,CAACjC,QAFS,CAAzB,CAHoC,CASpC;;AACA,UAAIwC,uBAAuB,GACzBF,qBAAqB,CAACD,MAAtB,KAAiCE,gBAAgB,CAACF,MADpD;;AAGA,UAAI,CAACG,uBAAL,EAA8B;AAC5B;AACA,aAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGG,gBAAgB,CAACF,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAChD,eACE;AACAG,UAAAA,gBAAgB,CAACH,CAAD,CAAhB,CAAoBK,SAApB,KAAkCH,qBAAqB,CAACF,CAAD,CAArB,CAAyBK,SAF7D,EAGE;AACAD,YAAAA,uBAAuB,GAAG,IAA1B;AACA;AACD;AACF;AACF;;AAED,UAAIA,uBAAJ,EAA6B;AAC3BlD,QAAAA,eAAe,CAACyC,gBAAhB,CAAiCG,KAAjC,GAAyCK,gBAAzC;AACD;AACF;;AAED;AACD,GA7DD;AA8DD;;AAED,SAASG,eAAT,CACEpD,eADF,EAEEO,OAFF,EAGE;AACA,MAAIA,OAAO,CAACwC,MAAR,KAAmB/C,eAAe,CAACE,MAAhB,CAAuB6C,MAA9C,EAAsD;AACpD,WAAO,IAAP;AACD;;AACD,OAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGvC,OAAO,CAACwC,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACvC,QACEvC,OAAO,CAACuC,CAAD,CAAP,CAAWrB,WAAX,KAA2BzB,eAAe,CAACE,MAAhB,CAAuB4C,CAAvB,EAA0BrB,WAArD,IACAlB,OAAO,CAACuC,CAAD,CAAP,CAAWb,mBAAX,KACEjC,eAAe,CAACE,MAAhB,CAAuB4C,CAAvB,EAA0Bb,mBAH9B,EAIE;AACA,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD;;AAED,SAASoB,kBAAT,CACEC,KADF,EAEoC;AAClC,YADkC,CAElC;;AACA,SAAOA,KAAK,CAACC,QAAN,IAAkB,IAAzB;AACD;;AAED,SAASC,YAAT,CACEF,KADF,EAE8B;AAC5B;;AACA,SAAOA,KAAK,CAACG,SAAN,IAAmB,IAA1B;AACD;;AAED,SAASC,UAAT,CACEC,IADF,EAEEpD,OAFF,EAGE;AACA;;AACA,UAAQoD,IAAR;AACE,SAAKC,uBAAcC,KAAnB;AACE,aAAOtD,OAAO,CAACuD,OAAf;;AACF,SAAKF,uBAAcG,KAAnB;AACE,aAAOxD,OAAO,CAACyD,OAAf;;AACF,SAAKJ,uBAAcK,MAAnB;AACE,aAAO1D,OAAO,CAAC2D,QAAf;;AACF,SAAKN,uBAAcO,MAAnB;AACE,aAAO5D,OAAO,CAAC6D,QAAf;;AACF,SAAKR,uBAAcS,GAAnB;AACE,aAAO9D,OAAO,CAAC+D,KAAf;;AACF,SAAKV,uBAAcW,QAAnB;AACE,aAAOhE,OAAO,CAACiE,UAAf;;AACF,SAAKZ,uBAAca,YAAnB;AACE,aAAOlE,OAAO,CAACmE,aAAf;;AACF,SAAKd,uBAAce,YAAnB;AACE,aAAOpE,OAAO,CAACqE,aAAf;;AACF,SAAKhB,uBAAciB,UAAnB;AACE,aAAOtE,OAAO,CAACuE,WAAf;;AACF,SAAKlB,uBAAcmB,iBAAnB;AACE,aAAOxE,OAAO,CAACyE,kBAAf;AApBJ;AAsBD;;AAED,SAASC,4BAAT,CACExB,SADF,EAEiB;AACf;;AACA,UAAQA,SAAR;AACE,SAAKyB,+BAAeT,YAApB;AACE,aAAOb,uBAAca,YAArB;;AACF,SAAKS,+BAAeP,YAApB;AACE,aAAOf,uBAAce,YAArB;;AACF,SAAKO,+BAAeL,UAApB;AACE,aAAOjB,uBAAciB,UAArB;;AACF,SAAKK,+BAAeH,iBAApB;AACE,aAAOnB,uBAAcmB,iBAArB;AARJ;;AAUA,SAAOnB,uBAAcuB,SAArB;AACD;;AAED,SAASC,UAAT,CACEzB,IADF,EAEEpD,OAFF,EAGE+C,KAHF,EAIE,GAAG+B,IAJL,EAKE;AACA;;AACA,QAAMpF,OAAO,GAAGyD,UAAU,CAACC,IAAD,EAAOpD,OAAP,CAA1B;;AACA,MAAIA,OAAO,CAACI,SAAR,CAAkBgD,IAAlB,CAAJ,EAA6B;AAC3B;AACA;AACA1D,IAAAA,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAGqD,KAAH,EAAU,GAAG+B,IAAb,CAAP;AACD,GAJD,MAIO,IAAIpF,OAAJ,EAAa;AAClBa,IAAAA,OAAO,CAACwE,IAAR,CAAa,uBAAW,6CAAX,CAAb;AACD;AACF;;AAED,SAASC,kBAAT,CACEvF,eADF,EAEEwF,YAFF,EAGE;AACA,MAAI,CAACC,6BAAL,EAAiB;AACf;AACD,GAHD,CAKA;AACA;AACA;;;AACA,QAAMC,uBAAuB,GAAGD,8BAAWE,cAAX,CAE9B,IAF8B,CAAhC,CARA,CAYA;;;AACA,QAAMC,eAAe,GAAGH,8BAAWE,cAAX,CAEtB,EAFsB,CAAxB,CAbA,CAiBA;;;AACA,QAAME,gBAA2C,GAAG,EAApD;;AAEA,QAAMC,QAAQ,GACZxC,KADe,IAEZ;AACH;;AAEA,UAAMyC,eAAe,GAAGL,uBAAuB,CAAC9C,KAAhD;;AACA,QAAI,CAACmD,eAAL,EAAsB;AACpB;AACD;;AAED,SAAK,IAAIjD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiD,eAAe,CAAChD,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;AAC/C,YAAMvC,OAAO,GAAGwF,eAAe,CAACjD,CAAD,CAA/B;;AAEA,UAAIQ,KAAK,CAAC9D,UAAN,KAAqBe,OAAO,CAACf,UAAjC,EAA6C;AAC3C,YAAI6D,kBAAkB,CAACC,KAAD,CAAtB,EAA+B;AAC7B,cACEA,KAAK,CAACC,QAAN,KAAmByC,aAAMC,YAAzB,IACA3C,KAAK,CAAC4C,KAAN,KAAgBF,aAAMnC,KAFxB,EAGE;AACAuB,YAAAA,UAAU,CAACxB,uBAAcC,KAAf,EAAsBtD,OAAtB,EAA+B+C,KAA/B,CAAV;AACD,WALD,MAKO,IACL,CAACA,KAAK,CAACC,QAAN,KAAmByC,aAAMnC,KAAzB,IACCP,KAAK,CAACC,QAAN,KAAmByC,aAAMC,YAD3B,KAEA3C,KAAK,CAAC4C,KAAN,KAAgBF,aAAMG,MAHjB,EAIL;AACAf,YAAAA,UAAU,CAACxB,uBAAcG,KAAf,EAAsBxD,OAAtB,EAA+B+C,KAA/B,CAAV;AACAsC,YAAAA,eAAe,CAAChD,KAAhB,CAAsBrC,OAAO,CAACf,UAA9B,IAA4C4G,SAA5C;AACD,WAPM,MAOA,IACL9C,KAAK,CAACC,QAAN,KAAmBD,KAAK,CAAC4C,KAAzB,IACA5C,KAAK,CAAC4C,KAAN,KAAgBF,aAAM3B,GAFjB,EAGL;AACA,gBAAIf,KAAK,CAACC,QAAN,KAAmByC,aAAMG,MAA7B,EAAqC;AACnCf,cAAAA,UAAU,CAACxB,uBAAcS,GAAf,EAAoB9D,OAApB,EAA6B+C,KAA7B,EAAoC,IAApC,CAAV;AACD;;AACD8B,YAAAA,UAAU,CAACxB,uBAAcW,QAAf,EAAyBhE,OAAzB,EAAkC+C,KAAlC,EAAyC,IAAzC,CAAV;AACD,WARM,MAQA,IACL,CAACA,KAAK,CAAC4C,KAAN,KAAgBF,aAAMK,MAAtB,IAAgC/C,KAAK,CAAC4C,KAAN,KAAgBF,aAAMM,SAAvD,KACAhD,KAAK,CAAC4C,KAAN,KAAgB5C,KAAK,CAACC,QAFjB,EAGL;AACA,gBAAID,KAAK,CAACC,QAAN,KAAmByC,aAAMG,MAA7B,EAAqC;AACnCf,cAAAA,UAAU,CAACxB,uBAAcS,GAAf,EAAoB9D,OAApB,EAA6B+C,KAA7B,EAAoC,KAApC,CAAV;AACD;;AACD8B,YAAAA,UAAU,CAACxB,uBAAcW,QAAf,EAAyBhE,OAAzB,EAAkC+C,KAAlC,EAAyC,KAAzC,CAAV;AACD;AACF,SA9BD,MA8BO,IAAIE,YAAY,CAACF,KAAD,CAAhB,EAAyB;AAC9B,cAAI,CAACuC,gBAAgB,CAAC/C,CAAD,CAArB,EAA0B;AACxB+C,YAAAA,gBAAgB,CAAC/C,CAAD,CAAhB,GAAsByD,yCAAoBC,MAApB,CAA2BlD,KAAK,CAAC9D,UAAjC,CAAtB;AACD;;AAED,cAAI8D,KAAK,CAACG,SAAN,KAAoByB,+BAAee,YAAvC,EAAqD;AACnDb,YAAAA,UAAU,CACRH,4BAA4B,CAAC3B,KAAK,CAACG,SAAP,CADpB,EAERlD,OAFQ,EAGR+C,KAHQ,EAIRuC,gBAAgB,CAAC/C,CAAD,CAJR,CAAV;AAMD;AACF,SAbM,MAaA;AACLsC,UAAAA,UAAU,CAACxB,uBAAcK,MAAf,EAAuB1D,OAAvB,EAAgC+C,KAAhC,CAAV;;AAEA,cAAI/C,OAAO,CAAC6D,QAAR,IAAoB7D,OAAO,CAACkG,qBAAhC,EAAuD;AAAA;;AACrDrB,YAAAA,UAAU,CACRxB,uBAAcO,MADN,EAER5D,OAFQ,2BAGRA,OAAO,CAACkG,qBAHA,0DAGR,2BAAAlG,OAAO,EACL+C,KADK,EAELsC,eAAe,CAAChD,KAAhB,CAAsBrC,OAAO,CAACf,UAA9B,CAFK,CAHC,CAAV;AASAoG,YAAAA,eAAe,CAAChD,KAAhB,CAAsBrC,OAAO,CAACf,UAA9B,IAA4C8D,KAA5C;AACD;AACF;AACF;AACF;AACF,GA3ED,CApBA,CAiGA;;;AACA,QAAMA,KAAK,GAAGmC,8BAAWiB,QAAX,CACZZ,QADY,EAEZ,CAAC,6BAAD,EAAgC,uBAAhC,CAFY,EAGZN,YAHY,CAAd;;AAMAxF,EAAAA,eAAe,CAAC2G,oBAAhB,GAAuCrD,KAAvC;AACAtD,EAAAA,eAAe,CAACyC,gBAAhB,GAAmCiD,uBAAnC;AACD,C,CAED;;;AACA,SAASkB,wBAAT,CAAkCtH,GAAlC,EAA4C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAIuH,OAAO,IAAIxE,sBAASC,EAAT,KAAgB,KAA/B,EAAsC;AACpC,UAAMwE,oBAAoB,GAAG,mDAA7B,CADoC,CAEpC;;AACA,UAAMC,QAAQ,GACZD,oBAAoB,CAACE,KAArB,GAA6B,EAA7B,IAAmCF,oBAAoB,CAACG,KAArB,GAA6B,CAAhE,GACI;AACA3H,IAAAA,GAAG,CAAC4H,eAAJ,CAAoBC,WAFxB,GAGI;AACA7H,IAAAA,GAAG,CAAC8H,mBAAJ,CAAwBD,WAL9B,CAHoC,CASpC;;AACA,QAAIE,QAAQ,GACVC,uBAAWC,2BAAX,CACEjI,GADF,EAEEkI,+BAHJ,CAVoC,CAepC;;;AACA,WAAOH,QAAQ,IAAIA,QAAQ,CAACF,WAAT,KAAyBJ,QAA5C,EAAsD;AACpD;AACA,UAAIM,QAAQ,CAACI,OAAb,EAAsB;AACpB,cAAM,IAAIC,KAAJ,CACJ,mPADI,CAAN;AAGD,OANmD,CAQpD;;;AACAL,MAAAA,QAAQ,GAAGA,QAAQ,CAACM,MAApB;AACD;AACF;AACF;;AAED,MAAMC,mBAAmB,GAAG,CAC1BC,UAD0B,EAE1BtH,OAF0B,KAGjB;AACT,OAAK,MAAMoC,CAAX,IAAgBpC,OAAO,CAACuH,cAAR,EAAhB,EAA0C;AACxCnF,IAAAA,CAAC,CAACzC,MAAF,CAAS2H,UAAT,GAAsBA,UAAtB;AACD;AACF,CAPD;;AAoBO,MAAME,eAAe,GAAIC,KAAD,IAAiC;AAC9D,QAAMC,eAAe,GAAG,uBAAWC,sCAAX,CAAxB;;AACA,MAAIrB,OAAO,IAAI,CAACoB,eAAZ,IAA+B,CAAC,uBAAhC,IAA+C5F,sBAASC,EAAT,KAAgB,KAAnE,EAA0E;AACxE,UAAM,IAAIoF,KAAJ,CACJ,wNADI,CAAN;AAGD;;AAED,QAAMzG,aAAa,GAAG+G,KAAK,CAACzH,OAA5B;;AAEA,MAAIyH,KAAK,CAACH,UAAV,EAAsB;AACpBD,IAAAA,mBAAmB,CAACI,KAAK,CAACH,UAAP,EAAmB5G,aAAnB,CAAnB;AACD;;AAED,QAAMV,OAAO,GAAGU,aAAa,CAAC6G,cAAd,EAAhB;AACA,QAAMK,iBAAiB,GAAG5H,OAAO,CAAC6H,IAAR,CAAczF,CAAD,IAAOA,CAAC,CAACV,mBAAtB,CAA1B,CAf8D,CAiB9D;;AACA,QAAMiE,KAAK,GAAG,mBAA6B;AACzCmC,IAAAA,WAAW,EAAE,IAD4B;AAEzCC,IAAAA,OAAO,EAAE,IAFgC;AAGzCC,IAAAA,eAAe,EAAE,CAAC,CAHuB;AAIzCC,IAAAA,aAAa,EAAE;AAJ0B,GAA7B,EAKX/I,OALH;AAMA,QAAM2B,UAAU,GAAG,mBAAO,KAAP,CAAnB;AACA,QAAMD,mBAAmB,GAAG,mBAAwB;AAClDsH,IAAAA,qBAAqB,EAAGC,CAAD,IAAyC;AAC9D,gDAAsBA,CAAC,CAACC,WAAxB;AACD,KAHiD;AAIlDC,IAAAA,2BAA2B,EAAE,mEACxBF,CAAD,IAAyC;AACvC,gDAAsBA,CAAC,CAACC,WAAxB;AACD,KAHwB,GAIzBvC;AAR8C,GAAxB,CAA5B;AAWA,QAAM,CAACyC,WAAD,EAAcC,cAAd,IAAgC,qBAAS,KAAT,CAAtC;;AACA,WAASC,WAAT,GAAuB;AACrBD,IAAAA,cAAc,CAAC,CAACD,WAAF,CAAd;AACD;;AAED,QAAM7I,eAAe,GAAGgJ,eAAMC,MAAN,CAAqC;AAC3D/I,IAAAA,MAAM,EAAEK,OADmD;AAE3DoG,IAAAA,oBAAoB,EAAE,IAFqC;AAG3DlE,IAAAA,gBAAgB,EAAE,IAHyC;AAI3DpB,IAAAA,cAAc,EAAE,IAJ2C;AAK3D8G,IAAAA,iBAAiB,EAAEA;AALwC,GAArC,EAMrB1I,OANH;;AAQA,MAAI0I,iBAAiB,KAAKnI,eAAe,CAACmI,iBAA1C,EAA6D;AAC3D,UAAM,IAAIT,KAAJ,CACJ,uBACE,gFADF,CADI,CAAN;AAKD;;AAED,WAASwB,gBAAT,CAA0BC,gBAA1B,EAAsD;AACpD;AACA,UAAMjI,OAAO,GAAG,0CAAegF,KAAK,CAACoC,OAArB,CAAhB;AACA,UAAME,aAAa,GAAGtH,OAAO,KAAKgF,KAAK,CAACqC,eAAxC;;AAEA,QAAIC,aAAa,IAAIpF,eAAe,CAACpD,eAAD,EAAkBO,OAAlB,CAApC,EAAgE;AAC9DqG,MAAAA,wBAAwB,CAACV,KAAK,CAACoC,OAAP,CAAxB;AACAvI,MAAAA,YAAY,CAACC,eAAD,CAAZ;AACAgB,MAAAA,cAAc,CAAC;AACbhB,QAAAA,eADa;AAEbiB,QAAAA,aAFa;AAGbV,QAAAA,OAHa;AAIbY,QAAAA,mBAJa;AAKbD,QAAAA,OALa;AAMbE,QAAAA;AANa,OAAD,CAAd;AASA8E,MAAAA,KAAK,CAACqC,eAAN,GAAwBrH,OAAxB;AACAgF,MAAAA,KAAK,CAACsC,aAAN,GAAsBA,aAAtB;;AACA,UAAIA,aAAJ,EAAmB;AACjBO,QAAAA,WAAW;AACZ;AACF,KAjBD,MAiBO,IAAI,CAACI,gBAAL,EAAuB;AAC5BtG,MAAAA,cAAc,CAAC7C,eAAD,EAAkBiB,aAAlB,EAAiCV,OAAjC,EAA0Ca,UAA1C,CAAd;AACD;AACF,GAlF6D,CAoF9D;AACA;;;AACA,QAAMgI,6BAA6B,GACjCpJ,eAAe,CAACqB,cAAhB,IACA+B,eAAe,CAACpD,eAAD,EAAkBO,OAAlB,CADf,IAEA2F,KAAK,CAACsC,aAHR;AAKAtC,EAAAA,KAAK,CAACsC,aAAN,GAAsB,KAAtB;;AAEA,MAAIxI,eAAe,CAACqB,cAApB,EAAoC;AAClCJ,IAAAA,aAAa,CAACK,UAAd;AACD;;AAED,MAAI6G,iBAAJ,EAAuB;AACrB;AACA;AACA5C,IAAAA,kBAAkB,CAACvF,eAAD,EAAkBoJ,6BAAlB,CAAlB;AACD;;AAED,wBAAU,MAAM;AACd,UAAMlI,OAAO,GAAG,0CAAegF,KAAK,CAACoC,OAArB,CAAhB;AACApC,IAAAA,KAAK,CAACmC,WAAN,GAAoB,IAApB;AACAjH,IAAAA,UAAU,CAAC3B,OAAX,GAAqB,IAArB;AAEAmH,IAAAA,wBAAwB,CAACV,KAAK,CAACoC,OAAP,CAAxB;AAEAtH,IAAAA,cAAc,CAAC;AACbhB,MAAAA,eADa;AAEbiB,MAAAA,aAFa;AAGbV,MAAAA,OAHa;AAIbY,MAAAA,mBAJa;AAKbD,MAAAA,OALa;AAMbE,MAAAA;AANa,KAAD,CAAd;AASA,WAAO,MAAM;AACXA,MAAAA,UAAU,CAAC3B,OAAX,GAAqB,KAArB;AACAM,MAAAA,YAAY,CAACC,eAAD,CAAZ;AACD,KAHD;AAID,GApBD,EAoBG,EApBH;AAsBA,wBAAU,MAAM;AACd,QAAI,CAACkG,KAAK,CAACmC,WAAX,EAAwB;AACtBa,MAAAA,gBAAgB;AACjB,KAFD,MAEO;AACLhD,MAAAA,KAAK,CAACmC,WAAN,GAAoB,KAApB;AACD;AACF,GAND,EAMG,CAACL,KAAD,CANH;;AAQA,QAAMqB,WAAW,GAAI/J,GAAD,IAAkB;AACpC,QAAIA,GAAG,KAAK,IAAZ,EAAkB;AAChB;AACA4G,MAAAA,KAAK,CAACoC,OAAN,GAAgBhJ,GAAhB,CAFgB,CAIhB;;AACA,UAAI4G,KAAK,CAACqC,eAAN,KAA0B,CAAC,CAA/B,EAAkC;AAChCrC,QAAAA,KAAK,CAACqC,eAAN,GAAwB,0CAAerC,KAAK,CAACoC,OAArB,CAAxB;AACD,OAPe,CAShB;AACA;;;AACAY,MAAAA,gBAAgB,CAAC,IAAD,CAAhB;;AAEA,UAAI,sBAAJ,EAAgB;AACd,cAAMI,IAAI,GAAG,gDAAqBhK,GAArB,CAAb;;AACA,YAAIiK,MAAM,CAACC,sBAAP,CAA8BF,IAA9B,MAAwC,KAA5C,EAAmD;AACjDxI,UAAAA,OAAO,CAACC,KAAR,CACE,uBACE,uEACE,kGAFJ,CADF;AAMD;AACF;AACF;AACF,GA1BD;;AA4BA,MAAIoH,iBAAJ,EAAuB;AACrB,wBACE,6BAAC,YAAD;AACE,MAAA,GAAG,EAAEkB,WADP;AAEE,MAAA,qBAAqB,EAAErJ,eAAe,CAAC2G;AAFzC,OAGGqB,KAAK,CAACyB,QAHT,CADF;AAOD,GARD,MAQO;AACL,wBAAO,6BAAC,IAAD;AAAM,MAAA,GAAG,EAAEJ;AAAX,OAAyBrB,KAAK,CAACyB,QAA/B,CAAP;AACD;AACF,CA5KM;;;;AA8KP,MAAMC,IAAN,SAAmBV,eAAMW,SAAzB,CAIG;AACDC,EAAAA,MAAM,GAAG;AACP,QAAI;AACF;AACA;AACA;AACA;AACA;AACA,YAAMC,KAAU,GAAGb,eAAMc,QAAN,CAAeC,IAAf,CAAoB,KAAK/B,KAAL,CAAWyB,QAA/B,CAAnB;;AACA,0BAAOT,eAAMgB,YAAN,CACLH,KADK,EAEL;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAFK,EAGL;AACAJ,MAAAA,KAAK,CAAC7B,KAAN,CAAYyB,QAJP,CAAP;AAMD,KAbD,CAaE,OAAOf,CAAP,EAAU;AACV,YAAM,IAAIhB,KAAJ,CACJ,uBACG,2KADH,CADI,CAAN;AAKD;AACF;;AAtBA;;AAyBH,MAAMwC,YAAY,4BAAGzE,6BAAH,aAAGA,6BAAH,8CAAGA,8BAAY0E,OAAf,wDAAG,oBAAqBC,uBAArB,CAA6CV,IAA7C,CAAH,yEAAyDA,IAA3E", "sourcesContent": ["import React, { useContext, useEffect, useRef, useState } from 'react';\nimport {\n  GestureType,\n  HandlerCallbacks,\n  BaseGesture,\n  GestureRef,\n  CALLBACK_TYPE,\n} from './gesture';\nimport { Reanimated, SharedValue } from './reanimatedWrapper';\nimport { registerHandler, unregisterHandler } from '../handlersRegistry';\nimport RNGestureHandlerModule from '../../RNGestureHandlerModule';\nimport {\n  baseGestureHandlerWithMonitorProps,\n  filterConfig,\n  findNodeHandle,\n  GestureTouchEvent,\n  GestureUpdateEvent,\n  GestureStateChangeEvent,\n  HandlerStateChangeEvent,\n  scheduleFlushOperations,\n  UserSelect,\n} from '../gestureHandlerCommon';\nimport {\n  GestureStateManager,\n  GestureStateManagerType,\n} from './gestureStateManager';\nimport { flingGestureHandlerProps } from '../FlingGestureHandler';\nimport { forceTouchGestureHandlerProps } from '../ForceTouchGestureHandler';\nimport { longPressGestureHandlerProps } from '../LongPressGestureHandler';\nimport {\n  panGestureHandlerProps,\n  panGestureHandlerCustomNativeProps,\n} from '../PanGestureHandler';\nimport { tapGestureHandlerProps } from '../TapGestureHandler';\nimport { hoverGestureHandlerProps } from './hoverGesture';\nimport { State } from '../../State';\nimport { TouchEventType } from '../../TouchEventType';\nimport { ComposedGesture } from './gestureComposition';\nimport { ActionType } from '../../ActionType';\nimport { isFabric, isJestEnv, tagMessage } from '../../utils';\nimport { getReactNativeVersion } from '../../getReactNativeVersion';\nimport { getShadowNodeFromRef } from '../../getShadowNodeFromRef';\nimport { Platform } from 'react-native';\nimport type RNGestureHandlerModuleWeb from '../../RNGestureHandlerModule.web';\nimport { onGestureHandlerEvent } from './eventReceiver';\nimport { RNRenderer } from '../../RNRenderer';\nimport { isNewWebImplementationEnabled } from '../../EnableNewWebImplementation';\nimport { nativeViewGestureHandlerProps } from '../NativeViewGestureHandler';\nimport GestureHandlerRootViewContext from '../../GestureHandlerRootViewContext';\nimport { ghQueueMicrotask } from '../../ghQueueMicrotask';\n\ndeclare const global: {\n  isFormsStackingContext: (node: unknown) => boolean | null; // JSI function\n};\n\nconst ALLOWED_PROPS = [\n  ...baseGestureHandlerWithMonitorProps,\n  ...tapGestureHandlerProps,\n  ...panGestureHandlerProps,\n  ...panGestureHandlerCustomNativeProps,\n  ...longPressGestureHandlerProps,\n  ...forceTouchGestureHandlerProps,\n  ...flingGestureHandlerProps,\n  ...hoverGestureHandlerProps,\n  ...nativeViewGestureHandlerProps,\n];\n\nexport type GestureConfigReference = {\n  config: GestureType[];\n  animatedEventHandler: unknown;\n  animatedHandlers: SharedValue<\n    HandlerCallbacks<Record<string, unknown>>[] | null\n  > | null;\n  firstExecution: boolean;\n  useReanimatedHook: boolean;\n};\n\nfunction convertToHandlerTag(ref: GestureRef): number {\n  if (typeof ref === 'number') {\n    return ref;\n  } else if (ref instanceof BaseGesture) {\n    return ref.handlerTag;\n  } else {\n    // @ts-ignore in this case it should be a ref either to gesture object or\n    // a gesture handler component, in both cases handlerTag property exists\n    return ref.current?.handlerTag ?? -1;\n  }\n}\n\nfunction extractValidHandlerTags(interactionGroup: GestureRef[] | undefined) {\n  return (\n    interactionGroup?.map(convertToHandlerTag)?.filter((tag) => tag > 0) ?? []\n  );\n}\n\nfunction dropHandlers(preparedGesture: GestureConfigReference) {\n  for (const handler of preparedGesture.config) {\n    RNGestureHandlerModule.dropGestureHandler(handler.handlerTag);\n\n    unregisterHandler(handler.handlerTag, handler.config.testId);\n  }\n\n  scheduleFlushOperations();\n}\n\nfunction checkGestureCallbacksForWorklets(gesture: GestureType) {\n  // if a gesture is explicitly marked to run on the JS thread there is no need to check\n  // if callbacks are worklets as the user is aware they will be ran on the JS thread\n  if (gesture.config.runOnJS) {\n    return;\n  }\n\n  const areSomeNotWorklets = gesture.handlers.isWorklet.includes(false);\n  const areSomeWorklets = gesture.handlers.isWorklet.includes(true);\n\n  // if some of the callbacks are worklets and some are not, and the gesture is not\n  // explicitly marked with `.runOnJS(true)` show an error\n  if (areSomeNotWorklets && areSomeWorklets) {\n    console.error(\n      tagMessage(\n        `Some of the callbacks in the gesture are worklets and some are not. Either make sure that all calbacks are marked as 'worklet' if you wish to run them on the UI thread or use '.runOnJS(true)' modifier on the gesture explicitly to run all callbacks on the JS thread.`\n      )\n    );\n  }\n}\n\ninterface WebEventHandler {\n  onGestureHandlerEvent: (event: HandlerStateChangeEvent<unknown>) => void;\n  onGestureHandlerStateChange?: (\n    event: HandlerStateChangeEvent<unknown>\n  ) => void;\n}\n\ninterface AttachHandlersConfig {\n  preparedGesture: GestureConfigReference;\n  gestureConfig: ComposedGesture | GestureType;\n  gesture: GestureType[];\n  viewTag: number;\n  webEventHandlersRef: React.RefObject<WebEventHandler>;\n  mountedRef: React.RefObject<boolean>;\n}\n\nfunction attachHandlers({\n  preparedGesture,\n  gestureConfig,\n  gesture,\n  viewTag,\n  webEventHandlersRef,\n  mountedRef,\n}: AttachHandlersConfig) {\n  if (!preparedGesture.firstExecution) {\n    gestureConfig.initialize();\n  } else {\n    preparedGesture.firstExecution = false;\n  }\n\n  // use queueMicrotask to extract handlerTags, because all refs should be initialized\n  // when it's ran\n  ghQueueMicrotask(() => {\n    if (!mountedRef.current) {\n      return;\n    }\n    gestureConfig.prepare();\n  });\n\n  for (const handler of gesture) {\n    checkGestureCallbacksForWorklets(handler);\n    RNGestureHandlerModule.createGestureHandler(\n      handler.handlerName,\n      handler.handlerTag,\n      filterConfig(handler.config, ALLOWED_PROPS)\n    );\n\n    registerHandler(handler.handlerTag, handler, handler.config.testId);\n  }\n\n  // use queueMicrotask to extract handlerTags, because all refs should be initialized\n  // when it's ran\n  ghQueueMicrotask(() => {\n    if (!mountedRef.current) {\n      return;\n    }\n    for (const handler of gesture) {\n      let requireToFail: number[] = [];\n      if (handler.config.requireToFail) {\n        requireToFail = extractValidHandlerTags(handler.config.requireToFail);\n      }\n\n      let simultaneousWith: number[] = [];\n      if (handler.config.simultaneousWith) {\n        simultaneousWith = extractValidHandlerTags(\n          handler.config.simultaneousWith\n        );\n      }\n\n      let blocksHandlers: number[] = [];\n      if (handler.config.blocksHandlers) {\n        blocksHandlers = extractValidHandlerTags(handler.config.blocksHandlers);\n      }\n\n      RNGestureHandlerModule.updateGestureHandler(\n        handler.handlerTag,\n        filterConfig(handler.config, ALLOWED_PROPS, {\n          simultaneousHandlers: simultaneousWith,\n          waitFor: requireToFail,\n          blocksHandlers: blocksHandlers,\n        })\n      );\n    }\n\n    scheduleFlushOperations();\n  });\n\n  preparedGesture.config = gesture;\n\n  for (const gesture of preparedGesture.config) {\n    const actionType = gesture.shouldUseReanimated\n      ? ActionType.REANIMATED_WORKLET\n      : ActionType.JS_FUNCTION_NEW_API;\n\n    if (Platform.OS === 'web') {\n      (\n        RNGestureHandlerModule.attachGestureHandler as typeof RNGestureHandlerModuleWeb.attachGestureHandler\n      )(\n        gesture.handlerTag,\n        viewTag,\n        ActionType.JS_FUNCTION_OLD_API, // ignored on web\n        webEventHandlersRef\n      );\n    } else {\n      RNGestureHandlerModule.attachGestureHandler(\n        gesture.handlerTag,\n        viewTag,\n        actionType\n      );\n    }\n  }\n\n  if (preparedGesture.animatedHandlers) {\n    const isAnimatedGesture = (g: GestureType) => g.shouldUseReanimated;\n\n    preparedGesture.animatedHandlers.value = gesture\n      .filter(isAnimatedGesture)\n      .map((g) => g.handlers) as unknown as HandlerCallbacks<\n      Record<string, unknown>\n    >[];\n  }\n}\n\nfunction updateHandlers(\n  preparedGesture: GestureConfigReference,\n  gestureConfig: ComposedGesture | GestureType,\n  gesture: GestureType[],\n  mountedRef: React.RefObject<boolean>\n) {\n  gestureConfig.prepare();\n\n  for (let i = 0; i < gesture.length; i++) {\n    const handler = preparedGesture.config[i];\n    checkGestureCallbacksForWorklets(handler);\n\n    // only update handlerTag when it's actually different, it may be the same\n    // if gesture config object is wrapped with useMemo\n    if (gesture[i].handlerTag !== handler.handlerTag) {\n      gesture[i].handlerTag = handler.handlerTag;\n      gesture[i].handlers.handlerTag = handler.handlerTag;\n    }\n  }\n\n  // use queueMicrotask to extract handlerTags, because when it's ran, all refs should be updated\n  // and handlerTags in BaseGesture references should be updated in the loop above (we need to wait\n  // in case of external relations)\n  ghQueueMicrotask(() => {\n    if (!mountedRef.current) {\n      return;\n    }\n    for (let i = 0; i < gesture.length; i++) {\n      const handler = preparedGesture.config[i];\n\n      handler.config = gesture[i].config;\n      handler.handlers = gesture[i].handlers;\n\n      const requireToFail = extractValidHandlerTags(\n        handler.config.requireToFail\n      );\n\n      const simultaneousWith = extractValidHandlerTags(\n        handler.config.simultaneousWith\n      );\n\n      RNGestureHandlerModule.updateGestureHandler(\n        handler.handlerTag,\n        filterConfig(handler.config, ALLOWED_PROPS, {\n          simultaneousHandlers: simultaneousWith,\n          waitFor: requireToFail,\n        })\n      );\n\n      registerHandler(handler.handlerTag, handler, handler.config.testId);\n    }\n\n    if (preparedGesture.animatedHandlers) {\n      const previousHandlersValue =\n        preparedGesture.animatedHandlers.value ?? [];\n      const newHandlersValue = preparedGesture.config\n        .filter((g) => g.shouldUseReanimated) // ignore gestures that shouldn't run on UI\n        .map((g) => g.handlers) as unknown as HandlerCallbacks<\n        Record<string, unknown>\n      >[];\n\n      // if amount of gesture configs changes, we need to update the callbacks in shared value\n      let shouldUpdateSharedValue =\n        previousHandlersValue.length !== newHandlersValue.length;\n\n      if (!shouldUpdateSharedValue) {\n        // if the amount is the same, we need to check if any of the configs inside has changed\n        for (let i = 0; i < newHandlersValue.length; i++) {\n          if (\n            // we can use the `gestureId` prop as it's unique for every config instance\n            newHandlersValue[i].gestureId !== previousHandlersValue[i].gestureId\n          ) {\n            shouldUpdateSharedValue = true;\n            break;\n          }\n        }\n      }\n\n      if (shouldUpdateSharedValue) {\n        preparedGesture.animatedHandlers.value = newHandlersValue;\n      }\n    }\n\n    scheduleFlushOperations();\n  });\n}\n\nfunction needsToReattach(\n  preparedGesture: GestureConfigReference,\n  gesture: GestureType[]\n) {\n  if (gesture.length !== preparedGesture.config.length) {\n    return true;\n  }\n  for (let i = 0; i < gesture.length; i++) {\n    if (\n      gesture[i].handlerName !== preparedGesture.config[i].handlerName ||\n      gesture[i].shouldUseReanimated !==\n        preparedGesture.config[i].shouldUseReanimated\n    ) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction isStateChangeEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureStateChangeEvent {\n  'worklet';\n  // @ts-ignore Yes, the oldState prop is missing on GestureTouchEvent, that's the point\n  return event.oldState != null;\n}\n\nfunction isTouchEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureTouchEvent {\n  'worklet';\n  return event.eventType != null;\n}\n\nfunction getHandler(\n  type: CALLBACK_TYPE,\n  gesture: HandlerCallbacks<Record<string, unknown>>\n) {\n  'worklet';\n  switch (type) {\n    case CALLBACK_TYPE.BEGAN:\n      return gesture.onBegin;\n    case CALLBACK_TYPE.START:\n      return gesture.onStart;\n    case CALLBACK_TYPE.UPDATE:\n      return gesture.onUpdate;\n    case CALLBACK_TYPE.CHANGE:\n      return gesture.onChange;\n    case CALLBACK_TYPE.END:\n      return gesture.onEnd;\n    case CALLBACK_TYPE.FINALIZE:\n      return gesture.onFinalize;\n    case CALLBACK_TYPE.TOUCHES_DOWN:\n      return gesture.onTouchesDown;\n    case CALLBACK_TYPE.TOUCHES_MOVE:\n      return gesture.onTouchesMove;\n    case CALLBACK_TYPE.TOUCHES_UP:\n      return gesture.onTouchesUp;\n    case CALLBACK_TYPE.TOUCHES_CANCELLED:\n      return gesture.onTouchesCancelled;\n  }\n}\n\nfunction touchEventTypeToCallbackType(\n  eventType: TouchEventType\n): CALLBACK_TYPE {\n  'worklet';\n  switch (eventType) {\n    case TouchEventType.TOUCHES_DOWN:\n      return CALLBACK_TYPE.TOUCHES_DOWN;\n    case TouchEventType.TOUCHES_MOVE:\n      return CALLBACK_TYPE.TOUCHES_MOVE;\n    case TouchEventType.TOUCHES_UP:\n      return CALLBACK_TYPE.TOUCHES_UP;\n    case TouchEventType.TOUCHES_CANCELLED:\n      return CALLBACK_TYPE.TOUCHES_CANCELLED;\n  }\n  return CALLBACK_TYPE.UNDEFINED;\n}\n\nfunction runWorklet(\n  type: CALLBACK_TYPE,\n  gesture: HandlerCallbacks<Record<string, unknown>>,\n  event: GestureStateChangeEvent | GestureUpdateEvent | GestureTouchEvent,\n  ...args: any[]\n) {\n  'worklet';\n  const handler = getHandler(type, gesture);\n  if (gesture.isWorklet[type]) {\n    // @ts-ignore Logic below makes sure the correct event is send to the\n    // correct handler.\n    handler?.(event, ...args);\n  } else if (handler) {\n    console.warn(tagMessage('Animated gesture callback must be a worklet'));\n  }\n}\n\nfunction useAnimatedGesture(\n  preparedGesture: GestureConfigReference,\n  needsRebuild: boolean\n) {\n  if (!Reanimated) {\n    return;\n  }\n\n  // Hooks are called conditionally, but the condition is whether the\n  // react-native-reanimated is installed, which shouldn't change while running\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const sharedHandlersCallbacks = Reanimated.useSharedValue<\n    HandlerCallbacks<Record<string, unknown>>[] | null\n  >(null);\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const lastUpdateEvent = Reanimated.useSharedValue<\n    (GestureUpdateEvent | undefined)[]\n  >([]);\n\n  // not every gesture needs a state controller, init them lazily\n  const stateControllers: GestureStateManagerType[] = [];\n\n  const callback = (\n    event: GestureStateChangeEvent | GestureUpdateEvent | GestureTouchEvent\n  ) => {\n    'worklet';\n\n    const currentCallback = sharedHandlersCallbacks.value;\n    if (!currentCallback) {\n      return;\n    }\n\n    for (let i = 0; i < currentCallback.length; i++) {\n      const gesture = currentCallback[i];\n\n      if (event.handlerTag === gesture.handlerTag) {\n        if (isStateChangeEvent(event)) {\n          if (\n            event.oldState === State.UNDETERMINED &&\n            event.state === State.BEGAN\n          ) {\n            runWorklet(CALLBACK_TYPE.BEGAN, gesture, event);\n          } else if (\n            (event.oldState === State.BEGAN ||\n              event.oldState === State.UNDETERMINED) &&\n            event.state === State.ACTIVE\n          ) {\n            runWorklet(CALLBACK_TYPE.START, gesture, event);\n            lastUpdateEvent.value[gesture.handlerTag] = undefined;\n          } else if (\n            event.oldState !== event.state &&\n            event.state === State.END\n          ) {\n            if (event.oldState === State.ACTIVE) {\n              runWorklet(CALLBACK_TYPE.END, gesture, event, true);\n            }\n            runWorklet(CALLBACK_TYPE.FINALIZE, gesture, event, true);\n          } else if (\n            (event.state === State.FAILED || event.state === State.CANCELLED) &&\n            event.state !== event.oldState\n          ) {\n            if (event.oldState === State.ACTIVE) {\n              runWorklet(CALLBACK_TYPE.END, gesture, event, false);\n            }\n            runWorklet(CALLBACK_TYPE.FINALIZE, gesture, event, false);\n          }\n        } else if (isTouchEvent(event)) {\n          if (!stateControllers[i]) {\n            stateControllers[i] = GestureStateManager.create(event.handlerTag);\n          }\n\n          if (event.eventType !== TouchEventType.UNDETERMINED) {\n            runWorklet(\n              touchEventTypeToCallbackType(event.eventType),\n              gesture,\n              event,\n              stateControllers[i]\n            );\n          }\n        } else {\n          runWorklet(CALLBACK_TYPE.UPDATE, gesture, event);\n\n          if (gesture.onChange && gesture.changeEventCalculator) {\n            runWorklet(\n              CALLBACK_TYPE.CHANGE,\n              gesture,\n              gesture.changeEventCalculator?.(\n                event,\n                lastUpdateEvent.value[gesture.handlerTag]\n              )\n            );\n\n            lastUpdateEvent.value[gesture.handlerTag] = event;\n          }\n        }\n      }\n    }\n  };\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const event = Reanimated.useEvent(\n    callback,\n    ['onGestureHandlerStateChange', 'onGestureHandlerEvent'],\n    needsRebuild\n  );\n\n  preparedGesture.animatedEventHandler = event;\n  preparedGesture.animatedHandlers = sharedHandlersCallbacks;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction validateDetectorChildren(ref: any) {\n  // finds the first native view under the Wrap component and traverses the fiber tree upwards\n  // to check whether there is more than one native view as a pseudo-direct child of GestureDetector\n  // i.e. this is not ok:\n  //            Wrap\n  //             |\n  //            / \\\n  //           /   \\\n  //          /     \\\n  //         /       \\\n  //   NativeView  NativeView\n  //\n  // but this is fine:\n  //            Wrap\n  //             |\n  //         NativeView\n  //             |\n  //            / \\\n  //           /   \\\n  //          /     \\\n  //         /       \\\n  //   NativeView  NativeView\n  if (__DEV__ && Platform.OS !== 'web') {\n    const REACT_NATIVE_VERSION = getReactNativeVersion();\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n    const wrapType =\n      REACT_NATIVE_VERSION.minor > 63 || REACT_NATIVE_VERSION.major > 0\n        ? // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          ref._reactInternals.elementType\n        : // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          ref._reactInternalFiber.elementType;\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n    let instance =\n      RNRenderer.findHostInstance_DEPRECATED(\n        ref\n      )._internalFiberInstanceHandleDEV;\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    while (instance && instance.elementType !== wrapType) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      if (instance.sibling) {\n        throw new Error(\n          'GestureDetector has more than one native view as its children. This can happen if you are using a custom component that renders multiple views, like React.Fragment. You should wrap content of GestureDetector with a <View> or <Animated.View>.'\n        );\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n      instance = instance.return;\n    }\n  }\n}\n\nconst applyUserSelectProp = (\n  userSelect: UserSelect,\n  gesture: ComposedGesture | GestureType\n): void => {\n  for (const g of gesture.toGestureArray()) {\n    g.config.userSelect = userSelect;\n  }\n};\n\ninterface GestureDetectorProps {\n  gesture: ComposedGesture | GestureType;\n  userSelect?: UserSelect;\n  children?: React.ReactNode;\n}\ninterface GestureDetectorState {\n  firstRender: boolean;\n  viewRef: React.Component | null;\n  previousViewTag: number;\n  forceReattach: boolean;\n}\nexport const GestureDetector = (props: GestureDetectorProps) => {\n  const rootViewContext = useContext(GestureHandlerRootViewContext);\n  if (__DEV__ && !rootViewContext && !isJestEnv() && Platform.OS !== 'web') {\n    throw new Error(\n      'GestureDetector must be used as a descendant of GestureHandlerRootView. Otherwise the gestures will not be recognized. See https://docs.swmansion.com/react-native-gesture-handler/docs/installation for more details.'\n    );\n  }\n\n  const gestureConfig = props.gesture;\n\n  if (props.userSelect) {\n    applyUserSelectProp(props.userSelect, gestureConfig);\n  }\n\n  const gesture = gestureConfig.toGestureArray();\n  const useReanimatedHook = gesture.some((g) => g.shouldUseReanimated);\n\n  // store state in ref to prevent unnecessary renders\n  const state = useRef<GestureDetectorState>({\n    firstRender: true,\n    viewRef: null,\n    previousViewTag: -1,\n    forceReattach: false,\n  }).current;\n  const mountedRef = useRef(false);\n  const webEventHandlersRef = useRef<WebEventHandler>({\n    onGestureHandlerEvent: (e: HandlerStateChangeEvent<unknown>) => {\n      onGestureHandlerEvent(e.nativeEvent);\n    },\n    onGestureHandlerStateChange: isNewWebImplementationEnabled()\n      ? (e: HandlerStateChangeEvent<unknown>) => {\n          onGestureHandlerEvent(e.nativeEvent);\n        }\n      : undefined,\n  });\n\n  const [renderState, setRenderState] = useState(false);\n  function forceRender() {\n    setRenderState(!renderState);\n  }\n\n  const preparedGesture = React.useRef<GestureConfigReference>({\n    config: gesture,\n    animatedEventHandler: null,\n    animatedHandlers: null,\n    firstExecution: true,\n    useReanimatedHook: useReanimatedHook,\n  }).current;\n\n  if (useReanimatedHook !== preparedGesture.useReanimatedHook) {\n    throw new Error(\n      tagMessage(\n        'You cannot change the thread the callbacks are ran on while the app is running'\n      )\n    );\n  }\n\n  function onHandlersUpdate(skipConfigUpdate?: boolean) {\n    // if the underlying view has changed we need to reattach handlers to the new view\n    const viewTag = findNodeHandle(state.viewRef) as number;\n    const forceReattach = viewTag !== state.previousViewTag;\n\n    if (forceReattach || needsToReattach(preparedGesture, gesture)) {\n      validateDetectorChildren(state.viewRef);\n      dropHandlers(preparedGesture);\n      attachHandlers({\n        preparedGesture,\n        gestureConfig,\n        gesture,\n        webEventHandlersRef,\n        viewTag,\n        mountedRef,\n      });\n\n      state.previousViewTag = viewTag;\n      state.forceReattach = forceReattach;\n      if (forceReattach) {\n        forceRender();\n      }\n    } else if (!skipConfigUpdate) {\n      updateHandlers(preparedGesture, gestureConfig, gesture, mountedRef);\n    }\n  }\n\n  // Reanimated event should be rebuilt only when gestures are reattached, otherwise\n  // config update will be enough as all necessary items are stored in shared values anyway\n  const needsToRebuildReanimatedEvent =\n    preparedGesture.firstExecution ||\n    needsToReattach(preparedGesture, gesture) ||\n    state.forceReattach;\n\n  state.forceReattach = false;\n\n  if (preparedGesture.firstExecution) {\n    gestureConfig.initialize();\n  }\n\n  if (useReanimatedHook) {\n    // Whether animatedGesture or gesture is used shouldn't change while the app is running\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useAnimatedGesture(preparedGesture, needsToRebuildReanimatedEvent);\n  }\n\n  useEffect(() => {\n    const viewTag = findNodeHandle(state.viewRef) as number;\n    state.firstRender = true;\n    mountedRef.current = true;\n\n    validateDetectorChildren(state.viewRef);\n\n    attachHandlers({\n      preparedGesture,\n      gestureConfig,\n      gesture,\n      webEventHandlersRef,\n      viewTag,\n      mountedRef,\n    });\n\n    return () => {\n      mountedRef.current = false;\n      dropHandlers(preparedGesture);\n    };\n  }, []);\n\n  useEffect(() => {\n    if (!state.firstRender) {\n      onHandlersUpdate();\n    } else {\n      state.firstRender = false;\n    }\n  }, [props]);\n\n  const refFunction = (ref: unknown) => {\n    if (ref !== null) {\n      // @ts-ignore Just setting the view ref\n      state.viewRef = ref;\n\n      // if it's the first render, also set the previousViewTag to prevent reattaching gestures when not needed\n      if (state.previousViewTag === -1) {\n        state.previousViewTag = findNodeHandle(state.viewRef) as number;\n      }\n\n      // pass true as `skipConfigUpdate`, here we only want to trigger the eventual reattaching of handlers\n      // in case the view has changed, while config update would be handled be the `useEffect` above\n      onHandlersUpdate(true);\n\n      if (isFabric()) {\n        const node = getShadowNodeFromRef(ref);\n        if (global.isFormsStackingContext(node) === false) {\n          console.error(\n            tagMessage(\n              'GestureDetector has received a child that may get view-flattened. ' +\n                '\\nTo prevent it from misbehaving you need to wrap the child with a `<View collapsable={false}>`.'\n            )\n          );\n        }\n      }\n    }\n  };\n\n  if (useReanimatedHook) {\n    return (\n      <AnimatedWrap\n        ref={refFunction}\n        onGestureHandlerEvent={preparedGesture.animatedEventHandler}>\n        {props.children}\n      </AnimatedWrap>\n    );\n  } else {\n    return <Wrap ref={refFunction}>{props.children}</Wrap>;\n  }\n};\n\nclass Wrap extends React.Component<{\n  onGestureHandlerEvent?: unknown;\n  // implicit `children` prop has been removed in @types/react^18.0.0\n  children?: React.ReactNode;\n}> {\n  render() {\n    try {\n      // I don't think that fighting with types over such a simple function is worth it\n      // The only thing it does is add 'collapsable: false' to the child component\n      // to make sure it is in the native view hierarchy so the detector can find\n      // correct viewTag to attach to.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const child: any = React.Children.only(this.props.children);\n      return React.cloneElement(\n        child,\n        { collapsable: false },\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        child.props.children\n      );\n    } catch (e) {\n      throw new Error(\n        tagMessage(\n          `GestureDetector got more than one view as a child. If you want the gesture to work on multiple views, wrap them with a common parent and attach the gesture to that view.`\n        )\n      );\n    }\n  }\n}\n\nconst AnimatedWrap = Reanimated?.default?.createAnimatedComponent(Wrap) ?? Wrap;\n"]}