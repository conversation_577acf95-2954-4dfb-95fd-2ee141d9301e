{"version": 3, "sources": ["GestureHandlerRootView.web.tsx"], "names": ["React", "View", "GestureHandlerRootViewContext", "GestureHandlerRootView", "props"], "mappings": "AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAEA,SAASC,IAAT,QAAgC,cAAhC;AACA,OAAOC,6BAAP,MAA0C,kCAA1C;AAKA,eAAe,SAASC,sBAAT,CACbC,KADa,EAEb;AACA,sBACE,oBAAC,6BAAD,CAA+B,QAA/B;AAAwC,IAAA,KAAK;AAA7C,kBACE,oBAAC,IAAD,EAAUA,KAAV,CADF,CADF;AAKD", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport { View, ViewProps } from 'react-native';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\n\nexport interface GestureHandlerRootViewProps\n  extends PropsWithChildren<ViewProps> {}\n\nexport default function GestureHandlerRootView(\n  props: GestureHandlerRootViewProps\n) {\n  return (\n    <GestureHandlerRootViewContext.Provider value>\n      <View {...props} />\n    </GestureHandlerRootViewContext.Provider>\n  );\n}\n"]}