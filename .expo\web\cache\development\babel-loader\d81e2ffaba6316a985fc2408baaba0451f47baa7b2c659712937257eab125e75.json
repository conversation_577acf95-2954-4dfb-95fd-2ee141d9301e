{"ast": null, "code": "'use strict';\n\nfunction stiffnessFromOrigamiValue(oValue) {\n  return (oValue - 30) * 3.62 + 194;\n}\nfunction dampingFromOrigamiValue(oValue) {\n  return (oValue - 8) * 3 + 25;\n}\nfunction fromOrigamiTensionAndFriction(tension, friction) {\n  return {\n    stiffness: stiffnessFromOrigamiValue(tension),\n    damping: dampingFromOrigamiValue(friction)\n  };\n}\nfunction fromBouncinessAndSpeed(bounciness, speed) {\n  function normalize(value, startValue, endValue) {\n    return (value - startValue) / (endValue - startValue);\n  }\n  function projectNormal(n, start, end) {\n    return start + n * (end - start);\n  }\n  function linearInterpolation(t, start, end) {\n    return t * end + (1 - t) * start;\n  }\n  function quadraticOutInterpolation(t, start, end) {\n    return linearInterpolation(2 * t - t * t, start, end);\n  }\n  function b3Friction1(x) {\n    return 0.0007 * Math.pow(x, 3) - 0.031 * Math.pow(x, 2) + 0.64 * x + 1.28;\n  }\n  function b3Friction2(x) {\n    return 0.000044 * Math.pow(x, 3) - 0.006 * Math.pow(x, 2) + 0.36 * x + 2;\n  }\n  function b3Friction3(x) {\n    return 0.00000045 * Math.pow(x, 3) - 0.000332 * Math.pow(x, 2) + 0.1078 * x + 5.84;\n  }\n  function b3Nobounce(tension) {\n    if (tension <= 18) {\n      return b3Friction1(tension);\n    } else if (tension > 18 && tension <= 44) {\n      return b3Friction2(tension);\n    } else {\n      return b3Friction3(tension);\n    }\n  }\n  var b = normalize(bounciness / 1.7, 0, 20);\n  b = projectNormal(b, 0, 0.8);\n  var s = normalize(speed / 1.7, 0, 20);\n  var bouncyTension = projectNormal(s, 0.5, 200);\n  var bouncyFriction = quadraticOutInterpolation(b, b3Nobounce(bouncyTension), 0.01);\n  return {\n    stiffness: stiffnessFromOrigamiValue(bouncyTension),\n    damping: dampingFromOrigamiValue(bouncyFriction)\n  };\n}\nexport default {\n  fromOrigamiTensionAndFriction: fromOrigamiTensionAndFriction,\n  fromBouncinessAndSpeed: fromBouncinessAndSpeed\n};", "map": {"version": 3, "names": ["stiffnessFromOrigamiValue", "oValue", "dampingFromOrigamiValue", "fromOrigamiTensionAndFriction", "tension", "friction", "stiffness", "damping", "fromBouncinessAndSpeed", "bounciness", "speed", "normalize", "value", "startValue", "endValue", "projectNormal", "n", "start", "end", "linearInterpolation", "t", "quadraticOutInterpolation", "b3Friction1", "x", "Math", "pow", "b3Friction2", "b3Friction3", "b3Nobounce", "b", "s", "bouncyTension", "bouncyFriction"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/vendor/react-native/Animated/SpringConfig.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * \n */\n\n'use strict';\n\nfunction stiffnessFromOrigamiValue(oValue) {\n  return (oValue - 30) * 3.62 + 194;\n}\nfunction dampingFromOrigamiValue(oValue) {\n  return (oValue - 8) * 3 + 25;\n}\nfunction fromOrigamiTensionAndFriction(tension, friction) {\n  return {\n    stiffness: stiffnessFromOrigamiValue(tension),\n    damping: dampingFromOrigamiValue(friction)\n  };\n}\nfunction fromBouncinessAndSpeed(bounciness, speed) {\n  function normalize(value, startValue, endValue) {\n    return (value - startValue) / (endValue - startValue);\n  }\n  function projectNormal(n, start, end) {\n    return start + n * (end - start);\n  }\n  function linearInterpolation(t, start, end) {\n    return t * end + (1 - t) * start;\n  }\n  function quadraticOutInterpolation(t, start, end) {\n    return linearInterpolation(2 * t - t * t, start, end);\n  }\n  function b3Friction1(x) {\n    return 0.0007 * Math.pow(x, 3) - 0.031 * Math.pow(x, 2) + 0.64 * x + 1.28;\n  }\n  function b3Friction2(x) {\n    return 0.000044 * Math.pow(x, 3) - 0.006 * Math.pow(x, 2) + 0.36 * x + 2;\n  }\n  function b3Friction3(x) {\n    return 0.00000045 * Math.pow(x, 3) - 0.000332 * Math.pow(x, 2) + 0.1078 * x + 5.84;\n  }\n  function b3Nobounce(tension) {\n    if (tension <= 18) {\n      return b3Friction1(tension);\n    } else if (tension > 18 && tension <= 44) {\n      return b3Friction2(tension);\n    } else {\n      return b3Friction3(tension);\n    }\n  }\n  var b = normalize(bounciness / 1.7, 0, 20);\n  b = projectNormal(b, 0, 0.8);\n  var s = normalize(speed / 1.7, 0, 20);\n  var bouncyTension = projectNormal(s, 0.5, 200);\n  var bouncyFriction = quadraticOutInterpolation(b, b3Nobounce(bouncyTension), 0.01);\n  return {\n    stiffness: stiffnessFromOrigamiValue(bouncyTension),\n    damping: dampingFromOrigamiValue(bouncyFriction)\n  };\n}\nexport default {\n  fromOrigamiTensionAndFriction,\n  fromBouncinessAndSpeed\n};"], "mappings": "AAUA,YAAY;;AAEZ,SAASA,yBAAyBA,CAACC,MAAM,EAAE;EACzC,OAAO,CAACA,MAAM,GAAG,EAAE,IAAI,IAAI,GAAG,GAAG;AACnC;AACA,SAASC,uBAAuBA,CAACD,MAAM,EAAE;EACvC,OAAO,CAACA,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE;AAC9B;AACA,SAASE,6BAA6BA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EACxD,OAAO;IACLC,SAAS,EAAEN,yBAAyB,CAACI,OAAO,CAAC;IAC7CG,OAAO,EAAEL,uBAAuB,CAACG,QAAQ;EAC3C,CAAC;AACH;AACA,SAASG,sBAAsBA,CAACC,UAAU,EAAEC,KAAK,EAAE;EACjD,SAASC,SAASA,CAACC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IAC9C,OAAO,CAACF,KAAK,GAAGC,UAAU,KAAKC,QAAQ,GAAGD,UAAU,CAAC;EACvD;EACA,SAASE,aAAaA,CAACC,CAAC,EAAEC,KAAK,EAAEC,GAAG,EAAE;IACpC,OAAOD,KAAK,GAAGD,CAAC,IAAIE,GAAG,GAAGD,KAAK,CAAC;EAClC;EACA,SAASE,mBAAmBA,CAACC,CAAC,EAAEH,KAAK,EAAEC,GAAG,EAAE;IAC1C,OAAOE,CAAC,GAAGF,GAAG,GAAG,CAAC,CAAC,GAAGE,CAAC,IAAIH,KAAK;EAClC;EACA,SAASI,yBAAyBA,CAACD,CAAC,EAAEH,KAAK,EAAEC,GAAG,EAAE;IAChD,OAAOC,mBAAmB,CAAC,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGA,CAAC,EAAEH,KAAK,EAAEC,GAAG,CAAC;EACvD;EACA,SAASI,WAAWA,CAACC,CAAC,EAAE;IACtB,OAAO,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,IAAI;EAC3E;EACA,SAASG,WAAWA,CAACH,CAAC,EAAE;IACtB,OAAO,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,CAAC;EAC1E;EACA,SAASI,WAAWA,CAACJ,CAAC,EAAE;IACtB,OAAO,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,GAAGA,CAAC,GAAG,IAAI;EACpF;EACA,SAASK,UAAUA,CAACxB,OAAO,EAAE;IAC3B,IAAIA,OAAO,IAAI,EAAE,EAAE;MACjB,OAAOkB,WAAW,CAAClB,OAAO,CAAC;IAC7B,CAAC,MAAM,IAAIA,OAAO,GAAG,EAAE,IAAIA,OAAO,IAAI,EAAE,EAAE;MACxC,OAAOsB,WAAW,CAACtB,OAAO,CAAC;IAC7B,CAAC,MAAM;MACL,OAAOuB,WAAW,CAACvB,OAAO,CAAC;IAC7B;EACF;EACA,IAAIyB,CAAC,GAAGlB,SAAS,CAACF,UAAU,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;EAC1CoB,CAAC,GAAGd,aAAa,CAACc,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;EAC5B,IAAIC,CAAC,GAAGnB,SAAS,CAACD,KAAK,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;EACrC,IAAIqB,aAAa,GAAGhB,aAAa,CAACe,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EAC9C,IAAIE,cAAc,GAAGX,yBAAyB,CAACQ,CAAC,EAAED,UAAU,CAACG,aAAa,CAAC,EAAE,IAAI,CAAC;EAClF,OAAO;IACLzB,SAAS,EAAEN,yBAAyB,CAAC+B,aAAa,CAAC;IACnDxB,OAAO,EAAEL,uBAAuB,CAAC8B,cAAc;EACjD,CAAC;AACH;AACA,eAAe;EACb7B,6BAA6B,EAA7BA,6BAA6B;EAC7BK,sBAAsB,EAAtBA;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}