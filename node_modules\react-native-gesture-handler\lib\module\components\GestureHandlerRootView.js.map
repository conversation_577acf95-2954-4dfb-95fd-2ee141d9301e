{"version": 3, "sources": ["GestureHandlerRootView.tsx"], "names": ["React", "View", "maybeInitializeFabric", "GestureHandlerRootViewContext", "GestureHandlerRootView", "props"], "mappings": "AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAEA,SAASC,IAAT,QAAgC,cAAhC;AACA,SAASC,qBAAT,QAAsC,SAAtC;AACA,OAAOC,6BAAP,MAA0C,kCAA1C;AAKA,eAAe,SAASC,sBAAT,CACbC,KADa,EAEb;AACA;AACA;AACA;AACAH,EAAAA,qBAAqB;AAErB,sBACE,oBAAC,6BAAD,CAA+B,QAA/B;AAAwC,IAAA,KAAK;AAA7C,kBACE,oBAAC,IAAD,EAAUG,KAAV,CADF,CADF;AAKD", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport { View, ViewProps } from 'react-native';\nimport { maybeInitializeFabric } from '../init';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\n\nexport interface GestureHandlerRootViewProps\n  extends PropsWithChildren<ViewProps> {}\n\nexport default function GestureHandlerRootView(\n  props: GestureHandlerRootViewProps\n) {\n  // try initialize fabric on the first render, at this point we can\n  // reliably check if fabric is enabled (the function contains a flag\n  // to make sure it's called only once)\n  maybeInitializeFabric();\n\n  return (\n    <GestureHandlerRootViewContext.Provider value>\n      <View {...props} />\n    </GestureHandlerRootViewContext.Provider>\n  );\n}\n"]}