# 📱 Mobile Testing Guide - PSG-BMI Portal

## 🚨 Troubleshooting Expo Go Error

### Masalah yang Sudah Diperbaiki:
✅ **Expo SDK diupgrade dari 49 → 50** untuk kompatibilitas terbaru
✅ **Dependencies diupdate** ke versi yang kompatibel
✅ **Metro bundler cache dibersihkan** untuk build fresh
✅ **Port conflict resolved** (menggunakan 8081)

### 🔍 Langkah Troubleshooting Sistematis:

#### 1. **Verifikasi Network Connection**
```bash
# Pastikan device dan komputer di network yang sama
# Cek IP address komputer:
ipconfig
# Pastikan IP di QR code sesuai dengan IP komputer
```

#### 2. **Update Expo Go App**
- **Android**: Update Expo Go di Google Play Store
- **iOS**: Update Expo Go di App Store
- **Minimum version**: Expo Go 2.29.0+ untuk SDK 50

#### 3. **Clear Cache & Restart**
```bash
# Clear Metro cache
npx expo start --clear

# Clear npm cache jika perlu
npm cache clean --force

# Restart Metro bundler
npx expo start --reset-cache
```

#### 4. **Cek Firewall & Antivirus**
- Pastikan Windows Firewall tidak memblokir port 8081
- Whitelist Expo CLI di antivirus
- Temporary disable firewall untuk testing

#### 5. **Alternative Testing Methods**

##### A. **Expo Go dengan Manual URL**
```
exp://[IP_ADDRESS]:8081
# Contoh: exp://************:8081
```

##### B. **Tunnel Mode (jika network bermasalah)**
```bash
npx expo start --tunnel
# Akan menggunakan ngrok tunnel
```

##### C. **Development Build**
```bash
# Switch ke development build
npx expo start
# Tekan 's' untuk switch ke development build
```

##### D. **Web Testing (sudah berfungsi)**
```bash
npx expo start --web
# Akses: http://localhost:19006
```

### 🔧 Solusi Spesifik Error

#### Error: "Something went wrong"
**Penyebab umum:**
1. Network connectivity issues
2. Expo Go version tidak kompatibel
3. Metro bundler error
4. Firewall blocking

**Solusi:**
```bash
# 1. Restart dengan tunnel
npx expo start --tunnel

# 2. Cek logs detail
npx expo start --verbose

# 3. Reset project
npx expo start --clear --reset-cache
```

#### Error: "Unable to resolve module"
**Solusi:**
```bash
# Clear node_modules dan reinstall
rm -rf node_modules
npm install
npx expo start --clear
```

#### Error: "Network request failed"
**Solusi:**
1. Pastikan device dan PC di WiFi yang sama
2. Coba gunakan tunnel mode
3. Cek firewall settings

### 📱 Testing Checklist

#### Pre-Testing:
- [ ] Expo Go app updated ke versi terbaru
- [ ] Device dan PC di network yang sama
- [ ] Firewall/antivirus tidak blocking
- [ ] Metro bundler running tanpa error

#### Testing Process:
- [ ] Scan QR code dengan Expo Go
- [ ] Atau input manual URL: exp://************:8081
- [ ] Tunggu bundle download selesai
- [ ] Test semua fitur aplikasi

#### Fallback Options:
- [ ] Web testing: http://localhost:19006
- [ ] Tunnel mode: `npx expo start --tunnel`
- [ ] Development build
- [ ] Android emulator (jika tersedia)

### 🌐 Network Troubleshooting

#### Cek Network Configuration:
```bash
# Windows
ipconfig
netstat -an | findstr :8081

# Pastikan port 8081 listening
```

#### Alternative Network Solutions:
1. **Hotspot dari phone** - gunakan phone sebagai hotspot
2. **USB Debugging** - connect via USB
3. **Tunnel Mode** - bypass network issues

### 🚀 Recommended Testing Flow

1. **Start Fresh**:
   ```bash
   npx expo start --clear
   ```

2. **Verify QR Code**:
   - QR code menunjukkan: exp://************:8081
   - IP address sesuai dengan komputer

3. **Test dengan Expo Go**:
   - Scan QR code
   - Atau input manual URL

4. **Jika gagal, coba Tunnel**:
   ```bash
   npx expo start --tunnel
   ```

5. **Fallback ke Web**:
   ```bash
   npx expo start --web
   ```

### 📞 Support & Debugging

#### Logs untuk Debugging:
```bash
# Verbose logging
npx expo start --verbose

# Check Metro logs
# Lihat terminal untuk error messages
```

#### Common Error Messages:
- "Unable to connect" → Network/firewall issue
- "Bundle failed" → Code compilation error
- "Something went wrong" → General Expo Go error

#### Get Help:
1. Check Metro bundler logs
2. Try tunnel mode
3. Test on web first
4. Update Expo Go app
5. Restart development server

---

**🎯 Current Status**: 
- ✅ Web version working perfectly
- ✅ Expo SDK 50 installed
- ✅ Dependencies updated
- ✅ Metro bundler running
- 🔄 Ready for mobile testing

**Next Steps**: Try scanning the new QR code with updated Expo Go app!
