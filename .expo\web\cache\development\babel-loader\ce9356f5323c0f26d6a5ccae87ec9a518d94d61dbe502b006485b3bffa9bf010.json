{"ast": null, "code": "import sha1 from './lib/sha1';\nimport v35 from './lib/v35';\nimport { Uuidv5Namespace } from './uuid.types';\nfunction uuidv4() {\n  var cryptoObject = typeof crypto === 'undefined' || typeof crypto.randomUUID === 'undefined' ? require('crypto') : crypto;\n  if (!(cryptoObject != null && cryptoObject.randomUUID)) {\n    throw Error(\"The browser doesn't support `crypto.randomUUID` function\");\n  }\n  return cryptoObject.randomUUID();\n}\nvar uuid = {\n  v4: uuidv4,\n  v5: v35('v5', 0x50, sha1),\n  namespace: Uuidv5Namespace\n};\nexport default uuid;", "map": {"version": 3, "names": ["sha1", "v35", "Uuidv5Namespace", "uuidv4", "cryptoObject", "crypto", "randomUUID", "require", "Error", "uuid", "v4", "v5", "namespace"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-modules-core\\src\\uuid\\uuid.web.ts"], "sourcesContent": ["import sha1 from './lib/sha1';\nimport v35 from './lib/v35';\nimport { UUID, Uuidv5Namespace } from './uuid.types';\n\nfunction uuidv4(): string {\n  // Crypto needs to be required when run in Node.js environment.\n  const cryptoObject =\n    typeof crypto === 'undefined' || typeof crypto.randomUUID === 'undefined'\n      ? require('crypto')\n      : crypto;\n\n  if (!cryptoObject?.randomUUID) {\n    throw Error(\"The browser doesn't support `crypto.randomUUID` function\");\n  }\n  return cryptoObject.randomUUID();\n}\n\nconst uuid: UUID = {\n  v4: uuidv4,\n  v5: v35('v5', 0x50, sha1),\n  namespace: Uuidv5Namespace,\n};\n\nexport default uuid;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,OAAOC,GAAG,MAAM,WAAW;AAC3B,SAAeC,eAAe,QAAQ,cAAc;AAEpD,SAASC,MAAMA,CAAA;EAEb,IAAMC,YAAY,GAChB,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,UAAU,KAAK,WAAW,GACrEC,OAAO,CAAC,QAAQ,CAAC,GACjBF,MAAM;EAEZ,IAAI,EAACD,YAAY,YAAZA,YAAY,CAAEE,UAAU,GAAE;IAC7B,MAAME,KAAK,CAAC,0DAA0D,CAAC;;EAEzE,OAAOJ,YAAY,CAACE,UAAU,EAAE;AAClC;AAEA,IAAMG,IAAI,GAAS;EACjBC,EAAE,EAAEP,MAAM;EACVQ,EAAE,EAAEV,GAAG,CAAC,IAAI,EAAE,IAAI,EAAED,IAAI,CAAC;EACzBY,SAAS,EAAEV;CACZ;AAED,eAAeO,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}