{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { Platform } from 'expo-modules-core';\nimport PixelRatio from \"react-native-web/dist/exports/PixelRatio\";\nimport NativeModules from \"react-native-web/dist/exports/NativeModules\";\nimport AssetSourceResolver from './AssetSourceResolver';\nimport { getManifest, getManifest2, manifestBaseUrl } from './PlatformUtils';\nvar assetMapOverride = getManifest().assetMapOverride;\nexport function selectAssetSource(meta) {\n  var _meta$fileHashes$inde, _meta$fileUris$index, _manifest2$extra, _manifest2$extra$expo;\n  if (assetMapOverride && assetMapOverride.hasOwnProperty(meta.hash)) {\n    meta = _objectSpread(_objectSpread({}, meta), assetMapOverride[meta.hash]);\n  }\n  var scale = AssetSourceResolver.pickScale(meta.scales, PixelRatio.get());\n  var index = meta.scales.findIndex(function (s) {\n    return s === scale;\n  });\n  var hash = meta.fileHashes ? (_meta$fileHashes$inde = meta.fileHashes[index]) != null ? _meta$fileHashes$inde : meta.fileHashes[0] : meta.hash;\n  var uri = meta.fileUris ? (_meta$fileUris$index = meta.fileUris[index]) != null ? _meta$fileUris$index : meta.fileUris[0] : meta.uri;\n  if (uri) {\n    return {\n      uri: resolveUri(uri),\n      hash: hash\n    };\n  }\n  var assetUrlOverride = getManifest().assetUrlOverride;\n  if (assetUrlOverride) {\n    var _uri = pathJoin(assetUrlOverride, hash);\n    return {\n      uri: resolveUri(_uri),\n      hash: hash\n    };\n  }\n  var fileScale = scale === 1 ? '' : `@${scale}x`;\n  var fileExtension = meta.type ? `.${encodeURIComponent(meta.type)}` : '';\n  var suffix = `/${encodeURIComponent(meta.name)}${fileScale}${fileExtension}`;\n  var params = new URLSearchParams({\n    platform: Platform.OS,\n    hash: meta.hash\n  });\n  if (/^https?:\\/\\//.test(meta.httpServerLocation)) {\n    var _uri2 = meta.httpServerLocation + suffix + '?' + params;\n    return {\n      uri: _uri2,\n      hash: hash\n    };\n  }\n  var manifest2 = getManifest2();\n  var devServerUrl = manifest2 != null && (_manifest2$extra = manifest2.extra) != null && (_manifest2$extra$expo = _manifest2$extra.expoGo) != null && _manifest2$extra$expo.developer ? 'http://' + manifest2.extra.expoGo.debuggerHost : getManifest().developer ? getManifest().bundleUrl : null;\n  if (devServerUrl) {\n    var baseUrl = new URL(meta.httpServerLocation + suffix, devServerUrl);\n    baseUrl.searchParams.set('platform', Platform.OS);\n    baseUrl.searchParams.set('hash', meta.hash);\n    return {\n      uri: baseUrl.href,\n      hash: hash\n    };\n  }\n  if (NativeModules.ExponentKernel) {\n    return {\n      uri: `https://classic-assets.eascdn.net/~assets/${encodeURIComponent(hash)}`,\n      hash: hash\n    };\n  }\n  return {\n    uri: '',\n    hash: hash\n  };\n}\nexport function resolveUri(uri) {\n  return manifestBaseUrl ? new URL(uri, manifestBaseUrl).href : uri;\n}\nexport function pathJoin() {\n  for (var _len = arguments.length, paths = new Array(_len), _key = 0; _key < _len; _key++) {\n    paths[_key] = arguments[_key];\n  }\n  var combined = paths.map(function (part, index) {\n    if (index === 0) {\n      return part.trim().replace(/\\/*$/, '');\n    }\n    return part.trim().replace(/(^\\/*|\\/*$)/g, '');\n  }).filter(function (part) {\n    return part.length > 0;\n  }).join('/').split('/');\n  var resolved = [];\n  for (var part of combined) {\n    if (part === '..') {\n      resolved.pop();\n    } else if (part !== '.') {\n      resolved.push(part);\n    }\n  }\n  return resolved.join('/');\n}", "map": {"version": 3, "names": ["Platform", "PixelRatio", "NativeModules", "AssetSourceResolver", "getManifest", "getManifest2", "manifestBaseUrl", "assetMapOverride", "selectAssetSource", "meta", "_meta$fileHashes$inde", "_meta$fileUris$index", "_manifest2$extra", "_manifest2$extra$expo", "hasOwnProperty", "hash", "_objectSpread", "scale", "pickScale", "scales", "get", "index", "findIndex", "s", "fileHashes", "uri", "fileUris", "resolve<PERSON>ri", "assetUrlOverride", "pathJoin", "fileScale", "fileExtension", "type", "encodeURIComponent", "suffix", "name", "params", "URLSearchParams", "platform", "OS", "test", "httpServerLocation", "manifest2", "devServerUrl", "extra", "expoGo", "developer", "debuggerHost", "bundleUrl", "baseUrl", "URL", "searchParams", "set", "href", "ExponentKernel", "_len", "arguments", "length", "paths", "Array", "_key", "combined", "map", "part", "trim", "replace", "filter", "join", "split", "resolved", "pop", "push"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-asset\\src\\AssetSources.ts"], "sourcesContent": ["import type { PackagerAsset } from '@react-native/assets-registry/registry';\nimport { Platform } from 'expo-modules-core';\nimport { PixelRatio, NativeModules } from 'react-native';\n\nimport AssetSourceResolver from './AssetSourceResolver';\nimport { getManifest, getManifest2, manifestBaseUrl } from './PlatformUtils';\n\n// @docsMissing\nexport type AssetMetadata = Pick<\n  PackagerAsset,\n  'httpServerLocation' | 'name' | 'hash' | 'type' | 'scales' | 'width' | 'height'\n> & {\n  uri?: string;\n  fileHashes?: string[];\n  fileUris?: string[];\n};\n\nexport type AssetSource = {\n  uri: string;\n  hash: string;\n};\n\n// Fast lookup check if asset map has any overrides in the manifest.\n// This value will always be either null or an absolute URL, e.g. `https://expo.dev/`\nconst assetMapOverride = getManifest().assetMapOverride;\n\n/**\n * Selects the best file for the given asset (ex: choosing the best scale for images) and returns\n * a { uri, hash } pair for the specific asset file.\n *\n * If the asset isn't an image with multiple scales, the first file is selected.\n */\nexport function selectAssetSource(meta: AssetMetadata): AssetSource {\n  // Override with the asset map in manifest if available\n  if (assetMapOverride && assetMapOverride.hasOwnProperty(meta.hash)) {\n    meta = { ...meta, ...assetMapOverride[meta.hash] };\n  }\n\n  // This logic is based on that of AssetSourceResolver, with additional support for file hashes and\n  // explicitly provided URIs\n  const scale = AssetSourceResolver.pickScale(meta.scales, PixelRatio.get());\n  const index = meta.scales.findIndex((s) => s === scale);\n  const hash = meta.fileHashes ? meta.fileHashes[index] ?? meta.fileHashes[0] : meta.hash;\n\n  // Allow asset processors to directly provide the URL to load\n  const uri = meta.fileUris ? meta.fileUris[index] ?? meta.fileUris[0] : meta.uri;\n  if (uri) {\n    return { uri: resolveUri(uri), hash };\n  }\n\n  // Check if the assetUrl was overridden in the manifest\n  const assetUrlOverride = getManifest().assetUrlOverride;\n  if (assetUrlOverride) {\n    const uri = pathJoin(assetUrlOverride, hash);\n    return { uri: resolveUri(uri), hash };\n  }\n\n  const fileScale = scale === 1 ? '' : `@${scale}x`;\n  const fileExtension = meta.type ? `.${encodeURIComponent(meta.type)}` : '';\n  const suffix = `/${encodeURIComponent(meta.name)}${fileScale}${fileExtension}`;\n  const params = new URLSearchParams({\n    platform: Platform.OS,\n    hash: meta.hash,\n  });\n\n  // For assets with a specified absolute URL, we use the existing origin instead of prepending the\n  // development server or production CDN URL origin\n  if (/^https?:\\/\\//.test(meta.httpServerLocation)) {\n    const uri = meta.httpServerLocation + suffix + '?' + params;\n    return { uri, hash };\n  }\n\n  // For assets during development using manifest2, we use the development server's URL origin\n  const manifest2 = getManifest2();\n\n  const devServerUrl = manifest2?.extra?.expoGo?.developer\n    ? 'http://' + manifest2.extra.expoGo.debuggerHost\n    : // For assets during development, we use the development server's URL origin\n    getManifest().developer\n    ? getManifest().bundleUrl\n    : null;\n  if (devServerUrl) {\n    const baseUrl = new URL(meta.httpServerLocation + suffix, devServerUrl);\n\n    baseUrl.searchParams.set('platform', Platform.OS);\n    baseUrl.searchParams.set('hash', meta.hash);\n    return {\n      uri: baseUrl.href,\n      hash,\n    };\n  }\n\n  // Temporary fallback for loading assets in Expo Go home\n  if (NativeModules.ExponentKernel) {\n    return { uri: `https://classic-assets.eascdn.net/~assets/${encodeURIComponent(hash)}`, hash };\n  }\n\n  // In correctly configured apps, we arrive here if the asset is locally available on disk due to\n  // being managed by expo-updates, and `getLocalAssetUri(hash)` must return a local URI for this\n  // hash. Since the asset is local, we don't have a remote URL and specify an invalid URL (an empty\n  // string) as a placeholder.\n  return { uri: '', hash };\n}\n\n/**\n * Resolves the given URI to an absolute URI. If the given URI is already an absolute URI, it is\n * simply returned. Otherwise, if it is a relative URI, it is resolved relative to the manifest's\n * base URI.\n */\nexport function resolveUri(uri: string): string {\n  // `manifestBaseUrl` is always an absolute URL or `null`.\n  return manifestBaseUrl ? new URL(uri, manifestBaseUrl).href : uri;\n}\n\n// A very cheap path canonicalization like path.join but without depending on a `path` polyfill.\nexport function pathJoin(...paths: string[]): string {\n  // Start by simply combining paths, without worrying about \"..\" or \".\"\n  const combined = paths\n    .map((part, index) => {\n      if (index === 0) {\n        return part.trim().replace(/\\/*$/, '');\n      }\n      return part.trim().replace(/(^\\/*|\\/*$)/g, '');\n    })\n    .filter((part) => part.length > 0)\n    .join('/')\n    .split('/');\n\n  // Handle \"..\" and \".\" in paths\n  const resolved: string[] = [];\n  for (const part of combined) {\n    if (part === '..') {\n      resolved.pop(); // Remove the last element from the result\n    } else if (part !== '.') {\n      resolved.push(part);\n    }\n  }\n\n  return resolved.join('/');\n}\n"], "mappings": ";;;AACA,SAASA,QAAQ,QAAQ,mBAAmB;AAAC,OAAAC,UAAA;AAAA,OAAAC,aAAA;AAG7C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,WAAW,EAAEC,YAAY,EAAEC,eAAe,QAAQ,iBAAiB;AAmB5E,IAAMC,gBAAgB,GAAGH,WAAW,EAAE,CAACG,gBAAgB;AAQvD,OAAM,SAAUC,iBAAiBA,CAACC,IAAmB;EAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EAEnD,IAAIN,gBAAgB,IAAIA,gBAAgB,CAACO,cAAc,CAACL,IAAI,CAACM,IAAI,CAAC,EAAE;IAClEN,IAAI,GAAAO,aAAA,CAAAA,aAAA,KAAQP,IAAI,GAAKF,gBAAgB,CAACE,IAAI,CAACM,IAAI,CAAC,CAAE;;EAKpD,IAAME,KAAK,GAAGd,mBAAmB,CAACe,SAAS,CAACT,IAAI,CAACU,MAAM,EAAElB,UAAU,CAACmB,GAAG,EAAE,CAAC;EAC1E,IAAMC,KAAK,GAAGZ,IAAI,CAACU,MAAM,CAACG,SAAS,CAAC,UAACC,CAAC;IAAA,OAAKA,CAAC,KAAKN,KAAK;EAAA,EAAC;EACvD,IAAMF,IAAI,GAAGN,IAAI,CAACe,UAAU,IAAAd,qBAAA,GAAGD,IAAI,CAACe,UAAU,CAACH,KAAK,CAAC,YAAAX,qBAAA,GAAID,IAAI,CAACe,UAAU,CAAC,CAAC,CAAC,GAAGf,IAAI,CAACM,IAAI;EAGvF,IAAMU,GAAG,GAAGhB,IAAI,CAACiB,QAAQ,IAAAf,oBAAA,GAAGF,IAAI,CAACiB,QAAQ,CAACL,KAAK,CAAC,YAAAV,oBAAA,GAAIF,IAAI,CAACiB,QAAQ,CAAC,CAAC,CAAC,GAAGjB,IAAI,CAACgB,GAAG;EAC/E,IAAIA,GAAG,EAAE;IACP,OAAO;MAAEA,GAAG,EAAEE,UAAU,CAACF,GAAG,CAAC;MAAEV,IAAI,EAAJA;IAAI,CAAE;;EAIvC,IAAMa,gBAAgB,GAAGxB,WAAW,EAAE,CAACwB,gBAAgB;EACvD,IAAIA,gBAAgB,EAAE;IACpB,IAAMH,IAAG,GAAGI,QAAQ,CAACD,gBAAgB,EAAEb,IAAI,CAAC;IAC5C,OAAO;MAAEU,GAAG,EAAEE,UAAU,CAACF,IAAG,CAAC;MAAEV,IAAI,EAAJA;IAAI,CAAE;;EAGvC,IAAMe,SAAS,GAAGb,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,IAAIA,KAAK,GAAG;EACjD,IAAMc,aAAa,GAAGtB,IAAI,CAACuB,IAAI,GAAG,IAAIC,kBAAkB,CAACxB,IAAI,CAACuB,IAAI,CAAC,EAAE,GAAG,EAAE;EAC1E,IAAME,MAAM,GAAG,IAAID,kBAAkB,CAACxB,IAAI,CAAC0B,IAAI,CAAC,GAAGL,SAAS,GAAGC,aAAa,EAAE;EAC9E,IAAMK,MAAM,GAAG,IAAIC,eAAe,CAAC;IACjCC,QAAQ,EAAEtC,QAAQ,CAACuC,EAAE;IACrBxB,IAAI,EAAEN,IAAI,CAACM;GACZ,CAAC;EAIF,IAAI,cAAc,CAACyB,IAAI,CAAC/B,IAAI,CAACgC,kBAAkB,CAAC,EAAE;IAChD,IAAMhB,KAAG,GAAGhB,IAAI,CAACgC,kBAAkB,GAAGP,MAAM,GAAG,GAAG,GAAGE,MAAM;IAC3D,OAAO;MAAEX,GAAG,EAAHA,KAAG;MAAEV,IAAI,EAAJA;IAAI,CAAE;;EAItB,IAAM2B,SAAS,GAAGrC,YAAY,EAAE;EAEhC,IAAMsC,YAAY,GAAGD,SAAS,aAAA9B,gBAAA,GAAT8B,SAAS,CAAEE,KAAK,cAAA/B,qBAAA,GAAhBD,gBAAA,CAAkBiC,MAAM,aAAxBhC,qBAAA,CAA0BiC,SAAS,GACpD,SAAS,GAAGJ,SAAS,CAACE,KAAK,CAACC,MAAM,CAACE,YAAY,GAEjD3C,WAAW,EAAE,CAAC0C,SAAS,GACrB1C,WAAW,EAAE,CAAC4C,SAAS,GACvB,IAAI;EACR,IAAIL,YAAY,EAAE;IAChB,IAAMM,OAAO,GAAG,IAAIC,GAAG,CAACzC,IAAI,CAACgC,kBAAkB,GAAGP,MAAM,EAAES,YAAY,CAAC;IAEvEM,OAAO,CAACE,YAAY,CAACC,GAAG,CAAC,UAAU,EAAEpD,QAAQ,CAACuC,EAAE,CAAC;IACjDU,OAAO,CAACE,YAAY,CAACC,GAAG,CAAC,MAAM,EAAE3C,IAAI,CAACM,IAAI,CAAC;IAC3C,OAAO;MACLU,GAAG,EAAEwB,OAAO,CAACI,IAAI;MACjBtC,IAAI,EAAJA;KACD;;EAIH,IAAIb,aAAa,CAACoD,cAAc,EAAE;IAChC,OAAO;MAAE7B,GAAG,EAAE,6CAA6CQ,kBAAkB,CAAClB,IAAI,CAAC,EAAE;MAAEA,IAAI,EAAJA;IAAI,CAAE;;EAO/F,OAAO;IAAEU,GAAG,EAAE,EAAE;IAAEV,IAAI,EAAJA;EAAI,CAAE;AAC1B;AAOA,OAAM,SAAUY,UAAUA,CAACF,GAAW;EAEpC,OAAOnB,eAAe,GAAG,IAAI4C,GAAG,CAACzB,GAAG,EAAEnB,eAAe,CAAC,CAAC+C,IAAI,GAAG5B,GAAG;AACnE;AAGA,OAAM,SAAUI,QAAQA,CAAA,EAAmB;EAAA,SAAA0B,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAfC,KAAe,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAfF,KAAe,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEzC,IAAMC,QAAQ,GAAGH,KAAK,CACnBI,GAAG,CAAC,UAACC,IAAI,EAAE1C,KAAK,EAAI;IACnB,IAAIA,KAAK,KAAK,CAAC,EAAE;MACf,OAAO0C,IAAI,CAACC,IAAI,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;;IAExC,OAAOF,IAAI,CAACC,IAAI,EAAE,CAACC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;EAChD,CAAC,CAAC,CACDC,MAAM,CAAC,UAACH,IAAI;IAAA,OAAKA,IAAI,CAACN,MAAM,GAAG,CAAC;EAAA,EAAC,CACjCU,IAAI,CAAC,GAAG,CAAC,CACTC,KAAK,CAAC,GAAG,CAAC;EAGb,IAAMC,QAAQ,GAAa,EAAE;EAC7B,KAAK,IAAMN,IAAI,IAAIF,QAAQ,EAAE;IAC3B,IAAIE,IAAI,KAAK,IAAI,EAAE;MACjBM,QAAQ,CAACC,GAAG,EAAE;KACf,MAAM,IAAIP,IAAI,KAAK,GAAG,EAAE;MACvBM,QAAQ,CAACE,IAAI,CAACR,IAAI,CAAC;;;EAIvB,OAAOM,QAAQ,CAACF,IAAI,CAAC,GAAG,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}