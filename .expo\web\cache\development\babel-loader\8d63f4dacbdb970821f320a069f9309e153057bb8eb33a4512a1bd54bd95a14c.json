{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport { CodedError, UnavailabilityError } from 'expo-modules-core';\nimport ExpoFontLoader from \"./ExpoFontLoader\";\nimport { FontDisplay } from \"./Font.types\";\nimport { getAssetForSource, loadSingleFontAsync, fontFamilyNeedsScoping, getNativeFontName } from \"./FontLoader\";\nvar loaded = {};\nvar loadPromises = {};\nexport function processFontFamily(fontFamily) {\n  if (!fontFamily || !fontFamilyNeedsScoping(fontFamily)) {\n    return fontFamily;\n  }\n  if (!isLoaded(fontFamily)) {\n    if (__DEV__) {\n      if (isLoading(fontFamily)) {\n        console.error(`You started loading the font \"${fontFamily}\", but used it before it finished loading. You need to wait for Font.loadAsync to complete before using the font.`);\n      } else {\n        console.error(`fontFamily \"${fontFamily}\" is not a system font and has not been loaded through Font.loadAsync.\\n\n- If you intended to use a system font, make sure you typed the name correctly and that it is supported by your device operating system.\\n\n- If this is a custom font, be sure to load it with Font.loadAsync.`);\n      }\n    }\n    return 'System';\n  }\n  return `ExpoFont-${getNativeFontName(fontFamily)}`;\n}\nexport function isLoaded(fontFamily) {\n  return fontFamily in loaded;\n}\nexport function isLoading(fontFamily) {\n  return fontFamily in loadPromises;\n}\nexport function loadAsync(_x, _x2) {\n  return _loadAsync.apply(this, arguments);\n}\nfunction _loadAsync() {\n  _loadAsync = _asyncToGenerator(function* (fontFamilyOrFontMap, source) {\n    if (typeof fontFamilyOrFontMap === 'object') {\n      if (source) {\n        throw new CodedError(`ERR_FONT_API`, `No fontFamily can be used for the provided source: ${source}. The second argument of \\`loadAsync()\\` can only be used with a \\`string\\` value as the first argument.`);\n      }\n      var fontMap = fontFamilyOrFontMap;\n      var names = Object.keys(fontMap);\n      yield Promise.all(names.map(function (name) {\n        return loadFontInNamespaceAsync(name, fontMap[name]);\n      }));\n      return;\n    }\n    return yield loadFontInNamespaceAsync(fontFamilyOrFontMap, source);\n  });\n  return _loadAsync.apply(this, arguments);\n}\nfunction loadFontInNamespaceAsync(_x3, _x4) {\n  return _loadFontInNamespaceAsync.apply(this, arguments);\n}\nfunction _loadFontInNamespaceAsync() {\n  _loadFontInNamespaceAsync = _asyncToGenerator(function* (fontFamily, source) {\n    if (!source) {\n      throw new CodedError(`ERR_FONT_SOURCE`, `Cannot load null or undefined font source: { \"${fontFamily}\": ${source} }. Expected asset of type \\`FontSource\\` for fontFamily of name: \"${fontFamily}\"`);\n    }\n    if (loaded[fontFamily]) {\n      return;\n    }\n    if (loadPromises.hasOwnProperty(fontFamily)) {\n      return loadPromises[fontFamily];\n    }\n    var asset = getAssetForSource(source);\n    loadPromises[fontFamily] = _asyncToGenerator(function* () {\n      try {\n        yield loadSingleFontAsync(fontFamily, asset);\n        loaded[fontFamily] = true;\n      } finally {\n        delete loadPromises[fontFamily];\n      }\n    })();\n    yield loadPromises[fontFamily];\n  });\n  return _loadFontInNamespaceAsync.apply(this, arguments);\n}\nexport function unloadAllAsync() {\n  return _unloadAllAsync.apply(this, arguments);\n}\nfunction _unloadAllAsync() {\n  _unloadAllAsync = _asyncToGenerator(function* () {\n    if (!ExpoFontLoader.unloadAllAsync) {\n      throw new UnavailabilityError('expo-font', 'unloadAllAsync');\n    }\n    if (Object.keys(loadPromises).length) {\n      throw new CodedError(`ERR_UNLOAD`, `Cannot unload fonts while they're still loading: ${Object.keys(loadPromises).join(', ')}`);\n    }\n    for (var fontFamily of Object.keys(loaded)) {\n      delete loaded[fontFamily];\n    }\n    yield ExpoFontLoader.unloadAllAsync();\n  });\n  return _unloadAllAsync.apply(this, arguments);\n}\nexport function unloadAsync(_x5, _x6) {\n  return _unloadAsync.apply(this, arguments);\n}\nfunction _unloadAsync() {\n  _unloadAsync = _asyncToGenerator(function* (fontFamilyOrFontMap, options) {\n    if (!ExpoFontLoader.unloadAsync) {\n      throw new UnavailabilityError('expo-font', 'unloadAsync');\n    }\n    if (typeof fontFamilyOrFontMap === 'object') {\n      if (options) {\n        throw new CodedError(`ERR_FONT_API`, `No fontFamily can be used for the provided options: ${options}. The second argument of \\`unloadAsync()\\` can only be used with a \\`string\\` value as the first argument.`);\n      }\n      var fontMap = fontFamilyOrFontMap;\n      var names = Object.keys(fontMap);\n      yield Promise.all(names.map(function (name) {\n        return unloadFontInNamespaceAsync(name, fontMap[name]);\n      }));\n      return;\n    }\n    return yield unloadFontInNamespaceAsync(fontFamilyOrFontMap, options);\n  });\n  return _unloadAsync.apply(this, arguments);\n}\nfunction unloadFontInNamespaceAsync(_x7, _x8) {\n  return _unloadFontInNamespaceAsync.apply(this, arguments);\n}\nfunction _unloadFontInNamespaceAsync() {\n  _unloadFontInNamespaceAsync = _asyncToGenerator(function* (fontFamily, options) {\n    if (!loaded[fontFamily]) {\n      return;\n    } else {\n      delete loaded[fontFamily];\n    }\n    var nativeFontName = getNativeFontName(fontFamily);\n    if (!nativeFontName) {\n      throw new CodedError(`ERR_FONT_FAMILY`, `Cannot unload an empty name`);\n    }\n    yield ExpoFontLoader.unloadAsync(nativeFontName, options);\n  });\n  return _unloadFontInNamespaceAsync.apply(this, arguments);\n}\nexport { FontDisplay };", "map": {"version": 3, "names": ["CodedError", "UnavailabilityError", "ExpoFontLoader", "FontDisplay", "getAssetForSource", "loadSingleFontAsync", "fontFamilyNeedsScoping", "getNativeFontName", "loaded", "loadPromises", "processFontFamily", "fontFamily", "isLoaded", "__DEV__", "isLoading", "console", "error", "loadAsync", "_x", "_x2", "_loadAsync", "apply", "arguments", "_asyncToGenerator", "fontFamilyOrFontMap", "source", "fontMap", "names", "Object", "keys", "Promise", "all", "map", "name", "loadFontInNamespaceAsync", "_x3", "_x4", "_loadFontInNamespaceAsync", "hasOwnProperty", "asset", "unloadAllAsync", "_unloadAllAsync", "length", "join", "unloadAsync", "_x5", "_x6", "_unloadAsync", "options", "unloadFontInNamespaceAsync", "_x7", "_x8", "_unloadFontInNamespaceAsync", "nativeFontName"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-font\\src\\Font.ts"], "sourcesContent": ["import { CodedError, UnavailabilityError } from 'expo-modules-core';\n\nimport ExpoFontLoader from './ExpoFontLoader';\nimport { FontDisplay, FontSource, FontResource, UnloadFontOptions } from './Font.types';\nimport {\n  getAssetForSource,\n  loadSingleFontAsync,\n  fontFamilyNeedsScoping,\n  getNativeFontName,\n} from './FontLoader';\n\nconst loaded: { [name: string]: boolean } = {};\nconst loadPromises: { [name: string]: Promise<void> } = {};\n\n// @needsAudit\n// note(brentvatne): at some point we may want to warn if this is called outside of a managed app.\n/**\n * Used to transform font family names to the scoped name. This does not need to\n * be called in standalone or bare apps but it will return unscoped font family\n * names if it is called in those contexts.\n *\n * @param fontFamily Name of font to process.\n * @returns Returns a name processed for use with the [current workflow](https://docs.expo.dev/archive/managed-vs-bare/).\n */\nexport function processFontFamily(fontFamily: string | null): string | null {\n  if (!fontFamily || !fontFamilyNeedsScoping(fontFamily)) {\n    return fontFamily;\n  }\n\n  if (!isLoaded(fontFamily)) {\n    if (__DEV__) {\n      if (isLoading(fontFamily)) {\n        console.error(\n          `You started loading the font \"${fontFamily}\", but used it before it finished loading. You need to wait for Font.loadAsync to complete before using the font.`\n        );\n      } else {\n        console.error(\n          `fontFamily \"${fontFamily}\" is not a system font and has not been loaded through Font.loadAsync.\\n\n- If you intended to use a system font, make sure you typed the name correctly and that it is supported by your device operating system.\\n\n- If this is a custom font, be sure to load it with Font.loadAsync.`\n        );\n      }\n    }\n\n    return 'System';\n  }\n\n  return `ExpoFont-${getNativeFontName(fontFamily)}`;\n}\n\n// @needsAudit\n/**\n * Synchronously detect if the font for `fontFamily` has finished loading.\n *\n * @param fontFamily The name used to load the `FontResource`.\n * @return Returns `true` if the font has fully loaded.\n */\nexport function isLoaded(fontFamily: string): boolean {\n  return fontFamily in loaded;\n}\n\n// @needsAudit\n/**\n * Synchronously detect if the font for `fontFamily` is still being loaded.\n *\n * @param fontFamily The name used to load the `FontResource`.\n * @returns Returns `true` if the font is still loading.\n */\nexport function isLoading(fontFamily: string): boolean {\n  return fontFamily in loadPromises;\n}\n\n// @needsAudit\n/**\n * Highly efficient method for loading fonts from static or remote resources which can then be used\n * with the platform's native text elements. In the browser this generates a `@font-face` block in\n * a shared style sheet for fonts. No CSS is needed to use this method.\n *\n * @param fontFamilyOrFontMap string or map of values that can be used as the [`fontFamily`](https://reactnative.dev/docs/text#style)\n * style prop with React Native Text elements.\n * @param source the font asset that should be loaded into the `fontFamily` namespace.\n *\n * @return Returns a promise that fulfils when the font has loaded. Often you may want to wrap the\n * method in a `try/catch/finally` to ensure the app continues if the font fails to load.\n */\nexport async function loadAsync(\n  fontFamilyOrFontMap: string | Record<string, FontSource>,\n  source?: FontSource\n): Promise<void> {\n  if (typeof fontFamilyOrFontMap === 'object') {\n    if (source) {\n      throw new CodedError(\n        `ERR_FONT_API`,\n        `No fontFamily can be used for the provided source: ${source}. The second argument of \\`loadAsync()\\` can only be used with a \\`string\\` value as the first argument.`\n      );\n    }\n    const fontMap = fontFamilyOrFontMap;\n    const names = Object.keys(fontMap);\n    await Promise.all(names.map((name) => loadFontInNamespaceAsync(name, fontMap[name])));\n    return;\n  }\n\n  return await loadFontInNamespaceAsync(fontFamilyOrFontMap, source);\n}\n\nasync function loadFontInNamespaceAsync(\n  fontFamily: string,\n  source?: FontSource | null\n): Promise<void> {\n  if (!source) {\n    throw new CodedError(\n      `ERR_FONT_SOURCE`,\n      `Cannot load null or undefined font source: { \"${fontFamily}\": ${source} }. Expected asset of type \\`FontSource\\` for fontFamily of name: \"${fontFamily}\"`\n    );\n  }\n\n  if (loaded[fontFamily]) {\n    return;\n  }\n\n  if (loadPromises.hasOwnProperty(fontFamily)) {\n    return loadPromises[fontFamily];\n  }\n\n  // Important: we want all callers that concurrently try to load the same font to await the same\n  // promise. If we're here, we haven't created the promise yet. To ensure we create only one\n  // promise in the program, we need to create the promise synchronously without yielding the event\n  // loop from this point.\n\n  const asset = getAssetForSource(source);\n  loadPromises[fontFamily] = (async () => {\n    try {\n      await loadSingleFontAsync(fontFamily, asset);\n      loaded[fontFamily] = true;\n    } finally {\n      delete loadPromises[fontFamily];\n    }\n  })();\n\n  await loadPromises[fontFamily];\n}\n\n// @needsAudit\n/**\n * Unloads all the custom fonts. This is used for testing.\n */\nexport async function unloadAllAsync(): Promise<void> {\n  if (!ExpoFontLoader.unloadAllAsync) {\n    throw new UnavailabilityError('expo-font', 'unloadAllAsync');\n  }\n\n  if (Object.keys(loadPromises).length) {\n    throw new CodedError(\n      `ERR_UNLOAD`,\n      `Cannot unload fonts while they're still loading: ${Object.keys(loadPromises).join(', ')}`\n    );\n  }\n\n  for (const fontFamily of Object.keys(loaded)) {\n    delete loaded[fontFamily];\n  }\n\n  await ExpoFontLoader.unloadAllAsync();\n}\n\n// @needsAudit\n/**\n * Unload custom fonts matching the `fontFamily`s and display values provided.\n * Because fonts are automatically unloaded on every platform this is mostly used for testing.\n *\n * @param fontFamilyOrFontMap The name or names of the custom fonts that will be unloaded.\n * @param options When `fontFamilyOrFontMap` is a string, this should be the font source used to load\n * the custom font originally.\n */\nexport async function unloadAsync(\n  fontFamilyOrFontMap: string | Record<string, UnloadFontOptions>,\n  options?: UnloadFontOptions\n): Promise<void> {\n  if (!ExpoFontLoader.unloadAsync) {\n    throw new UnavailabilityError('expo-font', 'unloadAsync');\n  }\n  if (typeof fontFamilyOrFontMap === 'object') {\n    if (options) {\n      throw new CodedError(\n        `ERR_FONT_API`,\n        `No fontFamily can be used for the provided options: ${options}. The second argument of \\`unloadAsync()\\` can only be used with a \\`string\\` value as the first argument.`\n      );\n    }\n    const fontMap = fontFamilyOrFontMap;\n    const names = Object.keys(fontMap);\n    await Promise.all(names.map((name) => unloadFontInNamespaceAsync(name, fontMap[name])));\n    return;\n  }\n\n  return await unloadFontInNamespaceAsync(fontFamilyOrFontMap, options);\n}\n\nasync function unloadFontInNamespaceAsync(\n  fontFamily: string,\n  options?: UnloadFontOptions | null\n): Promise<void> {\n  if (!loaded[fontFamily]) {\n    return;\n  } else {\n    delete loaded[fontFamily];\n  }\n\n  // Important: we want all callers that concurrently try to load the same font to await the same\n  // promise. If we're here, we haven't created the promise yet. To ensure we create only one\n  // promise in the program, we need to create the promise synchronously without yielding the event\n  // loop from this point.\n\n  const nativeFontName = getNativeFontName(fontFamily);\n\n  if (!nativeFontName) {\n    throw new CodedError(`ERR_FONT_FAMILY`, `Cannot unload an empty name`);\n  }\n\n  await ExpoFontLoader.unloadAsync(nativeFontName, options);\n}\n\nexport { FontDisplay, FontSource, FontResource, UnloadFontOptions };\n"], "mappings": ";AAAA,SAASA,UAAU,EAAEC,mBAAmB,QAAQ,mBAAmB;AAEnE,OAAOC,cAAc;AACrB,SAASC,WAAW;AACpB,SACEC,iBAAiB,EACjBC,mBAAmB,EACnBC,sBAAsB,EACtBC,iBAAiB;AAGnB,IAAMC,MAAM,GAAgC,EAAE;AAC9C,IAAMC,YAAY,GAAsC,EAAE;AAY1D,OAAM,SAAUC,iBAAiBA,CAACC,UAAyB;EACzD,IAAI,CAACA,UAAU,IAAI,CAACL,sBAAsB,CAACK,UAAU,CAAC,EAAE;IACtD,OAAOA,UAAU;;EAGnB,IAAI,CAACC,QAAQ,CAACD,UAAU,CAAC,EAAE;IACzB,IAAIE,OAAO,EAAE;MACX,IAAIC,SAAS,CAACH,UAAU,CAAC,EAAE;QACzBI,OAAO,CAACC,KAAK,CACX,iCAAiCL,UAAU,mHAAmH,CAC/J;OACF,MAAM;QACLI,OAAO,CAACC,KAAK,CACX,eAAeL,UAAU;;oEAEiC,CAC3D;;;IAIL,OAAO,QAAQ;;EAGjB,OAAO,YAAYJ,iBAAiB,CAACI,UAAU,CAAC,EAAE;AACpD;AASA,OAAM,SAAUC,QAAQA,CAACD,UAAkB;EACzC,OAAOA,UAAU,IAAIH,MAAM;AAC7B;AASA,OAAM,SAAUM,SAASA,CAACH,UAAkB;EAC1C,OAAOA,UAAU,IAAIF,YAAY;AACnC;AAeA,gBAAsBQ,SAASA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAkB9B,SAAAF,WAAA;EAAAA,UAAA,GAAAG,iBAAA,CAlBM,WACLC,mBAAwD,EACxDC,MAAmB;IAEnB,IAAI,OAAOD,mBAAmB,KAAK,QAAQ,EAAE;MAC3C,IAAIC,MAAM,EAAE;QACV,MAAM,IAAIzB,UAAU,CAClB,cAAc,EACd,sDAAsDyB,MAAM,0GAA0G,CACvK;;MAEH,IAAMC,OAAO,GAAGF,mBAAmB;MACnC,IAAMG,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC;MAClC,MAAMI,OAAO,CAACC,GAAG,CAACJ,KAAK,CAACK,GAAG,CAAC,UAACC,IAAI;QAAA,OAAKC,wBAAwB,CAACD,IAAI,EAAEP,OAAO,CAACO,IAAI,CAAC,CAAC;MAAA,EAAC,CAAC;MACrF;;IAGF,aAAaC,wBAAwB,CAACV,mBAAmB,EAAEC,MAAM,CAAC;EACpE,CAAC;EAAA,OAAAL,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEcY,wBAAwBA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,yBAAA,CAAAhB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAe,0BAAA;EAAAA,yBAAA,GAAAd,iBAAA,CAAvC,WACEZ,UAAkB,EAClBc,MAA0B;IAE1B,IAAI,CAACA,MAAM,EAAE;MACX,MAAM,IAAIzB,UAAU,CAClB,iBAAiB,EACjB,iDAAiDW,UAAU,MAAMc,MAAM,sEAAsEd,UAAU,GAAG,CAC3J;;IAGH,IAAIH,MAAM,CAACG,UAAU,CAAC,EAAE;MACtB;;IAGF,IAAIF,YAAY,CAAC6B,cAAc,CAAC3B,UAAU,CAAC,EAAE;MAC3C,OAAOF,YAAY,CAACE,UAAU,CAAC;;IAQjC,IAAM4B,KAAK,GAAGnC,iBAAiB,CAACqB,MAAM,CAAC;IACvChB,YAAY,CAACE,UAAU,CAAC,GAAGY,iBAAA,CAAC,aAAW;MACrC,IAAI;QACF,MAAMlB,mBAAmB,CAACM,UAAU,EAAE4B,KAAK,CAAC;QAC5C/B,MAAM,CAACG,UAAU,CAAC,GAAG,IAAI;OAC1B,SAAS;QACR,OAAOF,YAAY,CAACE,UAAU,CAAC;;IAEnC,CAAC,EAAC,CAAE;IAEJ,MAAMF,YAAY,CAACE,UAAU,CAAC;EAChC,CAAC;EAAA,OAAA0B,yBAAA,CAAAhB,KAAA,OAAAC,SAAA;AAAA;AAMD,gBAAsBkB,cAAcA,CAAA;EAAA,OAAAC,eAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAiBnC,SAAAmB,gBAAA;EAAAA,eAAA,GAAAlB,iBAAA,CAjBM,aAA6B;IAClC,IAAI,CAACrB,cAAc,CAACsC,cAAc,EAAE;MAClC,MAAM,IAAIvC,mBAAmB,CAAC,WAAW,EAAE,gBAAgB,CAAC;;IAG9D,IAAI2B,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACiC,MAAM,EAAE;MACpC,MAAM,IAAI1C,UAAU,CAClB,YAAY,EACZ,oDAAoD4B,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACkC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC3F;;IAGH,KAAK,IAAMhC,UAAU,IAAIiB,MAAM,CAACC,IAAI,CAACrB,MAAM,CAAC,EAAE;MAC5C,OAAOA,MAAM,CAACG,UAAU,CAAC;;IAG3B,MAAMT,cAAc,CAACsC,cAAc,EAAE;EACvC,CAAC;EAAA,OAAAC,eAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAWD,gBAAsBsB,WAAWA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,YAAA,CAAA1B,KAAA,OAAAC,SAAA;AAAA;AAqBhC,SAAAyB,aAAA;EAAAA,YAAA,GAAAxB,iBAAA,CArBM,WACLC,mBAA+D,EAC/DwB,OAA2B;IAE3B,IAAI,CAAC9C,cAAc,CAAC0C,WAAW,EAAE;MAC/B,MAAM,IAAI3C,mBAAmB,CAAC,WAAW,EAAE,aAAa,CAAC;;IAE3D,IAAI,OAAOuB,mBAAmB,KAAK,QAAQ,EAAE;MAC3C,IAAIwB,OAAO,EAAE;QACX,MAAM,IAAIhD,UAAU,CAClB,cAAc,EACd,uDAAuDgD,OAAO,4GAA4G,CAC3K;;MAEH,IAAMtB,OAAO,GAAGF,mBAAmB;MACnC,IAAMG,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC;MAClC,MAAMI,OAAO,CAACC,GAAG,CAACJ,KAAK,CAACK,GAAG,CAAC,UAACC,IAAI;QAAA,OAAKgB,0BAA0B,CAAChB,IAAI,EAAEP,OAAO,CAACO,IAAI,CAAC,CAAC;MAAA,EAAC,CAAC;MACvF;;IAGF,aAAagB,0BAA0B,CAACzB,mBAAmB,EAAEwB,OAAO,CAAC;EACvE,CAAC;EAAA,OAAAD,YAAA,CAAA1B,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEc2B,0BAA0BA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,2BAAA,CAAA/B,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA8B,4BAAA;EAAAA,2BAAA,GAAA7B,iBAAA,CAAzC,WACEZ,UAAkB,EAClBqC,OAAkC;IAElC,IAAI,CAACxC,MAAM,CAACG,UAAU,CAAC,EAAE;MACvB;KACD,MAAM;MACL,OAAOH,MAAM,CAACG,UAAU,CAAC;;IAQ3B,IAAM0C,cAAc,GAAG9C,iBAAiB,CAACI,UAAU,CAAC;IAEpD,IAAI,CAAC0C,cAAc,EAAE;MACnB,MAAM,IAAIrD,UAAU,CAAC,iBAAiB,EAAE,6BAA6B,CAAC;;IAGxE,MAAME,cAAc,CAAC0C,WAAW,CAACS,cAAc,EAAEL,OAAO,CAAC;EAC3D,CAAC;EAAA,OAAAI,2BAAA,CAAA/B,KAAA,OAAAC,SAAA;AAAA;AAED,SAASnB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}