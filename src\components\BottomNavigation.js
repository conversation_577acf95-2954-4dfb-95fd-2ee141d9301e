import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors, Typography, Spacing, Animations } from '../constants';

const navigationItems = [
  {
    id: 1,
    name: 'Home',
    icon: 'home',
    label: 'Beranda',
    badge: 0,
  },
  {
    id: 2,
    name: 'Activities',
    icon: 'assignment',
    label: 'Aktivitas',
    badge: 3,
  },
  {
    id: 3,
    name: 'FAB', // This will be the FAB space
    icon: 'add',
    label: '',
    badge: 0,
  },
  {
    id: 4,
    name: 'Notifications',
    icon: 'notifications',
    label: 'Notifikasi',
    badge: 5,
  },
  {
    id: 5,
    name: 'Profile',
    icon: 'person',
    label: 'Profil',
    badge: 0,
  },
];

const BottomNavigation = () => {
  const [activeTab, setActiveTab] = React.useState('Home');
  const fabScale = React.useRef(new Animated.Value(1)).current;
  const tabAnimations = React.useRef(
    navigationItems.reduce((acc, item) => {
      acc[item.name] = new Animated.Value(item.name === 'Home' ? 1 : 0.8);
      return acc;
    }, {})
  ).current;

  const handleTabPress = (item) => {
    if (item.name === 'FAB') {
      handleFABPress();
      return;
    }

    setActiveTab(item.name);
    
    // Animate tab selection
    Object.keys(tabAnimations).forEach((key) => {
      Animated.spring(tabAnimations[key], {
        toValue: key === item.name ? 1 : 0.8,
        ...Animations.spring.default,
        useNativeDriver: true,
      }).start();
    });

    console.log(`Navigate to ${item.name}`);
  };

  const handleFABPress = () => {
    // Animate FAB press
    Animated.sequence([
      Animated.spring(fabScale, {
        toValue: 0.9,
        ...Animations.spring.default,
        useNativeDriver: true,
      }),
      Animated.spring(fabScale, {
        toValue: 1,
        ...Animations.spring.bouncy,
        useNativeDriver: true,
      }),
    ]).start();

    // Show quick actions menu
    Alert.alert(
      'Quick Actions',
      'Pilih aksi cepat yang ingin dilakukan',
      [
        {
          text: 'Check-in/out',
          onPress: () => console.log('Quick check-in'),
        },
        {
          text: 'Laporan Cepat',
          onPress: () => console.log('Quick report'),
        },
        {
          text: 'Emergency',
          onPress: () => console.log('Emergency action'),
          style: 'destructive',
        },
        {
          text: 'Batal',
          style: 'cancel',
        },
      ]
    );
  };

  const renderTabItem = (item) => {
    if (item.name === 'FAB') {
      return (
        <View key={item.id} style={styles.fabContainer}>
          <Animated.View style={{ transform: [{ scale: fabScale }] }}>
            <TouchableOpacity
              style={styles.fab}
              onPress={() => handleTabPress(item)}
              activeOpacity={0.8}
            >
              <MaterialIcons
                name={item.icon}
                size={Spacing.iconSize.lg}
                color={Colors.onPrimary}
              />
            </TouchableOpacity>
          </Animated.View>
        </View>
      );
    }

    const isActive = activeTab === item.name;
    
    return (
      <Animated.View
        key={item.id}
        style={[
          styles.tabItem,
          { transform: [{ scale: tabAnimations[item.name] }] },
        ]}
      >
        <TouchableOpacity
          style={styles.tabButton}
          onPress={() => handleTabPress(item)}
          activeOpacity={0.7}
        >
          <View style={styles.iconContainer}>
            <MaterialIcons
              name={item.icon}
              size={Spacing.iconSize.md}
              color={isActive ? Colors.bottomNavActive : Colors.bottomNavInactive}
            />
            {item.badge > 0 && (
              <View style={styles.badge}>
                <Text style={styles.badgeText}>
                  {item.badge > 9 ? '9+' : item.badge}
                </Text>
              </View>
            )}
          </View>
          <Text
            style={[
              styles.tabLabel,
              { color: isActive ? Colors.bottomNavActive : Colors.bottomNavInactive },
            ]}
          >
            {item.label}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.navigationBar}>
        {navigationItems.map(renderTabItem)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  navigationBar: {
    flexDirection: 'row',
    backgroundColor: Colors.bottomNavBackground,
    paddingTop: Spacing.padding.sm,
    paddingBottom: Spacing.padding.md,
    paddingHorizontal: Spacing.padding.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.outline,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
  },
  tabButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.padding.xs,
  },
  iconContainer: {
    position: 'relative',
    marginBottom: Spacing.xs / 2,
  },
  tabLabel: {
    ...Typography.caption,
    fontSize: 10,
    textAlign: 'center',
  },
  fabContainer: {
    flex: 1,
    alignItems: 'center',
    marginTop: -20, // Elevate FAB above the navigation bar
  },
  fab: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: Colors.error,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.bottomNavBackground,
  },
  badgeText: {
    ...Typography.caption,
    color: Colors.onPrimary,
    fontSize: 9,
    fontWeight: '600',
  },
});

export default BottomNavigation;
