{"version": 3, "sources": ["GestureHandlerRootView.web.tsx"], "names": ["GestureHandlerRootView", "props"], "mappings": ";;;;;;;AAAA;;AAEA;;AACA;;;;;;;;AAKe,SAASA,sBAAT,CACbC,KADa,EAEb;AACA,sBACE,oBAAC,sCAAD,CAA+B,QAA/B;AAAwC,IAAA,KAAK;AAA7C,kBACE,oBAAC,iBAAD,EAAUA,KAAV,CADF,CADF;AAKD", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport { View, ViewProps } from 'react-native';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\n\nexport interface GestureHandlerRootViewProps\n  extends PropsWithChildren<ViewProps> {}\n\nexport default function GestureHandlerRootView(\n  props: GestureHandlerRootViewProps\n) {\n  return (\n    <GestureHandlerRootViewContext.Provider value>\n      <View {...props} />\n    </GestureHandlerRootViewContext.Provider>\n  );\n}\n"]}