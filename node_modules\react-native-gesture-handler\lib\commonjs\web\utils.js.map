{"version": 3, "sources": ["utils.ts"], "names": ["isPointerInBounds", "view", "x", "y", "rect", "getBoundingClientRect", "left", "right", "top", "bottom"], "mappings": ";;;;;;;AAAO,SAASA,iBAAT,CACLC,IADK,EAEL;AAAEC,EAAAA,CAAF;AAAKC,EAAAA;AAAL,CAFK,EAGI;AACT,QAAMC,IAAa,GAAGH,IAAI,CAACI,qBAAL,EAAtB;AAEA,SAAOH,CAAC,IAAIE,IAAI,CAACE,IAAV,IAAkBJ,CAAC,IAAIE,IAAI,CAACG,KAA5B,IAAqCJ,CAAC,IAAIC,IAAI,CAACI,GAA/C,IAAsDL,CAAC,IAAIC,IAAI,CAACK,MAAvE;AACD", "sourcesContent": ["export function isPointerInBounds(\n  view: HTMLElement,\n  { x, y }: { x: number; y: number }\n): boolean {\n  const rect: DOMRect = view.getBoundingClientRect();\n\n  return x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;\n}\n"]}