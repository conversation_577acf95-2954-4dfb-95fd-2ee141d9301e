{"ast": null, "code": "var byteToHex = [];\nfor (var i = 0; i < 256; ++i) {\n  byteToHex[i] = (i + 0x100).toString(16).substr(1);\n}\nfunction bytesToUuid(buf, offset) {\n  var i = offset || 0;\n  var bth = byteToHex;\n  return [bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], '-', bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]]].join('');\n}\nexport default bytesToUuid;", "map": {"version": 3, "names": ["byteToHex", "i", "toString", "substr", "bytesToUuid", "buf", "offset", "bth", "join"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-modules-core\\src\\uuid\\lib\\bytesToUuid.ts"], "sourcesContent": ["/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\nconst byteToHex: string[] = [];\nfor (let i = 0; i < 256; ++i) {\n  byteToHex[i] = (i + 0x100).toString(16).substr(1);\n}\n\nfunction bytesToUuid(buf: number[], offset?: number) {\n  let i = offset || 0;\n  const bth = byteToHex;\n  // join used to fix memory issue caused by concatenation: https://bugs.chromium.org/p/v8/issues/detail?id=3175#c4\n  return [\n    bth[buf[i++]],\n    bth[buf[i++]],\n    bth[buf[i++]],\n    bth[buf[i++]],\n    '-',\n    bth[buf[i++]],\n    bth[buf[i++]],\n    '-',\n    bth[buf[i++]],\n    bth[buf[i++]],\n    '-',\n    bth[buf[i++]],\n    bth[buf[i++]],\n    '-',\n    bth[buf[i++]],\n    bth[buf[i++]],\n    bth[buf[i++]],\n    bth[buf[i++]],\n    bth[buf[i++]],\n    bth[buf[i++]],\n  ].join('');\n}\n\nexport default bytesToUuid;\n"], "mappings": "AAIA,IAAMA,SAAS,GAAa,EAAE;AAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;EAC5BD,SAAS,CAACC,CAAC,CAAC,GAAG,CAACA,CAAC,GAAG,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;;AAGnD,SAASC,WAAWA,CAACC,GAAa,EAAEC,MAAe;EACjD,IAAIL,CAAC,GAAGK,MAAM,IAAI,CAAC;EACnB,IAAMC,GAAG,GAAGP,SAAS;EAErB,OAAO,CACLO,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACb,GAAG,EACHM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACb,GAAG,EACHM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACb,GAAG,EACHM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACb,GAAG,EACHM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,EACbM,GAAG,CAACF,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,CACd,CAACO,IAAI,CAAC,EAAE,CAAC;AACZ;AAEA,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}