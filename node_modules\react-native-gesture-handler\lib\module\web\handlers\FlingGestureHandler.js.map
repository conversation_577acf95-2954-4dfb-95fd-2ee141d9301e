{"version": 3, "sources": ["FlingGestureHandler.ts"], "names": ["State", "Direction", "Gesture<PERSON>andler", "DEFAULT_MAX_DURATION_MS", "DEFAULT_MIN_ACCEPTABLE_DELTA", "DEFAULT_DIRECTION", "RIGHT", "DEFAULT_NUMBER_OF_TOUCHES_REQUIRED", "FlingGestureHandler", "NaN", "init", "ref", "propsRef", "updateGestureConfig", "enabled", "props", "config", "direction", "numberOfPointers", "numberOfPointersRequired", "startFling", "startX", "tracker", "getLastX", "keyPointer", "startY", "getLastY", "begin", "maxNumberOfPointersSimultaneously", "delayTimeout", "setTimeout", "fail", "maxDurationMs", "tryEndFling", "minAcceptableDel<PERSON>", "LEFT", "UP", "DOWN", "clearTimeout", "activate", "endFling", "onPointerDown", "event", "addToTracker", "pointerId", "newPointerAction", "onPointerAdd", "currentState", "UNDETERMINED", "BEGAN", "getTrackedPointersCount", "onPointerMove", "track", "onPointerUp", "onUp", "onPointerRemove", "removeFromTracker", "force", "end", "resetConfig"], "mappings": ";;AAAA,SAASA,KAAT,QAAsB,aAAtB;AACA,SAASC,SAAT,QAA0B,cAA1B;AAGA,OAAOC,cAAP,MAA2B,kBAA3B;AAEA,MAAMC,uBAAuB,GAAG,GAAhC;AACA,MAAMC,4BAA4B,GAAG,EAArC;AACA,MAAMC,iBAAiB,GAAGJ,SAAS,CAACK,KAApC;AACA,MAAMC,kCAAkC,GAAG,CAA3C;AAEA,eAAe,MAAMC,mBAAN,SAAkCN,cAAlC,CAAiD;AAAA;AAAA;;AAAA,sDAC3BK,kCAD2B;;AAAA,uCAE1CF,iBAF0C;;AAAA,2CAItCF,uBAJsC;;AAAA,gDAKjCC,4BALiC;;AAAA;;AAAA,oCAQ7C,CAR6C;;AAAA,oCAS7C,CAT6C;;AAAA,+DAWlB,CAXkB;;AAAA,wCAYzCK,GAZyC;AAAA;;AAcvDC,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAwD;AACjE,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AACD;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;;AAEA,QAAI,KAAKC,MAAL,CAAYC,SAAhB,EAA2B;AACzB,WAAKA,SAAL,GAAiB,KAAKD,MAAL,CAAYC,SAA7B;AACD;;AAED,QAAI,KAAKD,MAAL,CAAYE,gBAAhB,EAAkC;AAChC,WAAKC,wBAAL,GAAgC,KAAKH,MAAL,CAAYE,gBAA5C;AACD;AACF;;AAEOE,EAAAA,UAAU,GAAS;AACzB,SAAKC,MAAL,GAAc,KAAKC,OAAL,CAAaC,QAAb,CAAsB,KAAKC,UAA3B,CAAd;AACA,SAAKC,MAAL,GAAc,KAAKH,OAAL,CAAaI,QAAb,CAAsB,KAAKF,UAA3B,CAAd;AAEA,SAAKG,KAAL;AAEA,SAAKC,iCAAL,GAAyC,CAAzC;AAEA,SAAKC,YAAL,GAAoBC,UAAU,CAAC,MAAM,KAAKC,IAAL,EAAP,EAAoB,KAAKC,aAAzB,CAA9B;AACD;;AAEOC,EAAAA,WAAW,GAAY;AAC7B,QACE,KAAKL,iCAAL,KACE,KAAKT,wBADP,KAEE,KAAKF,SAAL,GAAiBhB,SAAS,CAACK,KAA3B,IACA,KAAKgB,OAAL,CAAaC,QAAb,CAAsB,KAAKC,UAA3B,IAAyC,KAAKH,MAA9C,GACE,KAAKa,kBAFR,IAGE,KAAKjB,SAAL,GAAiBhB,SAAS,CAACkC,IAA3B,IACC,KAAKd,MAAL,GAAc,KAAKC,OAAL,CAAaC,QAAb,CAAsB,KAAKC,UAA3B,CAAd,GACE,KAAKU,kBALV,IAME,KAAKjB,SAAL,GAAiBhB,SAAS,CAACmC,EAA3B,IACC,KAAKX,MAAL,GAAc,KAAKH,OAAL,CAAaI,QAAb,CAAsB,KAAKF,UAA3B,CAAd,GACE,KAAKU,kBARV,IASE,KAAKjB,SAAL,GAAiBhB,SAAS,CAACoC,IAA3B,IACC,KAAKf,OAAL,CAAaI,QAAb,CAAsB,KAAKF,UAA3B,IAAyC,KAAKC,MAA9C,GACE,KAAKS,kBAbX,CADF,EAeE;AACAI,MAAAA,YAAY,CAAC,KAAKT,YAAN,CAAZ;AACA,WAAKU,QAAL;AAEA,aAAO,IAAP;AACD;;AAED,WAAO,KAAP;AACD;;AAEOC,EAAAA,QAAQ,GAAG;AACjB,QAAI,CAAC,KAAKP,WAAL,EAAL,EAAyB;AACvB,WAAKF,IAAL;AACD;AACF;;AAESU,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAKpB,OAAL,CAAaqB,YAAb,CAA0BD,KAA1B;AACA,SAAKlB,UAAL,GAAkBkB,KAAK,CAACE,SAAxB;AAEA,UAAMH,aAAN,CAAoBC,KAApB;AACA,SAAKG,gBAAL;AACD;;AAESC,EAAAA,YAAY,CAACJ,KAAD,EAA4B;AAChD,SAAKpB,OAAL,CAAaqB,YAAb,CAA0BD,KAA1B;AACA,UAAMI,YAAN,CAAmBJ,KAAnB;AACA,SAAKG,gBAAL;AACD;;AAEOA,EAAAA,gBAAgB,GAAS;AAC/B,QAAI,KAAKE,YAAL,KAAsB/C,KAAK,CAACgD,YAAhC,EAA8C;AAC5C,WAAK5B,UAAL;AACD;;AAED,QAAI,KAAK2B,YAAL,KAAsB/C,KAAK,CAACiD,KAAhC,EAAuC;AACrC;AACD;;AAED,SAAKhB,WAAL;;AAEA,QACE,KAAKX,OAAL,CAAa4B,uBAAb,KACA,KAAKtB,iCAFP,EAGE;AACA,WAAKA,iCAAL,GACE,KAAKN,OAAL,CAAa4B,uBAAb,EADF;AAED;AACF;;AAESC,EAAAA,aAAa,CAACT,KAAD,EAA4B;AACjD,SAAKpB,OAAL,CAAa8B,KAAb,CAAmBV,KAAnB;;AAEA,QAAI,KAAKK,YAAL,KAAsB/C,KAAK,CAACiD,KAAhC,EAAuC;AACrC;AACD;;AAED,SAAKhB,WAAL;AAEA,UAAMkB,aAAN,CAAoBT,KAApB;AACD;;AAESW,EAAAA,WAAW,CAACX,KAAD,EAA4B;AAC/C,UAAMW,WAAN,CAAkBX,KAAlB;AACA,SAAKY,IAAL,CAAUZ,KAAV;AAEA,SAAKlB,UAAL,GAAkBf,GAAlB;AACD;;AAES8C,EAAAA,eAAe,CAACb,KAAD,EAA4B;AACnD,UAAMa,eAAN,CAAsBb,KAAtB;AACA,SAAKY,IAAL,CAAUZ,KAAV;AACD;;AAEOY,EAAAA,IAAI,CAACZ,KAAD,EAA4B;AACtC,QAAI,KAAKK,YAAL,KAAsB/C,KAAK,CAACiD,KAAhC,EAAuC;AACrC,WAAKT,QAAL;AACD;;AAED,SAAKlB,OAAL,CAAakC,iBAAb,CAA+Bd,KAAK,CAACE,SAArC;AACD;;AAEML,EAAAA,QAAQ,CAACkB,KAAD,EAAwB;AACrC,UAAMlB,QAAN,CAAekB,KAAf;AACA,SAAKC,GAAL;AACD;;AAESC,EAAAA,WAAW,GAAS;AAC5B,UAAMA,WAAN;AACA,SAAKxC,wBAAL,GAAgCZ,kCAAhC;AACA,SAAKU,SAAL,GAAiBZ,iBAAjB;AACD;;AApJ6D", "sourcesContent": ["import { State } from '../../State';\nimport { Direction } from '../constants';\nimport { AdaptedEvent, Config } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\n\nconst DEFAULT_MAX_DURATION_MS = 800;\nconst DEFAULT_MIN_ACCEPTABLE_DELTA = 32;\nconst DEFAULT_DIRECTION = Direction.RIGHT;\nconst DEFAULT_NUMBER_OF_TOUCHES_REQUIRED = 1;\n\nexport default class FlingGestureHandler extends GestureHandler {\n  private numberOfPointersRequired = DEFAULT_NUMBER_OF_TOUCHES_REQUIRED;\n  private direction = DEFAULT_DIRECTION;\n\n  private maxDurationMs = DEFAULT_MAX_DURATION_MS;\n  private minAcceptableDelta = DEFAULT_MIN_ACCEPTABLE_DELTA;\n  private delayTimeout!: number;\n\n  private startX = 0;\n  private startY = 0;\n\n  private maxNumberOfPointersSimultaneously = 0;\n  private keyPointer = NaN;\n\n  public init(ref: number, propsRef: React.RefObject<unknown>): void {\n    super.init(ref, propsRef);\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n\n    if (this.config.direction) {\n      this.direction = this.config.direction;\n    }\n\n    if (this.config.numberOfPointers) {\n      this.numberOfPointersRequired = this.config.numberOfPointers;\n    }\n  }\n\n  private startFling(): void {\n    this.startX = this.tracker.getLastX(this.keyPointer);\n    this.startY = this.tracker.getLastY(this.keyPointer);\n\n    this.begin();\n\n    this.maxNumberOfPointersSimultaneously = 1;\n\n    this.delayTimeout = setTimeout(() => this.fail(), this.maxDurationMs);\n  }\n\n  private tryEndFling(): boolean {\n    if (\n      this.maxNumberOfPointersSimultaneously ===\n        this.numberOfPointersRequired &&\n      ((this.direction & Direction.RIGHT &&\n        this.tracker.getLastX(this.keyPointer) - this.startX >\n          this.minAcceptableDelta) ||\n        (this.direction & Direction.LEFT &&\n          this.startX - this.tracker.getLastX(this.keyPointer) >\n            this.minAcceptableDelta) ||\n        (this.direction & Direction.UP &&\n          this.startY - this.tracker.getLastY(this.keyPointer) >\n            this.minAcceptableDelta) ||\n        (this.direction & Direction.DOWN &&\n          this.tracker.getLastY(this.keyPointer) - this.startY >\n            this.minAcceptableDelta))\n    ) {\n      clearTimeout(this.delayTimeout);\n      this.activate();\n\n      return true;\n    }\n\n    return false;\n  }\n\n  private endFling() {\n    if (!this.tryEndFling()) {\n      this.fail();\n    }\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    this.keyPointer = event.pointerId;\n\n    super.onPointerDown(event);\n    this.newPointerAction();\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n    this.newPointerAction();\n  }\n\n  private newPointerAction(): void {\n    if (this.currentState === State.UNDETERMINED) {\n      this.startFling();\n    }\n\n    if (this.currentState !== State.BEGAN) {\n      return;\n    }\n\n    this.tryEndFling();\n\n    if (\n      this.tracker.getTrackedPointersCount() >\n      this.maxNumberOfPointersSimultaneously\n    ) {\n      this.maxNumberOfPointersSimultaneously =\n        this.tracker.getTrackedPointersCount();\n    }\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n\n    if (this.currentState !== State.BEGAN) {\n      return;\n    }\n\n    this.tryEndFling();\n\n    super.onPointerMove(event);\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.onUp(event);\n\n    this.keyPointer = NaN;\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.onUp(event);\n  }\n\n  private onUp(event: AdaptedEvent): void {\n    if (this.currentState === State.BEGAN) {\n      this.endFling();\n    }\n\n    this.tracker.removeFromTracker(event.pointerId);\n  }\n\n  public activate(force?: boolean): void {\n    super.activate(force);\n    this.end();\n  }\n\n  protected resetConfig(): void {\n    super.resetConfig();\n    this.numberOfPointersRequired = DEFAULT_NUMBER_OF_TOUCHES_REQUIRED;\n    this.direction = DEFAULT_DIRECTION;\n  }\n}\n"]}