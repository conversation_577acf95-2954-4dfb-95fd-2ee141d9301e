{"ast": null, "code": "var isWebColor = function isWebColor(color) {\n  return color === 'currentcolor' || color === 'currentColor' || color === 'inherit' || color.indexOf('var(') === 0;\n};\nexport default isWebColor;", "map": {"version": 3, "names": ["isWebColor", "color", "indexOf"], "sources": ["D:/aplikasi/TRAE/psg-bmi/node_modules/react-native-web/dist/modules/isWebColor/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar isWebColor = color => color === 'currentcolor' || color === 'currentColor' || color === 'inherit' || color.indexOf('var(') === 0;\nexport default isWebColor;"], "mappings": "AASA,IAAIA,UAAU,GAAG,SAAbA,UAAUA,CAAGC,KAAK;EAAA,OAAIA,KAAK,KAAK,cAAc,IAAIA,KAAK,KAAK,cAAc,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,CAACC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AAAA;AACpI,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}