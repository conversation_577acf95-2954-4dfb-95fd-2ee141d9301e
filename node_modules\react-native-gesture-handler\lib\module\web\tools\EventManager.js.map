{"version": 3, "sources": ["EventManager.ts"], "names": ["EventManager", "constructor", "view", "activePointersCounter", "onPointerDown", "_event", "onPointerAdd", "onPointerUp", "onPointerRemove", "onPointerMove", "onPointerLeave", "onPointerEnter", "onPointerCancel", "onPointerOutOfBounds", "onPointerMoveOver", "onPointerMoveOut", "setOnPointerDown", "callback", "setOnPointerAdd", "setOnPointerUp", "setOnPointerRemove", "setOnPointerMove", "setOnPointerLeave", "setOnPointerEnter", "setOnPointerCancel", "setOnPointerOutOfBounds", "setOnPointerMoveOver", "setOnPointerMoveOut", "markAsInBounds", "pointerId", "pointersInBounds", "indexOf", "push", "markAsOutOfBounds", "index", "splice", "resetManager"], "mappings": ";;AAAA;AAGA,eAAe,MAAeA,YAAf,CAA+B;AAK5CC,EAAAA,WAAW,CAACC,IAAD,EAAU;AAAA;;AAAA,8CAHkB,EAGlB;;AAAA;;AACnB,SAAKA,IAAL,GAAYA,IAAZ;AACA,SAAKC,qBAAL,GAA6B,CAA7B;AACD;;AAUSC,EAAAA,aAAa,CAACC,MAAD,EAA6B,CAAE;;AAC5CC,EAAAA,YAAY,CAACD,MAAD,EAA6B,CAAE;;AAC3CE,EAAAA,WAAW,CAACF,MAAD,EAA6B,CAAE;;AAC1CG,EAAAA,eAAe,CAACH,MAAD,EAA6B,CAAE;;AAC9CI,EAAAA,aAAa,CAACJ,MAAD,EAA6B,CAAE;;AAC5CK,EAAAA,cAAc,CAACL,MAAD,EAA6B,CAAE,CAvBX,CAuBY;;;AAC9CM,EAAAA,cAAc,CAACN,MAAD,EAA6B,CAAE,CAxBX,CAwBY;;;AAC9CO,EAAAA,eAAe,CAACP,MAAD,EAA6B,CACpD;AACA;AACA;AACA;AACD;;AACSQ,EAAAA,oBAAoB,CAACR,MAAD,EAA6B,CAAE;;AACnDS,EAAAA,iBAAiB,CAACT,MAAD,EAA6B,CAAE;;AAChDU,EAAAA,gBAAgB,CAACV,MAAD,EAA6B,CAAE;;AAElDW,EAAAA,gBAAgB,CAACC,QAAD,EAAgD;AACrE,SAAKb,aAAL,GAAqBa,QAArB;AACD;;AACMC,EAAAA,eAAe,CAACD,QAAD,EAAgD;AACpE,SAAKX,YAAL,GAAoBW,QAApB;AACD;;AACME,EAAAA,cAAc,CAACF,QAAD,EAAgD;AACnE,SAAKV,WAAL,GAAmBU,QAAnB;AACD;;AACMG,EAAAA,kBAAkB,CAACH,QAAD,EAAgD;AACvE,SAAKT,eAAL,GAAuBS,QAAvB;AACD;;AACMI,EAAAA,gBAAgB,CAACJ,QAAD,EAAgD;AACrE,SAAKR,aAAL,GAAqBQ,QAArB;AACD;;AACMK,EAAAA,iBAAiB,CAACL,QAAD,EAAgD;AACtE,SAAKP,cAAL,GAAsBO,QAAtB;AACD;;AACMM,EAAAA,iBAAiB,CAACN,QAAD,EAAgD;AACtE,SAAKN,cAAL,GAAsBM,QAAtB;AACD;;AACMO,EAAAA,kBAAkB,CAACP,QAAD,EAAgD;AACvE,SAAKL,eAAL,GAAuBK,QAAvB;AACD;;AACMQ,EAAAA,uBAAuB,CAC5BR,QAD4B,EAEtB;AACN,SAAKJ,oBAAL,GAA4BI,QAA5B;AACD;;AACMS,EAAAA,oBAAoB,CAACT,QAAD,EAAgD;AACzE,SAAKH,iBAAL,GAAyBG,QAAzB;AACD;;AACMU,EAAAA,mBAAmB,CAACV,QAAD,EAAgD;AACxE,SAAKF,gBAAL,GAAwBE,QAAxB;AACD;;AAESW,EAAAA,cAAc,CAACC,SAAD,EAA0B;AAChD,QAAI,KAAKC,gBAAL,CAAsBC,OAAtB,CAA8BF,SAA9B,KAA4C,CAAhD,EAAmD;AACjD;AACD;;AAED,SAAKC,gBAAL,CAAsBE,IAAtB,CAA2BH,SAA3B;AACD;;AAESI,EAAAA,iBAAiB,CAACJ,SAAD,EAA0B;AACnD,UAAMK,KAAa,GAAG,KAAKJ,gBAAL,CAAsBC,OAAtB,CAA8BF,SAA9B,CAAtB;;AAEA,QAAIK,KAAK,GAAG,CAAZ,EAAe;AACb;AACD;;AAED,SAAKJ,gBAAL,CAAsBK,MAAtB,CAA6BD,KAA7B,EAAoC,CAApC;AACD;;AAEME,EAAAA,YAAY,GAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AAEA,SAAKjC,qBAAL,GAA6B,CAA7B;AACA,SAAK2B,gBAAL,GAAwB,EAAxB;AACD;;AAnG2C", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-empty-function */\nimport { AdaptedEvent, EventTypes, TouchEventType } from '../interfaces';\n\nexport default abstract class EventManager<T> {\n  protected readonly view: T;\n  protected pointersInBounds: number[] = [];\n  protected activePointersCounter: number;\n\n  constructor(view: T) {\n    this.view = view;\n    this.activePointersCounter = 0;\n  }\n\n  public abstract setListeners(): void;\n  protected abstract mapEvent(\n    event: Event,\n    eventType: EventTypes,\n    index?: number,\n    touchEventType?: TouchEventType\n  ): AdaptedEvent;\n\n  protected onPointerDown(_event: AdaptedEvent): void {}\n  protected onPointerAdd(_event: AdaptedEvent): void {}\n  protected onPointerUp(_event: AdaptedEvent): void {}\n  protected onPointerRemove(_event: AdaptedEvent): void {}\n  protected onPointerMove(_event: AdaptedEvent): void {}\n  protected onPointerLeave(_event: AdaptedEvent): void {} // called only when pointer is pressed (or touching)\n  protected onPointerEnter(_event: AdaptedEvent): void {} // called only when pointer is pressed (or touching)\n  protected onPointerCancel(_event: AdaptedEvent): void {\n    // When pointer cancel is triggered and there are more pointers on the view, only one pointer is cancelled\n    // Because we want all pointers to be cancelled by that event, we are doing it manually by reseting handler and changing activePointersCounter to 0\n    // Events that correspond to removing the pointer (pointerup, touchend) have condition, that they don't perform any action when activePointersCounter\n    // is equal to 0. This prevents counter from going to negative values, when pointers are removed from view after one of them has been cancelled\n  }\n  protected onPointerOutOfBounds(_event: AdaptedEvent): void {}\n  protected onPointerMoveOver(_event: AdaptedEvent): void {}\n  protected onPointerMoveOut(_event: AdaptedEvent): void {}\n\n  public setOnPointerDown(callback: (event: AdaptedEvent) => void): void {\n    this.onPointerDown = callback;\n  }\n  public setOnPointerAdd(callback: (event: AdaptedEvent) => void): void {\n    this.onPointerAdd = callback;\n  }\n  public setOnPointerUp(callback: (event: AdaptedEvent) => void): void {\n    this.onPointerUp = callback;\n  }\n  public setOnPointerRemove(callback: (event: AdaptedEvent) => void): void {\n    this.onPointerRemove = callback;\n  }\n  public setOnPointerMove(callback: (event: AdaptedEvent) => void): void {\n    this.onPointerMove = callback;\n  }\n  public setOnPointerLeave(callback: (event: AdaptedEvent) => void): void {\n    this.onPointerLeave = callback;\n  }\n  public setOnPointerEnter(callback: (event: AdaptedEvent) => void): void {\n    this.onPointerEnter = callback;\n  }\n  public setOnPointerCancel(callback: (event: AdaptedEvent) => void): void {\n    this.onPointerCancel = callback;\n  }\n  public setOnPointerOutOfBounds(\n    callback: (event: AdaptedEvent) => void\n  ): void {\n    this.onPointerOutOfBounds = callback;\n  }\n  public setOnPointerMoveOver(callback: (event: AdaptedEvent) => void): void {\n    this.onPointerMoveOver = callback;\n  }\n  public setOnPointerMoveOut(callback: (event: AdaptedEvent) => void): void {\n    this.onPointerMoveOut = callback;\n  }\n\n  protected markAsInBounds(pointerId: number): void {\n    if (this.pointersInBounds.indexOf(pointerId) >= 0) {\n      return;\n    }\n\n    this.pointersInBounds.push(pointerId);\n  }\n\n  protected markAsOutOfBounds(pointerId: number): void {\n    const index: number = this.pointersInBounds.indexOf(pointerId);\n\n    if (index < 0) {\n      return;\n    }\n\n    this.pointersInBounds.splice(index, 1);\n  }\n\n  public resetManager(): void {\n    // Reseting activePointersCounter is necessary to make gestures such as pinch work properly\n    // There are gestures that end when there is still one active pointer (like pinch/rotation)\n    // When these gestures end, they are reset, but they still receive events from pointer that is active\n    // This causes trouble, since only onPointerDown registers gesture in orchestrator, and while gestures receive\n    // Events from active pointer after they finished, next pointerdown event will be registered as additional pointer, not the first one\n    // This casues trouble like gestures getting stuck in END state, even though they should have gone to UNDETERMINED\n\n    this.activePointersCounter = 0;\n    this.pointersInBounds = [];\n  }\n}\n"]}