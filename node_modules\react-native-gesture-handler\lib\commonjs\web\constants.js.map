{"version": 3, "sources": ["constants.ts"], "names": ["DEFAULT_TOUCH_SLOP", "Direction", "RIGHT", "LEFT", "UP", "DOWN"], "mappings": ";;;;;;AAAO,MAAMA,kBAAkB,GAAG,EAA3B;;AAEA,MAAMC,SAAS,GAAG;AACvBC,EAAAA,KAAK,EAAE,CADgB;AAEvBC,EAAAA,IAAI,EAAE,CAFiB;AAGvBC,EAAAA,EAAE,EAAE,CAHmB;AAIvBC,EAAAA,IAAI,EAAE;AAJiB,CAAlB", "sourcesContent": ["export const DEFAULT_TOUCH_SLOP = 15;\n\nexport const Direction = {\n  RIGHT: 1,\n  LEFT: 2,\n  UP: 4,\n  DOWN: 8,\n};\n"]}