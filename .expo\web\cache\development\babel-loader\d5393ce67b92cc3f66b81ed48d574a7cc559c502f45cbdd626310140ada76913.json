{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { SafeAreaProvider } from 'react-native-safe-area-context';\nimport { StatusBar } from 'expo-status-bar';\nimport * as SplashScreen from 'expo-splash-screen';\nimport ErrorBoundary from \"./src/components/ErrorBoundary\";\nimport HomeScreen from \"./src/screens/HomeScreen\";\nimport BottomNavigation from \"./src/components/BottomNavigation\";\nimport { Colors } from \"./src/constants\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nSplashScreen.preventAutoHideAsync();\nexport default function App() {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    appIsReady = _React$useState2[0],\n    setAppIsReady = _React$useState2[1];\n  React.useEffect(function () {\n    function prepare() {\n      return _prepare.apply(this, arguments);\n    }\n    function _prepare() {\n      _prepare = _asyncToGenerator(function* () {\n        try {\n          yield new Promise(function (resolve) {\n            return setTimeout(resolve, 1000);\n          });\n        } catch (e) {\n          console.warn(e);\n        } finally {\n          setAppIsReady(true);\n        }\n      });\n      return _prepare.apply(this, arguments);\n    }\n    prepare();\n  }, []);\n  var onLayoutRootView = React.useCallback(_asyncToGenerator(function* () {\n    if (appIsReady) {\n      yield SplashScreen.hideAsync();\n    }\n  }), [appIsReady]);\n  if (!appIsReady) {\n    return null;\n  }\n  return _jsx(ErrorBoundary, {\n    children: _jsx(SafeAreaProvider, {\n      children: _jsxs(View, {\n        style: styles.container,\n        onLayout: onLayoutRootView,\n        children: [_jsx(StatusBar, {\n          style: \"light\",\n          backgroundColor: Colors.primary\n        }), _jsx(View, {\n          style: styles.content,\n          children: _jsx(HomeScreen, {})\n        }), _jsx(BottomNavigation, {})]\n      })\n    })\n  });\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: Colors.background\n  },\n  content: {\n    flex: 1\n  }\n});", "map": {"version": 3, "names": ["React", "View", "StyleSheet", "SafeAreaProvider", "StatusBar", "SplashScreen", "Error<PERSON>ou<PERSON><PERSON>", "HomeScreen", "BottomNavigation", "Colors", "jsx", "_jsx", "jsxs", "_jsxs", "preventAutoHideAsync", "App", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "appIsReady", "setAppIsReady", "useEffect", "prepare", "_prepare", "apply", "arguments", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "e", "console", "warn", "onLayoutRootView", "useCallback", "<PERSON><PERSON><PERSON>", "children", "style", "styles", "container", "onLayout", "backgroundColor", "primary", "content", "create", "flex", "background"], "sources": ["D:/aplikasi/TRAE/psg-bmi/App.js"], "sourcesContent": ["import React from 'react';\nimport { View, StyleSheet } from 'react-native';\nimport { SafeAreaProvider } from 'react-native-safe-area-context';\nimport { StatusBar } from 'expo-status-bar';\nimport * as SplashScreen from 'expo-splash-screen';\n\n// Import components\nimport ErrorBoundary from './src/components/ErrorBoundary';\nimport HomeScreen from './src/screens/HomeScreen';\nimport BottomNavigation from './src/components/BottomNavigation';\nimport { Colors } from './src/constants';\n\n// Keep the splash screen visible while we fetch resources\nSplashScreen.preventAutoHideAsync();\n\nexport default function App() {\n  const [appIsReady, setAppIsReady] = React.useState(false);\n\n  React.useEffect(() => {\n    async function prepare() {\n      try {\n        // Pre-load fonts, make any API calls you need to do here\n        // For now, we'll just simulate a loading delay\n        await new Promise(resolve => setTimeout(resolve, 1000));\n      } catch (e) {\n        console.warn(e);\n      } finally {\n        // Tell the application to render\n        setAppIsReady(true);\n      }\n    }\n\n    prepare();\n  }, []);\n\n  const onLayoutRootView = React.useCallback(async () => {\n    if (appIsReady) {\n      // This tells the splash screen to hide immediately\n      await SplashScreen.hideAsync();\n    }\n  }, [appIsReady]);\n\n  if (!appIsReady) {\n    return null;\n  }\n\n  return (\n    <ErrorBoundary>\n      <SafeAreaProvider>\n        <View style={styles.container} onLayout={onLayoutRootView}>\n          <StatusBar style=\"light\" backgroundColor={Colors.primary} />\n          \n          {/* Main Content */}\n          <View style={styles.content}>\n            <HomeScreen />\n          </View>\n          \n          {/* Bottom Navigation */}\n          <BottomNavigation />\n        </View>\n      </SafeAreaProvider>\n    </ErrorBoundary>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: Colors.background,\n  },\n  content: {\n    flex: 1,\n  },\n});\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAE1B,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,KAAKC,YAAY,MAAM,oBAAoB;AAGlD,OAAOC,aAAa;AACpB,OAAOC,UAAU;AACjB,OAAOC,gBAAgB;AACvB,SAASC,MAAM;AAA0B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAGzCR,YAAY,CAACS,oBAAoB,CAAC,CAAC;AAEnC,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC5B,IAAAC,eAAA,GAAoChB,KAAK,CAACiB,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAlDI,UAAU,GAAAF,gBAAA;IAAEG,aAAa,GAAAH,gBAAA;EAEhClB,KAAK,CAACsB,SAAS,CAAC,YAAM;IAAA,SACLC,OAAOA,CAAA;MAAA,OAAAC,QAAA,CAAAC,KAAA,OAAAC,SAAA;IAAA;IAAA,SAAAF,SAAA;MAAAA,QAAA,GAAAG,iBAAA,CAAtB,aAAyB;QACvB,IAAI;UAGF,MAAM,IAAIC,OAAO,CAAC,UAAAC,OAAO;YAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;UAAA,EAAC;QACzD,CAAC,CAAC,OAAOE,CAAC,EAAE;UACVC,OAAO,CAACC,IAAI,CAACF,CAAC,CAAC;QACjB,CAAC,SAAS;UAERV,aAAa,CAAC,IAAI,CAAC;QACrB;MACF,CAAC;MAAA,OAAAG,QAAA,CAAAC,KAAA,OAAAC,SAAA;IAAA;IAEDH,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMW,gBAAgB,GAAGlC,KAAK,CAACmC,WAAW,CAAAR,iBAAA,CAAC,aAAY;IACrD,IAAIP,UAAU,EAAE;MAEd,MAAMf,YAAY,CAAC+B,SAAS,CAAC,CAAC;IAChC;EACF,CAAC,GAAE,CAAChB,UAAU,CAAC,CAAC;EAEhB,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,OACET,IAAA,CAACL,aAAa;IAAA+B,QAAA,EACZ1B,IAAA,CAACR,gBAAgB;MAAAkC,QAAA,EACfxB,KAAA,CAACZ,IAAI;QAACqC,KAAK,EAAEC,MAAM,CAACC,SAAU;QAACC,QAAQ,EAAEP,gBAAiB;QAAAG,QAAA,GACxD1B,IAAA,CAACP,SAAS;UAACkC,KAAK,EAAC,OAAO;UAACI,eAAe,EAAEjC,MAAM,CAACkC;QAAQ,CAAE,CAAC,EAG5DhC,IAAA,CAACV,IAAI;UAACqC,KAAK,EAAEC,MAAM,CAACK,OAAQ;UAAAP,QAAA,EAC1B1B,IAAA,CAACJ,UAAU,IAAE;QAAC,CACV,CAAC,EAGPI,IAAA,CAACH,gBAAgB,IAAE,CAAC;MAAA,CAChB;IAAC,CACS;EAAC,CACN,CAAC;AAEpB;AAEA,IAAM+B,MAAM,GAAGrC,UAAU,CAAC2C,MAAM,CAAC;EAC/BL,SAAS,EAAE;IACTM,IAAI,EAAE,CAAC;IACPJ,eAAe,EAAEjC,MAAM,CAACsC;EAC1B,CAAC;EACDH,OAAO,EAAE;IACPE,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}