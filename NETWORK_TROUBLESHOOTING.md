# 🌐 Network Troubleshooting Guide - PSG-BMI Portal

## 🚨 Current Issue: "Network response timed out"

### ✅ **Quick Solutions (Try in Order):**

#### **1. New QR Code (Port 8082)**
- **Current URL**: `exp://************:8082`
- **Status**: ✅ LAN mode active
- **Action**: Scan the new QR code in terminal

#### **2. Manual URL Input**
1. Open Expo Go app
2. Tap "Enter URL manually" 
3. Type: `exp://************:8082`
4. Press "Connect"

#### **3. Check Network Settings**
- Ensure phone and PC are on same WiFi network
- Try switching to mobile hotspot from phone
- Disable VPN if active

#### **4. Firewall Check**
```bash
# Windows: Allow port 8082 through firewall
# Or temporarily disable Windows Firewall for testing
```

### 🔄 **Alternative Testing Methods:**

#### **Method A: Tunnel Mode (Bypass Network)**
```bash
# If <PERSON><PERSON> works, try:
npx expo start --tunnel
```

#### **Method B: Different Port**
```bash
# Try different port:
npx expo start --port 3000
```

#### **Method C: Localhost Testing**
```bash
# Test on same machine:
npx expo start --localhost
```

#### **Method D: Web Version (Always Works)**
```bash
npx expo start --web
# Access: http://localhost:19006
```

### 📱 **Mobile Hotspot Solution**
If WiFi has restrictions:
1. Enable mobile hotspot on your phone
2. Connect PC to phone's hotspot
3. Restart Expo server
4. Use new IP address in QR code

### 🔧 **Advanced Solutions:**

#### **1. Development Build**
```bash
npx expo start
# Press 's' to switch to development build
# More stable than Expo Go
```

#### **2. EAS Build (Production-like)**
```bash
npx expo install @expo/cli
npx eas build --platform android --profile development
```

#### **3. Android Emulator**
```bash
# If you have Android Studio:
npx expo start --android
```

### 🌐 **Network Diagnostics:**

#### **Check IP Configuration:**
```bash
# Windows
ipconfig
# Look for IPv4 Address of your WiFi adapter

# Ping test from phone browser:
# Open browser on phone, go to: http://************:8082
```

#### **Port Testing:**
```bash
# Check if port is accessible:
netstat -an | findstr :8082
```

### 📋 **Troubleshooting Checklist:**

- [ ] Phone and PC on same WiFi network
- [ ] Expo Go app updated to latest version
- [ ] Windows Firewall allows Expo/Node.js
- [ ] No VPN active on either device
- [ ] Router allows device-to-device communication
- [ ] Correct IP address in QR code
- [ ] Port 8082 not blocked

### 🎯 **Success Indicators:**

When working correctly, you should see:
- ✅ QR code scans successfully
- ✅ "Downloading JavaScript bundle" progress
- ✅ App loads with PSG-BMI Portal interface
- ✅ No timeout or network errors

### 🚨 **If All Else Fails:**

#### **Option 1: Web Testing**
- Access: `http://localhost:19006`
- Full functionality available
- Same UI/UX as mobile

#### **Option 2: Android Emulator**
- Install Android Studio
- Create virtual device
- Run: `npx expo start --android`

#### **Option 3: iOS Simulator (Mac only)**
- Install Xcode
- Run: `npx expo start --ios`

---

## 📞 **Current Status:**

- ✅ **Server**: Running on port 8082
- ✅ **Mode**: LAN mode active  
- ✅ **URL**: `exp://************:8082`
- ✅ **Web**: Available at `http://localhost:19006`
- 🔄 **Mobile**: Ready for testing with new QR code

**Next Step**: Try scanning the new QR code or input URL manually in Expo Go!
