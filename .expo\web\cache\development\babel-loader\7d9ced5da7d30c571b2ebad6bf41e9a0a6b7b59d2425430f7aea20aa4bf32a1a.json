{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport * as Font from \"expo-font\";\nimport React from \"react\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport createIconSet from \"./vendor/react-native-vector-icons/lib/create-icon-set\";\nimport createIconButtonComponent from \"./vendor/react-native-vector-icons/lib/icon-button\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport { DEFAULT_ICON_COLOR, DEFAULT_ICON_SIZE } from \"./vendor/react-native-vector-icons/lib/create-icon-set\";\nexport default function (glyphMap, fontName, expoAssetId, fontStyle) {\n  var _a;\n  var font = _defineProperty({}, fontName, expoAssetId);\n  var RNVIconComponent = createIconSet(glyphMap, fontName, null, fontStyle);\n  return _a = function (_React$Component) {\n    function Icon() {\n      var _this;\n      _classCallCheck(this, Icon);\n      _this = _callSuper(this, Icon, arguments);\n      _this._mounted = false;\n      _this.state = {\n        fontIsLoaded: Font.isLoaded(fontName)\n      };\n      return _this;\n    }\n    _inherits(Icon, _React$Component);\n    return _createClass(Icon, [{\n      key: \"componentDidMount\",\n      value: function () {\n        var _componentDidMount = _asyncToGenerator(function* () {\n          this._mounted = true;\n          if (!this.state.fontIsLoaded) {\n            yield Font.loadAsync(font);\n            this._mounted && this.setState({\n              fontIsLoaded: true\n            });\n          }\n        });\n        function componentDidMount() {\n          return _componentDidMount.apply(this, arguments);\n        }\n        return componentDidMount;\n      }()\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this._mounted = false;\n      }\n    }, {\n      key: \"setNativeProps\",\n      value: function setNativeProps(props) {\n        if (this._icon) {\n          this._icon.setNativeProps(props);\n        }\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        if (__DEV__ && this.props.name && !(this.props.name in glyphMap)) {\n          console.warn(`\"${this.props.name}\" is not a valid icon name for family \"${fontName}\"`);\n        }\n        if (!this.state.fontIsLoaded) {\n          return _jsx(Text, {});\n        }\n        return _jsx(RNVIconComponent, _objectSpread({\n          ref: function ref(view) {\n            _this2._icon = view;\n          }\n        }, this.props));\n      }\n    }]);\n  }(React.Component), _a.defaultProps = RNVIconComponent.defaultProps, _a.Button = createIconButtonComponent(_a), _a.glyphMap = glyphMap, _a.getRawGlyphMap = function () {\n    return glyphMap;\n  }, _a.getFontFamily = function () {\n    return fontName;\n  }, _a.loadFont = function () {\n    return Font.loadAsync(font);\n  }, _a.font = font, _a;\n}", "map": {"version": 3, "names": ["Font", "React", "Text", "createIconSet", "createIconButtonComponent", "jsx", "_jsx", "DEFAULT_ICON_COLOR", "DEFAULT_ICON_SIZE", "glyphMap", "fontName", "expoAssetId", "fontStyle", "font", "_defineProperty", "RNVIconComponent", "_a", "_React$Component", "Icon", "_this", "_classCallCheck", "_mounted", "state", "fontIsLoaded", "isLoaded", "_inherits", "_createClass", "key", "value", "_componentDidMount", "_asyncToGenerator", "loadAsync", "setState", "componentDidMount", "apply", "arguments", "componentWillUnmount", "setNativeProps", "props", "_icon", "render", "_this2", "__DEV__", "name", "console", "warn", "_objectSpread", "ref", "view", "Component", "defaultProps", "<PERSON><PERSON>", "getRawGlyphMap", "getFontFamily", "loadFont"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\@expo\\vector-icons\\src\\createIconSet.tsx"], "sourcesContent": ["import * as Font from \"expo-font\";\nimport React, { ComponentClass } from \"react\";\nimport {\n  Text,\n  TextProps,\n  TouchableHighlightProps,\n  ViewProps,\n  OpaqueColorValue,\n  TextStyle,\n  ViewStyle,\n} from \"react-native\";\n\nimport createIconSet from \"./vendor/react-native-vector-icons/lib/create-icon-set\";\nimport createIconButtonComponent from \"./vendor/react-native-vector-icons/lib/icon-button\";\n\nexport {\n  DEFAULT_ICON_COLOR,\n  DEFAULT_ICON_SIZE,\n} from \"./vendor/react-native-vector-icons/lib/create-icon-set\";\n\nexport interface IconProps<GLYPHS extends string> extends TextProps {\n  /**\n   * Size of the icon, can also be passed as fontSize in the style object.\n   *\n   * @default 12\n   */\n  size?: number;\n\n  /**\n   * Name of the icon to show\n   *\n   * See Icon Explorer app\n   * {@link https://expo.github.io/vector-icons/}\n   */\n  name: GLYPHS;\n\n  /**\n   * Color of the icon. Can be a string or OpaqueColorValue (returned from\n   * PlatformColor(..))\n   *\n   */\n  color?: string | OpaqueColorValue;\n}\n\nexport interface IconButtonProps<GLYPHS extends string>\n  extends IconProps<GLYPHS>,\n    ViewProps,\n    TouchableHighlightProps {\n  /**\n   * Text and icon color\n   * Use iconStyle or nest a Text component if you need different colors.\n   * Can be a string or OpaqueColorValue (returned from PlatformColor(..))\n   *\n   *  @default 'white'\n   */\n  color?: string | OpaqueColorValue;\n\n  /**\n   * Border radius of the button\n   * Set to 0 to disable.\n   *\n   * @default 5\n   */\n  borderRadius?: number;\n\n  /**\n   * Styles applied to the icon only\n   * Good for setting margins or a different color.\n   *\n   * @default {marginRight: 10}\n   */\n  iconStyle?: TextStyle;\n\n  /**\n   * Style prop inherited from TextProps and TouchableWithoutFeedbackProperties\n   * Only exist here so we can have ViewStyle or TextStyle\n   *\n   */\n  style?: ViewStyle | TextStyle;\n\n  /**\n   * Background color of the button. Can be a string or OpaqueColorValue (returned from\n   * PlatformColor(..))\n   *\n   * @default '#007AFF'\n   */\n  backgroundColor?: string | OpaqueColorValue;\n}\n\nexport type GlyphMap<G extends string> = { [K in G]: number | string };\n\nexport interface Icon<G extends string, FN extends string> {\n  defaultProps: any;\n  Button: ComponentClass<IconButtonProps<G>>;\n  glyphMap: GlyphMap<G>;\n  getRawGlyphMap: () => GlyphMap<G>;\n  getFontFamily: () => FN;\n  loadFont: () => Promise<void>;\n  font: { [x: string]: any };\n  new (props: IconProps<G>): React.Component<IconProps<G>>;\n}\n\nexport default function <G extends string, FN extends string>(\n  glyphMap: GlyphMap<G>,\n  fontName: FN,\n  expoAssetId,\n  fontStyle?: any\n): Icon<G, FN> {\n  const font = { [fontName]: expoAssetId };\n  const RNVIconComponent = createIconSet(glyphMap, fontName, null, fontStyle);\n\n  return class Icon extends React.Component<IconProps<G>> {\n    static defaultProps = RNVIconComponent.defaultProps;\n    static Button = createIconButtonComponent(Icon);\n    static glyphMap = glyphMap;\n    static getRawGlyphMap = () => glyphMap;\n    static getFontFamily = () => fontName;\n    static loadFont = () => Font.loadAsync(font);\n    static font = font;\n\n    _mounted = false;\n    _icon?: any;\n\n    state = {\n      fontIsLoaded: Font.isLoaded(fontName),\n    };\n\n    async componentDidMount() {\n      this._mounted = true;\n      if (!this.state.fontIsLoaded) {\n        await Font.loadAsync(font);\n        this._mounted && this.setState({ fontIsLoaded: true });\n      }\n    }\n\n    componentWillUnmount() {\n      this._mounted = false;\n    }\n\n    setNativeProps(props) {\n      if (this._icon) {\n        this._icon.setNativeProps(props);\n      }\n    }\n\n    render() {\n      if (__DEV__ && this.props.name && !(this.props.name in glyphMap)) {\n        console.warn(\n          `\"${this.props.name}\" is not a valid icon name for family \"${fontName}\"`\n        );\n      }\n\n      if (!this.state.fontIsLoaded) {\n        return <Text />;\n      }\n\n      return (\n        <RNVIconComponent\n          ref={(view) => {\n            this._icon = view;\n          }}\n          {...this.props}\n        />\n      );\n    }\n  };\n}\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,IAAI,MAAM,WAAW;AACjC,OAAOC,KAAyB,MAAM,OAAO;AAAC,OAAAC,IAAA;AAW9C,OAAOC,aAAa;AACpB,OAAOC,yBAAyB;AAA2D,SAAAC,GAAA,IAAAC,IAAA;AAE3F,SACEC,kBAAkB,EAClBC,iBAAiB;AAqFnB,eAAc,UACZC,QAAqB,EACrBC,QAAY,EACZC,WAAW,EACXC,SAAe;;EAEf,IAAMC,IAAI,GAAAC,eAAA,KAAMJ,QAAQ,EAAGC,WAAW,CAAE;EACxC,IAAMI,gBAAgB,GAAGZ,aAAa,CAACM,QAAQ,EAAEC,QAAQ,EAAE,IAAI,EAAEE,SAAS,CAAC;EAE3E,OAAAI,EAAA,aAAAC,gBAAA;IAAO,SAAAC,KAAA;MAAA,IAAAC,KAAA;MAAAC,eAAA,OAAAF,IAAA;;MASLC,KAAA,CAAAE,QAAQ,GAAG,KAAK;MAGhBF,KAAA,CAAAG,KAAK,GAAG;QACNC,YAAY,EAAEvB,IAAI,CAACwB,QAAQ,CAACd,QAAQ;OACrC;MAAC,OAAAS,KAAA;IAwCJ;IAACM,SAAA,CAAAP,IAAA,EAAAD,gBAAA;IAAA,OAAAS,YAAA,CAAAR,IAAA;MAAAS,GAAA;MAAAC,KAAA;QAAA,IAAAC,kBAAA,GAAAC,iBAAA,CAtCC,aAAuB;UACrB,IAAI,CAACT,QAAQ,GAAG,IAAI;UACpB,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,YAAY,EAAE;YAC5B,MAAMvB,IAAI,CAAC+B,SAAS,CAAClB,IAAI,CAAC;YAC1B,IAAI,CAACQ,QAAQ,IAAI,IAAI,CAACW,QAAQ,CAAC;cAAET,YAAY,EAAE;YAAI,CAAE,CAAC;;QAE1D,CAAC;QAAA,SANKU,iBAAiBA,CAAA;UAAA,OAAAJ,kBAAA,CAAAK,KAAA,OAAAC,SAAA;QAAA;QAAA,OAAjBF,iBAAiB;MAAA;IAAA;MAAAN,GAAA;MAAAC,KAAA,EAQvB,SAAAQ,oBAAoBA,CAAA;QAClB,IAAI,CAACf,QAAQ,GAAG,KAAK;MACvB;IAAC;MAAAM,GAAA;MAAAC,KAAA,EAED,SAAAS,cAAcA,CAACC,KAAK;QAClB,IAAI,IAAI,CAACC,KAAK,EAAE;UACd,IAAI,CAACA,KAAK,CAACF,cAAc,CAACC,KAAK,CAAC;;MAEpC;IAAC;MAAAX,GAAA;MAAAC,KAAA,EAED,SAAAY,MAAMA,CAAA;QAAA,IAAAC,MAAA;QACJ,IAAIC,OAAO,IAAI,IAAI,CAACJ,KAAK,CAACK,IAAI,IAAI,EAAE,IAAI,CAACL,KAAK,CAACK,IAAI,IAAIlC,QAAQ,CAAC,EAAE;UAChEmC,OAAO,CAACC,IAAI,CACV,IAAI,IAAI,CAACP,KAAK,CAACK,IAAI,0CAA0CjC,QAAQ,GAAG,CACzE;;QAGH,IAAI,CAAC,IAAI,CAACY,KAAK,CAACC,YAAY,EAAE;UAC5B,OAAOjB,IAAA,CAACJ,IAAI,KAAG;;QAGjB,OACEI,IAAA,CAACS,gBAAgB,EAAA+B,aAAA;UACfC,GAAG,EAAE,SAALA,GAAGA,CAAGC,IAAI,EAAI;YACZP,MAAI,CAACF,KAAK,GAAGS,IAAI;UACnB;QAAE,GACE,IAAI,CAACV,KAAK,CAAC,CACf;MAEN;IAAC;EAAA,EArDuBrC,KAAK,CAACgD,SAAuB,CAsDtD,EArDQjC,EAAA,CAAAkC,YAAY,GAAGnC,gBAAgB,CAACmC,YAAa,EAC7ClC,EAAA,CAAAmC,MAAM,GAAG/C,yBAAyB,CAACY,EAAI,CAAE,EACzCA,EAAA,CAAAP,QAAQ,GAAGA,QAAS,EACpBO,EAAA,CAAAoC,cAAc,GAAG;IAAA,OAAM3C,QAAS;EAAA,GAChCO,EAAA,CAAAqC,aAAa,GAAG;IAAA,OAAM3C,QAAS;EAAA,GAC/BM,EAAA,CAAAsC,QAAQ,GAAG;IAAA,OAAMtD,IAAI,CAAC+B,SAAS,CAAClB,IAAI,CAAE;EAAA,GACtCG,EAAA,CAAAH,IAAI,GAAGA,IAAK,E;AAgDvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}