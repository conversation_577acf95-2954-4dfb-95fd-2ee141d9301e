{"version": 3, "sources": ["PanGestureHandler.ts"], "names": ["DEFAULT_MIN_POINTERS", "DEFAULT_MAX_POINTERS", "DEFAULT_MIN_DIST_SQ", "DEFAULT_TOUCH_SLOP", "PanGestureHandler", "Gesture<PERSON>andler", "Number", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "init", "ref", "propsRef", "updateGestureConfig", "enabled", "props", "resetConfig", "checkCustomActivationCriteria", "customActivationProperties", "config", "minDist", "undefined", "minDistSq", "hasCustomActivationCriteria", "minPointers", "maxPointers", "minVelocity", "minVelocityX", "minVelocityY", "activateAfterLongPress", "activeOffsetXStart", "activeOffsetXEnd", "failOffsetXStart", "failOffsetXEnd", "activeOffsetYStart", "activeOffsetYEnd", "failOffsetYStart", "failOffsetYEnd", "minVelocitySq", "transformNativeEvent", "translationX", "getTranslationX", "translationY", "getTranslationY", "isNaN", "velocityX", "velocityY", "lastX", "startX", "offsetX", "lastY", "startY", "offsetY", "clearActivationTimeout", "clearTimeout", "activationTimeout", "onPointerDown", "event", "tracker", "addToTracker", "getLastAvgX", "getLastAvgY", "tryBegin", "<PERSON><PERSON><PERSON>", "onPointerAdd", "getTrackedPointersCount", "currentState", "State", "ACTIVE", "cancel", "fail", "onPointerUp", "removeFromTracker", "pointerId", "end", "resetProgress", "onPointerRemove", "onPointerMove", "track", "getVelocityX", "getVelocityY", "onPointerOutOfBounds", "getShouldCancelWhenOutside", "shouldActivate", "dx", "dy", "distanceSq", "vx", "vy", "velocitySq", "shouldFail", "UNDETERMINED", "begin", "setTimeout", "activate", "BEGAN", "force", "onCancel", "onReset"], "mappings": ";;;;;;;AAAA;;AACA;;AAGA;;;;;;AAEA,MAAMA,oBAAoB,GAAG,CAA7B;AACA,MAAMC,oBAAoB,GAAG,EAA7B;AACA,MAAMC,mBAAmB,GAAGC,gCAAqBA,6BAAjD;;AAEe,MAAMC,iBAAN,SAAgCC,uBAAhC,CAA+C;AAAA;AAAA;;AAAA,wDACJ,CACtD,oBADsD,EAEtD,kBAFsD,EAGtD,kBAHsD,EAItD,gBAJsD,EAKtD,oBALsD,EAMtD,kBANsD,EAOtD,kBAPsD,EAQtD,gBARsD,EAStD,cATsD,EAUtD,cAVsD,CADI;;AAAA,uCAczC,CAdyC;;AAAA,uCAezC,CAfyC;;AAAA,uCAiBxCH,mBAjBwC;;AAAA,gDAmB/B,CAACI,MAAM,CAACC,gBAnBuB;;AAAA,8CAoBjCD,MAAM,CAACE,gBApB0B;;AAAA,8CAqBjCF,MAAM,CAACE,gBArB0B;;AAAA,4CAsBnCF,MAAM,CAACC,gBAtB4B;;AAAA,gDAwB/BD,MAAM,CAACC,gBAxBwB;;AAAA,8CAyBjCD,MAAM,CAACE,gBAzB0B;;AAAA,8CA0BjCF,MAAM,CAACE,gBA1B0B;;AAAA,4CA2BnCF,MAAM,CAACC,gBA3B4B;;AAAA,0CA6BrCD,MAAM,CAACC,gBA7B8B;;AAAA,0CA8BrCD,MAAM,CAACC,gBA9B8B;;AAAA,2CA+BpCD,MAAM,CAACC,gBA/B6B;;AAAA,yCAiCtCP,oBAjCsC;;AAAA,yCAkCtCC,oBAlCsC;;AAAA,oCAoC3C,CApC2C;;AAAA,oCAqC3C,CArC2C;;AAAA,qCAsC1C,CAtC0C;;AAAA,qCAuC1C,CAvC0C;;AAAA,mCAwC5C,CAxC4C;;AAAA,mCAyC5C,CAzC4C;;AAAA,oDA2C3B,CA3C2B;;AAAA,+CA4ChC,CA5CgC;AAAA;;AA8CrDQ,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAwD;AACjE,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AACD;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,SAAKC,WAAL;AAEA,UAAMH,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;AACA,SAAKE,6BAAL,CAAmC,KAAKC,0BAAxC;;AAEA,QAAI,KAAKC,MAAL,CAAYC,OAAZ,KAAwBC,SAA5B,EAAuC;AACrC,WAAKC,SAAL,GAAiB,KAAKH,MAAL,CAAYC,OAAZ,GAAsB,KAAKD,MAAL,CAAYC,OAAnD;AACD,KAFD,MAEO,IAAI,KAAKG,2BAAT,EAAsC;AAC3C,WAAKD,SAAL,GAAiBf,MAAM,CAACC,gBAAxB;AACD;;AAED,QAAI,KAAKW,MAAL,CAAYK,WAAZ,KAA4BH,SAAhC,EAA2C;AACzC,WAAKG,WAAL,GAAmB,KAAKL,MAAL,CAAYK,WAA/B;AACD;;AAED,QAAI,KAAKL,MAAL,CAAYM,WAAZ,KAA4BJ,SAAhC,EAA2C;AACzC,WAAKI,WAAL,GAAmB,KAAKN,MAAL,CAAYM,WAA/B;AACD;;AAED,QAAI,KAAKN,MAAL,CAAYO,WAAZ,KAA4BL,SAAhC,EAA2C;AACzC,WAAKM,YAAL,GAAoB,KAAKR,MAAL,CAAYO,WAAhC;AACA,WAAKE,YAAL,GAAoB,KAAKT,MAAL,CAAYO,WAAhC;AACD;;AAED,QAAI,KAAKP,MAAL,CAAYQ,YAAZ,KAA6BN,SAAjC,EAA4C;AAC1C,WAAKM,YAAL,GAAoB,KAAKR,MAAL,CAAYQ,YAAhC;AACD;;AAED,QAAI,KAAKR,MAAL,CAAYS,YAAZ,KAA6BP,SAAjC,EAA4C;AAC1C,WAAKO,YAAL,GAAoB,KAAKT,MAAL,CAAYS,YAAhC;AACD;;AAED,QAAI,KAAKT,MAAL,CAAYU,sBAAZ,KAAuCR,SAA3C,EAAsD;AACpD,WAAKQ,sBAAL,GAA8B,KAAKV,MAAL,CAAYU,sBAA1C;AACD;;AAED,QAAI,KAAKV,MAAL,CAAYW,kBAAZ,KAAmCT,SAAvC,EAAkD;AAChD,WAAKS,kBAAL,GAA0B,KAAKX,MAAL,CAAYW,kBAAtC;;AAEA,UAAI,KAAKX,MAAL,CAAYY,gBAAZ,KAAiCV,SAArC,EAAgD;AAC9C,aAAKU,gBAAL,GAAwBxB,MAAM,CAACC,gBAA/B;AACD;AACF;;AAED,QAAI,KAAKW,MAAL,CAAYY,gBAAZ,KAAiCV,SAArC,EAAgD;AAC9C,WAAKU,gBAAL,GAAwB,KAAKZ,MAAL,CAAYY,gBAApC;;AAEA,UAAI,KAAKZ,MAAL,CAAYW,kBAAZ,KAAmCT,SAAvC,EAAkD;AAChD,aAAKS,kBAAL,GAA0BvB,MAAM,CAACE,gBAAjC;AACD;AACF;;AAED,QAAI,KAAKU,MAAL,CAAYa,gBAAZ,KAAiCX,SAArC,EAAgD;AAC9C,WAAKW,gBAAL,GAAwB,KAAKb,MAAL,CAAYa,gBAApC;;AAEA,UAAI,KAAKb,MAAL,CAAYc,cAAZ,KAA+BZ,SAAnC,EAA8C;AAC5C,aAAKY,cAAL,GAAsB1B,MAAM,CAACC,gBAA7B;AACD;AACF;;AAED,QAAI,KAAKW,MAAL,CAAYc,cAAZ,KAA+BZ,SAAnC,EAA8C;AAC5C,WAAKY,cAAL,GAAsB,KAAKd,MAAL,CAAYc,cAAlC;;AAEA,UAAI,KAAKd,MAAL,CAAYa,gBAAZ,KAAiCX,SAArC,EAAgD;AAC9C,aAAKW,gBAAL,GAAwBzB,MAAM,CAACE,gBAA/B;AACD;AACF;;AAED,QAAI,KAAKU,MAAL,CAAYe,kBAAZ,KAAmCb,SAAvC,EAAkD;AAChD,WAAKa,kBAAL,GAA0B,KAAKf,MAAL,CAAYe,kBAAtC;;AAEA,UAAI,KAAKf,MAAL,CAAYgB,gBAAZ,KAAiCd,SAArC,EAAgD;AAC9C,aAAKc,gBAAL,GAAwB5B,MAAM,CAACC,gBAA/B;AACD;AACF;;AAED,QAAI,KAAKW,MAAL,CAAYgB,gBAAZ,KAAiCd,SAArC,EAAgD;AAC9C,WAAKc,gBAAL,GAAwB,KAAKhB,MAAL,CAAYgB,gBAApC;;AAEA,UAAI,KAAKhB,MAAL,CAAYe,kBAAZ,KAAmCb,SAAvC,EAAkD;AAChD,aAAKa,kBAAL,GAA0B3B,MAAM,CAACE,gBAAjC;AACD;AACF;;AAED,QAAI,KAAKU,MAAL,CAAYiB,gBAAZ,KAAiCf,SAArC,EAAgD;AAC9C,WAAKe,gBAAL,GAAwB,KAAKjB,MAAL,CAAYiB,gBAApC;;AAEA,UAAI,KAAKjB,MAAL,CAAYkB,cAAZ,KAA+BhB,SAAnC,EAA8C;AAC5C,aAAKgB,cAAL,GAAsB9B,MAAM,CAACC,gBAA7B;AACD;AACF;;AAED,QAAI,KAAKW,MAAL,CAAYkB,cAAZ,KAA+BhB,SAAnC,EAA8C;AAC5C,WAAKgB,cAAL,GAAsB,KAAKlB,MAAL,CAAYkB,cAAlC;;AAEA,UAAI,KAAKlB,MAAL,CAAYiB,gBAAZ,KAAiCf,SAArC,EAAgD;AAC9C,aAAKe,gBAAL,GAAwB7B,MAAM,CAACE,gBAA/B;AACD;AACF;AACF;;AAESO,EAAAA,WAAW,GAAS;AAC5B,UAAMA,WAAN;AAEA,SAAKc,kBAAL,GAA0B,CAACvB,MAAM,CAACC,gBAAlC;AACA,SAAKuB,gBAAL,GAAwBxB,MAAM,CAACE,gBAA/B;AACA,SAAKuB,gBAAL,GAAwBzB,MAAM,CAACE,gBAA/B;AACA,SAAKwB,cAAL,GAAsB1B,MAAM,CAACC,gBAA7B;AAEA,SAAK0B,kBAAL,GAA0B3B,MAAM,CAACC,gBAAjC;AACA,SAAK2B,gBAAL,GAAwB5B,MAAM,CAACE,gBAA/B;AACA,SAAK2B,gBAAL,GAAwB7B,MAAM,CAACE,gBAA/B;AACA,SAAK4B,cAAL,GAAsB9B,MAAM,CAACC,gBAA7B;AAEA,SAAKmB,YAAL,GAAoBpB,MAAM,CAACC,gBAA3B;AACA,SAAKoB,YAAL,GAAoBrB,MAAM,CAACC,gBAA3B;AACA,SAAK8B,aAAL,GAAqB/B,MAAM,CAACC,gBAA5B;AAEA,SAAKc,SAAL,GAAiBnB,mBAAjB;AAEA,SAAKqB,WAAL,GAAmBvB,oBAAnB;AACA,SAAKwB,WAAL,GAAmBvB,oBAAnB;AAEA,SAAK2B,sBAAL,GAA8B,CAA9B;AACD;;AAESU,EAAAA,oBAAoB,GAAG;AAC/B,UAAMC,YAAoB,GAAG,KAAKC,eAAL,EAA7B;AACA,UAAMC,YAAoB,GAAG,KAAKC,eAAL,EAA7B;AAEA,WAAO,EACL,GAAG,MAAMJ,oBAAN,EADE;AAELC,MAAAA,YAAY,EAAEI,KAAK,CAACJ,YAAD,CAAL,GAAsB,CAAtB,GAA0BA,YAFnC;AAGLE,MAAAA,YAAY,EAAEE,KAAK,CAACF,YAAD,CAAL,GAAsB,CAAtB,GAA0BA,YAHnC;AAILG,MAAAA,SAAS,EAAE,KAAKA,SAJX;AAKLC,MAAAA,SAAS,EAAE,KAAKA;AALX,KAAP;AAOD;;AAEOL,EAAAA,eAAe,GAAW;AAChC,WAAO,KAAKM,KAAL,GAAa,KAAKC,MAAlB,GAA2B,KAAKC,OAAvC;AACD;;AACON,EAAAA,eAAe,GAAW;AAChC,WAAO,KAAKO,KAAL,GAAa,KAAKC,MAAlB,GAA2B,KAAKC,OAAvC;AACD;;AAEOC,EAAAA,sBAAsB,GAAS;AACrCC,IAAAA,YAAY,CAAC,KAAKC,iBAAN,CAAZ;AACD,GAvM2D,CAyM5D;;;AACUC,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AAEA,SAAKV,KAAL,GAAa,KAAKW,OAAL,CAAaE,WAAb,EAAb;AACA,SAAKV,KAAL,GAAa,KAAKQ,OAAL,CAAaG,WAAb,EAAb;AAEA,SAAKC,QAAL,CAAcL,KAAd;AACA,SAAKM,UAAL;AACD;;AAESC,EAAAA,YAAY,CAACP,KAAD,EAA4B;AAChD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMO,YAAN,CAAmBP,KAAnB;AACA,SAAKK,QAAL,CAAcL,KAAd;AAEA,SAAKR,OAAL,IAAgB,KAAKF,KAAL,GAAa,KAAKC,MAAlC;AACA,SAAKI,OAAL,IAAgB,KAAKF,KAAL,GAAa,KAAKC,MAAlC;AAEA,SAAKJ,KAAL,GAAa,KAAKW,OAAL,CAAaE,WAAb,EAAb;AACA,SAAKV,KAAL,GAAa,KAAKQ,OAAL,CAAaG,WAAb,EAAb;AAEA,SAAKb,MAAL,GAAc,KAAKD,KAAnB;AACA,SAAKI,MAAL,GAAc,KAAKD,KAAnB;;AAEA,QAAI,KAAKQ,OAAL,CAAaO,uBAAb,KAAyC,KAAKxC,WAAlD,EAA+D;AAC7D,UAAI,KAAKyC,YAAL,KAAsBC,aAAMC,MAAhC,EAAwC;AACtC,aAAKC,MAAL;AACD,OAFD,MAEO;AACL,aAAKC,IAAL;AACD;AACF,KAND,MAMO;AACL,WAAKP,UAAL;AACD;AACF;;AAESQ,EAAAA,WAAW,CAACd,KAAD,EAA4B;AAC/C,UAAMc,WAAN,CAAkBd,KAAlB;;AAEA,QAAI,KAAKS,YAAL,KAAsBC,aAAMC,MAAhC,EAAwC;AACtC,WAAKrB,KAAL,GAAa,KAAKW,OAAL,CAAaE,WAAb,EAAb;AACA,WAAKV,KAAL,GAAa,KAAKQ,OAAL,CAAaG,WAAb,EAAb;AACD;;AAED,SAAKH,OAAL,CAAac,iBAAb,CAA+Bf,KAAK,CAACgB,SAArC;;AAEA,QAAI,KAAKP,YAAL,KAAsBC,aAAMC,MAAhC,EAAwC;AACtC,WAAKM,GAAL;AACD,KAFD,MAEO;AACL,WAAKC,aAAL;AACA,WAAKL,IAAL;AACD;AACF;;AACSM,EAAAA,eAAe,CAACnB,KAAD,EAA4B;AACnD,UAAMmB,eAAN,CAAsBnB,KAAtB;AACA,SAAKC,OAAL,CAAac,iBAAb,CAA+Bf,KAAK,CAACgB,SAArC;AAEA,SAAKxB,OAAL,IAAgB,KAAKF,KAAL,GAAa,KAAKC,MAAlC;AACA,SAAKI,OAAL,IAAgB,KAAKF,KAAL,GAAa,KAAKC,MAAlC;AAEA,SAAKJ,KAAL,GAAa,KAAKW,OAAL,CAAaE,WAAb,EAAb;AACA,SAAKV,KAAL,GAAa,KAAKQ,OAAL,CAAaG,WAAb,EAAb;AAEA,SAAKb,MAAL,GAAc,KAAKD,KAAnB;AACA,SAAKI,MAAL,GAAc,KAAKD,KAAnB;;AAEA,QACE,EACE,KAAKgB,YAAL,KAAsBC,aAAMC,MAA5B,IACA,KAAKV,OAAL,CAAaO,uBAAb,KAAyC,KAAKzC,WAFhD,CADF,EAKE;AACA,WAAKuC,UAAL;AACD;AACF;;AAESc,EAAAA,aAAa,CAACpB,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaoB,KAAb,CAAmBrB,KAAnB;AAEA,SAAKV,KAAL,GAAa,KAAKW,OAAL,CAAaE,WAAb,EAAb;AACA,SAAKV,KAAL,GAAa,KAAKQ,OAAL,CAAaG,WAAb,EAAb;AACA,SAAKhB,SAAL,GAAiB,KAAKa,OAAL,CAAaqB,YAAb,CAA0BtB,KAAK,CAACgB,SAAhC,CAAjB;AACA,SAAK3B,SAAL,GAAiB,KAAKY,OAAL,CAAasB,YAAb,CAA0BvB,KAAK,CAACgB,SAAhC,CAAjB;AAEA,SAAKV,UAAL;AAEA,UAAMc,aAAN,CAAoBpB,KAApB;AACD;;AAESwB,EAAAA,oBAAoB,CAACxB,KAAD,EAA4B;AACxD,QAAI,KAAKyB,0BAAL,EAAJ,EAAuC;AACrC;AACD;;AAED,SAAKxB,OAAL,CAAaoB,KAAb,CAAmBrB,KAAnB;AAEA,SAAKV,KAAL,GAAa,KAAKW,OAAL,CAAaE,WAAb,EAAb;AACA,SAAKV,KAAL,GAAa,KAAKQ,OAAL,CAAaG,WAAb,EAAb;AACA,SAAKhB,SAAL,GAAiB,KAAKa,OAAL,CAAaqB,YAAb,CAA0BtB,KAAK,CAACgB,SAAhC,CAAjB;AACA,SAAK3B,SAAL,GAAiB,KAAKY,OAAL,CAAasB,YAAb,CAA0BvB,KAAK,CAACgB,SAAhC,CAAjB;AAEA,SAAKV,UAAL;;AAEA,QAAI,KAAKG,YAAL,KAAsBC,aAAMC,MAAhC,EAAwC;AACtC,YAAMa,oBAAN,CAA2BxB,KAA3B;AACD;AACF;;AAEO0B,EAAAA,cAAc,GAAY;AAChC,UAAMC,EAAU,GAAG,KAAK3C,eAAL,EAAnB;;AAEA,QACE,KAAKX,kBAAL,KAA4BvB,MAAM,CAACC,gBAAnC,IACA4E,EAAE,GAAG,KAAKtD,kBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,QACE,KAAKC,gBAAL,KAA0BxB,MAAM,CAACE,gBAAjC,IACA2E,EAAE,GAAG,KAAKrD,gBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,UAAMsD,EAAU,GAAG,KAAK1C,eAAL,EAAnB;;AAEA,QACE,KAAKT,kBAAL,KAA4B3B,MAAM,CAACC,gBAAnC,IACA6E,EAAE,GAAG,KAAKnD,kBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,QACE,KAAKC,gBAAL,KAA0B5B,MAAM,CAACE,gBAAjC,IACA4E,EAAE,GAAG,KAAKlD,gBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,UAAMmD,UAAkB,GAAGF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAA1C;;AAEA,QACE,KAAK/D,SAAL,KAAmBf,MAAM,CAACC,gBAA1B,IACA8E,UAAU,IAAI,KAAKhE,SAFrB,EAGE;AACA,aAAO,IAAP;AACD;;AAED,UAAMiE,EAAU,GAAG,KAAK1C,SAAxB;;AAEA,QACE,KAAKlB,YAAL,KAAsBpB,MAAM,CAACC,gBAA7B,KACE,KAAKmB,YAAL,GAAoB,CAApB,IAAyB4D,EAAE,IAAI,KAAK5D,YAArC,IACE,KAAKA,YAAL,IAAqB,CAArB,IAA0B,KAAKA,YAAL,IAAqB4D,EAFlD,CADF,EAIE;AACA,aAAO,IAAP;AACD;;AAED,UAAMC,EAAU,GAAG,KAAK1C,SAAxB;;AACA,QACE,KAAKlB,YAAL,KAAsBrB,MAAM,CAACC,gBAA7B,KACE,KAAKoB,YAAL,GAAoB,CAApB,IAAyB4D,EAAE,IAAI,KAAK5D,YAArC,IACE,KAAKA,YAAL,IAAqB,CAArB,IAA0B,KAAKA,YAAL,IAAqB4D,EAFlD,CADF,EAIE;AACA,aAAO,IAAP;AACD;;AAED,UAAMC,UAAkB,GAAGF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAA1C;AAEA,WACE,KAAKlD,aAAL,KAAuB/B,MAAM,CAACC,gBAA9B,IACAiF,UAAU,IAAI,KAAKnD,aAFrB;AAID;;AAEOoD,EAAAA,UAAU,GAAY;AAC5B,UAAMN,EAAU,GAAG,KAAK3C,eAAL,EAAnB;AACA,UAAM4C,EAAU,GAAG,KAAK1C,eAAL,EAAnB;AACA,UAAM2C,UAAU,GAAGF,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAAlC;;AAEA,QAAI,KAAKxD,sBAAL,GAA8B,CAA9B,IAAmCyD,UAAU,GAAGnF,mBAApD,EAAyE;AACvE,WAAKkD,sBAAL;AACA,aAAO,IAAP;AACD;;AAED,QACE,KAAKrB,gBAAL,KAA0BzB,MAAM,CAACE,gBAAjC,IACA2E,EAAE,GAAG,KAAKpD,gBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,QACE,KAAKC,cAAL,KAAwB1B,MAAM,CAACC,gBAA/B,IACA4E,EAAE,GAAG,KAAKnD,cAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,QACE,KAAKG,gBAAL,KAA0B7B,MAAM,CAACE,gBAAjC,IACA4E,EAAE,GAAG,KAAKjD,gBAFZ,EAGE;AACA,aAAO,IAAP;AACD;;AAED,WACE,KAAKC,cAAL,KAAwB9B,MAAM,CAACC,gBAA/B,IACA6E,EAAE,GAAG,KAAKhD,cAFZ;AAID;;AAEOyB,EAAAA,QAAQ,CAACL,KAAD,EAA4B;AAC1C,QACE,KAAKS,YAAL,KAAsBC,aAAMwB,YAA5B,IACA,KAAKjC,OAAL,CAAaO,uBAAb,MAA0C,KAAKzC,WAFjD,EAGE;AACA,WAAKmD,aAAL;AACA,WAAK1B,OAAL,GAAe,CAAf;AACA,WAAKG,OAAL,GAAe,CAAf;AACA,WAAKP,SAAL,GAAiB,CAAjB;AACA,WAAKC,SAAL,GAAiB,CAAjB;AAEA,WAAK8C,KAAL;;AAEA,UAAI,KAAK/D,sBAAL,GAA8B,CAAlC,EAAqC;AACnC,aAAK0B,iBAAL,GAAyBsC,UAAU,CAAC,MAAM;AACxC,eAAKC,QAAL;AACD,SAFkC,EAEhC,KAAKjE,sBAF2B,CAAnC;AAGD;AACF,KAjBD,MAiBO;AACL,WAAKgB,SAAL,GAAiB,KAAKa,OAAL,CAAaqB,YAAb,CAA0BtB,KAAK,CAACgB,SAAhC,CAAjB;AACA,WAAK3B,SAAL,GAAiB,KAAKY,OAAL,CAAasB,YAAb,CAA0BvB,KAAK,CAACgB,SAAhC,CAAjB;AACD;AACF;;AAEOV,EAAAA,UAAU,GAAS;AACzB,QAAI,KAAKG,YAAL,KAAsBC,aAAM4B,KAAhC,EAAuC;AACrC,UAAI,KAAKL,UAAL,EAAJ,EAAuB;AACrB,aAAKpB,IAAL;AACD,OAFD,MAEO,IAAI,KAAKa,cAAL,EAAJ,EAA2B;AAChC,aAAKW,QAAL;AACD;AACF;AACF;;AAEMA,EAAAA,QAAQ,CAACE,KAAK,GAAG,KAAT,EAAsB;AACnC,QAAI,KAAK9B,YAAL,KAAsBC,aAAMC,MAAhC,EAAwC;AACtC,WAAKO,aAAL;AACD;;AAED,UAAMmB,QAAN,CAAeE,KAAf;AACD;;AAESC,EAAAA,QAAQ,GAAS;AACzB,SAAK5C,sBAAL;AACD;;AAES6C,EAAAA,OAAO,GAAS;AACxB,SAAK7C,sBAAL;AACD;;AAESsB,EAAAA,aAAa,GAAS;AAC9B,QAAI,KAAKT,YAAL,KAAsBC,aAAMC,MAAhC,EAAwC;AACtC;AACD;;AAED,SAAKpB,MAAL,GAAc,KAAKD,KAAnB;AACA,SAAKI,MAAL,GAAc,KAAKD,KAAnB;AACD;;AAzd2D", "sourcesContent": ["import { State } from '../../State';\nimport { DEFAULT_TOUCH_SLOP } from '../constants';\nimport { AdaptedEvent, Config } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\n\nconst DEFAULT_MIN_POINTERS = 1;\nconst DEFAULT_MAX_POINTERS = 10;\nconst DEFAULT_MIN_DIST_SQ = DEFAULT_TOUCH_SLOP * DEFAULT_TOUCH_SLOP;\n\nexport default class PanGestureHandler extends GestureHandler {\n  private readonly customActivationProperties: string[] = [\n    'activeOffsetXStart',\n    'activeOffsetXEnd',\n    'failOffsetXStart',\n    'failOffsetXEnd',\n    'activeOffsetYStart',\n    'activeOffsetYEnd',\n    'failOffsetYStart',\n    'failOffsetYEnd',\n    'minVelocityX',\n    'minVelocityY',\n  ];\n\n  public velocityX = 0;\n  public velocityY = 0;\n\n  private minDistSq = DEFAULT_MIN_DIST_SQ;\n\n  private activeOffsetXStart = -Number.MAX_SAFE_INTEGER;\n  private activeOffsetXEnd = Number.MIN_SAFE_INTEGER;\n  private failOffsetXStart = Number.MIN_SAFE_INTEGER;\n  private failOffsetXEnd = Number.MAX_SAFE_INTEGER;\n\n  private activeOffsetYStart = Number.MAX_SAFE_INTEGER;\n  private activeOffsetYEnd = Number.MIN_SAFE_INTEGER;\n  private failOffsetYStart = Number.MIN_SAFE_INTEGER;\n  private failOffsetYEnd = Number.MAX_SAFE_INTEGER;\n\n  private minVelocityX = Number.MAX_SAFE_INTEGER;\n  private minVelocityY = Number.MAX_SAFE_INTEGER;\n  private minVelocitySq = Number.MAX_SAFE_INTEGER;\n\n  private minPointers = DEFAULT_MIN_POINTERS;\n  private maxPointers = DEFAULT_MAX_POINTERS;\n\n  private startX = 0;\n  private startY = 0;\n  private offsetX = 0;\n  private offsetY = 0;\n  private lastX = 0;\n  private lastY = 0;\n\n  private activateAfterLongPress = 0;\n  private activationTimeout = 0;\n\n  public init(ref: number, propsRef: React.RefObject<unknown>): void {\n    super.init(ref, propsRef);\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    this.resetConfig();\n\n    super.updateGestureConfig({ enabled: enabled, ...props });\n    this.checkCustomActivationCriteria(this.customActivationProperties);\n\n    if (this.config.minDist !== undefined) {\n      this.minDistSq = this.config.minDist * this.config.minDist;\n    } else if (this.hasCustomActivationCriteria) {\n      this.minDistSq = Number.MAX_SAFE_INTEGER;\n    }\n\n    if (this.config.minPointers !== undefined) {\n      this.minPointers = this.config.minPointers;\n    }\n\n    if (this.config.maxPointers !== undefined) {\n      this.maxPointers = this.config.maxPointers;\n    }\n\n    if (this.config.minVelocity !== undefined) {\n      this.minVelocityX = this.config.minVelocity;\n      this.minVelocityY = this.config.minVelocity;\n    }\n\n    if (this.config.minVelocityX !== undefined) {\n      this.minVelocityX = this.config.minVelocityX;\n    }\n\n    if (this.config.minVelocityY !== undefined) {\n      this.minVelocityY = this.config.minVelocityY;\n    }\n\n    if (this.config.activateAfterLongPress !== undefined) {\n      this.activateAfterLongPress = this.config.activateAfterLongPress;\n    }\n\n    if (this.config.activeOffsetXStart !== undefined) {\n      this.activeOffsetXStart = this.config.activeOffsetXStart;\n\n      if (this.config.activeOffsetXEnd === undefined) {\n        this.activeOffsetXEnd = Number.MAX_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.activeOffsetXEnd !== undefined) {\n      this.activeOffsetXEnd = this.config.activeOffsetXEnd;\n\n      if (this.config.activeOffsetXStart === undefined) {\n        this.activeOffsetXStart = Number.MIN_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.failOffsetXStart !== undefined) {\n      this.failOffsetXStart = this.config.failOffsetXStart;\n\n      if (this.config.failOffsetXEnd === undefined) {\n        this.failOffsetXEnd = Number.MAX_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.failOffsetXEnd !== undefined) {\n      this.failOffsetXEnd = this.config.failOffsetXEnd;\n\n      if (this.config.failOffsetXStart === undefined) {\n        this.failOffsetXStart = Number.MIN_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.activeOffsetYStart !== undefined) {\n      this.activeOffsetYStart = this.config.activeOffsetYStart;\n\n      if (this.config.activeOffsetYEnd === undefined) {\n        this.activeOffsetYEnd = Number.MAX_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.activeOffsetYEnd !== undefined) {\n      this.activeOffsetYEnd = this.config.activeOffsetYEnd;\n\n      if (this.config.activeOffsetYStart === undefined) {\n        this.activeOffsetYStart = Number.MIN_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.failOffsetYStart !== undefined) {\n      this.failOffsetYStart = this.config.failOffsetYStart;\n\n      if (this.config.failOffsetYEnd === undefined) {\n        this.failOffsetYEnd = Number.MAX_SAFE_INTEGER;\n      }\n    }\n\n    if (this.config.failOffsetYEnd !== undefined) {\n      this.failOffsetYEnd = this.config.failOffsetYEnd;\n\n      if (this.config.failOffsetYStart === undefined) {\n        this.failOffsetYStart = Number.MIN_SAFE_INTEGER;\n      }\n    }\n  }\n\n  protected resetConfig(): void {\n    super.resetConfig();\n\n    this.activeOffsetXStart = -Number.MAX_SAFE_INTEGER;\n    this.activeOffsetXEnd = Number.MIN_SAFE_INTEGER;\n    this.failOffsetXStart = Number.MIN_SAFE_INTEGER;\n    this.failOffsetXEnd = Number.MAX_SAFE_INTEGER;\n\n    this.activeOffsetYStart = Number.MAX_SAFE_INTEGER;\n    this.activeOffsetYEnd = Number.MIN_SAFE_INTEGER;\n    this.failOffsetYStart = Number.MIN_SAFE_INTEGER;\n    this.failOffsetYEnd = Number.MAX_SAFE_INTEGER;\n\n    this.minVelocityX = Number.MAX_SAFE_INTEGER;\n    this.minVelocityY = Number.MAX_SAFE_INTEGER;\n    this.minVelocitySq = Number.MAX_SAFE_INTEGER;\n\n    this.minDistSq = DEFAULT_MIN_DIST_SQ;\n\n    this.minPointers = DEFAULT_MIN_POINTERS;\n    this.maxPointers = DEFAULT_MAX_POINTERS;\n\n    this.activateAfterLongPress = 0;\n  }\n\n  protected transformNativeEvent() {\n    const translationX: number = this.getTranslationX();\n    const translationY: number = this.getTranslationY();\n\n    return {\n      ...super.transformNativeEvent(),\n      translationX: isNaN(translationX) ? 0 : translationX,\n      translationY: isNaN(translationY) ? 0 : translationY,\n      velocityX: this.velocityX,\n      velocityY: this.velocityY,\n    };\n  }\n\n  private getTranslationX(): number {\n    return this.lastX - this.startX + this.offsetX;\n  }\n  private getTranslationY(): number {\n    return this.lastY - this.startY + this.offsetY;\n  }\n\n  private clearActivationTimeout(): void {\n    clearTimeout(this.activationTimeout);\n  }\n\n  //EventsHandling\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n\n    this.lastX = this.tracker.getLastAvgX();\n    this.lastY = this.tracker.getLastAvgY();\n\n    this.tryBegin(event);\n    this.checkBegan();\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n    this.tryBegin(event);\n\n    this.offsetX += this.lastX - this.startX;\n    this.offsetY += this.lastY - this.startY;\n\n    this.lastX = this.tracker.getLastAvgX();\n    this.lastY = this.tracker.getLastAvgY();\n\n    this.startX = this.lastX;\n    this.startY = this.lastY;\n\n    if (this.tracker.getTrackedPointersCount() > this.maxPointers) {\n      if (this.currentState === State.ACTIVE) {\n        this.cancel();\n      } else {\n        this.fail();\n      }\n    } else {\n      this.checkBegan();\n    }\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n\n    if (this.currentState === State.ACTIVE) {\n      this.lastX = this.tracker.getLastAvgX();\n      this.lastY = this.tracker.getLastAvgY();\n    }\n\n    this.tracker.removeFromTracker(event.pointerId);\n\n    if (this.currentState === State.ACTIVE) {\n      this.end();\n    } else {\n      this.resetProgress();\n      this.fail();\n    }\n  }\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.tracker.removeFromTracker(event.pointerId);\n\n    this.offsetX += this.lastX - this.startX;\n    this.offsetY += this.lastY - this.startY;\n\n    this.lastX = this.tracker.getLastAvgX();\n    this.lastY = this.tracker.getLastAvgY();\n\n    this.startX = this.lastX;\n    this.startY = this.lastY;\n\n    if (\n      !(\n        this.currentState === State.ACTIVE &&\n        this.tracker.getTrackedPointersCount() < this.minPointers\n      )\n    ) {\n      this.checkBegan();\n    }\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n\n    this.lastX = this.tracker.getLastAvgX();\n    this.lastY = this.tracker.getLastAvgY();\n    this.velocityX = this.tracker.getVelocityX(event.pointerId);\n    this.velocityY = this.tracker.getVelocityY(event.pointerId);\n\n    this.checkBegan();\n\n    super.onPointerMove(event);\n  }\n\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    if (this.getShouldCancelWhenOutside()) {\n      return;\n    }\n\n    this.tracker.track(event);\n\n    this.lastX = this.tracker.getLastAvgX();\n    this.lastY = this.tracker.getLastAvgY();\n    this.velocityX = this.tracker.getVelocityX(event.pointerId);\n    this.velocityY = this.tracker.getVelocityY(event.pointerId);\n\n    this.checkBegan();\n\n    if (this.currentState === State.ACTIVE) {\n      super.onPointerOutOfBounds(event);\n    }\n  }\n\n  private shouldActivate(): boolean {\n    const dx: number = this.getTranslationX();\n\n    if (\n      this.activeOffsetXStart !== Number.MAX_SAFE_INTEGER &&\n      dx < this.activeOffsetXStart\n    ) {\n      return true;\n    }\n\n    if (\n      this.activeOffsetXEnd !== Number.MIN_SAFE_INTEGER &&\n      dx > this.activeOffsetXEnd\n    ) {\n      return true;\n    }\n\n    const dy: number = this.getTranslationY();\n\n    if (\n      this.activeOffsetYStart !== Number.MAX_SAFE_INTEGER &&\n      dy < this.activeOffsetYStart\n    ) {\n      return true;\n    }\n\n    if (\n      this.activeOffsetYEnd !== Number.MIN_SAFE_INTEGER &&\n      dy > this.activeOffsetYEnd\n    ) {\n      return true;\n    }\n\n    const distanceSq: number = dx * dx + dy * dy;\n\n    if (\n      this.minDistSq !== Number.MAX_SAFE_INTEGER &&\n      distanceSq >= this.minDistSq\n    ) {\n      return true;\n    }\n\n    const vx: number = this.velocityX;\n\n    if (\n      this.minVelocityX !== Number.MAX_SAFE_INTEGER &&\n      ((this.minVelocityX < 0 && vx <= this.minVelocityX) ||\n        (this.minVelocityX >= 0 && this.minVelocityX <= vx))\n    ) {\n      return true;\n    }\n\n    const vy: number = this.velocityY;\n    if (\n      this.minVelocityY !== Number.MAX_SAFE_INTEGER &&\n      ((this.minVelocityY < 0 && vy <= this.minVelocityY) ||\n        (this.minVelocityY >= 0 && this.minVelocityY <= vy))\n    ) {\n      return true;\n    }\n\n    const velocitySq: number = vx * vx + vy * vy;\n\n    return (\n      this.minVelocitySq !== Number.MAX_SAFE_INTEGER &&\n      velocitySq >= this.minVelocitySq\n    );\n  }\n\n  private shouldFail(): boolean {\n    const dx: number = this.getTranslationX();\n    const dy: number = this.getTranslationY();\n    const distanceSq = dx * dx + dy * dy;\n\n    if (this.activateAfterLongPress > 0 && distanceSq > DEFAULT_MIN_DIST_SQ) {\n      this.clearActivationTimeout();\n      return true;\n    }\n\n    if (\n      this.failOffsetXStart !== Number.MIN_SAFE_INTEGER &&\n      dx < this.failOffsetXStart\n    ) {\n      return true;\n    }\n\n    if (\n      this.failOffsetXEnd !== Number.MAX_SAFE_INTEGER &&\n      dx > this.failOffsetXEnd\n    ) {\n      return true;\n    }\n\n    if (\n      this.failOffsetYStart !== Number.MIN_SAFE_INTEGER &&\n      dy < this.failOffsetYStart\n    ) {\n      return true;\n    }\n\n    return (\n      this.failOffsetYEnd !== Number.MAX_SAFE_INTEGER &&\n      dy > this.failOffsetYEnd\n    );\n  }\n\n  private tryBegin(event: AdaptedEvent): void {\n    if (\n      this.currentState === State.UNDETERMINED &&\n      this.tracker.getTrackedPointersCount() >= this.minPointers\n    ) {\n      this.resetProgress();\n      this.offsetX = 0;\n      this.offsetY = 0;\n      this.velocityX = 0;\n      this.velocityY = 0;\n\n      this.begin();\n\n      if (this.activateAfterLongPress > 0) {\n        this.activationTimeout = setTimeout(() => {\n          this.activate();\n        }, this.activateAfterLongPress);\n      }\n    } else {\n      this.velocityX = this.tracker.getVelocityX(event.pointerId);\n      this.velocityY = this.tracker.getVelocityY(event.pointerId);\n    }\n  }\n\n  private checkBegan(): void {\n    if (this.currentState === State.BEGAN) {\n      if (this.shouldFail()) {\n        this.fail();\n      } else if (this.shouldActivate()) {\n        this.activate();\n      }\n    }\n  }\n\n  public activate(force = false): void {\n    if (this.currentState !== State.ACTIVE) {\n      this.resetProgress();\n    }\n\n    super.activate(force);\n  }\n\n  protected onCancel(): void {\n    this.clearActivationTimeout();\n  }\n\n  protected onReset(): void {\n    this.clearActivationTimeout();\n  }\n\n  protected resetProgress(): void {\n    if (this.currentState === State.ACTIVE) {\n      return;\n    }\n\n    this.startX = this.lastX;\n    this.startY = this.lastY;\n  }\n}\n"]}