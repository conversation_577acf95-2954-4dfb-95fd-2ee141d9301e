{"version": 3, "sources": ["PinchGestureHandler.ts"], "names": ["pinchHandlerName", "PinchGestureHandler", "name", "allowedProps", "baseGestureHandlerProps", "config"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAmCO,MAAMA,gBAAgB,GAAG,qBAAzB;;AAGP;AACO,MAAMC,mBAAmB,GAAG,4BAGjC;AACAC,EAAAA,IAAI,EAAEF,gBADN;AAEAG,EAAAA,YAAY,EAAEC,6CAFd;AAGAC,EAAAA,MAAM,EAAE;AAHR,CAHiC,CAA5B", "sourcesContent": ["import createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\nexport type PinchGestureHandlerEventPayload = {\n  /**\n   * The scale factor relative to the points of the two touches in screen\n   * coordinates.\n   */\n  scale: number;\n\n  /**\n   * Position expressed in points along X axis of center anchor point of\n   * gesture.\n   */\n  focalX: number;\n\n  /**\n   * Position expressed in points along Y axis of center anchor point of\n   * gesture.\n   */\n  focalY: number;\n\n  /**\n   *\n   * Velocity of the pan gesture the current moment. The value is expressed in\n   * point units per second.\n   */\n  velocity: number;\n};\n\nexport interface PinchGestureHandlerProps\n  extends BaseGestureHandlerProps<PinchGestureHandlerEventPayload> {}\n\nexport const pinchHandlerName = 'PinchGestureHandler';\n\nexport type PinchGestureHandler = typeof PinchGestureHandler;\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const PinchGestureHandler = createHandler<\n  PinchGestureHandlerProps,\n  PinchGestureHandlerEventPayload\n>({\n  name: pinchHandlerName,\n  allowedProps: baseGestureHandlerProps,\n  config: {},\n});\n"]}