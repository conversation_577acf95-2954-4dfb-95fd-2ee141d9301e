{"name": "psg-bmi-portal", "version": "1.0.0", "description": "Portal Karyawan Mobile App - PSG-BMI with modern mBanking design", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "keywords": ["react-native", "expo", "employee-portal", "psg-bmi", "mobile-app"], "author": "PSG-BMI Team", "license": "MIT", "dependencies": {"expo": "~49.0.0", "react": "18.2.0", "react-native": "0.72.10", "react-native-safe-area-context": "4.6.3", "@expo/vector-icons": "^13.0.0", "expo-status-bar": "~1.6.0", "expo-font": "~11.4.0", "expo-splash-screen": "~0.20.5", "react-native-screens": "~3.22.0", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}