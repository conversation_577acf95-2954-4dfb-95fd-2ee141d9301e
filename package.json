{"name": "psg-bmi-portal", "version": "1.0.0", "description": "Portal Karyawan Mobile App - PSG-BMI with modern mBanking design", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "verify": "node scripts/verify-setup.js", "clean": "expo start -c"}, "keywords": ["react-native", "expo", "employee-portal", "psg-bmi", "mobile-app"], "author": "PSG-BMI Team", "license": "MIT", "dependencies": {"@expo/vector-icons": "^14.0.0", "@expo/webpack-config": "^19.0.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "expo": "~50.0.0", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.73.6", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-web": "~0.19.6", "stream-browserify": "^3.0.0", "vm-browserify": "^1.1.2"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}