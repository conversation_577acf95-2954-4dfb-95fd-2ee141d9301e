{"ast": null, "code": "export function getFilename(url) {\n  var _URL = new URL(url, 'https://e'),\n    pathname = _URL.pathname,\n    searchParams = _URL.searchParams;\n  if (__DEV__) {\n    if (searchParams.has('unstable_path')) {\n      var encodedFilePath = decodeURIComponent(searchParams.get('unstable_path'));\n      return getBasename(encodedFilePath);\n    }\n  }\n  return getBasename(pathname);\n}\nfunction getBasename(pathname) {\n  return pathname.substring(pathname.lastIndexOf('/') + 1);\n}\nexport function getFileExtension(url) {\n  var filename = getFilename(url);\n  var dotIndex = filename.lastIndexOf('.');\n  return dotIndex > 0 ? filename.substring(dotIndex) : '';\n}\nexport function getManifestBaseUrl(manifestUrl) {\n  var urlObject = new URL(manifestUrl);\n  var nextProtocol = urlObject.protocol;\n  if (nextProtocol === 'exp:') {\n    nextProtocol = 'http:';\n  } else if (nextProtocol === 'exps:') {\n    nextProtocol = 'https:';\n  }\n  urlObject.protocol = nextProtocol;\n  var directory = urlObject.pathname.substring(0, urlObject.pathname.lastIndexOf('/') + 1);\n  urlObject.pathname = directory;\n  urlObject.search = '';\n  urlObject.hash = '';\n  return urlObject.protocol !== nextProtocol ? urlObject.href.replace(urlObject.protocol, nextProtocol) : urlObject.href;\n}", "map": {"version": 3, "names": ["getFilename", "url", "_URL", "URL", "pathname", "searchParams", "__DEV__", "has", "encodedFilePath", "decodeURIComponent", "get", "getBasename", "substring", "lastIndexOf", "getFileExtension", "filename", "dotIndex", "getManifestBaseUrl", "manifestUrl", "urlObject", "nextProtocol", "protocol", "directory", "search", "hash", "href", "replace"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\expo-asset\\src\\AssetUris.ts"], "sourcesContent": ["export function getFilename(url: string): string {\n  const { pathname, searchParams } = new URL(url, 'https://e');\n\n  // When attached to a dev server, we use `unstable_path` to represent the file path. This ensures\n  // the file name is not canonicalized by the browser.\n  // NOTE(EvanBacon): This is technically not tied to `__DEV__` as it's possible to use this while bundling in production\n  // mode.\n  if (__DEV__) {\n    if (searchParams.has('unstable_path')) {\n      const encodedFilePath = decodeURIComponent(searchParams.get('unstable_path')!);\n      return getBasename(encodedFilePath);\n    }\n  }\n\n  return getBasename(pathname);\n}\n\nfunction getBasename(pathname: string): string {\n  return pathname.substring(pathname.lastIndexOf('/') + 1);\n}\n\nexport function getFileExtension(url: string): string {\n  const filename = getFilename(url);\n  const dotIndex = filename.lastIndexOf('.');\n  // Ignore leading dots for hidden files\n  return dotIndex > 0 ? filename.substring(dotIndex) : '';\n}\n\n/**\n * Returns the base URL from a manifest's URL. For example, given a manifest hosted at\n * https://example.com/app/manifest.json, the base URL would be https://example.com/app/. Query\n * parameters and fragments also are removed.\n *\n * For an Expo-hosted project with a manifest hosted at https://exp.host/@user/project/index.exp, the\n * base URL would be https://exp.host/@user/project.\n *\n * We also normalize the \"exp\" protocol to \"http\" to handle internal URLs with the Expo schemes used\n * to tell the OS to open the URLs in the the Expo client.\n */\nexport function getManifestBaseUrl(manifestUrl: string): string {\n  const urlObject = new URL(manifestUrl);\n\n  let nextProtocol = urlObject.protocol;\n  // Change the scheme to http(s) if it is exp(s)\n  if (nextProtocol === 'exp:') {\n    nextProtocol = 'http:';\n  } else if (nextProtocol === 'exps:') {\n    nextProtocol = 'https:';\n  }\n  urlObject.protocol = nextProtocol;\n\n  // Trim filename, query parameters, and fragment, if any\n  const directory = urlObject.pathname.substring(0, urlObject.pathname.lastIndexOf('/') + 1);\n  urlObject.pathname = directory;\n  urlObject.search = '';\n  urlObject.hash = '';\n\n  // The URL spec doesn't allow for changing the protocol to `http` or `https`\n  // without a port set so instead, we'll just swap the protocol manually.\n  return urlObject.protocol !== nextProtocol\n    ? urlObject.href.replace(urlObject.protocol, nextProtocol)\n    : urlObject.href;\n}\n"], "mappings": "AAAA,OAAM,SAAUA,WAAWA,CAACC,GAAW;EACrC,IAAAC,IAAA,GAAmC,IAAIC,GAAG,CAACF,GAAG,EAAE,WAAW,CAAC;IAApDG,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IAAEC,YAAY,GAAAH,IAAA,CAAZG,YAAY;EAM9B,IAAIC,OAAO,EAAE;IACX,IAAID,YAAY,CAACE,GAAG,CAAC,eAAe,CAAC,EAAE;MACrC,IAAMC,eAAe,GAAGC,kBAAkB,CAACJ,YAAY,CAACK,GAAG,CAAC,eAAe,CAAE,CAAC;MAC9E,OAAOC,WAAW,CAACH,eAAe,CAAC;;;EAIvC,OAAOG,WAAW,CAACP,QAAQ,CAAC;AAC9B;AAEA,SAASO,WAAWA,CAACP,QAAgB;EACnC,OAAOA,QAAQ,CAACQ,SAAS,CAACR,QAAQ,CAACS,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1D;AAEA,OAAM,SAAUC,gBAAgBA,CAACb,GAAW;EAC1C,IAAMc,QAAQ,GAAGf,WAAW,CAACC,GAAG,CAAC;EACjC,IAAMe,QAAQ,GAAGD,QAAQ,CAACF,WAAW,CAAC,GAAG,CAAC;EAE1C,OAAOG,QAAQ,GAAG,CAAC,GAAGD,QAAQ,CAACH,SAAS,CAACI,QAAQ,CAAC,GAAG,EAAE;AACzD;AAaA,OAAM,SAAUC,kBAAkBA,CAACC,WAAmB;EACpD,IAAMC,SAAS,GAAG,IAAIhB,GAAG,CAACe,WAAW,CAAC;EAEtC,IAAIE,YAAY,GAAGD,SAAS,CAACE,QAAQ;EAErC,IAAID,YAAY,KAAK,MAAM,EAAE;IAC3BA,YAAY,GAAG,OAAO;GACvB,MAAM,IAAIA,YAAY,KAAK,OAAO,EAAE;IACnCA,YAAY,GAAG,QAAQ;;EAEzBD,SAAS,CAACE,QAAQ,GAAGD,YAAY;EAGjC,IAAME,SAAS,GAAGH,SAAS,CAACf,QAAQ,CAACQ,SAAS,CAAC,CAAC,EAAEO,SAAS,CAACf,QAAQ,CAACS,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1FM,SAAS,CAACf,QAAQ,GAAGkB,SAAS;EAC9BH,SAAS,CAACI,MAAM,GAAG,EAAE;EACrBJ,SAAS,CAACK,IAAI,GAAG,EAAE;EAInB,OAAOL,SAAS,CAACE,QAAQ,KAAKD,YAAY,GACtCD,SAAS,CAACM,IAAI,CAACC,OAAO,CAACP,SAAS,CAACE,QAAQ,EAAED,YAAY,CAAC,GACxDD,SAAS,CAACM,IAAI;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}