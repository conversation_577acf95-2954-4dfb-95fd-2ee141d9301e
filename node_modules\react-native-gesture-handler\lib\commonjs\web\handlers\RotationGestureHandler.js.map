{"version": 3, "sources": ["RotationGestureHandler.ts"], "names": ["ROTATION_RECOGNITION_THRESHOLD", "Math", "PI", "RotationGestureHandler", "Gesture<PERSON>andler", "onRotationBegin", "_detector", "onRotation", "detector", "previousRotation", "rotation", "getRotation", "delta", "getTimeDelta", "velocity", "abs", "currentState", "State", "BEGAN", "activate", "onRotationEnd", "end", "RotationGestureDetector", "rotationGestureListener", "init", "ref", "propsRef", "setShouldCancelWhenOutside", "updateGestureConfig", "enabled", "props", "transformNativeEvent", "anchorX", "getAnchorX", "anchorY", "getAnchorY", "rotationGestureDetector", "cachedAnchorX", "cachedAnchorY", "onPointerDown", "event", "tracker", "addToTracker", "onPointerAdd", "tryBegin", "onTouchEvent", "onPointerMove", "getTrackedPointersCount", "track", "onPointerOutOfBounds", "onPointerUp", "removeFromTracker", "pointerId", "ACTIVE", "fail", "onPointerRemove", "UNDETERMINED", "begin", "_force", "onReset", "reset"], "mappings": ";;;;;;;AAAA;;AAGA;;AACA;;;;;;AAIA,MAAMA,8BAA8B,GAAGC,IAAI,CAACC,EAAL,GAAU,EAAjD;;AAEe,MAAMC,sBAAN,SAAqCC,uBAArC,CAAoD;AAAA;AAAA;;AAAA,sCAC9C,CAD8C;;AAAA,sCAE9C,CAF8C;;AAAA,2CAIzC,CAJyC;;AAAA,2CAKzC,CALyC;;AAAA,qDAON;AACzDC,MAAAA,eAAe,EAAGC,SAAD,IAAiD,IADT;AAEzDC,MAAAA,UAAU,EAAGC,QAAD,IAAgD;AAC1D,cAAMC,gBAAwB,GAAG,KAAKC,QAAtC;AACA,aAAKA,QAAL,IAAiBF,QAAQ,CAACG,WAAT,EAAjB;AAEA,cAAMC,KAAK,GAAGJ,QAAQ,CAACK,YAAT,EAAd;;AAEA,YAAID,KAAK,GAAG,CAAZ,EAAe;AACb,eAAKE,QAAL,GAAgB,CAAC,KAAKJ,QAAL,GAAgBD,gBAAjB,IAAqCG,KAArD;AACD;;AAED,YACEX,IAAI,CAACc,GAAL,CAAS,KAAKL,QAAd,KAA2BV,8BAA3B,IACA,KAAKgB,YAAL,KAAsBC,aAAMC,KAF9B,EAGE;AACA,eAAKC,QAAL;AACD;;AAED,eAAO,IAAP;AACD,OApBwD;AAqBzDC,MAAAA,aAAa,EAAGd,SAAD,IAA8C;AAC3D,aAAKe,GAAL;AACD;AAvBwD,KAPM;;AAAA,qDAkC/D,IAAIC,gCAAJ,CAA4B,KAAKC,uBAAjC,CAlC+D;AAAA;;AAoC1DC,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAwD;AACjE,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AAEA,SAAKC,0BAAL,CAAgC,KAAhC;AACD;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;AACD;;AAESC,EAAAA,oBAAoB,GAAG;AAC/B,WAAO;AACLrB,MAAAA,QAAQ,EAAE,KAAKA,QAAL,GAAgB,KAAKA,QAArB,GAAgC,CADrC;AAELsB,MAAAA,OAAO,EAAE,KAAKC,UAAL,EAFJ;AAGLC,MAAAA,OAAO,EAAE,KAAKC,UAAL,EAHJ;AAILrB,MAAAA,QAAQ,EAAE,KAAKA,QAAL,GAAgB,KAAKA,QAArB,GAAgC;AAJrC,KAAP;AAMD;;AAEMmB,EAAAA,UAAU,GAAW;AAC1B,UAAMD,OAAO,GAAG,KAAKI,uBAAL,CAA6BH,UAA7B,EAAhB;AAEA,WAAOD,OAAO,GAAGA,OAAH,GAAa,KAAKK,aAAhC;AACD;;AAEMF,EAAAA,UAAU,GAAW;AAC1B,UAAMD,OAAO,GAAG,KAAKE,uBAAL,CAA6BD,UAA7B,EAAhB;AAEA,WAAOD,OAAO,GAAGA,OAAH,GAAa,KAAKI,aAAhC;AACD;;AAESC,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AACD;;AAESG,EAAAA,YAAY,CAACH,KAAD,EAA4B;AAChD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMG,YAAN,CAAmBH,KAAnB;AAEA,SAAKI,QAAL;AACA,SAAKR,uBAAL,CAA6BS,YAA7B,CAA0CL,KAA1C,EAAiD,KAAKC,OAAtD;AACD;;AAESK,EAAAA,aAAa,CAACN,KAAD,EAA4B;AACjD,QAAI,KAAKC,OAAL,CAAaM,uBAAb,KAAyC,CAA7C,EAAgD;AAC9C;AACD;;AAED,QAAI,KAAKd,UAAL,EAAJ,EAAuB;AACrB,WAAKI,aAAL,GAAqB,KAAKJ,UAAL,EAArB;AACD;;AACD,QAAI,KAAKE,UAAL,EAAJ,EAAuB;AACrB,WAAKG,aAAL,GAAqB,KAAKH,UAAL,EAArB;AACD;;AAED,SAAKM,OAAL,CAAaO,KAAb,CAAmBR,KAAnB;AAEA,SAAKJ,uBAAL,CAA6BS,YAA7B,CAA0CL,KAA1C,EAAiD,KAAKC,OAAtD;AAEA,UAAMK,aAAN,CAAoBN,KAApB;AACD;;AAESS,EAAAA,oBAAoB,CAACT,KAAD,EAA4B;AACxD,QAAI,KAAKC,OAAL,CAAaM,uBAAb,KAAyC,CAA7C,EAAgD;AAC9C;AACD;;AAED,QAAI,KAAKd,UAAL,EAAJ,EAAuB;AACrB,WAAKI,aAAL,GAAqB,KAAKJ,UAAL,EAArB;AACD;;AACD,QAAI,KAAKE,UAAL,EAAJ,EAAuB;AACrB,WAAKG,aAAL,GAAqB,KAAKH,UAAL,EAArB;AACD;;AAED,SAAKM,OAAL,CAAaO,KAAb,CAAmBR,KAAnB;AAEA,SAAKJ,uBAAL,CAA6BS,YAA7B,CAA0CL,KAA1C,EAAiD,KAAKC,OAAtD;AAEA,UAAMQ,oBAAN,CAA2BT,KAA3B;AACD;;AAESU,EAAAA,WAAW,CAACV,KAAD,EAA4B;AAC/C,UAAMU,WAAN,CAAkBV,KAAlB;AACA,SAAKC,OAAL,CAAaU,iBAAb,CAA+BX,KAAK,CAACY,SAArC;AACA,SAAKhB,uBAAL,CAA6BS,YAA7B,CAA0CL,KAA1C,EAAiD,KAAKC,OAAtD;;AAEA,QAAI,KAAKzB,YAAL,KAAsBC,aAAMoC,MAAhC,EAAwC;AACtC;AACD;;AAED,QAAI,KAAKrC,YAAL,KAAsBC,aAAMoC,MAAhC,EAAwC;AACtC,WAAKhC,GAAL;AACD,KAFD,MAEO;AACL,WAAKiC,IAAL;AACD;AACF;;AAESC,EAAAA,eAAe,CAACf,KAAD,EAA4B;AACnD,UAAMe,eAAN,CAAsBf,KAAtB;AACA,SAAKJ,uBAAL,CAA6BS,YAA7B,CAA0CL,KAA1C,EAAiD,KAAKC,OAAtD;AACA,SAAKA,OAAL,CAAaU,iBAAb,CAA+BX,KAAK,CAACY,SAArC;AACD;;AAESR,EAAAA,QAAQ,GAAS;AACzB,QAAI,KAAK5B,YAAL,KAAsBC,aAAMuC,YAAhC,EAA8C;AAC5C;AACD;;AAED,SAAKC,KAAL;AACD;;AAEMtC,EAAAA,QAAQ,CAACuC,MAAD,EAAyB;AACtC,UAAMvC,QAAN;AACD;;AAESwC,EAAAA,OAAO,GAAS;AACxB,QAAI,KAAK3C,YAAL,KAAsBC,aAAMoC,MAAhC,EAAwC;AACtC;AACD;;AAED,SAAK3C,QAAL,GAAgB,CAAhB;AACA,SAAKI,QAAL,GAAgB,CAAhB;AACA,SAAKsB,uBAAL,CAA6BwB,KAA7B;AACD;;AAhKgE", "sourcesContent": ["import { State } from '../../State';\nimport { AdaptedEvent, Config } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\nimport RotationGestureDetector, {\n  RotationGestureListener,\n} from '../detectors/RotationGestureDetector';\n\nconst ROTATION_RECOGNITION_THRESHOLD = Math.PI / 36;\n\nexport default class RotationGestureHandler extends GestureHandler {\n  private rotation = 0;\n  private velocity = 0;\n\n  private cachedAnchorX = 0;\n  private cachedAnchorY = 0;\n\n  private rotationGestureListener: RotationGestureListener = {\n    onRotationBegin: (_detector: RotationGestureDetector): boolean => true,\n    onRotation: (detector: RotationGestureDetector): boolean => {\n      const previousRotation: number = this.rotation;\n      this.rotation += detector.getRotation();\n\n      const delta = detector.getTimeDelta();\n\n      if (delta > 0) {\n        this.velocity = (this.rotation - previousRotation) / delta;\n      }\n\n      if (\n        Math.abs(this.rotation) >= ROTATION_RECOGNITION_THRESHOLD &&\n        this.currentState === State.BEGAN\n      ) {\n        this.activate();\n      }\n\n      return true;\n    },\n    onRotationEnd: (_detector: RotationGestureDetector): void => {\n      this.end();\n    },\n  };\n\n  private rotationGestureDetector: RotationGestureDetector =\n    new RotationGestureDetector(this.rotationGestureListener);\n\n  public init(ref: number, propsRef: React.RefObject<unknown>): void {\n    super.init(ref, propsRef);\n\n    this.setShouldCancelWhenOutside(false);\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n  }\n\n  protected transformNativeEvent() {\n    return {\n      rotation: this.rotation ? this.rotation : 0,\n      anchorX: this.getAnchorX(),\n      anchorY: this.getAnchorY(),\n      velocity: this.velocity ? this.velocity : 0,\n    };\n  }\n\n  public getAnchorX(): number {\n    const anchorX = this.rotationGestureDetector.getAnchorX();\n\n    return anchorX ? anchorX : this.cachedAnchorX;\n  }\n\n  public getAnchorY(): number {\n    const anchorY = this.rotationGestureDetector.getAnchorY();\n\n    return anchorY ? anchorY : this.cachedAnchorY;\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n\n    this.tryBegin();\n    this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    if (this.tracker.getTrackedPointersCount() < 2) {\n      return;\n    }\n\n    if (this.getAnchorX()) {\n      this.cachedAnchorX = this.getAnchorX();\n    }\n    if (this.getAnchorY()) {\n      this.cachedAnchorY = this.getAnchorY();\n    }\n\n    this.tracker.track(event);\n\n    this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n\n    super.onPointerMove(event);\n  }\n\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    if (this.tracker.getTrackedPointersCount() < 2) {\n      return;\n    }\n\n    if (this.getAnchorX()) {\n      this.cachedAnchorX = this.getAnchorX();\n    }\n    if (this.getAnchorY()) {\n      this.cachedAnchorY = this.getAnchorY();\n    }\n\n    this.tracker.track(event);\n\n    this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n\n    super.onPointerOutOfBounds(event);\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.tracker.removeFromTracker(event.pointerId);\n    this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n\n    if (this.currentState !== State.ACTIVE) {\n      return;\n    }\n\n    if (this.currentState === State.ACTIVE) {\n      this.end();\n    } else {\n      this.fail();\n    }\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n    this.tracker.removeFromTracker(event.pointerId);\n  }\n\n  protected tryBegin(): void {\n    if (this.currentState !== State.UNDETERMINED) {\n      return;\n    }\n\n    this.begin();\n  }\n\n  public activate(_force?: boolean): void {\n    super.activate();\n  }\n\n  protected onReset(): void {\n    if (this.currentState === State.ACTIVE) {\n      return;\n    }\n\n    this.rotation = 0;\n    this.velocity = 0;\n    this.rotationGestureDetector.reset();\n  }\n}\n"]}