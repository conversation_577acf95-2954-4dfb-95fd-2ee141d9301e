{"version": 3, "sources": ["HoverGestureHandler.ts"], "names": ["State", "GestureHandlerOrchestrator", "Gesture<PERSON>andler", "HoverGestureHandler", "init", "ref", "propsRef", "updateGestureConfig", "enabled", "props", "onPointerMoveOver", "event", "getInstance", "recordHandlerIfNotPresent", "tracker", "addToTracker", "getState", "UNDETERMINED", "begin", "activate", "onPointerMoveOut", "end", "onPointerMove", "track", "onPointerCancel", "reset"], "mappings": "AAAA,SAASA,KAAT,QAAsB,aAAtB;AAEA,OAAOC,0BAAP,MAAuC,qCAAvC;AACA,OAAOC,cAAP,MAA2B,kBAA3B;AAEA,eAAe,MAAMC,mBAAN,SAAkCD,cAAlC,CAAiD;AACvDE,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAkD;AAC3D,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AACD;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;AACD;;AAESC,EAAAA,iBAAiB,CAACC,KAAD,EAA4B;AACrDV,IAAAA,0BAA0B,CAACW,WAA3B,GAAyCC,yBAAzC,CAAmE,IAAnE;AAEA,SAAKC,OAAL,CAAaC,YAAb,CAA0BJ,KAA1B;AACA,UAAMD,iBAAN,CAAwBC,KAAxB;;AAEA,QAAI,KAAKK,QAAL,OAAoBhB,KAAK,CAACiB,YAA9B,EAA4C;AAC1C,WAAKC,KAAL;AACA,WAAKC,QAAL;AACD;AACF;;AAESC,EAAAA,gBAAgB,CAACT,KAAD,EAA4B;AACpD,SAAKG,OAAL,CAAaC,YAAb,CAA0BJ,KAA1B;AACA,UAAMS,gBAAN,CAAuBT,KAAvB;AAEA,SAAKU,GAAL;AACD;;AAESC,EAAAA,aAAa,CAACX,KAAD,EAA4B;AACjD,SAAKG,OAAL,CAAaS,KAAb,CAAmBZ,KAAnB;AACA,UAAMW,aAAN,CAAoBX,KAApB;AACD;;AAESa,EAAAA,eAAe,CAACb,KAAD,EAA4B;AACnD,UAAMa,eAAN,CAAsBb,KAAtB;AACA,SAAKc,KAAL;AACD;;AApC6D", "sourcesContent": ["import { State } from '../../State';\nimport { AdaptedEvent, Config } from '../interfaces';\nimport GestureHandlerOrchestrator from '../tools/GestureHandlerOrchestrator';\nimport GestureHandler from './GestureHandler';\n\nexport default class HoverGestureHandler extends GestureHandler {\n  public init(ref: number, propsRef: React.RefObject<unknown>) {\n    super.init(ref, propsRef);\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n  }\n\n  protected onPointerMoveOver(event: AdaptedEvent): void {\n    GestureHandlerOrchestrator.getInstance().recordHandlerIfNotPresent(this);\n\n    this.tracker.addToTracker(event);\n    super.onPointerMoveOver(event);\n\n    if (this.getState() === State.UNDETERMINED) {\n      this.begin();\n      this.activate();\n    }\n  }\n\n  protected onPointerMoveOut(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerMoveOut(event);\n\n    this.end();\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n    super.onPointerMove(event);\n  }\n\n  protected onPointerCancel(event: AdaptedEvent): void {\n    super.onPointerCancel(event);\n    this.reset();\n  }\n}\n"]}