{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/Octicons.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/Octicons.json\";\nexport default createIconSet(glyphMap, 'octicons', font);", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap"], "sources": ["D:\\aplikasi\\TRAE\\psg-bmi\\node_modules\\@expo\\vector-icons\\src\\Octicons.ts"], "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/Octicons.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/Octicons.json';\n\nexport default createIconSet(glyphMap, 'octicons', font);\n"], "mappings": "AAAA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,eAAeF,aAAa,CAACE,QAAQ,EAAE,UAAU,EAAED,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}