{"version": 3, "sources": ["PointerEventManager.ts"], "names": ["POINTER_CAPTURE_EXCLUDE_LIST", "Set", "PointerEventManager", "EventManager", "setListeners", "view", "addEventListener", "event", "pointerType", "PointerType", "TOUCH", "x", "clientX", "y", "clientY", "adaptedEvent", "mapEvent", "EventTypes", "DOWN", "target", "has", "tagName", "setPointerCapture", "pointerId", "markAsInBounds", "trackedPointers", "add", "activePointersCounter", "eventType", "ADDITIONAL_POINTER_DOWN", "onPointerAdd", "onPointerDown", "UP", "releasePointerCapture", "markAsOutOfBounds", "delete", "ADDITIONAL_POINTER_UP", "onPointerRemove", "onPointerUp", "MOVE", "hasPointerCapture", "inBounds", "pointerIndex", "pointersInBounds", "indexOf", "ENTER", "onPointerEnter", "onPointerMove", "LEAVE", "onPointerLeave", "onPointerOutOfBounds", "CANCEL", "onPointerCancel", "clear", "onPointerMoveOver", "onPointerMoveOut", "offsetX", "offsetY", "buttons", "time", "timeStamp", "resetManager"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;;;;;AAEA,MAAMA,4BAA4B,GAAG,IAAIC,GAAJ,CAAgB,CAAC,QAAD,EAAW,OAAX,CAAhB,CAArC;;AAEe,MAAMC,mBAAN,SAAkCC,qBAAlC,CAA4D;AAAA;AAAA;;AAAA,6CAC/C,IAAIF,GAAJ,EAD+C;AAAA;;AAGlEG,EAAAA,YAAY,GAAS;AAC1B,SAAKC,IAAL,CAAUC,gBAAV,CAA2B,aAA3B,EAA2CC,KAAD,IAA+B;AACvE,UAAIA,KAAK,CAACC,WAAN,KAAsBC,wBAAYC,KAAtC,EAA6C;AAC3C;AACD;;AACD,UACE,CAAC,8BAAkB,KAAKL,IAAvB,EAA6B;AAAEM,QAAAA,CAAC,EAAEJ,KAAK,CAACK,OAAX;AAAoBC,QAAAA,CAAC,EAAEN,KAAK,CAACO;AAA7B,OAA7B,CADH,EAEE;AACA;AACD;;AAED,YAAMC,YAA0B,GAAG,KAAKC,QAAL,CAAcT,KAAd,EAAqBU,uBAAWC,IAAhC,CAAnC;AACA,YAAMC,MAAM,GAAGZ,KAAK,CAACY,MAArB;;AAEA,UAAI,CAACnB,4BAA4B,CAACoB,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAAL,EAAuD;AACrDF,QAAAA,MAAM,CAACG,iBAAP,CAAyBP,YAAY,CAACQ,SAAtC;AACD;;AAED,WAAKC,cAAL,CAAoBT,YAAY,CAACQ,SAAjC;AACA,WAAKE,eAAL,CAAqBC,GAArB,CAAyBX,YAAY,CAACQ,SAAtC;;AAEA,UAAI,EAAE,KAAKI,qBAAP,GAA+B,CAAnC,EAAsC;AACpCZ,QAAAA,YAAY,CAACa,SAAb,GAAyBX,uBAAWY,uBAApC;AACA,aAAKC,YAAL,CAAkBf,YAAlB;AACD,OAHD,MAGO;AACL,aAAKgB,aAAL,CAAmBhB,YAAnB;AACD;AACF,KA1BD;AA4BA,SAAKV,IAAL,CAAUC,gBAAV,CAA2B,WAA3B,EAAyCC,KAAD,IAA+B;AACrE,UAAIA,KAAK,CAACC,WAAN,KAAsBC,wBAAYC,KAAtC,EAA6C;AAC3C;AACD,OAHoE,CAKrE;AACA;AACA;AACA;;;AACA,UAAI,KAAKiB,qBAAL,KAA+B,CAAnC,EAAsC;AACpC;AACD;;AAED,YAAMZ,YAA0B,GAAG,KAAKC,QAAL,CAAcT,KAAd,EAAqBU,uBAAWe,EAAhC,CAAnC;AACA,YAAMb,MAAM,GAAGZ,KAAK,CAACY,MAArB;;AAEA,UAAI,CAACnB,4BAA4B,CAACoB,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAAL,EAAuD;AACrDF,QAAAA,MAAM,CAACc,qBAAP,CAA6BlB,YAAY,CAACQ,SAA1C;AACD;;AAED,WAAKW,iBAAL,CAAuBnB,YAAY,CAACQ,SAApC;AACA,WAAKE,eAAL,CAAqBU,MAArB,CAA4BpB,YAAY,CAACQ,SAAzC;;AAEA,UAAI,EAAE,KAAKI,qBAAP,GAA+B,CAAnC,EAAsC;AACpCZ,QAAAA,YAAY,CAACa,SAAb,GAAyBX,uBAAWmB,qBAApC;AACA,aAAKC,eAAL,CAAqBtB,YAArB;AACD,OAHD,MAGO;AACL,aAAKuB,WAAL,CAAiBvB,YAAjB;AACD;AACF,KA7BD;AA+BA,SAAKV,IAAL,CAAUC,gBAAV,CAA2B,aAA3B,EAA2CC,KAAD,IAA+B;AACvE,UAAIA,KAAK,CAACC,WAAN,KAAsBC,wBAAYC,KAAtC,EAA6C;AAC3C;AACD;;AAED,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcT,KAAd,EAAqBU,uBAAWsB,IAAhC,CAAnC;AACA,YAAMpB,MAAM,GAAGZ,KAAK,CAACY,MAArB,CANuE,CAQvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,UACE,CAACA,MAAM,CAACqB,iBAAP,CAAyBjC,KAAK,CAACgB,SAA/B,CAAD,IACA,CAACvB,4BAA4B,CAACoB,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAFH,EAGE;AACAF,QAAAA,MAAM,CAACG,iBAAP,CAAyBf,KAAK,CAACgB,SAA/B;AACD;;AAED,YAAMkB,QAAiB,GAAG,8BAAkB,KAAKpC,IAAvB,EAA6B;AACrDM,QAAAA,CAAC,EAAEI,YAAY,CAACJ,CADqC;AAErDE,QAAAA,CAAC,EAAEE,YAAY,CAACF;AAFqC,OAA7B,CAA1B;AAKA,YAAM6B,YAAoB,GAAG,KAAKC,gBAAL,CAAsBC,OAAtB,CAC3B7B,YAAY,CAACQ,SADc,CAA7B;;AAIA,UAAIkB,QAAJ,EAAc;AACZ,YAAIC,YAAY,GAAG,CAAnB,EAAsB;AACpB3B,UAAAA,YAAY,CAACa,SAAb,GAAyBX,uBAAW4B,KAApC;AACA,eAAKC,cAAL,CAAoB/B,YAApB;AACA,eAAKS,cAAL,CAAoBT,YAAY,CAACQ,SAAjC;AACD,SAJD,MAIO;AACL,eAAKwB,aAAL,CAAmBhC,YAAnB;AACD;AACF,OARD,MAQO;AACL,YAAI2B,YAAY,IAAI,CAApB,EAAuB;AACrB3B,UAAAA,YAAY,CAACa,SAAb,GAAyBX,uBAAW+B,KAApC;AACA,eAAKC,cAAL,CAAoBlC,YAApB;AACA,eAAKmB,iBAAL,CAAuBnB,YAAY,CAACQ,SAApC;AACD,SAJD,MAIO;AACL,eAAK2B,oBAAL,CAA0BnC,YAA1B;AACD;AACF;AACF,KArDD;AAuDA,SAAKV,IAAL,CAAUC,gBAAV,CAA2B,eAA3B,EAA6CC,KAAD,IAA+B;AACzE,UAAIA,KAAK,CAACC,WAAN,KAAsBC,wBAAYC,KAAtC,EAA6C;AAC3C;AACD;;AAED,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CACjCT,KADiC,EAEjCU,uBAAWkC,MAFsB,CAAnC;AAKA,WAAKC,eAAL,CAAqBrC,YAArB;AACA,WAAKmB,iBAAL,CAAuBnB,YAAY,CAACQ,SAApC;AACA,WAAKI,qBAAL,GAA6B,CAA7B;AACA,WAAKF,eAAL,CAAqB4B,KAArB;AACD,KAdD,EAnH0B,CAmI1B;AACA;AACA;AACA;;AAEA,SAAKhD,IAAL,CAAUC,gBAAV,CAA2B,cAA3B,EAA4CC,KAAD,IAA+B;AACxE,UAAIA,KAAK,CAACC,WAAN,KAAsBC,wBAAYC,KAAtC,EAA6C;AAC3C;AACD;;AAED,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcT,KAAd,EAAqBU,uBAAW4B,KAAhC,CAAnC;AAEA,WAAKS,iBAAL,CAAuBvC,YAAvB;AACD,KARD;AAUA,SAAKV,IAAL,CAAUC,gBAAV,CAA2B,cAA3B,EAA4CC,KAAD,IAA+B;AACxE,UAAIA,KAAK,CAACC,WAAN,KAAsBC,wBAAYC,KAAtC,EAA6C;AAC3C;AACD;;AAED,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcT,KAAd,EAAqBU,uBAAW+B,KAAhC,CAAnC;AAEA,WAAKO,gBAAL,CAAsBxC,YAAtB;AACD,KARD;AAUA,SAAKV,IAAL,CAAUC,gBAAV,CACE,oBADF,EAEGC,KAAD,IAA+B;AAC7B,YAAMQ,YAA0B,GAAG,KAAKC,QAAL,CACjCT,KADiC,EAEjCU,uBAAWkC,MAFsB,CAAnC;;AAKA,UAAI,KAAK1B,eAAL,CAAqBL,GAArB,CAAyBL,YAAY,CAACQ,SAAtC,CAAJ,EAAsD;AACpD;AACA;AACA,aAAK6B,eAAL,CAAqBrC,YAArB;AAEA,aAAKY,qBAAL,GAA6B,CAA7B;AACA,aAAKF,eAAL,CAAqB4B,KAArB;AACD;AACF,KAhBH;AAkBD;;AAESrC,EAAAA,QAAQ,CAACT,KAAD,EAAsBqB,SAAtB,EAA2D;AAC3E,WAAO;AACLjB,MAAAA,CAAC,EAAEJ,KAAK,CAACK,OADJ;AAELC,MAAAA,CAAC,EAAEN,KAAK,CAACO,OAFJ;AAGL0C,MAAAA,OAAO,EAAEjD,KAAK,CAACiD,OAHV;AAILC,MAAAA,OAAO,EAAElD,KAAK,CAACkD,OAJV;AAKLlC,MAAAA,SAAS,EAAEhB,KAAK,CAACgB,SALZ;AAMLK,MAAAA,SAAS,EAAEA,SANN;AAOLpB,MAAAA,WAAW,EAAED,KAAK,CAACC,WAPd;AAQLkD,MAAAA,OAAO,EAAEnD,KAAK,CAACmD,OARV;AASLC,MAAAA,IAAI,EAAEpD,KAAK,CAACqD;AATP,KAAP;AAWD;;AAEMC,EAAAA,YAAY,GAAS;AAC1B,UAAMA,YAAN;AACA,SAAKpC,eAAL,CAAqB4B,KAArB;AACD;;AApMwE", "sourcesContent": ["import { AdaptedEvent, EventTypes, PointerType } from '../interfaces';\nimport EventManager from './EventManager';\nimport { isPointerInBounds } from '../utils';\n\nconst POINTER_CAPTURE_EXCLUDE_LIST = new Set<string>(['SELECT', 'INPUT']);\n\nexport default class PointerEventManager extends EventManager<HTMLElement> {\n  private trackedPointers = new Set<number>();\n\n  public setListeners(): void {\n    this.view.addEventListener('pointerdown', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n      if (\n        !isPointerInBounds(this.view, { x: event.clientX, y: event.clientY })\n      ) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.DOWN);\n      const target = event.target as HTMLElement;\n\n      if (!POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n        target.setPointerCapture(adaptedEvent.pointerId);\n      }\n\n      this.markAsInBounds(adaptedEvent.pointerId);\n      this.trackedPointers.add(adaptedEvent.pointerId);\n\n      if (++this.activePointersCounter > 1) {\n        adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_DOWN;\n        this.onPointerAdd(adaptedEvent);\n      } else {\n        this.onPointerDown(adaptedEvent);\n      }\n    });\n\n    this.view.addEventListener('pointerup', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n\n      // When we call reset on gesture handlers, it also resets their event managers\n      // In some handlers (like RotationGestureHandler) reset is called before all pointers leave view\n      // This means, that activePointersCounter will be set to 0, while there are still remaining pointers on view\n      // Removing them will end in activePointersCounter going below 0, therefore handlers won't behave properly\n      if (this.activePointersCounter === 0) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.UP);\n      const target = event.target as HTMLElement;\n\n      if (!POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n        target.releasePointerCapture(adaptedEvent.pointerId);\n      }\n\n      this.markAsOutOfBounds(adaptedEvent.pointerId);\n      this.trackedPointers.delete(adaptedEvent.pointerId);\n\n      if (--this.activePointersCounter > 0) {\n        adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_UP;\n        this.onPointerRemove(adaptedEvent);\n      } else {\n        this.onPointerUp(adaptedEvent);\n      }\n    });\n\n    this.view.addEventListener('pointermove', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.MOVE);\n      const target = event.target as HTMLElement;\n\n      // You may be wondering why are we setting pointer capture here, when we\n      // already set it in `pointerdown` handler. Well, that's a great question,\n      // for which I don't have an answer. Specification (https://www.w3.org/TR/pointerevents2/#dom-element-setpointercapture)\n      // says that the requirement for `setPointerCapture` to work is that pointer\n      // must be in 'active buttons state`, otherwise it will fail silently, which\n      // is lovely. Obviously, when `pointerdown` is fired, one of the buttons\n      // (when using mouse) is pressed, but that doesn't mean that `setPointerCapture`\n      // will succeed, for some reason. Since it fails silently, we don't actually know\n      // if it worked or not (there's `gotpointercapture` event, but the complexity of\n      // incorporating it here seems stupid), so we just call it again here, every time\n      // pointer moves until it succeeds.\n      // God, I do love web development.\n      if (\n        !target.hasPointerCapture(event.pointerId) &&\n        !POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)\n      ) {\n        target.setPointerCapture(event.pointerId);\n      }\n\n      const inBounds: boolean = isPointerInBounds(this.view, {\n        x: adaptedEvent.x,\n        y: adaptedEvent.y,\n      });\n\n      const pointerIndex: number = this.pointersInBounds.indexOf(\n        adaptedEvent.pointerId\n      );\n\n      if (inBounds) {\n        if (pointerIndex < 0) {\n          adaptedEvent.eventType = EventTypes.ENTER;\n          this.onPointerEnter(adaptedEvent);\n          this.markAsInBounds(adaptedEvent.pointerId);\n        } else {\n          this.onPointerMove(adaptedEvent);\n        }\n      } else {\n        if (pointerIndex >= 0) {\n          adaptedEvent.eventType = EventTypes.LEAVE;\n          this.onPointerLeave(adaptedEvent);\n          this.markAsOutOfBounds(adaptedEvent.pointerId);\n        } else {\n          this.onPointerOutOfBounds(adaptedEvent);\n        }\n      }\n    });\n\n    this.view.addEventListener('pointercancel', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(\n        event,\n        EventTypes.CANCEL\n      );\n\n      this.onPointerCancel(adaptedEvent);\n      this.markAsOutOfBounds(adaptedEvent.pointerId);\n      this.activePointersCounter = 0;\n      this.trackedPointers.clear();\n    });\n\n    // onPointerEnter and onPointerLeave are triggered by a custom logic responsible for\n    // handling shouldCancelWhenOutside flag, and are unreliable unless the pointer is down.\n    // We therefore use pointerenter and pointerleave events to handle the hover gesture,\n    // mapping them to onPointerMoveOver and onPointerMoveOut respectively.\n\n    this.view.addEventListener('pointerenter', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.ENTER);\n\n      this.onPointerMoveOver(adaptedEvent);\n    });\n\n    this.view.addEventListener('pointerleave', (event: PointerEvent): void => {\n      if (event.pointerType === PointerType.TOUCH) {\n        return;\n      }\n\n      const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.LEAVE);\n\n      this.onPointerMoveOut(adaptedEvent);\n    });\n\n    this.view.addEventListener(\n      'lostpointercapture',\n      (event: PointerEvent): void => {\n        const adaptedEvent: AdaptedEvent = this.mapEvent(\n          event,\n          EventTypes.CANCEL\n        );\n\n        if (this.trackedPointers.has(adaptedEvent.pointerId)) {\n          // in some cases the `pointerup` event is not fired, but `lostpointercapture` is\n          // we simulate the `pointercancel` event here to make sure the gesture handler stops tracking it\n          this.onPointerCancel(adaptedEvent);\n\n          this.activePointersCounter = 0;\n          this.trackedPointers.clear();\n        }\n      }\n    );\n  }\n\n  protected mapEvent(event: PointerEvent, eventType: EventTypes): AdaptedEvent {\n    return {\n      x: event.clientX,\n      y: event.clientY,\n      offsetX: event.offsetX,\n      offsetY: event.offsetY,\n      pointerId: event.pointerId,\n      eventType: eventType,\n      pointerType: event.pointerType as PointerType,\n      buttons: event.buttons,\n      time: event.timeStamp,\n    };\n  }\n\n  public resetManager(): void {\n    super.resetManager();\n    this.trackedPointers.clear();\n  }\n}\n"]}