{"version": 3, "sources": ["NodeManager.ts"], "names": ["NodeManager", "<PERSON><PERSON><PERSON><PERSON>", "tag", "gestures", "Error", "createGestureHandler", "handlerTag", "handler", "setTag", "dropGestureHandler", "getNodes"], "mappings": ";;;;;;;;;AAGA;AACe,MAAeA,WAAf,CAA2B;AAMhB,SAAVC,UAAU,CAACC,GAAD,EAAc;AACpC,QAAIA,GAAG,IAAI,KAAKC,QAAhB,EAA0B;AACxB,aAAO,KAAKA,QAAL,CAAcD,GAAd,CAAP;AACD;;AAED,UAAM,IAAIE,KAAJ,CAAW,sBAAqBF,GAAI,EAApC,CAAN;AACD;;AAEiC,SAApBG,oBAAoB,CAChCC,UADgC,EAEhCC,OAFgC,EAG1B;AACN,QAAID,UAAU,IAAI,KAAKH,QAAvB,EAAiC;AAC/B,YAAM,IAAIC,KAAJ,CAAW,oBAAmBE,UAAW,iBAAzC,CAAN;AACD;;AAED,SAAKH,QAAL,CAAcG,UAAd,IAA4BC,OAA5B;AACA,SAAKJ,QAAL,CAAcG,UAAd,EAA0BE,MAA1B,CAAiCF,UAAjC;AACD;;AAE+B,SAAlBG,kBAAkB,CAACH,UAAD,EAA2B;AACzD,QAAI,EAAEA,UAAU,IAAI,KAAKH,QAArB,CAAJ,EAAoC;AAClC;AACD,KAHwD,CAKzD;;;AACA,WAAO,KAAKA,QAAL,CAAcG,UAAd,CAAP;AACD;;AAEqB,SAARI,QAAQ,GAAG;AACvB,WAAO,EAAE,GAAG,KAAKP;AAAV,KAAP;AACD;;AArCuC;;;;gBAAZH,W,cAIxB,E", "sourcesContent": ["import { ValueOf } from '../../typeUtils';\nimport { Gestures } from '../../RNGestureHandlerModule.web';\n\n// eslint-disable-next-line @typescript-eslint/no-extraneous-class\nexport default abstract class NodeManager {\n  private static gestures: Record<\n    number,\n    InstanceType<ValueOf<typeof Gestures>>\n  > = {};\n\n  public static getHandler(tag: number) {\n    if (tag in this.gestures) {\n      return this.gestures[tag];\n    }\n\n    throw new Error(`No handler for tag ${tag}`);\n  }\n\n  public static createGestureHandler(\n    handlerTag: number,\n    handler: InstanceType<ValueOf<typeof Gestures>>\n  ): void {\n    if (handlerTag in this.gestures) {\n      throw new Error(`Handler with tag ${handlerTag} already exists`);\n    }\n\n    this.gestures[handlerTag] = handler;\n    this.gestures[handlerTag].setTag(handlerTag);\n  }\n\n  public static dropGestureHandler(handlerTag: number): void {\n    if (!(handlerTag in this.gestures)) {\n      return;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n    delete this.gestures[handlerTag];\n  }\n\n  public static getNodes() {\n    return { ...this.gestures };\n  }\n}\n"]}